<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-DigitalFilter</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-DigitalFilter.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Digital Filter</span></h3>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">This function digitally filters the input. </span></p>
  <p class="spyderbodytext"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">+(X –Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><sup>(–t/Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">). </span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Where, t = 1 sec and Tau is in the range 0 - 65535 sec.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The output can be initialized to zero (zeroInit=TRUE) or the first valid input value (zeroInit=FALSE).</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">From iteration to iteration, the Function Block keeps track of the tau multiplier (1 – exp (–t/Tau)). On power up/reset, this is recalculated</span></p>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr>
          <td style=" vertical-align: bottom; width: 213.3pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Digital Filterlogicblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><strong><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></strong></p>
          </td>
          <td style=" vertical-align: bottom; width: 150.95pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Digital Filterfunctionblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Digital Filter Function</span></p>
  
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Inputs</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 01: Inputs of Digital Filter Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td rowspan="2" style=" vertical-align: middle; width: 11.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 29.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 15.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Value</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 43.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 14.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 14.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 11.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">X</span></p>
          </td>
          <td style=" vertical-align: top; width: 14.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=- infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 14.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 15.7%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 43.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The output is invalid.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: top; width: 11.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 14.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 14.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 15.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 43.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid and filter reinitializes when the input returns to valid.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Output of Digital Filter Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 16.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td style=" vertical-align: middle; width: 25.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 58.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y</span></p>
          </td>
          <td style=" vertical-align: top; width: 25.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point value</span></p>
          </td>
          <td style=" vertical-align: top; width: 58.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">= Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">+(X –Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"><sup>(–t/Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">).</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Setpoint</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 03: Setpoint of Digital Filter Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td style=" vertical-align: middle; width: 25.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 58.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y</span></p>
          </td>
          <td style=" vertical-align: top; width: 25.0%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point value</span></p>
          </td>
          <td style=" vertical-align: top; width: 58.3%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 7pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">+(X –Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><sup>(–t/Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">).</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p><span style=" font-size: 9pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.41in;">
          <td style=" vertical-align: top; width: 5.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 94.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: bold; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.27in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Both Analog and Digital inputs can be connected as inputs to this function block.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Example 1</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Set In1 (X) = 4, tau = 2.0, Set ZeroInit = 1 (initializes filter to 0.0)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> + (X – Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"><sup>(–t / Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the first iteration, </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">= 0; Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> + (X – Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">(1 – exp (–t/Tau))</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 0 + (4 - 0) * (1 – 2.718 (–1 / 2))</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 0 + 4 * (0.393)</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">= 1.572</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the second iteration,</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 1.572; X = 4; Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 1.57 + </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">(4 – 1.57) * (0.393)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 2.52</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the third iteration,</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 2.52 + (4 – 2.52) * (0.393)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 3.107</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The iterations continue until the input is reached.</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Example 2</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Set In1 (X) = 4, tau = 2.0, Set ZeroInit = 0 (initializes filter to first valid value)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> + (X – Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"><sup>(–t / Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the first iteration,</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = X</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 4</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the second iteration, if X = 6</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> + (X – Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"><sup>(–t / Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 4 + (6 – 4) * (0.393)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 4 + 0.786</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 4.786</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">In the third iteration, if X = 6</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">new</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> + (X – Y</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; position: relative; top: 3pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">old</span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">) * (1 – exp </span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"><sup>(–t / Tau)</sup></span><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 4.786 + (6 – 4.786) * (0.393)</span></p>
  <p class="spyderbodytext" style=" margin-top: 2pt; margin-bottom: 0;"><span style=" color: #0000FF; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> = 5.263.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The iterations continue until the input is reached.</span></p>
  <p><span style=" font-size: 9pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
</body>
</html>