<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-SquareRoot</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-SquareRoot.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Square Root</span></h3>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">This function takes the square root of the input. The Output Y is the Sqrt (x), where x is the input. The behavior of a negative x input is controlled by the parameter negInvalid.</span></p>
    <div align="left">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr style=" height: 0.27in;">
          <td style=" vertical-align: top; width: 3.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 96.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: bold; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.23in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset" style=" margin-bottom: 5pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Negative values are treated as absolute values. Example: Square root of -9801 is given as 99, taking the absolute value of –9801 as 9801.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">TailOperation:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The output value is based on one of four specified property values:</span></p>
  <ul class="List0" data-start="1" xmlns="">
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">No Change:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The actual result is returned.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Absolute:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The absolute (modulus or non-negative) value of the result is returned. For example, if the output is -3, the result is 3.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Integer:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The integer value of the result is returned. For example, if the output is 3.25, the result is 3.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Fractional:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The fractional value of the result is returned. For example, if the output is 3.25, the result is 25.</span></li>
  </ul>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse; margin-left: 0;" width="100%">
      <tbody>
        <tr>
          <td style=" vertical-align: bottom; width: 238.8pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt; text-align: center;"><img src="images/SquareRootlogicblock.png" /></p>
            <p class="spyderbodytext" style=" margin-top: 2pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></p>
          </td>
          <td style=" vertical-align: bottom; width: 152.45pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt; text-align: center;"><img src="images/SquareRootfunctionblock.png" /></p>
            <p class="spyderbodytext" style=" margin-top: 2pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Square Root Function</span></p>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Analog Inputs</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 01: Analog Inputs of Square Root Function</span></p>

  <table class="Table" style="width:100%; border-collapse:collapse; border:none" width="100%">
    <thead>
      <tr style="height:26.55pt">
        <td rowspan="2" style="border-bottom:solid 1.5pt; background:#d9d9d9; border-top:double 1.5pt; border-left:solid 1.0pt; border-right:solid 1.0pt; border-color:windowtext; padding:0in 5.4pt 0in 5.4pt; height:26.55pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Input Name</b></span></span></span></span></p>
        </td>
        <td colspan="2" style="border-bottom:solid windowtext 1.0pt; background:#d9d9d9; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.55pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Range</b></span></span></span></span></p>
        </td>
        <td rowspan="2" style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.55pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Input Value</b></span></span></span></span></p>
        </td>
        <td rowspan="2" style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.55pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Description</b></span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:14.1pt">
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:14.1pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Low</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:14.1pt">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>High</b></span></span></span></span></p>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr style="height:21.2pt">
        <td rowspan="3" style="border:solid windowtext 1.0pt; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td rowspan="3" style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">&gt;=– infinity</span></span></span></span></p>
        </td>
        <td rowspan="3" style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">&lt;+ infinity</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Y= 0 </span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:22.0pt">
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:22.0pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">invalid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:22.0pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Output is set to invalid</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:22.6pt">
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:22.6pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">x1 &lt; 0</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:22.6pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">See the description for negInvalid input.</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:26.35pt">
        <td rowspan="4" style="border:solid windowtext 1.0pt; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:26.35pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><a name="negInvalid">negInvalid</a></span></span></span></span></p>
        </td>
        <td rowspan="4" style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.35pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">0</span></span></span></span></p>
        </td>
        <td rowspan="2" style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.35pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">1</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.35pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">0</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:26.35pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Use the square root of the absolute value.</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:36.95pt">
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:36.95pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">1</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:36.95pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">If the input is negative, the output is invalid. The default value is 0. </span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:15.55pt">
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:15.55pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:15.55pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:15.55pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Y = sqrt(X), output is invalid for neg x1</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:21.2pt">
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">invalid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:21.2pt" valign="top">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Y = sqrt(X), output is invalid for neg x1</span></span></span></span></p>
        </td>
      </tr>
    </tbody>
  </table>
  
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Output of Square Root Function</span></p>

  <table class="Table" style="width:100.0%; border-collapse:collapse; border:solid windowtext 1.0pt" width="100%">
    <thead>
      <tr style="page-break-inside:avoid; height:9.85pt">
        <td style="border-bottom:solid 1.5pt; background:#d9d9d9; width:16.52%; border-top:double 1.5pt; border-left:solid 1.0pt; border-right:solid 1.0pt; border-color:windowtext; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="16%">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Input Name</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:25.1%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="25%">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Range</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:58.36%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="58%">
          <p align="center" class="spyderbodytext" style="margin-top:2.0pt; margin-right:0in; margin-bottom:2.0pt; margin-left:0in; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Description</b></span></span></span></span></p>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr style="height:14.35pt">
        <td style="border:solid windowtext 1.0pt; width:16.52%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:14.35pt" valign="top" width="16%">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Y </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:25.1%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:14.35pt" valign="top" width="25%">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Any floating-point value</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:58.36%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:14.35pt" valign="top" width="58%">
          <p class="spyderbodytext" style="margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Y= Sqrt (X)</span></span></span></span></p>
        </td>
      </tr>
    </tbody>
  </table>
  <p><span style=" font-size: 11pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"></span></p>
</body>
</html>