<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-TemperatureSetpointCalculator</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-TemperatureSetpointCalculator.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">TemperatureSetpointCalculator</span></h3>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">This function calculates the current Effective Heat setpoint and Effective Cool setpoint based on the current schedule information, occupancy override, and intelligent recovery information.</span></p>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr>
          <td style=" vertical-align: bottom; width: 248.45pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/TemperatureSetpointCalculatorlogicblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></p>
          </td>
          <td style=" vertical-align: bottom; width: 130.85pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/TemperatureSetpointCalculatorfunctionblock.png" /><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"></span></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Temperature Set Point Calculator Function</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"> </span></p>
  <p class="spyderH4"><span style=" font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Analog Inputs</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 01: </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Inputs of Temperature Set Point Calculator Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td rowspan="2" style=" vertical-align: middle; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Value</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: middle; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EffOccCurrentState</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">3</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Eff Occ Current State = 0 (OCC)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Eff Occ Current State = 0 (OCC)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Eff Occ Current State = 0 (OCC)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Eff Occ Current State = 0 (OCC)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">ScheduleNextState</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1, 3, 255</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule Next State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule Next State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule Next State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule Next State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">ScheduleTUNCOS (min)</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">11520</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule TUNCOS = 11520</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule TUNCOS = 11520</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule TUNCOS = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Schedule TUNCOS = 11520</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">HeatRampRate</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heat Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heat Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heat Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heat Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CoolRampRate</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool Ramp Rate = 0</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">ManualOverrideState</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">3,255</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Manual Override State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Manual Override State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Manual Override State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Manual Override State = 255 (OCCNUL)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">occupiedCool</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 75</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 75</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 75</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 75</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">standbyCool *</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 78</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 78</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 78</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 78</span></p>
          </td>
        </tr>
        <tr style=" height: 0.31in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unoccupiedCool *</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 85</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 85</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 85</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 85</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">occupiedHeat *</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 70</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 70</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 70</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 70</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">standbyHeat *</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 67</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 67</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 67</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 67</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td rowspan="4" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unoccupiedHeat *</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 7.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-∞</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+∞</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 55</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 55</span></p>
          </td>
        </tr>
        <tr style=" height: 0.05in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 55</span></p>
          </td>
        </tr>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: top; width: 16.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 44.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Setpoint = 55</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Occ State enumeration: Occ = 0, Unocc=1, Bypass =2, Standby = 3, Null = 255.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">* - extension block inputs (note: extension block PVID# = Block PVID# - 9)</span></p>
    <p class="spyderH4"><span style=" font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Outputs</span></p>
    <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Outputs of Temperature Set Point Calculator Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 22.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output Name</span></p>
          </td>
          <td style=" vertical-align: middle; width: 30.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td style=" vertical-align: middle; width: 46.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 22.8%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EFF_HEAT_SETPT</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.3%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point number</span></p>
          </td>
          <td style=" vertical-align: top; width: 46.9%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Effective Heat Setpoint</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 22.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EFF_COOL_SETPT</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point number</span></p>
          </td>
          <td style=" vertical-align: top; width: 46.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Effective Cool Setpoint</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderH4"><span style=" font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Operation:</span></p>
  <p class="spyderbodytext"><img src="images/TemperatureSetpointCalculatorfunctionblock01.png" /></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 02: Temperature Setpoint Function Block</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The Temperature Setpoint Calculator uses the 6 programmed-set-points, effective occupancy current state, scheduled next state and TUNCOS, center/offset setpoint, manual override state, recovery heat and cool ramp rates to determine the effective heat setpoint and effective cool setpoint.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The algorithm flow is:</span></p>
  <ol class="List3" data-start="1" xmlns="">
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Verify inputs are within range.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Compute the occupied and standby heat and cool setpoints based on the setpoint input and programmed setpoints.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If the effective occupancy current state is in unoccupied mode and not in manual override, calculate the recovery ramps.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If the effective occupancy current state is in occupied or bypass mode, use the occupied setpoints.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If the effective occupancy current state is in standby mode, use the standby setpoints.</span></li>
  </ol>
  <p class="spyderbodytext" style=" margin-bottom: 0;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <p class="Spydercaption" style=" margin-bottom: 0;"><img height="394" src="images/TemperatureSetpointCalculatorfunctionblock02.png" width="462" /></p>
  <p class="Spydercaption" style=" margin-bottom: 0;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">04</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">: </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Heat and Cool Recovery Ramps</span></p>
  <p class="spyderbodytext" style=" margin-bottom: 0;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">You can provide the heat and cool recovery ramp rates to the Temperature Setpoint Calculator. These can be constants, values calculated using the Ratio function block using outdoor air temperature, or some other method.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heating and cooling recovery ramp rates can be any value greater than or equal to zero and have units of </span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">/hr. A ramp rate of 0</span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">/hr. means no recovery ramp for that mode. This means the setpoint steps from one setpoint to the other at the event time that is no extra 10 minutes. You must ensure consistent units. That is, the ramp rates should be in the same units as the setpoints.</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.41in;">
          <td style=" vertical-align: top; width: 5.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 94.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: bold; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.27in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">If you program a rate of 1</span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">/Hr. and have more than 192</span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> spread between OCC and UNOCC set points, the algorithm is in recovery immediately when going to UNOCC. This is because the maximum TUNCOS is 11520 minutes times 1</span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">/Hr. = 192</span><span style=" color: #000000; font-family: Symbol; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;"> maximum delta.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p style=" font-size: 10pt;"><span style=" font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"> </span></p>
</body>
</html>