<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Honeywell CIPer Model 30 Controller Function Block</title>
	<meta content="Adobe RoboHelp 2019" name="generator">
	<link href="css/default.css" rel="stylesheet">
	<link href="css/ipcCommBus-ApplicationFolder.css" rel="stylesheet">
</head>
<body>
	<h1 style="color: #21618C; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 20pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Honeywell CIPer Model 30 Controller Function Block</span></h1>
	<p class="spyderH4">&nbsp;</p>
	
	<p class="spyderbodytext"><span style="color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Function blocks are the library of the objects used to implement any custom application logic for wide variety of HVAC applications. A function block has inputs and outputs. A Function block receives inputs from physical inputs, network inputs, or from output of another function block. The function block processes the received input data and produces an output. Processing depends upon the function block type. All function blocks are available in the ipcProgrammingTool palette in the WEBStation N4 application. The function block for CIPer Model 30 also provides offline expansion I/O configuration support, which helps you to connect more inputs and outputs.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Common Behavior Overview</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Following are the common behaviors, which are applicable for all function blocks.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Execution Time</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The execution time displays the default time (write synchronization) that is continuous 5-minute Interval. If required you can adjust this, or set to Daily or Manual.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Status</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">It shows the component status at the last check.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Facets</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">It gives the facets in use by the parent proxy point.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Out Save</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The Out Save feature saves the value of the component which was last saved. Suppose, you have enabled the Out Save feature. The output value is 50 and it is saved, and later the output value changes to 60, which is not yet saved. In this case, if the controller stops functioning, the Out Save displays the output value as 50, because 50 was the value which was saved last. If the Out Save feature is disabled, the output is some garbage value.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Copyright and patent notice</span></p>
	<p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The material in this document is for information purposes only. The content and the product described are subject to change without notice. Honeywell makes no representations or warranties with respect to this document. In no event shall Honeywell be liable for technical or editorial omissions or mistakes in this document, nor shall it be liable for any damages, direct or incidental, arising out of or related to the use of this document. No part of this document may be reproduced in any form or by any means without prior written permission from Honeywell.</span></p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Copyright © 2021 HONEYWELL International, Inc. All rights reserved.</span></p>
	<p class="spyderH4">&nbsp;</p>
	<p style=" font-size: 12pt;">See also </p> <p style=" font-size: 12pt;"><a href="HoneywellSoftwareEndUserLicenseAgreement.html">End User License Agreement</a></p>
	<p style=" font-size: 12pt;">See also </p> <p style=" font-size: 12pt;"><a href="LicenseAgreementWindow.html">License Agreement Window</a></p>
	<p class="spyderH4">&nbsp;</p>
	<p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Niagara Framework® is a registered trademark of Tridium Inc.</span></p>
</body>
</html>