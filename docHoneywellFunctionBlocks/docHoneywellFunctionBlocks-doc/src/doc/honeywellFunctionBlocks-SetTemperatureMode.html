<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-SetTemperatureMode</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-SetTemperatureMode.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">SetTemperatureMode</span></h3>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">This function automatically calculates the effective temperature control mode based on the control type, system switch setting, network mode command, temperature set points, supply temperature and space temperature.</span></p>
   <p class="spyderbodytext" style=" text-align: center;"><img src="images/SetTemperaturepropertysheet.png" /></p>
   <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">SetTemperatureMode Property Sheet</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">From iteration to iteration, the Function Block keeps track of the previous command mode and the effective temperature mode. On power up/reset, these are cleared.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">effTempMode indicates the current Mode determined by input states and arbitrated by control logic. SetTempMode does not generate all the possible Modes available. The following table shows the meaning of the valid enumerated values. The following table shows the meaning of valid enumerated values.</span></p>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: bottom; width: 233.75pt; border-top: none; border-right: none; padding-right: 5.4pt; border-bottom: none; border-left: none; padding-left: 5.4pt; padding-top: 0; padding-bottom: 0;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/SetTemperatureModelogicblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></p>
          </td>
          <td style=" vertical-align: bottom; width: 180pt; border-top: none; border-right: none; padding-right: 5.4pt; border-bottom: none; border-left: none; padding-left: 5.4pt; padding-top: 0; padding-bottom: 0;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"></span><img src="images/SetTemperatureModefunctionblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Set Temperature Mode Function</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The following table shows the meaning of valid enumerated values.</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">01: Meanings of Valid Enumerated Values</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: middle; width: 28.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">effTempMode</span></p>
          </td>
          <td style=" vertical-align: middle; width: 71.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Meaning</span></p>
          </td>
        </tr>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: top; width: 28.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">COOL_MODE = 0</span></p>
          </td>
          <td style=" vertical-align: top; width: 71.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool air is being supplied to the node via the central air supply and cooling energy is being supplied to the controlled space.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 28.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">REHEAT_MODE = 1</span></p>
          </td>
          <td style=" vertical-align: top; width: 71.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Cool air is being supplied to the node via the central air supply. The air is being reheated by a local Heat source.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: top; width: 28.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">HEAT_MODE = 2</span></p>
          </td>
          <td style=" vertical-align: top; width: 71.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Heated air is being supplied to the node via the central air supply and heated air is being supplied to the controlled space</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 28.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EMERG_HEAT = 3</span></p>
          </td>
          <td style=" vertical-align: top; width: 71.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Emergency Heat is being supplied to the node via the central air supply. </span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 28.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">OFF_MODE = 255</span></p>
          </td>
          <td style=" vertical-align: top; width: 71.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The controller is commanded off.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Analog Input</span></p>
 
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Analog Inputs of Set Temperature Mode Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td rowspan="2" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Value</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: top; width: 21.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">sysSwitch</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 10.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 10.7%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">System Switch = SS_AUTO(0) </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">System Switch = SS_AUTO(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">System Switch = SS_AUTO(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">System Switch = SS_AUTO(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: top; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">cmdMode</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: top; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">val = CMD_AUTO_MODE(0) </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">val = CMD_AUTO_MODE(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">val = CMD_AUTO_MODE(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">VAL &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">val = CMD_AUTO_MODE(0)</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">supplyTemp</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Supply Temp = invalid </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Supply Temp = invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SupplyTemp = low</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SupplyTemp = high</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">spaceTemp</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SpaceTemp = invalid </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SpaceTemp = invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SpaceTemp = low</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SpaceTemp = high</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="2" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">effHeatSP</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+</span></p>
          </td>
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EffHeatSp = 68</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EffHeatSp = 68</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="2" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">effCoolSP</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=-</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+</span></p>
          </td>
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EffCoolSp = 75 </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">EffCoolSp = 75</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: middle; width: 21.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">allowAutoChange</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; width: 10.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1</span></p>
          </td>
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">allowAutoChange=1 </span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">allowAutoChange=1</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &lt; low</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">allowAutoChange=1</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Val &gt; high</span></p>
          </td>
          <td style=" vertical-align: top; width: 41.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">allowAutoChange=1</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderH4"> </p>
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Outputs</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 03: Output of Set Temperature Mode Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.16in;">
          <td rowspan="2" style=" vertical-align: middle; width: 16.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 16.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 67.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.10in;">
          <td style=" vertical-align: middle; width: 8.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 8.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">effSetpt</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0.0</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: top; width: 67.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If effTempMode=COOL_MODE then val= effCoolSetPt, else val=effHeatSetPt</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">effTempMode</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">255</span></p>
          </td>
          <td style=" vertical-align: top; width: 67.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">See arbitration table for VAV and CVAHU behavior</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderH4"> </p>
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Configuration</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Specify the control Type (controlType) </span></p>
  <ul class="List0" data-start="1" xmlns="">
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 – CVAHU</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1 – VAV</span></li>
  </ul>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Specify the Behavior Type (controlType) </span></p>
  <ul class="List0" data-start="1" xmlns="">
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Legacy – For normal output (Spyder Behavior) </span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Enhanced – For enhanced output. If no device is connected sysSwitch considered as SS Auto as default input for both CVAHU and VAV and EFF Temp mode output displays Heat mode.</span></li>
  </ul>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 101%;" width="100%">
      <tbody>
        <tr style=" height: 0.16in;">
          <td rowspan="2" style=" vertical-align: middle; width: 16.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 16.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 67.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.10in;">
          <td style=" vertical-align: middle; width: 8.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 8.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">controlType</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.0%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0</span></p>
          </td>
          <td style=" vertical-align: top; width: 8.3%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1</span></p>
          </td>
          <td style=" vertical-align: top; width: 67.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: left;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">You can specify the control Type 0 = CVAHU; 1 = VAV</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderH4"> </p>
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Enumerations</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 04: Input Enumerations of Set Temperature Mode Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: middle; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">sysSwitch</span></p>
          </td>
          <td style=" vertical-align: middle; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
        </tr>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SS_AUTO</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 0 </span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SS_COOL</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 1</span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SS_HEAT</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 2</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SS_EMERG_HEAT</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 3</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">SS_OFF</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 255</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">cmdMode</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_AUTO_MODE= 0</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 0 </span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_ HEAT_MODE = 1</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 1</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_COOL_MODE = 2</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 2</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_OFF_MODE = 3</span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 3</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_ EMERG_HEAT_MODE = 4 </span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 4</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 73.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">CMD_NUL_MODE = 255 </span></p>
          </td>
          <td style=" vertical-align: top; width: 26.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">= 255</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderbodytext" style=" font-size: 10pt;"><span class="SC290826">The CVAHU arbitration logic for ControlType = 0 (CVAHU) is summarized in the following table.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"> </span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">05: </span><span class="SC290826" style=" font-size: 10pt; font-weight: bold;">CVAHU Arbitration Logic for ControlType = 0 (CVAHU)</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
  <table class="Table" style="width:100.0%; border-collapse:collapse; border:solid windowtext 1.0pt" width="100%">
    <thead>
      <tr>
        <td style="border-bottom:solid 1.5pt; background:#d9d9d9; width:15.48%; border-top:double 1.5pt; border-left:solid 1.0pt; border-right:solid 1.0pt; border-color:windowtext; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="center" class="spyderbodytext" style="text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Space Temp</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:21.0%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" width="21%">
          <p align="center" class="spyderbodytext" style="text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>sysSwitch</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:31.34%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" width="31%">
          <p align="center" class="spyderbodytext" style="text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>cmdMode</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:32.18%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" width="32%">
          <p align="center" class="spyderbodytext" style="text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>effTempMode</b></span></span></span></span></p>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_OFF(3)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">OFF_MODE(255) </span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_EMERG_HEAT_MODE(4)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">EMERG_HEAT(3)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:17.15pt">
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:17.15pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:17.15pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:17.15pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_COOL_MODE(2)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:17.15pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0)</span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_HEAT_MODE(1)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2)</span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">ENUMERATION (5) through ENUMERATION (254)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2) </span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_COOL (1)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE (0)</span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_HEAT (2) or ENUMERATION(4) through ENUMERATION (254)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2) </span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_EMERGENCY_HEAT(3)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255), </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">EMERG_HEAT(3)</span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_OFF (255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">OFF_MODE(255)</span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">INVALID</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_AUTO (0), invalid, unconnected, or a non-listed enumeration.</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2) </span></span></span></span></p>
        </td>
      </tr>
      <tr>
        <td style="border:solid windowtext 1.0pt; width:15.48%; border-top:none; padding:0in 5.4pt 0in 5.4pt" width="15%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">VALID</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:21.0%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="21%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_AUTO (0), invalid, unconnected, or a non-listed enumeration.</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:31.34%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="31%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE(0), CMD_NUL_MODE(255),</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:32.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt" valign="top" width="32%">
          <p align="left" class="spyderbodytext" style="text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0) or HEAT_MODE(2) (See the note following this table.)</span></span></span></span></p>
        </td>
      </tr>
    </tbody>
  </table>
  <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"></span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.41in;">
          <td style=" vertical-align: top; width: 6.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 93.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.27in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <ul class="List2" data-start="1" xmlns="">
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">X means don’t care.</span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">If allowAutoChange = 1 then allow to switch between HEAT_MODE and COOL_MODE. </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Must have valid effHeatSP and effCoolSP. If allowAutoChange = 1 and effHeatSp &gt; effCoolSp then effHeatSp is internally set to effCoolSP.</span></li>
            </ul>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="spyderbodytext" style=" margin-top: 2pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">The VAV Mode arbitration logic for controlType = 1 the following table summarizes (VAV):</span></p>
  
  <p style=" font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 9pt;"> </p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">06: VAV Mode Arbitration Logic for controlType = 1</span></p>

  <table align="center" class="Table" style="width:100.0%; border-collapse:collapse; border:solid windowtext 1.0pt" width="100%">
    <thead>
      <tr style="height:9.85pt">
        <td style="border-bottom:solid 1.5pt; background:#d9d9d9; width:10.26%; border-top:double 1.5pt; border-left:solid 1.0pt; border-right:solid 1.0pt; border-color:windowtext; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="10%">
          <p align="center" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Space Temp </b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:19.86%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="19%">
          <p align="center" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>sysSwitch</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:14.58%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="14%">
          <p align="center" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>Supply Temp</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:19.18%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="19%">
          <p align="center" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>cmdMode</b></span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.5pt; background:#d9d9d9; width:36.12%; border-top:double windowtext 1.5pt; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:9.85pt" width="36%">
          <p align="center" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:center; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"><b>effTempMode</b></span></span></span></span></p>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_OFF_MODE(3)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">OFF_MODE(255) </span></span></span></span></p>
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black"></span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_EMERG_HEAT_MODE(4)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">ENUMERATION (5) through ENUMERATION (254)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">&lt;70.0</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_HEAT_MODE (1), CMD_NUL_MODE (255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE (0) or REHEAT_MODE (1)<br />
                    (See the note above this table.)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">70.0 To 75.0</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_HEAT_MODE (1), CMD_COOL_MODE (2), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p align="left" class="spyderbodytext" style="margin-bottom:3.0pt; text-align:left; margin:5pt 0in"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE (0), REHEAT_MODE (1), HEAT_MODE (2)<br />
                    (See the note above this table for transition between cool mode and reheat mode.)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">&gt;75</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_HEAT_MODE (1), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_HEAT_MODE (1) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE (2)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">X</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_COOL_MODE (2) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE (0)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_COOL(1)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_HEAT (2)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid SS_</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">EMERGENCY_HEAT (3)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">HEAT_MODE(2)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_OFF (255)</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255) </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">OFF_MODE(255)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Valid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_AUTO (0), invalid, unconnected, or a non-listed enumeration.</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255), </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0) or REHEAT_MODE(1) (See the note above this table.)</span></span></span></span></p>
        </td>
      </tr>
      <tr style="height:13.0pt">
        <td style="border:solid windowtext 1.0pt; width:10.26%; border-top:none; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="10%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.86%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">SS_AUTO (0), invalid, unconnected, or a non-listed enumeration.</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:14.58%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="14%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">Invalid or unconnected</span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:19.18%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="19%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">CMD_AUTO_MODE (0), CMD_NUL_MODE (255), </span></span></span></span></p>
        </td>
        <td style="border-bottom:solid windowtext 1.0pt; width:36.12%; border-top:none; border-left:none; border-right:solid windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt; height:13.0pt" valign="top" width="36%">
          <p class="spyderbodytext" style="margin-bottom:3.0pt; margin:5pt 0in; text-align:justify"><span style="font-size:10pt"><span style="line-height:120%"><span style="font-family:Arial,sans-serif"><span style="color:black">COOL_MODE(0)</span></span></span></span></p>
        </td>
      </tr>
    </tbody>
  </table>
  <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"></span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.41in;">
          <td style=" vertical-align: top; width: 6.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 93.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: bold; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.27in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <ul class="List1" data-start="1" xmlns="">
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">X means don’t care.</span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">If allowAutoChange = 1 then allow to switch between REHEAT_MODE and COOL_MODE. Must have valid effHeatSP and effCoolSP. </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">If in cool mode and spaceTemp &lt; effHeatSetPt and space temp &lt; effCoolSetPt – 1.0 then go to reheat mode. If in reheat mode and spacetemp &gt; effCoolSetPt and spacetemp &gt; effHeatSetPt + 1.0 then go to cool mode.</span></li>
            </ul>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p style=" font-family: Calibri;"><span style=" font-size: 11pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
</body>
</html>