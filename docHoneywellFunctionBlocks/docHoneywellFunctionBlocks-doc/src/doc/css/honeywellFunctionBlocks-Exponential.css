ul.List0 {
counter-reset: section01;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
span.SC291167 {
    color: #000000;
    font-family: 'Arial', 'sans-serif';
    font-size: 10pt;
    font-style: normal;
    font-weight: bold;
    margin: 0;
    padding: 0
}
body {
margin: 1cm auto; 
 max-width: 20cm; 
 padding: 0
}
ul.List0 > li {
 position: relative
}
ul.List0 > li::before {
counter-increment: section01;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}