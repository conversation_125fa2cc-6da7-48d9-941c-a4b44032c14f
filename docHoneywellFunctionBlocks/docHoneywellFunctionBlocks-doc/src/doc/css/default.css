/*Created with Adobe RoboHelp 2019.*/
body {
  font-family: Calibri;
  background-color: #ffffff;
}
h1 {
  font-weight: bold;
  font-size: 24.0pt;
}
h2 {
  font-weight: bold;
  font-size: 18.0pt;
}
h3 {
  font-weight: bold;
  font-size: 14.0pt;
}
h4 {
  font-weight: bold;
  font-size: 12.0pt;
}
h5 {
  font-weight: bold;
  font-size: 10.0pt;
}
h6 {
  font-weight: bold;
  font-size: 8.0pt;
}
p {
  font-size: 11.0pt;
  margin-top: 4.0pt;
  margin-bottom: 4.0pt;
}
a.expandspot {
  color: #008000;
  cursor: pointer;
  font-style: italic;
  text-decoration: none;
}
span.expandtext {
  font-style: italic;
  font-weight: normal;
  color: #ff0000;
}
a.dropspot {
  cursor: pointer;
  color: #008000;
  font-style: italic;
  text-decoration: none;
}
a.glossterm {
  color: #800000;
  cursor: pointer;
  font-style: italic;
  text-decoration: none;
}
span.glosstext {
  font-style: italic;
  font-weight: normal;
  color: #0000ff;
}
ol {
  margin-top: 0px;
  margin-bottom: 0px;
}
ul {
  margin-top: 0px;
  margin-bottom: 0px;
}
a.minitoc-caption {
  text-decoration: none;
  color: initial;
  font-weight: bold;
  font-size: 12pt;
}
p.minitoc-caption {
  font-weight: bold;
  font-size: 12pt;
}
ol.minitoc-list {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
}
ol.minitoc-list ol {
  list-style: none;
}
a.minitoc-list-item {}
p.seealso-caption {
  font-weight: bold;
  font-size: 12pt;
}
ol.seealso-list {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
}
a.seealso-list-item {}
p.reltopics-caption {
  font-weight: bold;
  font-size: 12pt;
}
ol.reltopics-list {
  padding-left: 0;
  margin-left: 0;
  list-style: none;
}
a.reltopics-list-item {}
h3.spyderH3 {
  margin-top: 8pt;
  line-height: 150.0%;
  margin-bottom: 8pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 12pt;
  margin-left: 0;
  margin-right: 0
}
table.NormalTable {
  border-collapse: collapse;
  border: none;
  width: 100%;
  margin-bottom: .001pt
}
p.Spydercaption {
  margin-top: 5pt;
  line-height: 115.0%;
  margin-bottom: 5pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  margin-left: 0;
  margin-right: 0
}
body {
  margin: 1cm auto;
  max-width: 20cm;
  padding: 0
}
p.spyderH4 {
  margin-top: 5pt;
  line-height: 115.0%;
  margin-bottom: 5pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  margin-left: 0;
  margin-right: 0
}
p.spyderbodytext {
  margin-top: 10pt;
  margin-bottom: 10pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  line-height: 1.15;
  margin-left: 0;
  margin-right: 0
}
p.CVAHUNote {
  margin-top: 8pt;
  margin-bottom: 2pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 9pt;
  line-height: 108%;
  margin-left: 0;
  margin-right: 0
}
ul.List1 {
  counter-reset: section11;
  --prefix-size: 40px;
  --prefix-gap: 4px;
  --list-indent: 40px;
  --prefix-fixed-size: false;
  list-style: none;
  padding-left: var(--prefix-size);
  margin-left: calc(var(--list-indent) - var(--prefix-size))
}
p.Numbering21 {
  margin-top: 5pt;
  line-height: 120.0%;
  margin-bottom: 5pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  margin-left: 0;
  margin-right: 0
}
p.Numbering21::before {
  content: counter(bullet, disc);
  padding-right: 0.5rem
}
span.SC290826 {
  color: #000000;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  font-style: normal;
  font-weight: normal;
  margin: 0;
  padding: 0
}
body {
  counter-reset: Numbering21;
  margin: 1cm auto;
  max-width: 20cm;
  padding: 0
}
span {
  white-space: pre-wrap
}
p.spydernoteinset {
  margin-top: 5pt;
  line-height: 120.0%;
  margin-bottom: 5pt;
  font-family: 'Arial', 'sans-serif';
  font-size: 10pt;
  margin-left: 0;
  margin-right: 0
}
ul.List1>li {
  position: relative
}
ul.List1>li::before {
  counter-increment: section11;
  content: counter(bullet, disc);
  --prefix-size: 40px;
  --prefix-gap: 4px;
  --list-indent: 40px;
  --prefix-fixed-size: false;
  text-align: right;
  position: absolute;
  left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
  margin-right: var(--prefix-gap);
  width: var(--prefix-size);
  overflow: hidden
}
ol.Decimal {
  list-style-type: decimal;
}