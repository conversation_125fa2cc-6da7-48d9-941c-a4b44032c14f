ol.List0 {
counter-reset: section01;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ol.List10 {
counter-reset: section101;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ul.List3 {
counter-reset: section31;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ul.List4 {
counter-reset: section41;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ul.List7 {
counter-reset: section71;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ol.List8 {
counter-reset: section81;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
body {
margin: 1cm auto; 
 max-width: 20cm; 
 padding: 0
}
ol.List0 > li {
 position: relative
}
ol.List0 > li::before {
counter-increment: section01;
content:counter(section01,decimal)".";
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ol.List10 > li {
 position: relative
}
ol.List10 > li::before {
counter-increment: section101;
content:counter(section101,decimal)".";
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ol.List8 > li {
 position: relative
}
ol.List8 > li::before {
counter-increment: section81;
content:counter(section81,decimal)".";
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ul.List3 > li {
 position: relative
}
ul.List3 > li::before {
counter-increment: section31;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ul.List4 > li {
 position: relative
}
ul.List4 > li::before {
counter-increment: section41;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ul.List7 > li {
 position: relative
}
ul.List7 > li::before {
counter-increment: section71;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}