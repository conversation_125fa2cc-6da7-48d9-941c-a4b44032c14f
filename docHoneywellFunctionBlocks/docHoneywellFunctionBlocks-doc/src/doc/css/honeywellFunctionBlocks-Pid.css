p.CVAHUbodytext {
    margin-top: 2pt;
    margin-bottom: 2pt;
    font-family: 'Arial', 'sans-serif';
    font-size: 9pt;
    line-height: 108%;
    margin-left: 0;
    margin-right: 0
}
ul.List2 {
counter-reset: section21;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ul.List3 {
counter-reset: section31;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
p.Numbering31 {
    margin-top: 5pt;
    line-height: 120.0%;
    margin-bottom: 5pt;
    text-align: justify;
    font-family: 'Arial', 'sans-serif';
    font-size: 10pt;
    margin-left: 0;
    margin-right: 0
}
p.Numbering31::before {
    counter-increment: Numbering31;
    content: counter(Numbering31,decimal);
    padding-right : 0.5rem
}
body {
counter-reset:Numbering31 ;
margin: 1cm auto; 
 max-width: 20cm; 
 padding: 0
}
ul.List2 > li {
 position: relative
}
ul.List2 > li::before {
counter-increment: section21;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ul.List3 > li {
 position: relative
}
ul.List3 > li::before {
counter-increment: section31;
content:counter(bullet,disc);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}
ul.List4 > li > ul {
counter-reset: section42;
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
list-style:none;
padding-left: var(--prefix-size);
margin-left: calc(var(--list-indent) - var(--prefix-size))
}
ul.List4 > li > ul > li {
 position: relative
}
ul.List4 > li > ul > li::before {
counter-increment: section42;
content:counter(bullet,circle);
--prefix-size: 40px;
--prefix-gap: 4px;
--list-indent: 40px;
--prefix-fixed-size: false;
text-align: right;
position: absolute;
left: calc(-1 * var(--prefix-size) - var(--prefix-gap));
margin-right: var(--prefix-gap);
width: var(--prefix-size);
overflow: hidden
}