<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-Divide</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-Divide.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Divide</span></h3>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">This function divides one input by the other.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y = x1 / x2. Division by 0 results in an invalid output. If the result overflows the range of a single precision floating point number (approximately minus 3.4e</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><sup>38</sup></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> to plus 3.4e</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><sup>38</sup></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">) the result returned is invalid.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">divOperation:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The result of the division is based on one of two specified property values. For Modulo, the output is the remainder of the division; for </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Divide</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">, the output is the quotient.</span></p>
  <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">TailOperation</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">: The output value is based on one of four specified property values:</span></p>
  <ul class="List0" data-start="1" xmlns="">
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">No Change:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The actual result is returned.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Absolute:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The absolute (modulus or non-negative) value of the result is returned. For example, if the output is -3, the result is 3.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Integer:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The integer value of the result is returned. For example, if the output is 3.25, the result is 3.</span></li>
    <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Fractional:</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> The fractional value of the result is returned. For example, if the output is 3.25, the result is .25.</span></li>
  </ul>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr style=" height: 0.41in;">
          <td style=" vertical-align: top; width: 5.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="CVAHUNote"><img src="images/NoteIcon.png" /></p>
          </td>
          <td style=" vertical-align: top; width: 94.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: bold; margin: 0; padding: 0;">Note: </span></p>
          </td>
        </tr>
        <tr style=" height: 0.27in;">
          <td colspan="2" style=" vertical-align: top; width: 100.0%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spydernoteinset"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: italic; font-weight: normal; margin: 0; padding: 0;">Both Analog and Digital inputs can be connected as inputs to this function block.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"> </span></p>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse; margin-left: 0;" width="100%">
      <tbody>
        <tr>
          <td style=" vertical-align: bottom; width: 215.75pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Dividelogicblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></p>
          </td>
          <td style=" vertical-align: bottom; width: 148.5pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Dividefunctionblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Divide Function</span></p>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Analog Inputs</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 01: Analog Inputs of Divide Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr style=" height: 0.20in;">
          <td rowspan="2" style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-top: 4pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Value</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Ignore Invalid Input</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;width:55pt">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;width:55pt">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.18in;">
          <td rowspan="4" style=" vertical-align: middle; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">x1, x2</span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;= – infinity </span></p>
          </td>
          <td rowspan="4" style=" vertical-align: middle; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity </span></p>
          </td>
          <td style=" vertical-align: middle; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected </span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Not used in the calculation. If all inputs are unconnected, output is set to invalid.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.24in;">
          <td style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid </span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">false</span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If any input is invalid, output is invalid.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.24in;">
          <td style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">true</span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output considers only valid inputs while determining the division of the inputs.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.24in;">
          <td style=" vertical-align: middle; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">valid</span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Calculates the division of two inputs or those set as constant.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p><span style=" font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;"> </span></p>
  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Output of Divide Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr style=" height: 0.09in;">
          <td style=" vertical-align: middle; width: 25.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output Name</span></p>
          </td>
          <td style=" vertical-align: top; width: 37.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td style=" vertical-align: middle; width: 37.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.13in;">
          <td style=" vertical-align: middle; width: 25.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y out</span></p>
          </td>
          <td style=" vertical-align: top; width: 37.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point value</span></p>
          </td>
          <td style=" vertical-align: middle; width: 37.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y= x1/x2</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Configuration</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 03: Configuration of Divide Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 103%;" width="100%">
      <tbody>
        <tr style=" height: 0.09in;">
          <td style=" vertical-align: middle; width: 15.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Name</span></p>
          </td>
          <td style=" vertical-align: top; width: 11.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td style=" vertical-align: middle; width: 73.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.13in;">
          <td style=" vertical-align: top; width: 15.3%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalidFlag</span></p>
          </td>
          <td style=" vertical-align: top; width: 11.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 to 1</span></p>
          </td>
          <td style=" vertical-align: top; width: 73.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">See above table.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.13in;">
          <td style=" vertical-align: top; width: 15.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">divOperation</span></p>
          </td>
          <td style=" vertical-align: top; width: 11.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 to 1</span></p>
          </td>
          <td style=" vertical-align: top; width: 73.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: left;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Divide operation to be performed:</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 = division/quotient. Result is x1/x2.</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1 = modulo/remainder. The result is x1 modulo x2, that is,</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">the remainder f, where</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">x1 = a*x2 + f </span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">for some integer a and 0 &lt;= f &lt; x2.</span></p>
          </td>
        </tr>
        <tr style=" height: 0.13in;">
          <td style=" vertical-align: top; width: 15.3%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">tailOperation</span></p>
          </td>
          <td style=" vertical-align: top; width: 11.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 to 3</span></p>
          </td>
          <td style=" vertical-align: top; width: 73.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: left;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Operation to be applied to the result just prior to output:</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0 = no change to result</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">1 = absolute value (result is made positive if negative)</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">2 = integer part. The fractional part is truncated to 0. The sign is retained</span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><br />
                ‎</span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">3 = fractional part. The integer part is truncated to 0. The result is always positive.</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p style=" font-size: 9pt;"><span style=" font-size: 9pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
</body>
</html>