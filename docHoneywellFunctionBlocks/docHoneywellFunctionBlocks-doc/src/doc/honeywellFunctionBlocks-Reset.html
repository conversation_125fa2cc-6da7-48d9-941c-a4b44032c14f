<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8" />
  <title>honeywellFunctionBlocks-Reset</title>
  <meta name="generator" content="Adobe RoboHelp 2019" />
  <link rel="stylesheet" href="css/default.css" />
  <link rel="stylesheet" href="css/honeywellFunctionBlocks-Reset.css" />
</head>
<body>
  <h3 class="spyderH3"><span style=" font-family: Arial Bold; font-size: 18pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Reset</span></h3>
  <p class="spyderbodytext"><span class="SC290826">This function computes the reset value based on the relation of the input to the reset parameters.</span></p>
  <div align="left">
    <table border="0" cellpadding="0" cellspacing="0" class="NormalTable" style="border-collapse:collapse;" width="100%">
      <tbody>
        <tr>
          <td style=" vertical-align: bottom; width: 238.8pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Resetlogicblock.png" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Logic Diagram</span></p>
          </td>
          <td style=" vertical-align: bottom; width: 168.1pt; border-top-style: none; padding-top: 0; border-right-style: none; padding-right: 5.4pt; border-bottom-style: none; padding-bottom: 0; border-left-style: none; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" text-align: center;"><img src="images/Resetfunctionblock.png" style="cursor: nesw-resize;" /></p>
            <p class="spyderbodytext" style=" text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Function Block</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 01: Reset Function</span></p>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Analog Inputs </span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 01: Analog Inputs of Reset Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td rowspan="2" style=" vertical-align: middle; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td colspan="2" style=" vertical-align: middle; width: 27.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Value</span></p>
          </td>
          <td rowspan="2" style=" vertical-align: middle; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Low</span></p>
          </td>
          <td style=" vertical-align: middle; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">High</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td rowspan="2" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">input</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=– infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid </span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td rowspan="2" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">sensor</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=– infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td rowspan="3" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">zeroPctResetVal</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=– infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0%RV = 100%RV</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td rowspan="3" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">hundredPctResetVal</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=– infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">0%RV = 100%RV</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to input</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td rowspan="2" style=" vertical-align: top; width: 22.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">resetAmount</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&gt;=– infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">&lt;+ infinity</span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">unconnected</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output is set to invalid</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 13.6%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
          </td>
          <td style=" vertical-align: top; width: 19.9%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">invalid</span></p>
          </td>
          <td style=" vertical-align: top; width: 30.7%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output</span></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 02: Output of Reset Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.14in;">
          <td style=" vertical-align: middle; width: 16.5%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Input Name</span></p>
          </td>
          <td style=" vertical-align: middle; width: 25.1%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Range</span></p>
          </td>
          <td style=" vertical-align: middle; width: 58.4%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Description</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 16.5%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">OUTPUT</span></p>
          </td>
          <td style=" vertical-align: top; width: 25.1%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Any floating-point value</span></p>
          </td>
          <td style=" vertical-align: top; width: 58.4%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Y = Reset (input, sensor, 0%, 100%, reset amount)</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <p class="spyderH4"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 12pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Working</span></p>
  <p class="spyderbodytext"><img src="images/Reset01.png" /></p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Figure 02: Working of Reset</span></p>
  <p class="Spydercaption"> </p>
  <p class="Spydercaption"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Table 03: Input and Output of Reset Function</span></p>
  <div align="center">
    <table border="1" cellpadding="1" cellspacing="1" class="NormalTable" style="border-collapse:collapse; width: 100%;" width="100%">
      <tbody>
        <tr style=" height: 0.19in;">
          <td style=" vertical-align: middle; width: 46.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">If Input Condition is </span></p>
          </td>
          <td style=" vertical-align: middle; width: 53.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.5pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt; background: #d8d8d8;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt; text-align: center;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: bold; margin: 0; padding: 0;">Output</span></p>
          </td>
        </tr>
        <tr style=" height: 0.20in;">
          <td style=" vertical-align: top; width: 46.2%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <ul class="List0" data-start="1" xmlns="">
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Input is unconnected </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Input is invalid</span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Sensor is unconnected </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">zeroPctResetVal is unconnected </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">hundredPctResetVal is unconnected </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">resetAmount is unconnected</span></li>
            </ul>
          </td>
          <td style=" vertical-align: top; width: 53.8%; border-top: solid windowtext 1.5pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = invalid </span></p>
          </td>
        </tr>
        <tr style=" height: 0.11in;">
          <td style=" vertical-align: top; width: 46.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <ul class="List1" data-start="1" xmlns="">
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Sensor is invalid </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Sensor &lt; zeroPctResetVal </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">zeroPctResetVal is invalid </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">hundredPctResetVal is invalid </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">resetAmount is invalid </span></li>
              <li><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">hundredPctResetVal = zeroPctResetVal</span></li>
            </ul>
          </td>
          <td style=" vertical-align: top; width: 53.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 46.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Sensor &gt; hundredPctResetVal </span></p>
          </td>
          <td style=" vertical-align: top; width: 53.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">Output = input + resetAmount</span></p>
          </td>
        </tr>
        <tr style=" height: 0.15in;">
          <td style=" vertical-align: top; width: 46.2%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">If none of the above conditions are satisfied</span></p>
          </td>
          <td style=" vertical-align: top; width: 53.8%; border-top: solid windowtext 1.0pt; padding-top: 0; border-right: solid windowtext 1.0pt; padding-right: 5.4pt; border-bottom: solid windowtext 1.0pt; padding-bottom: 0; border-left: solid windowtext 1.0pt; padding-left: 5.4pt;">
            <p class="spyderbodytext" style=" margin-bottom: 3pt;"><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0; display: inline-block; text-indent: 0; width: 0.550in;">Output = </span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">input + ((sensor – zeroPctResetVal) / </span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><span style=" margin: 0 0 0 0.45in; padding: 0 0 0 0;"> </span></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">hundredPctResetVal – zeroPctResetVal)) * </span><span style=" color: #000000; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"><span style=" margin: 0 0 0 0.50in; padding: 0 0 0 0;"> </span></span><span style=" color: #000000; font-family: &#39;Arial&#39;, &#39;sans-serif&#39;; font-size: 10pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;">resetAmount</span></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <p><span style=" font-size: 11pt; font-style: normal; font-weight: normal; margin: 0; padding: 0;"> </span></p>
</body>
</html>