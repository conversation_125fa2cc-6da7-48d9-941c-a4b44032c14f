# GitHub Copilot Instructions for HoneywellForgeConnect

## Project Overview
This repository contains the Honeywell Forge Connect Niagara module, including Java source code for Niagara 4, Tridium, and Honeywell Forge Connect integration. It includes command processing, event handling, job management, and API endpoints for cloud connectivity and device management.

## Coding Conventions
- **Language:** Java (Niagara 4 APIs, Tridium APIs, Honeywell Forge Connect APIs)
- **Formatting:**
  - Use 4 spaces for indentation.
  - Use block comments for file headers and Javadoc for public methods/classes.
  - Use `// ...existing code...` in code suggestions to avoid repeating unchanged code.
- **Imports:**
  - Use explicit imports, avoid wildcard imports.
  - Group imports: Java, javax, third-party, project-specific.
- **Constants:**
  - Use `private static final` for constants.
  - Place constants at the top of the class after static fields.
- **Error Handling:**
  - Use try/catch/finally for resource management.
  - Log exceptions using the provided `Logger` or `HoneywellForgeConnectLogger`.
  - Prefer `finally` for resource cleanup (e.g., shutting down executors).
- **Access Control:**
  - Use `AccessController.doPrivileged` only when required by security policy.
  - For Niagara/Tridium access control, check user roles/permissions via `Context` or `BUserService`.
- **Threading:**
  - Use `ExecutorService` for asynchronous tasks.
  - Always shut down executors in a `finally` block.
- **Niagara/Tridium APIs:**
  - Use `BComponent`, `BUserService`, `BJobService`, etc., as per Niagara 4 best practices.
  - Use `@NiagaraType` and `@NiagaraProperty` annotations for module classes.

## Copilot Usage
- **When suggesting code:**
  - Do not repeat unchanged code; use `// ...existing code...`.
  - For new methods, include Javadoc and follow the class's style.
  - For access control, suggest both Java SE (`AccessController`) and Niagara-specific (`Context.isAuthorized`, `BUserService`) patterns as appropriate.
  - **Always add the comment `Generated by Copilot begin` at the start of the code generated by Copilot and `Generated by Copilot end` at the end of the code.**
- **When editing files:**
  - Use the existing file header and copyright.
  - Maintain import order and grouping.
  - Do not introduce unused imports or variables.
- **When generating tests:**
  - Use TestNG for unit tests.
  - Use reflection for private method testing if needed.
  - Place test files in `srcTest/com/honeywell/honeywellforgeconnect/api/`.

## Special Notes
- **Do not expose trade secrets or proprietary logic in public suggestions.**
- **Always respect the Honeywell copyright and licensing.**
- **Do not suggest code that would break Niagara module conventions or Tridium best practices.**

---

For more details, see the README.md and code comments in the repository.
