/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Tests the behavior of NegatableStatusValue Widget
  * <AUTHOR> - <PERSON><PERSON>
  */

define(['nmodule/honeywellFunctionBlocks/rc/negatablestatusvaluewidget/NegatableStatusValueWidget',
  'nmodule/js/rc/jasmine/promiseUtils',
  'jquery',
  'baja!',
  'Promise'], function (
    NegatableStatusValueWidget,
    promiseUtils,
    $,
    baja,
    Promise) {

    'use strict';

    var doPromise = promiseUtils.doPromise;

    describe('nmodule/honeywellFunctionBlocks/rc/NegatableStatusValueWidget', function () {
    
     beforeEach(function () {
          this.widget = new NegatableStatusValueWidget();
          this.elem = $('<div/>');
          var that = this;
          doPromise(baja.Ord.make("station:|slot:/Apps/AND").get({lease: true})
            .then(function(and){
              that.parent = and;
              that.comp = and.get('in1');
            }));
        });

      it('renders negate box', function () {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function () {
            expect(that.widget.negateBox.length).toBe(1);
          }));
      });

      it('updates the UI accordingly', function () {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function () {
            expect(that.widget.negateBox[0].checked).toBe(false);
            that.widget.negateBox.trigger('change');
            that.widget.negateBox[0].click();
          })
          .then(function () {
            expect(that.widget.negateBox[0].checked).toBe(true);
            expect(that.widget.isModified()).toBe(true);
          }));
      });
         
      it('renders the correct value of properties', function () {
          var that = this;
          doPromise(that.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            })
            .then(function () {
              expect(that.widget.negateBox[0].checked).toBe(false);
            }));
        });

     it('reads the correct value', function () {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function () {
            return that.widget.load(that.comp);
          })
          .then(function () {
            that.widget.negateBox.trigger('change');
            that.widget.negateBox[0].click();
            return that.widget.doRead();
          })
          .then(function (readValue) {
        	   expect(that.widget.isModified()).toBe(true);
               expect(that.widget.isNegated).toBe(true);
          }));
      });

      it('saves the widget correctly', function () {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function () {
            return that.widget.load(that.comp);
          })
          .then(function () {
            that.widget.negateBox.trigger('change');
            that.widget.negateBox[0].click();
            return that.widget.read();
          })
          .then(function (readValue) {
            console.log(readValue);
            return that.widget.doSave(readValue,{batch: new baja.comm.Batch()});
          })
          .then(function(){
        	  expect(that.widget.negateBox[0].checked).toBe(true);
          }));
      });

    });
  }
);

