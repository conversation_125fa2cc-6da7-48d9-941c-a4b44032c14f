/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Tests the behavior of InOnlyStatusValue Widget
  * <AUTHOR> - <PERSON><PERSON>
  */

define(['nmodule/honeywellFunctionBlocks/rc/honstatusvaluewidget/HonStatusValueWidget',
  'nmodule/js/rc/jasmine/promiseUtils',
  'jquery',
  'baja!',
  'Promise',
  'baja!honeywellFunctionBlocksTest:HonStatusValueFETestData'], function (
    HonStatusValueWidget,
    promiseUtils,
    $,
    baja,
    Promise) {

    'use strict';

    var doPromise = promiseUtils.doPromise;

    describe('nmodule/honeywellFunctionBlocks/rc/HonStatusValueWidget', function () {

      beforeEach(function () {
        this.widget = new HonStatusValueWidget();
        this.elem = $('<div/>');
        this.parent = baja.$('honeywellFunctionBlocksTest:HonStatusValueFETestData');
      });

      it('disables the widget for user_defined_1 flag', function() {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function() {
            return that.widget.load(that.parent.getHonStatusBooleanInOnly());
          }).then(function() {
            expect(that.widget.isEnabled()).toBe(false);
          }));
      });

      it('does not disable widget for other flag', function() {
        var that = this;
        doPromise(that.widget.initialize(this.elem)
          .then(function() {
            return that.widget.load(that.parent.getHonStatusBoolean());
          }).then(function() {
            expect(that.widget.isEnabled()).toBe(true);
          }));
      });

    });
  }
);

