/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Tests the behavior of OutSlotWidget
  * <AUTHOR> - <PERSON><PERSON>
  */

define(['nmodule/honeywellFunctionBlocks/rc/outslotwidget/OutSlotWidget',
  'nmodule/honeywellFunctionBlocks/rc/outslotwidget/NegatableOutSlotWidget',
  'nmodule/honeywellFunctionBlocks/rc/outslotwidget/OutSaveOnlyWidget',
  'nmodule/webEditors/rc/fe/baja/StatusValueEditor',
  'nmodule/js/rc/jasmine/promiseUtils',
  'jquery',
  'baja!',
  'Promise',
  'baja!honeywellFunctionBlocks:Switch',
  'baja!honeywellFunctionBlocks:AnalogLatch',
  'baja!honeywellFunctionBlocks:FlowVelocity'], function (
    OutSlotWidget,
    NegatableOutSlotWidget,
    OutSaveOnlyWidget,
    StatusValueEditor,
    promiseUtils,
    $,
    baja,
    Promise ) {

    'use strict';

    var doPromise = promiseUtils.doPromise;

    describe('nmodule/honeywellFunctionBlocks/rc/OutSlotWidget', function () {

      describe('behavior', function () {

        beforeEach(function () {
          this.widget = new OutSlotWidget();
          this.elem = $('<div/>');
          this.parent = baja.$('honeywellFunctionBlocks:AnalogLatch');
          this.comp = this.parent.get('Y');
        });

        it('builds UI correctly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              expect(that.widget.jq().find('#outsave').length).toBe(1);
            }));
        });

        it('sets correct value on checkbox', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              expect(that.widget.jq().find('#outsave')[0].checked).toBe(false);
            }));
        });

        it('updates flag accordingly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              that.widget.jq().find('#outsave').trigger('change');
              that.widget.jq().find('#outsave')[0].click();
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
              return that.widget.read();
            }).then(function(val){
              return StatusValueEditor.prototype.doSave.call(that.widget,val);
            })
            .then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).toBe(0);
              that.widget.jq().find('#outsave').trigger('change');
              that.widget.jq().find('#outsave')[0].click();
              return that.widget.read();
            }).then(function(val){
              return StatusValueEditor.prototype.doSave.call(that.widget,val);
            }).then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
            }));
        });

        it('handles no change correctly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              return that.widget.save(that.widget.read());
            }).then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
            }));
        });

      });

    });
  
    describe('nmodule/honeywellFunctionBlocks/rc/NegatableOutSlotWidget', function () {

      describe('behavior', function () {

        beforeEach(function () {
          this.widget = new NegatableOutSlotWidget();
          this.elem = $('<div/>');
          var that = this;
          doPromise(baja.Ord.make("station:|slot:/Apps/Switch").get({lease: true})
            .then(function(Switch){
              that.parent = Switch;
              that.comp = Switch.get('OUTPUT0');
            }));
                
        });

        it('builds UI correctly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              expect(that.widget.jq().find('#outsave').length).toBe(1);
            }));
        });

        it('sets correct value on checkbox', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              expect(that.widget.jq().find('#outsave')[0].checked).toBe(false);
            }));
        });

       it('updates flag accordingly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              that.widget.jq().find('#outsave').trigger('change');
              that.widget.jq().find('#outsave')[0].click();
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
              return that.widget.read();
            }).then(function(val){
              return StatusValueEditor.prototype.doSave.call(that.widget,val);
            })
            .then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).toBe(0);
              that.widget.jq().find('#outsave').trigger('change');
              that.widget.jq().find('#outsave')[0].click();
              return that.widget.read();
            }).then(function(val){
              return StatusValueEditor.prototype.doSave.call(that.widget,val);
            }).then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
            }));
        });

        it('handles no change correctly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              return that.widget.save(that.widget.read());
            }).then(function () {
              expect(that.comp.getFlags() & baja.Flags.TRANSIENT).not.toBe(0);
            }));
        });

      });

    });

    describe('nmodule/honeywellFunctionBlocks/rc/OutSaveOnlyWidget', function () {

      describe('behavior', function () {

        beforeEach(function () {
          this.widget = new OutSaveOnlyWidget();
          this.elem = $('<div/>');
          this.parent = baja.$('honeywellFunctionBlocks:FlowVelocity');
          this.comp = this.parent.get('OFFSET');
        });

        it('builds UI correctly', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              expect(that.widget.jq().find('#outsave').length).toBe(1);
            }));
        });

        it('sets correct value on checkbox', function () {
          var that = this;
          doPromise(this.widget.initialize(this.elem)
            .then(function () {
              return that.widget.load(that.comp);
            }).then(function () {
              expect(that.widget.jq().find('#outsave')[0].checked).toBe(true);
            }));
        });

      });

    });
  }
);

