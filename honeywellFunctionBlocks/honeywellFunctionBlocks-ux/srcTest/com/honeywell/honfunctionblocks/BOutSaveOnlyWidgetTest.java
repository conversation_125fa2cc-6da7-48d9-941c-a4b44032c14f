package com.honeywell.honfunctionblocks;

import org.testng.Assert;
import org.testng.annotations.Test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

@NiagaraType
public class BOutSaveOnlyWidgetTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.BOutSaveOnlyWidgetTest(2979906276)1.0$ @*/
/* Generated Tue May 08 13:55:13 IST 2018 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOutSaveOnlyWidgetTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @Test
  public void testJsInfo(){
    Assert.assertEquals(BOutSaveOnlyWidget.INSTANCE.getJsInfo(null).getJs().toString(null),
        "module://honeywellFunctionBlocks/rc/outslotwidget/OutSaveOnlyWidget.js");
  }
}
