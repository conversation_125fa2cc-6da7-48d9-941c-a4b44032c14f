package com.honeywell.honfunctionblocks;

import org.testng.Assert;
import org.testng.annotations.Test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

@NiagaraType
public class BHonStatusValueWidgetTest extends BTestNg{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.BHonStatusValueWidgetTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Mat<PERSON> (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusValueWidgetTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

  @Test
  public void testJsInfo(){
    Assert.assertEquals(BHonStatusValueWidget.INSTANCE.getJsInfo(null).getJs().toString(null),
        "module://honeywellFunctionBlocks/rc/honstatusvaluewidget/HonStatusValueWidget.js");
  }

}
