/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.Test;

/**
 *
 * <AUTHOR> - <PERSON>
 * @since 05-Sep-2025
 */
@NiagaraType
public class BIntDataTypeWidgetTest extends BTestNg {
//region /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.BIntDataTypeWidgetTest(2979906276)1.0$ @*/
/* Generated Fri Sep 05 14:28:44 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BIntDataTypeWidgetTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @Test
  public void testJsInfo(){
    Assert.assertEquals(BIntDataTypeWidget.INSTANCE.getJsInfo(null).getJs().toString(null),
        "module://honeywellFunctionBlocks/rc/datatype/IntDataTypeWidget.js");
  }

}
