<!-- Module Include File -->
<!-- Types -->
<types>
  <!--com.honeywell.honfunctionblocks-->
  <type class="com.honeywell.honfunctionblocks.BNegatableStatusValueWidget" name="NegatableStatusValueWidget">
    <agent>
      <on type="honeywellFunctionBlocks:NegatableStatusBoolean"/>
      <on type="honeywellFunctionBlocks:NegatableFiniteStatusBoolean"/>
      <on type="honeywellFunctionBlocks:NegatableHonStatusNumeric"/>
    </agent>
  </type>
  <type class="com.honeywell.honfunctionblocks.BOutSlotWidget" name="OutSlotWidget"/>
  <type class="com.honeywell.honfunctionblocks.BNegatableOutSlotWidget" name="NegatableOutSlotWidget"/>
  <type class="com.honeywell.honfunctionblocks.BHoneywellFunctionBlocksJsBuild" name="HoneywellFunctionBlocksJsBuild"/>
  <type class="com.honeywell.honfunctionblocks.BHonStatusValueWidget" name="HonStatusValueWidget">
    <agent>
      <on type="honeywellFunctionBlocks:HonStatusNumeric"/>
      <on type="honeywellFunctionBlocks:HonStatusBoolean"/>
      <on type="honeywellFunctionBlocks:FiniteStatusBoolean"/>
      <on type="honeywellFunctionBlocks:HonStatusEnum"/>
    </agent>
  </type>
  <type class="com.honeywell.honfunctionblocks.BOutSaveOnlyWidget" name="OutSaveOnlyWidget"/>
    <type class="com.honeywell.honfunctionblocks.BIntDataTypeWidget" name="IntDataTypeWidget"/>
</types>