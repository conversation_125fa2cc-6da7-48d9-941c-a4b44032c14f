/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
package com.honeywell.honfunctionblocks;

import javax.baja.naming.BOrd;
import javax.baja.sys.BSingleton;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.web.BIFormFactorMini;
import javax.baja.web.js.BIJavaScript;
import javax.baja.web.js.JsInfo;

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160","squid:S2974"})
public class BOutSaveOnlyWidget extends BSingleton
    implements BIJavaScript, BIFormFactorMini {
  public static final BOutSaveOnlyWidget INSTANCE = new BOutSaveOnlyWidget();
  private static final JsInfo jsInfo =
      JsInfo.make(
          BOrd.make("module://honeywellFunctionBlocks/rc/outslotwidget/OutSaveOnlyWidget.js"),
          BHoneywellFunctionBlocksJsBuild.TYPE
      );

  private BOutSaveOnlyWidget() {}


  public static final Type TYPE = Sys.loadType(BOutSaveOnlyWidget.class);
  @Override
  public Type getType() { return TYPE; }

  @Override
  public JsInfo getJsInfo(Context cx) { return jsInfo; }
}
