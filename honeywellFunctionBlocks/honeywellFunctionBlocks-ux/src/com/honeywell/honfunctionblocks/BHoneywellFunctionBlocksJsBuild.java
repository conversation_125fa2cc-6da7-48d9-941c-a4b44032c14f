/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
package com.honeywell.honfunctionblocks;

import javax.baja.naming.BOrd;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.web.js.BJsBuild;

/**
 * Responsible for building and minification of the JS widgets.
 * Combines all js files and minifies them into one file.
 */
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160","squid:S2974"})
public class BHoneywellFunctionBlocksJsBuild extends BJsBuild
{
  public static final BHoneywellFunctionBlocksJsBuild INSTANCE = new BHoneywellFunctionBlocksJsBuild(
    "honeywellFunctionBlocks",
    new BOrd[] {
      BOrd.make("module://honeywellFunctionBlocks/rc/honeywellFunctionBlocks.built.min.js")
    }
  );

  public static final Type TYPE = Sys.loadType(BHoneywellFunctionBlocksJsBuild.class);
  @Override
  public Type getType() { return TYPE; }

  private BHoneywellFunctionBlocksJsBuild(String id, BOrd[] files) { super(id, files); }
}
