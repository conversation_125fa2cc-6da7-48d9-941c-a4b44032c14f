/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
package com.honeywell.honfunctionblocks;

import javax.baja.naming.BOrd;
import javax.baja.sys.BSingleton;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.web.BIFormFactorMini;
import javax.baja.web.js.BIJavaScript;
import javax.baja.web.js.JsInfo;

/**
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON>
 * @since Jul 3, 2018
 */

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160","squid:S2974"})
public class BIntDataTypeWidget extends <PERSON><PERSON>leton
    implements BIJavaScript, BIFormFactorMini {
  public static final BIntDataTypeWidget INSTANCE = new BIntDataTypeWidget();
  private static final JsInfo jsInfo =
      JsInfo.make(
          BOrd.make("module://honeywellFunctionBlocks/rc/datatype/IntDataTypeWidget.js"),
          BHoneywellFunctionBlocksJsBuild.TYPE
      );

  private BIntDataTypeWidget() {}


  public static final Type TYPE = Sys.loadType(BIntDataTypeWidget.class);
  @Override
  public Type getType() { return TYPE; }

  @Override
  public JsInfo getJsInfo(Context cx) { return jsInfo; }
}
