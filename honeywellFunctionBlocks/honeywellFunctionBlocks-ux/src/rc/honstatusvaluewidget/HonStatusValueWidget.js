/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/

/*
	Builds a javascript widget that builds property sheet for In only slots.
	<AUTHOR> - <PERSON><PERSON>
*/
define(['baja!',
  'nmodule/webEditors/rc/fe/baja/StatusValueEditor',
  'bajaux/mixin/subscriberMixIn',
  'jquery',
  'Promise',
  'bajaux/util/SaveCommand'],
  function (      //NOSONAR
    baja,
    StatusValueEditor,
    subscriberMixin,
    $,
    Promise,     //NOSONAR
    SaveCommand) {

    /**
    * Editor that will not allow to change the IN_ONLY slot value
    */

    var HonStatusValueWidget = function () {
      StatusValueEditor.apply(this, arguments);    //NOSONAR
      subscriberMixin(this);
      this.getCommandGroup().add(new SaveCommand());
    };

    //extend and set up prototype chain
    HonStatusValueWidget.prototype = Object.create(StatusValueEditor.prototype);
    HonStatusValueWidget.prototype.constructor = HonStatusValueWidget;

    HonStatusValueWidget.prototype.doInitialize = function (dom) {
      StatusValueEditor.prototype.doInitialize.call(this, dom);
    };

    HonStatusValueWidget.prototype.doLoad = function (comp) {
      return StatusValueEditor.prototype.doLoad.call(this, comp).then(function(){});
    };
    
    
    HonStatusValueWidget.prototype.checkIfNotStageDriver = function (facetObjSReturned) {
    	
    	if(facetObjSReturned != null && facetObjSReturned.length > 0) {
    		for (var x = 0; x < facetObjSReturned.length; x++) {
    			var objs = facetObjSReturned[x].split("=");
        		if(objs[0] === "isStageDriver" && objs[1] === "true") {
        			return false;
        		}
    		}
    	}
    	return true;
    }
    
    HonStatusValueWidget.prototype.$getSlotFacets = function () {
    	var that = this;
        var complex = this.getComplex();
        var slot = this.getSlot();
		 return complex.rpc('getFacetsDataToRPCCall',slot.getName()).then(function (value) {
			if(value !== undefined){
			var facetObjSReturned = value.split(",");
			if(that.checkIfNotStageDriver(facetObjSReturned)) {
				return Promise.resolve((complex && slot && complex.getFacets(slot)) ||
		          	      baja.Facets.DEFAULT);
			}
			var facetObjArray = [];
			for (var x = 0; x < facetObjSReturned.length; x++) {
				var facetObj = facetObjSReturned[x].split("=");
				facetObj[1] = facetObj[1] === 'null' ? '' : facetObj[1];
				facetObjArray.push(baja.Facets.make([facetObj[0]],[facetObj[1]]));
			}
			var facetSetResult = facetObjArray[0];
			for (var y = 1; y < facetObjArray.length; y++) {
				facetSetResult = baja.Facets.make(facetSetResult,facetObjArray[y]);
			}
			that.facetSetResultReturned = facetSetResult;
	        return Promise.resolve(that.facetSetResultReturned);
			}else{
				 return Promise.resolve((complex && slot && complex.getFacets(slot)) ||
		          	      baja.Facets.DEFAULT);
		   }
		});
    };
    
    return HonStatusValueWidget;
  });
