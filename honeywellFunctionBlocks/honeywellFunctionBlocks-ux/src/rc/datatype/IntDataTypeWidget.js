/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/

/*
	Builds a javascript widget that provides the mechanism to show the facets in UX editor 
*/
define(['baja!',
  'nmodule/webEditors/rc/fe/baja/IntegerEditor',
  'bajaux/mixin/subscriberMixIn',
  'jquery',
  'Promise',
  'bajaux/util/SaveCommand'],
  function (      //NOSONAR
    baja,
    IntegerEditor,
    subscriberMixin,
    $,
    Promise,     //NOSONAR
    SaveCommand) {

    /**
    * Editor provides the mechanism to show the facets in UX
    */

    var IntDataTypeWidget = function () {
      IntegerEditor.apply(this, arguments);    //NOSONAR
      subscriberMixin(this);
      this.getCommandGroup().add(new SaveCommand());
    };

    //extend and set up prototype chain
    IntDataTypeWidget.prototype = Object.create(IntegerEditor.prototype);
    IntDataTypeWidget.prototype.constructor = IntDataTypeWidget;

    IntDataTypeWidget.prototype.doInitialize = function (dom) {
    	IntegerEditor.prototype.doInitialize.call(this, dom);
    };

   IntDataTypeWidget.prototype.doLoad = function (comp) {
	  var that = this;
      return IntegerEditor.prototype.doLoad.call(this,comp).then(function(){
    	  var postLableElems = that.$jq.find('.postlabel');
    	  if(postLableElems != null && postLableElems.length > 0) {
    		  var minVal = that.getSlot().getFacets().$map.$map.min.$val;
    		  var maxVal = that.getSlot().getFacets().$map.$map.max.$val;
    		  postLableElems[0].innerHTML = " [" + minVal + "-" + maxVal + "]";
    	  }
      });
    };
    
    IntDataTypeWidget.prototype.populateEncFacetString = function (facetSetResultReturned) {
    	facetSetResultReturned.$map.$array = ["min", "max", "precision"];
    	var mapValues = facetSetResultReturned.$map.$map;
    	facetSetResultReturned.$map.$map = {min: mapValues.min, max: mapValues.max, precision: mapValues.precision};
    	facetSetResultReturned.$cEncStr = "min=i:" + mapValues.min+"|max=i:" + mapValues.max+
    													"|precision=i:" + mapValues.precision;
    	return facetSetResultReturned;
    }
    
    IntDataTypeWidget.prototype.$getSlotFacets = function () {
    	var that = this;
        var complex = this.getComplex();
        var slot = this.getSlot();
		 return complex.rpc('getFacetsDataToRPCCall',slot.getName()).then(function (value) {
			if(value !== undefined){
			var facetObjSReturned = value.split(",");
			var facetObjArray = [];
			for (var x = 0; x < facetObjSReturned.length; x++) {
				var facetObj = facetObjSReturned[x].split("=");
    			if(facetObj[1] !== 'null')
    			{
    				facetObjArray.push(baja.Facets.make([facetObj[0]],[facetObj[1]]));
    			}			
			}
			var facetSetResult = facetObjArray[0];
			for (var y = 1; y < facetObjArray.length; y++) {
				facetSetResult = baja.Facets.make(facetSetResult,facetObjArray[y]);
			}
			that.facetSetResultReturned = facetSetResult;
			that.facetSetResultReturned = that.populateEncFacetString(that.facetSetResultReturned);
			
			// Populating customized Facets based on the Fetched values.
			var newModifiedFacets = (complex && slot && complex.getFacets(slot));
			newModifiedFacets.$map.$map.max.$cEncStr = that.facetSetResultReturned.$map.$map.max;
			newModifiedFacets.$map.$map.max.$val = Number(that.facetSetResultReturned.$map.$map.max);
			newModifiedFacets.$cEncStr = newModifiedFacets.$cEncStr.replace("max=i:20", "max=i:" + that.facetSetResultReturned.$map.$map.max);

			return Promise.resolve(newModifiedFacets);
			}else{
				 return Promise.resolve((complex && slot && complex.getFacets(slot)) ||
		          	      baja.Facets.DEFAULT);
		   }
		});
    };

    return IntDataTypeWidget;
  });

