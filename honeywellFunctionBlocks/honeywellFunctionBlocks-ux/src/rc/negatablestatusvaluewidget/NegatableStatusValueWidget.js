/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Builds a javascript widget that builds property sheet for Negatable complexes.
  * <AUTHOR> - <PERSON><PERSON>
  */
define(['baja!',
  'nmodule/honeywellFunctionBlocks/rc/honstatusvaluewidget/HonStatusValueWidget',
  'bajaux/mixin/subscriberMixIn',
  'jquery',
  'Promise',
  'bajaux/util/SaveCommand',
  'lex!honeywellFunctionBlocks'],
  function (      //NOSONAR
    baja,
    HonStatusValueWidget,
    subscriberMixin,
    $,
    Promise,     //NOSONAR
    SaveCommand,
    lex
    ) {

    /**
    * An editor which helps in editing value,
    * nullable, and negatable properties of a negatable structs.
    * The editor uses 'fe' module to dynamically build an editor 
    * for the value property.
    *
    * @class
    * @extends module:nmodule/webEditors/rc/fe/baja/HonStatusValueWidget
    * @alias module:nmodule/honeywellFunctionBlocks/rc/NegatableStatusValueWidget
    */
    var NegatableStatusValueWidget = function () {
      HonStatusValueWidget.apply(this, arguments);    //NOSONAR
      subscriberMixin(this);
      this.getCommandGroup().add(new SaveCommand());
    };

    //extend and set up prototype chain
    NegatableStatusValueWidget.prototype = Object.create(HonStatusValueWidget.prototype);
    NegatableStatusValueWidget.prototype.constructor = NegatableStatusValueWidget;

    /**
    * Do initial setup of the DOM for the widget. This will set up the DOM's
    * structure and create a space where the editors would go.
    *
    * @param {jQuery} element the DOM element into which to load this widget
    */
    NegatableStatusValueWidget.prototype.doInitialize = function (dom) {
      HonStatusValueWidget.prototype.doInitialize.call(this,dom);
      var that = this;
      dom.prepend('<label style="margin-right:5px"><input style="margin-right:4px" type="checkbox" class="negate"/>'+
    		  lex[0].get('widget.negate') + '</label>');
      that.negateBox = dom.find('.negate');
      that.negateBox.on('change', function () {
    	  that.setModified(true);
        });
    };


    NegatableStatusValueWidget.prototype.$updateDisplay = function(){
      //do nothing
    };

    /**
    * Called when the component is ready.
    */
    NegatableStatusValueWidget.prototype.doLoad = function (comp) {
      var that = this;
      return HonStatusValueWidget.prototype.doLoad.call(this,comp).then(function(){
    	  comp.getParent().rpc('getNegateValueToRPCCall',comp.getName()).then(function (value) {		  
    		  that.negateBox.prop('checked', value);
    		  that.isNegated = value;
    	      	if(value === true &&  !comp.toString().includes("(NOT)")){
    	      		that.$getDisplayEditor().load("(NOT) " + comp.toString());
    	      	}else{
    	      		that.$getDisplayEditor().load(comp.toString());
    	      	}
    	  }); 
    	  
      });
    };

    /**
    * Gets the values in the editors.
    *
    * @returns {Promise} promise to be resolved with the values 
    * held by the editors.
    */
    NegatableStatusValueWidget.prototype.doRead = function () {
      var that = this;
      //promises are optional - the slot could also be returned directly
      return HonStatusValueWidget.prototype.doRead.call(this).then(function (value) {
        that.isNegated = that.negateBox[0].checked;
        return value;
      });
    };

    /**
    * Save the user-entered changes to the loaded component.
    *
    * Note that the parameter to this function is the same as that resolved by
    * doRead().
    *
    */
    NegatableStatusValueWidget.prototype.doSave = function (readValue, params) {
        var that = this,
        comp = this.value();
       
        comp.getParent().rpc("setNegateValueFromRPCCall",comp.getName(),this.isNegated).then(function(value) {
  	      	if(value === true &&  !comp.toString().includes("(NOT)")){
  	      		that.getSlot().$setDisplay("(NOT) " + comp.toString());
  	      	}else if(value === false && comp.toString().includes("(NOT)")){
  	      		that.getSlot().$setDisplay(comp.toString().replace("(NOT) ", ""));
  	      	}else {
  	      		that.getSlot().$setDisplay(comp.toString());
  	      	}
  	   	});
        
        return HonStatusValueWidget.prototype.doSave.call(this,readValue,params).then(function(){
	          that.doLoad(comp);
	   });
       
        
    };

    return NegatableStatusValueWidget;
  });
