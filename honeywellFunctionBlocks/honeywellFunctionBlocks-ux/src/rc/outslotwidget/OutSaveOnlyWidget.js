/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Builds a javascript widget that builds property sheet for out slots.
  * Usage: Add facet as such
  * Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")
  * <AUTHOR> - <PERSON><PERSON>
  */
define(['baja!',
  'nmodule/honeywellFunctionBlocks/rc/outslotwidget/OutSlotWidget',
  'bajaux/mixin/subscriberMixIn',
  'jquery',
  'Promise',
  'bajaux/util/SaveCommand'],
  function (      //NOSONAR
    baja,
    OutSlotWidget,
    subscriberMixin,
    $,
    Promise,     //NOSONAR
    SaveCommand) {

    /**
    * An editor which helps in editing value,
    * nullable, and negatable properties of a negatable structs.
    * The editor uses 'fe' module to dynamically build an editor
    * for the value property.
    *
    * @class
    * @extends module:nmodule/webEditors/rc/fe/baja/BaseEditor
    * @alias module:nmodule/honeywellFunctionBlocks/rc/OutSaveOnly
    */
    var OutSaveOnly = function () {
      OutSlotWidget.apply(this, arguments);    //NOSONAR
      subscriberMixin(this);
      this.getCommandGroup().add(new SaveCommand());
    };

    //extend and set up prototype chain
    OutSaveOnly.prototype = Object.create(OutSlotWidget.prototype);
    OutSaveOnly.prototype.constructor = OutSaveOnly;

    /**
    * Do initial setup of the DOM for the widget. This will set up the DOM's
    * structure and create a space where the editors would go.
    *
    * @param {jQuery} element the DOM element into which to load this widget
    */
    OutSaveOnly.prototype.doInitialize = function (dom) {
      OutSlotWidget.prototype.doInitialize.call(this, dom);
      this.outSave.prop('disabled',true);
    };

    return OutSaveOnly;
  });
