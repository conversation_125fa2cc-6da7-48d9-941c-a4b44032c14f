/*
Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
This file contains trade secrets of Honeywell International, Inc. No part
may be reproduced or transmitted in any form by any means or for any
purpose without the express written permission of Honeywell.
*/
/**
  * Builds a javascript widget that builds property sheet for out slots.
  * Usage: Add facet as such
  * Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
  * <AUTHOR> - <PERSON><PERSON>
  */
define(['baja!',
  'nmodule/honeywellFunctionBlocks/rc/honstatusvaluewidget/HonStatusValueWidget',
  'bajaux/mixin/subscriberMixIn',
  'jquery',
  'Promise',
  'bajaux/util/SaveCommand',
  'lex!honeywellFunctionBlocks'],
  function (      //NOSONAR
    baja,
    HonStatusValueWidget,
    subscriberMixin,
    $,
    Promise,     //NOSONAR
    SaveCommand,
    lex) {

    /**
    * An editor which helps in editing value,
    * nullable, and negatable properties of a negatable structs.
    * The editor uses 'fe' module to dynamically build an editor 
    * for the value property.
    *
    * @class
    * @extends module:nmodule/webEditors/rc/fe/baja/BaseEditor
    * @alias module:nmodule/honeywellFunctionBlocks/rc/OutSlotWidget
    */
    var OutSlotWidget = function () {
      HonStatusValueWidget.apply(this, arguments);    //NOSONAR
      subscriberMixin(this);
      this.getCommandGroup().add(new SaveCommand());
    };

    //extend and set up prototype chain
    OutSlotWidget.prototype = Object.create(HonStatusValueWidget.prototype);
    OutSlotWidget.prototype.constructor = OutSlotWidget;

    /**
    * Do initial setup of the DOM for the widget. This will set up the DOM's
    * structure and create a space where the editors would go.
    *
    * @param {jQuery} element the DOM element into which to load this widget
    */
    OutSlotWidget.prototype.doInitialize = function (dom) {
      HonStatusValueWidget.prototype.doInitialize.call(this, dom);
      var that = this;
      dom.append('<label style="margin-left:5px"><input style="margin-right:4px" type="checkbox" id="outsave"/>'+lex[0].get('widget.outsave') + '</label>');
      this.outSave = dom.find('#outsave');
      this.outSave.on('change', function () {
        that.setModified(true);
      });
    };
    /**
    * Called when the component is ready.
    */
    OutSlotWidget.prototype.doLoad = function (comp) {
      var that = this;
      return HonStatusValueWidget.prototype.doLoad.call(this, comp).then(function () {
        var isTransient = (comp.getFlags() & baja.Flags.TRANSIENT) !== 0;  //NOSONAR
        that.outSave.prop('checked', !isTransient);
      });
    };

    /**
    * Gets the values in the editors.
    *
    * @returns {Promise} promise to be resolved with the values 
    * held by the editors.
    */
    OutSlotWidget.prototype.doRead = function () {
      var that = this;
      var currTransient = !that.outSave[0].checked;
      var flags = this.value().getFlags();
      if (currTransient) {
        flags = flags | 2;     //NOSONAR
      } else {
        flags = flags & ~2;   //NOSONAR
      }
      return this.value().getParent().setFlags({
        slot: this.value().getName().toString(),
        flags: flags,
      }).then(function () {
        return HonStatusValueWidget.prototype.doRead.call(that);
      });
    };

    return OutSlotWidget;
  });
