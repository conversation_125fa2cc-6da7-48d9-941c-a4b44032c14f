<!-- Module Include File -->
<!-- Types -->
<types>
  <!--com.honeywell.honfunctionblocks-->
  <type class="com.honeywell.honfunctionblocks.BOutSlotWidgetTest" name="OutSlotWidgetTest"/>
  <type class="com.honeywell.honfunctionblocks.BNegatableOutSlotWidgetTest" name="NegatableOutSlotWidgetTest"/>
  <type class="com.honeywell.honfunctionblocks.BNegatableStatusValueWidgetTest" name="NegatableStatusValueWidgetTest"/>
  <type class="com.honeywell.honfunctionblocks.BOutSaveOnlyWidgetTest" name="OutSaveOnlyWidgetTest"/>
  <type class="com.honeywell.honfunctionblocks.BHonStatusValueWidgetTest" name="HonStatusValueWidgetTest"/>
  <type class="com.honeywell.honfunctionblocks.BIntDataTypeWidgetTest" name="IntDataTypeWidgetTest"/>
</types>