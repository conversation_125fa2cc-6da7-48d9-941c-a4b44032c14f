/*jshint node: true */

"use strict";

var SRC_FILES = [
    'src/rc/**/*.js',
    'Gruntfile.js',
    '!src/rc/**/*.built.js',
    '!src/rc/**/*.min.js'
  ],
  SPEC_FILES = [
    'srcTest/rc/spec/**/*.js'
  ],
  TEST_FILES = [
    'srcTest/rc/*.js'
  ],
  TEMPLATE_FILES = [
    'src/rc/**/*.hbs'
  ],
  ALL_FILES = SRC_FILES.concat(SPEC_FILES).concat(TEST_FILES).concat(TEMPLATE_FILES),
  JSHINT_NEEDED_FILES = SRC_FILES.concat(SPEC_FILES).concat(TEST_FILES);

module.exports = function runGrunt(grunt) {

  grunt.initConfig({
    pkg: grunt.file.readJSON('package.json'),

    jsdoc:     { src: SRC_FILES.concat(['README.md']) },
    jshint:    { src: JSHINT_NEEDED_FILES, options: {esversion: 6, strict: false, devel: true} },
    plato:     { src: SRC_FILES },
    watch:     { src: ALL_FILES, options: {atBegin: true} },
    karma:     { },
    requirejs: { },
    niagara:   {
      station: {
        stationName: 'honeywellFunctionBlocks',
        forceCopy: true,
        sourceStationFolder: './srcTest/rc/stations/honeywellFunctionBlocksUnitTest',
      }
    }
  });

  grunt.loadNpmTasks('grunt-niagara');
};
