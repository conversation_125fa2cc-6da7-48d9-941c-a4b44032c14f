/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Flags;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.fieldeditor.BStatusValueOutSlotFE;
import com.honeywell.honfunctionblocks.fbs.analog.BAnalogLatch;

@NiagaraType
public class BStatusValueOutSlotFETest extends BTestNg{
  /*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
  /*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BStatusValueOutSlotFETest(**********)1.0$ @*/
  /* Generated Mon Apr 23 12:06:48 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusValueOutSlotFETest.class);

  /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  private BStatusValueOutSlotFE getStatusValueOutSlotFE() {return new BStatusValueOutSlotFE();}
  
	@BeforeClass(alwaysRun = true)
	public void setUp() {
		honStatusValFE = new BStatusValueOutSlotFE();
	}

  @DataProvider(name = "outSlotWithTransient")
  public Object[] provideTestData(){
    BAnalogLatch testData = new BAnalogLatch();
    BStatusValueOutSlotFE widget = honStatusValFE;
    widget.loadValue(testData.getY());
    return new Object[] {widget};
  }

  @Test(dataProvider = "outSlotWithTransient")
  public void testOutSaveCheckStatus(BStatusValueOutSlotFE widget) throws Exception{
    BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
    BWidget expansion = expandablePane.getExpansion();

    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isEnabled(), true);
    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isSelected(), false);
  }

  @DataProvider(name = "outSlotWithoutTransient")
  public Object[] provideTestDataWithoutTransient(){
    BAnalogLatch testData = new BAnalogLatch();
    BStatusValueOutSlotFE widget = honStatusValFE;
    int flags = testData.getFlags(testData.getSlot(testData.getY().getName()));
    flags &= ~Flags.TRANSIENT;
    testData.setFlags(testData.getSlot(testData.getY().getName()),flags);
    widget.loadValue(testData.getY());
    return new Object[] {widget};
  }

  @Test(dataProvider = "outSlotWithoutTransient")
  public void testOutSaveCheckStatusWithoutTransient(BStatusValueOutSlotFE widget) throws Exception{
    BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
    BWidget expansion = expandablePane.getExpansion();

    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isEnabled(), true);
    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isSelected(), true);
  }

  @DataProvider(name = "outSlotUpdatedFlag")
  public Object[] provideTestDataWithUpdatedFlag() throws Exception{
    BAnalogLatch testData = new BAnalogLatch();
    BStatusValueOutSlotFE widget = getStatusValueOutSlotFE();
    widget.loadValue(testData.getY());
    BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
    BWidget expansion = expandablePane.getExpansion();
    ((BCheckBox)expansion.get("OUT_SAVE")).setSelected(true);
    widget.saveValue();
    return new Object[] {testData};
  }

  @Test(dataProvider = "outSlotUpdatedFlag")
  public void testOutSaveWithUpdatedFlag(BAnalogLatch testData) throws Exception{
    int flags = testData.getFlags(testData.getSlot(testData.getY().getName()));
    Assert.assertEquals(flags & Flags.TRANSIENT,0);
  }
  
  @Test(dataProvider = "outSlotWithoutTransient")
  public void testOutSaveCheckStatusAsSelectedFalse(BStatusValueOutSlotFE widget) throws Exception{
    BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
    BWidget expansion = expandablePane.getExpansion();
    ((BCheckBox)expansion.get("OUT_SAVE")).setSelected(false);
    widget.saveValue(); 
    
    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isEnabled(), true);
    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isSelected(), false);
  }

  private BStatusValueOutSlotFE  honStatusValFE;

}
