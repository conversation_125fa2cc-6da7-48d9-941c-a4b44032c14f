/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.agent.AgentInfo;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.workbench.fieldeditor.BWbFieldEditor;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.fieldeditor.BHonStatusValueFE;

/**
 * Testing workbench field editor for all HonStatusValues like HonStatusNumeric,
 * HonStatusBoolean, FiniteStatusBoolean, HonStatusEnum fields
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-195
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 15, 2018
 */

@NiagaraType

public class BHonStatusValueFETest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BHonStatusValueFETest(**********)1.0$ @*/
/* Generated Thu Jan 18 12:18:51 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusValueFETest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@BeforeClass(alwaysRun = true)
	public void setUp() {
		honStatusValFE = new BHonStatusValueFE();
	}
  
	private BHonStatusValueFE getHonStatusValueFE() {
	    return new BHonStatusValueFE();
	}
	
	@DataProvider(name = "provideEditableStatusValues")
	public Object[] provideEditableStatusValues() {
		BHonStatusValueFETestData testData = new BHonStatusValueFETestData();
		BHonStatusValueFE enumData = getHonStatusValueFE();enumData.loadValue(testData.getHonStatusEnum());
		BHonStatusValueFE booleanData = getHonStatusValueFE();booleanData.loadValue(testData.getHonStatusBoolean());
		BHonStatusValueFE numericData = getHonStatusValueFE();numericData.loadValue(testData.getHonStatusNumeric());
		BHonStatusValueFE fsbData = getHonStatusValueFE();fsbData.loadValue(testData.getFiniteStatusBoolean());
		
		return new Object[] {enumData, booleanData, numericData, fsbData};
	}
	
	@Test(dataProvider="provideEditableStatusValues")
	public void testDoLoadValueForEditableStatusValue(BHonStatusValueFE statusValueFE) throws Exception {
		BExpandablePane expandablePane = (BExpandablePane) statusValueFE.getContent();
		BWidget expansion = expandablePane.getExpansion();
	    Assert.assertEquals(((BWbFieldEditor)expansion.get("editor")).isEnabled(), true);
	    Assert.assertEquals(((BCheckBox)expansion.get("null")).isEnabled(), true);
	}
	
	@DataProvider(name = "provideReadOnlyValues")
	public Object[] provideReadOnlyValues() {
		BHonStatusValueFETestData testData = new BHonStatusValueFETestData();
		BHonStatusValueFE enumData = honStatusValFE;enumData.loadValue(testData.getHonStatusEnumReadOnly());
		BHonStatusValueFE booleanData = getHonStatusValueFE();booleanData.loadValue(testData.getHonStatusBooleanReadOnly());
		BHonStatusValueFE numericData = getHonStatusValueFE();numericData.loadValue(testData.getHonStatusNumericReadOnly());
		BHonStatusValueFE fsbData = getHonStatusValueFE();fsbData.loadValue(testData.getFiniteStatusBooleanReadOnly());
		
		return new Object[] {enumData, booleanData, numericData, fsbData};
	}
	
	@Test(dataProvider="provideReadOnlyValues")
	public void testDoLoadValueForReadOnly(BHonStatusValueFE statusValueFE) throws Exception {
		BExpandablePane expandablePane = (BExpandablePane) statusValueFE.getContent();
		BWidget expansion = expandablePane.getExpansion();
	    Assert.assertEquals(((BWbFieldEditor)expansion.get("editor")).isReadonly(), true);
	    Assert.assertEquals(((BCheckBox)expansion.get("null")).isEnabled(), false);
	}
	
	@Test()
	public void testAgents() {
		BHonStatusNumeric hsn = new BHonStatusNumeric();
		AgentInfo info = hsn.getAgents().get("honeywellFunctionBlocks:HonStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BHonStatusNumeric");

		BHonStatusBoolean hsb = new BHonStatusBoolean();
		info = hsb.getAgents().get("honeywellFunctionBlocks:HonStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BHonStatusBoolean");
		
		BFiniteStatusBoolean fsb = new BFiniteStatusBoolean();
		info = fsb.getAgents().get("honeywellFunctionBlocks:HonStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BFiniteStatusBoolean");
		
		BHonStatusEnum hse = new BHonStatusEnum();
		info = hse.getAgents().get("honeywellFunctionBlocks:HonStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BHonStatusEnum");
	}

	@DataProvider(name = "provideUpdatedStatusValue")
	public Object[] provideUpdatedStatusValue() throws Exception{
		BHonStatusValueFETestData testData = new BHonStatusValueFETestData();
		BHonStatusValueFE booleanData = getHonStatusValueFE();
		booleanData.loadValue(testData.getHonStatusBoolean());
		BExpandablePane expandablePane = (BExpandablePane) booleanData.getContent();
		BWidget expansion = expandablePane.getExpansion();
		((BCheckBox)expansion.get("null")).setSelected(true);
		booleanData.saveValue();
		return new Object[] {testData};
	}

	@Test(dataProvider = "provideUpdatedStatusValue")
	public void testUpdatedStatusValue(BHonStatusValueFETestData testData){
		Assert.assertEquals(testData.getHonStatusBoolean().getStatus().isNull(),true);
	}
	 private BHonStatusValueFE honStatusValFE;
}
