/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;

/**
 * Testing workbench field editor for NegatableStatusBoolean, NegatableHonStatusNumeric and NegatableFiniteStatusBoolean fields
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-195
 * <AUTHOR> - <PERSON><PERSON> Khatri
 * @since Jan 19, 2018
 */

@NiagaraType
@NiagaraProperty(name = "negatableStatusBoolean", type = "BNegatableStatusBoolean", defaultValue = "new BNegatableStatusBoolean(false, BStatus.nullStatus, false)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "negatableStatusBooleanReadOnly", type = "BNegatableStatusBoolean", defaultValue = "new BNegatableStatusBoolean(false, BStatus.nullStatus, false)", flags = Flags.SUMMARY | Flags.READONLY)
@NiagaraProperty(name="negatableFiniteStatusBoolean", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="negatableFiniteStatusBooleanReadOnly", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY | Flags.READONLY)
@NiagaraProperty(name="negatableHonStatusNumeric", type="BNegatableHonStatusNumeric", defaultValue="new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="negatableHonStatusNumericReadOnly", type="BNegatableHonStatusNumeric", defaultValue="new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false)", flags=Flags.SUMMARY | Flags.READONLY)

public class BNegatableStatusValueFETestData extends BComponent{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BNegatableStatusValueFETestData(1191442550)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "negatableStatusBoolean"

  /**
   * Slot for the {@code negatableStatusBoolean} property.
   * @see #getNegatableStatusBoolean
   * @see #setNegatableStatusBoolean
   */
  public static final Property negatableStatusBoolean = newProperty(Flags.SUMMARY, new BNegatableStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableStatusBoolean} property.
   * @see #negatableStatusBoolean
   */
  public BNegatableStatusBoolean getNegatableStatusBoolean() { return (BNegatableStatusBoolean)get(negatableStatusBoolean); }

  /**
   * Set the {@code negatableStatusBoolean} property.
   * @see #negatableStatusBoolean
   */
  public void setNegatableStatusBoolean(BNegatableStatusBoolean v) { set(negatableStatusBoolean, v, null); }

  //endregion Property "negatableStatusBoolean"

  //region Property "negatableStatusBooleanReadOnly"

  /**
   * Slot for the {@code negatableStatusBooleanReadOnly} property.
   * @see #getNegatableStatusBooleanReadOnly
   * @see #setNegatableStatusBooleanReadOnly
   */
  public static final Property negatableStatusBooleanReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BNegatableStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableStatusBooleanReadOnly} property.
   * @see #negatableStatusBooleanReadOnly
   */
  public BNegatableStatusBoolean getNegatableStatusBooleanReadOnly() { return (BNegatableStatusBoolean)get(negatableStatusBooleanReadOnly); }

  /**
   * Set the {@code negatableStatusBooleanReadOnly} property.
   * @see #negatableStatusBooleanReadOnly
   */
  public void setNegatableStatusBooleanReadOnly(BNegatableStatusBoolean v) { set(negatableStatusBooleanReadOnly, v, null); }

  //endregion Property "negatableStatusBooleanReadOnly"

  //region Property "negatableFiniteStatusBoolean"

  /**
   * Slot for the {@code negatableFiniteStatusBoolean} property.
   * @see #getNegatableFiniteStatusBoolean
   * @see #setNegatableFiniteStatusBoolean
   */
  public static final Property negatableFiniteStatusBoolean = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableFiniteStatusBoolean} property.
   * @see #negatableFiniteStatusBoolean
   */
  public BNegatableFiniteStatusBoolean getNegatableFiniteStatusBoolean() { return (BNegatableFiniteStatusBoolean)get(negatableFiniteStatusBoolean); }

  /**
   * Set the {@code negatableFiniteStatusBoolean} property.
   * @see #negatableFiniteStatusBoolean
   */
  public void setNegatableFiniteStatusBoolean(BNegatableFiniteStatusBoolean v) { set(negatableFiniteStatusBoolean, v, null); }

  //endregion Property "negatableFiniteStatusBoolean"

  //region Property "negatableFiniteStatusBooleanReadOnly"

  /**
   * Slot for the {@code negatableFiniteStatusBooleanReadOnly} property.
   * @see #getNegatableFiniteStatusBooleanReadOnly
   * @see #setNegatableFiniteStatusBooleanReadOnly
   */
  public static final Property negatableFiniteStatusBooleanReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableFiniteStatusBooleanReadOnly} property.
   * @see #negatableFiniteStatusBooleanReadOnly
   */
  public BNegatableFiniteStatusBoolean getNegatableFiniteStatusBooleanReadOnly() { return (BNegatableFiniteStatusBoolean)get(negatableFiniteStatusBooleanReadOnly); }

  /**
   * Set the {@code negatableFiniteStatusBooleanReadOnly} property.
   * @see #negatableFiniteStatusBooleanReadOnly
   */
  public void setNegatableFiniteStatusBooleanReadOnly(BNegatableFiniteStatusBoolean v) { set(negatableFiniteStatusBooleanReadOnly, v, null); }

  //endregion Property "negatableFiniteStatusBooleanReadOnly"

  //region Property "negatableHonStatusNumeric"

  /**
   * Slot for the {@code negatableHonStatusNumeric} property.
   * @see #getNegatableHonStatusNumeric
   * @see #setNegatableHonStatusNumeric
   */
  public static final Property negatableHonStatusNumeric = newProperty(Flags.SUMMARY, new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableHonStatusNumeric} property.
   * @see #negatableHonStatusNumeric
   */
  public BNegatableHonStatusNumeric getNegatableHonStatusNumeric() { return (BNegatableHonStatusNumeric)get(negatableHonStatusNumeric); }

  /**
   * Set the {@code negatableHonStatusNumeric} property.
   * @see #negatableHonStatusNumeric
   */
  public void setNegatableHonStatusNumeric(BNegatableHonStatusNumeric v) { set(negatableHonStatusNumeric, v, null); }

  //endregion Property "negatableHonStatusNumeric"

  //region Property "negatableHonStatusNumericReadOnly"

  /**
   * Slot for the {@code negatableHonStatusNumericReadOnly} property.
   * @see #getNegatableHonStatusNumericReadOnly
   * @see #setNegatableHonStatusNumericReadOnly
   */
  public static final Property negatableHonStatusNumericReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false), null);

  /**
   * Get the {@code negatableHonStatusNumericReadOnly} property.
   * @see #negatableHonStatusNumericReadOnly
   */
  public BNegatableHonStatusNumeric getNegatableHonStatusNumericReadOnly() { return (BNegatableHonStatusNumeric)get(negatableHonStatusNumericReadOnly); }

  /**
   * Set the {@code negatableHonStatusNumericReadOnly} property.
   * @see #negatableHonStatusNumericReadOnly
   */
  public void setNegatableHonStatusNumericReadOnly(BNegatableHonStatusNumeric v) { set(negatableHonStatusNumericReadOnly, v, null); }

  //endregion Property "negatableHonStatusNumericReadOnly"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusValueFETestData.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/


}
