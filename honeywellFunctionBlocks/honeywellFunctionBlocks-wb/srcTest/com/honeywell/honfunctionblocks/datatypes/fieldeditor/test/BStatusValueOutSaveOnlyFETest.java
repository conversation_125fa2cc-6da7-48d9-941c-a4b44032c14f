/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.fieldeditor.BStatusValueOutSaveOnlyFE;
import com.honeywell.honfunctionblocks.fbs.math.BFlowVelocity;
import com.honeywell.honfunctionblocks.fbs.math.BLimit;

@NiagaraType
public class BStatusValueOutSaveOnlyFETest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BStatusValueOutSaveOnlyFETest(**********)1.0$ @*/
/* Generated Tue May 08 15:05:11 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusValueOutSaveOnlyFETest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@BeforeClass(alwaysRun = true)
	public void setUp() {
		honStatusValFE = new BStatusValueOutSaveOnlyFE();
	}

  @DataProvider(name = "outSlotWithTransient")
  public Object[] provideTestData(){
    BFlowVelocity testData = new BFlowVelocity();
    BStatusValueOutSaveOnlyFE widget = honStatusValFE;
    widget.loadValue(testData.getOFFSET());
    return new Object[] {widget};
  }

  @Test(dataProvider = "outSlotWithTransient")
  public void testOutSaveCheckStatus(BStatusValueOutSaveOnlyFE widget) throws Exception{
    BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
    BWidget expansion = expandablePane.getExpansion();

    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isEnabled(), false);
    Assert.assertEquals(((BCheckBox)expansion.get("OUT_SAVE")).isSelected(), true);
  }
  
	@DataProvider(name = "outSlotWithTransientProvider")
	public Object[] provideTestDataForBLimit() {
		BLimit testData = new BLimit();
		BStatusValueOutSaveOnlyFE widget = honStatusValFE;
		widget.loadValue(testData.getHiLimit());
		return new Object[] { widget };
	}

	@Test(dataProvider = "outSlotWithTransientProvider")
	public void testOutSaveCheckStatusFromProviderBLimit(BStatusValueOutSaveOnlyFE widget) throws Exception {
		BExpandablePane expandablePane = (BExpandablePane) widget.getContent();
		BWidget expansion = expandablePane.getExpansion();

		Assert.assertEquals(((BCheckBox) expansion.get("OUT_SAVE")).isEnabled(), false);
		Assert.assertEquals(((BCheckBox) expansion.get("OUT_SAVE")).isSelected(), true);
	}
  
  
  private BStatusValueOutSaveOnlyFE  honStatusValFE;


}
