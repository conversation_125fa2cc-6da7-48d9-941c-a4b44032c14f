/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.agent.AgentInfo;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.workbench.fieldeditor.BWbFieldEditor;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.fieldeditor.BHonStatusValueFE;
import com.honeywell.honfunctionblocks.datatypes.fieldeditor.BNegatableStatusValueFE;

/**
 * Testing workbench field editor for NegatableStatusBoolean, NegatableHonStatusNumeric and NegatableFiniteStatusBoolean fields
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-195
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 15, 2018
 */
@NiagaraType
public class BNegatableStatusValueFETest extends BTestNg{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BNegatableStatusValueFETest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusValueFETest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
	@BeforeClass(alwaysRun = true)
	public void setUp() {
		negatableStatusValFE = new BNegatableStatusValueFE();
	}

	private BNegatableStatusValueFE getNegatableStatusValueFE() {
		return new BNegatableStatusValueFE();
	}

	@DataProvider(name = "provideEditableStatusValues")
	public Object[] provideEditableStatusValues() {
		BNegatableStatusValueFETestData testData = new BNegatableStatusValueFETestData();
		BNegatableStatusValueFE booleanData = getNegatableStatusValueFE();booleanData.loadValue(testData.getNegatableStatusBoolean());
		BNegatableStatusValueFE numericData = getNegatableStatusValueFE();numericData.loadValue(testData.getNegatableHonStatusNumeric());
		BNegatableStatusValueFE fsbData = getNegatableStatusValueFE();fsbData.loadValue(testData.getNegatableFiniteStatusBoolean());
		
		return new Object[] {booleanData, numericData, fsbData};
	}
	
	@Test(dataProvider="provideEditableStatusValues")
	public void testDoLoadValueForEditableStatusValue(BHonStatusValueFE statusValueFE) throws Exception {
		BExpandablePane expandablePane = (BExpandablePane) statusValueFE.getContent();
		BWidget expansion = expandablePane.getExpansion();
		
	    Assert.assertEquals(((BWbFieldEditor)expansion.get("editor")).isEnabled(), true);
	    Assert.assertEquals(((BCheckBox)expansion.get("null")).isEnabled(), true);
	    Assert.assertEquals(((BCheckBox)expansion.get("negate")).isEnabled(), true);
	}
	
	@DataProvider(name = "provideReadOnlyStatusValues")
	public Object[] provideReadOnlyStatusValues() {
		BNegatableStatusValueFETestData testData = new BNegatableStatusValueFETestData();
		BNegatableStatusValueFE booleanData = negatableStatusValFE;booleanData.loadValue(testData.getNegatableStatusBooleanReadOnly());
		BNegatableStatusValueFE numericData = getNegatableStatusValueFE();numericData.loadValue(testData.getNegatableHonStatusNumericReadOnly());
		BNegatableStatusValueFE fsbData = getNegatableStatusValueFE();fsbData.loadValue(testData.getNegatableFiniteStatusBooleanReadOnly());
		
		return new Object[] {booleanData, numericData, fsbData};
	}
	
	@Test(dataProvider="provideReadOnlyStatusValues")
	public void testDoLoadValueForReadOnlyStatusValue(BHonStatusValueFE statusValueFE) throws Exception {
		BExpandablePane expandablePane = (BExpandablePane) statusValueFE.getContent();
		BWidget expansion = expandablePane.getExpansion();
		
	    Assert.assertEquals(((BWbFieldEditor)expansion.get("editor")).isReadonly(), true);
	    Assert.assertEquals(((BCheckBox)expansion.get("null")).isEnabled(), false);
	    Assert.assertEquals(((BCheckBox)expansion.get("negate")).isEnabled(), true);
	}
	
	@Test()
	public void testAgents() {
		BNegatableStatusBoolean nsb = new BNegatableStatusBoolean();
		AgentInfo info = nsb.getAgents().get("honeywellFunctionBlocks:NegatableStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BNegatableStatusBoolean");

		BNegatableFiniteStatusBoolean nfsb = new BNegatableFiniteStatusBoolean();
		info = nfsb.getAgents().get("honeywellFunctionBlocks:NegatableStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BNegatableFiniteStatusBoolean");
		
		BNegatableHonStatusNumeric nhsn = new BNegatableHonStatusNumeric();
		info = nhsn.getAgents().get("honeywellFunctionBlocks:NegatableStatusValueFE");
		Assert.assertNotNull(info, "Agent 'honeywellFunctionBlocks:HonStatusValueFE' not present on BNegatableHonStatusNumeric");
	}

	@DataProvider(name = "provideUpdatedStatusValue")
	public Object[] provideUpdatedStatusValue() throws Exception{
		BNegatableStatusValueFETestData testData = new BNegatableStatusValueFETestData();
		BNegatableStatusValueFE booleanData = getNegatableStatusValueFE();
		booleanData.loadValue(testData.getNegatableStatusBoolean());
		BExpandablePane expandablePane = (BExpandablePane) booleanData.getContent();
		BWidget expansion = expandablePane.getExpansion();
		((BCheckBox)expansion.get("negate")).setSelected(true);
		booleanData.saveValue();
		return new Object[] {testData};
	}

	@Test(dataProvider = "provideUpdatedStatusValue")
	public void testUpdatedStatusValue(BNegatableStatusValueFETestData testData){
		Assert.assertEquals(testData.getNegatableStatusBoolean().getNegate(),true);
	}
	
	
	@Test(dataProvider = "provideUpdatedStatusValue")
	public void testFlowPaneInstantiated(BNegatableStatusValueFETestData testData){
		
		Assert.assertEquals(testData.getNegatableStatusBoolean().getNegate(),true);
	}
	
	 private BNegatableStatusValueFE negatableStatusValFE;
}
