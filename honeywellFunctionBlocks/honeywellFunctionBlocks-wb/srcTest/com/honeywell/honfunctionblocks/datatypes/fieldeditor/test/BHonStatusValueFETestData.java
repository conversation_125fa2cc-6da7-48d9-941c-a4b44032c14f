/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor.test;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Testing  workbench field editor for HonStatusNumeric and FiniteStatusBoolean fields
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-195
 * <AUTHOR> - Suresh Khatri
 * @since Jan 18, 2018
 */
@NiagaraType
@NiagaraProperty(name = "finiteStatusBoolean", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "finiteStatusBooleanReadOnly", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags = Flags.SUMMARY | Flags.READONLY)
@NiagaraProperty(name="honStatusNumeric", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY)
@NiagaraProperty(name="honStatusNumericReadOnly", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY | Flags.READONLY)
@NiagaraProperty(name="honStatusBoolean", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.nullStatus)", flags=Flags.SUMMARY)
@NiagaraProperty(name="honStatusBooleanReadOnly", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.nullStatus)", flags=Flags.SUMMARY|Flags.READONLY)
@NiagaraProperty(name="honStatusEnum", type="BHonStatusEnum", defaultValue="new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY)
@NiagaraProperty(name="honStatusEnumReadOnly", type="BHonStatusEnum", defaultValue="new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY|Flags.READONLY)

public class BHonStatusValueFETestData extends BComponent{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BHonStatusValueFETestData(213076441)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "finiteStatusBoolean"

  /**
   * Slot for the {@code finiteStatusBoolean} property.
   * @see #getFiniteStatusBoolean
   * @see #setFiniteStatusBoolean
   */
  public static final Property finiteStatusBoolean = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code finiteStatusBoolean} property.
   * @see #finiteStatusBoolean
   */
  public BFiniteStatusBoolean getFiniteStatusBoolean() { return (BFiniteStatusBoolean)get(finiteStatusBoolean); }

  /**
   * Set the {@code finiteStatusBoolean} property.
   * @see #finiteStatusBoolean
   */
  public void setFiniteStatusBoolean(BFiniteStatusBoolean v) { set(finiteStatusBoolean, v, null); }

  //endregion Property "finiteStatusBoolean"

  //region Property "finiteStatusBooleanReadOnly"

  /**
   * Slot for the {@code finiteStatusBooleanReadOnly} property.
   * @see #getFiniteStatusBooleanReadOnly
   * @see #setFiniteStatusBooleanReadOnly
   */
  public static final Property finiteStatusBooleanReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BFiniteStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code finiteStatusBooleanReadOnly} property.
   * @see #finiteStatusBooleanReadOnly
   */
  public BFiniteStatusBoolean getFiniteStatusBooleanReadOnly() { return (BFiniteStatusBoolean)get(finiteStatusBooleanReadOnly); }

  /**
   * Set the {@code finiteStatusBooleanReadOnly} property.
   * @see #finiteStatusBooleanReadOnly
   */
  public void setFiniteStatusBooleanReadOnly(BFiniteStatusBoolean v) { set(finiteStatusBooleanReadOnly, v, null); }

  //endregion Property "finiteStatusBooleanReadOnly"

  //region Property "honStatusNumeric"

  /**
   * Slot for the {@code honStatusNumeric} property.
   * @see #getHonStatusNumeric
   * @see #setHonStatusNumeric
   */
  public static final Property honStatusNumeric = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusNumeric} property.
   * @see #honStatusNumeric
   */
  public BHonStatusNumeric getHonStatusNumeric() { return (BHonStatusNumeric)get(honStatusNumeric); }

  /**
   * Set the {@code honStatusNumeric} property.
   * @see #honStatusNumeric
   */
  public void setHonStatusNumeric(BHonStatusNumeric v) { set(honStatusNumeric, v, null); }

  //endregion Property "honStatusNumeric"

  //region Property "honStatusNumericReadOnly"

  /**
   * Slot for the {@code honStatusNumericReadOnly} property.
   * @see #getHonStatusNumericReadOnly
   * @see #setHonStatusNumericReadOnly
   */
  public static final Property honStatusNumericReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusNumericReadOnly} property.
   * @see #honStatusNumericReadOnly
   */
  public BHonStatusNumeric getHonStatusNumericReadOnly() { return (BHonStatusNumeric)get(honStatusNumericReadOnly); }

  /**
   * Set the {@code honStatusNumericReadOnly} property.
   * @see #honStatusNumericReadOnly
   */
  public void setHonStatusNumericReadOnly(BHonStatusNumeric v) { set(honStatusNumericReadOnly, v, null); }

  //endregion Property "honStatusNumericReadOnly"

  //region Property "honStatusBoolean"

  /**
   * Slot for the {@code honStatusBoolean} property.
   * @see #getHonStatusBoolean
   * @see #setHonStatusBoolean
   */
  public static final Property honStatusBoolean = newProperty(Flags.SUMMARY, new BHonStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusBoolean} property.
   * @see #honStatusBoolean
   */
  public BHonStatusBoolean getHonStatusBoolean() { return (BHonStatusBoolean)get(honStatusBoolean); }

  /**
   * Set the {@code honStatusBoolean} property.
   * @see #honStatusBoolean
   */
  public void setHonStatusBoolean(BHonStatusBoolean v) { set(honStatusBoolean, v, null); }

  //endregion Property "honStatusBoolean"

  //region Property "honStatusBooleanReadOnly"

  /**
   * Slot for the {@code honStatusBooleanReadOnly} property.
   * @see #getHonStatusBooleanReadOnly
   * @see #setHonStatusBooleanReadOnly
   */
  public static final Property honStatusBooleanReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BHonStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusBooleanReadOnly} property.
   * @see #honStatusBooleanReadOnly
   */
  public BHonStatusBoolean getHonStatusBooleanReadOnly() { return (BHonStatusBoolean)get(honStatusBooleanReadOnly); }

  /**
   * Set the {@code honStatusBooleanReadOnly} property.
   * @see #honStatusBooleanReadOnly
   */
  public void setHonStatusBooleanReadOnly(BHonStatusBoolean v) { set(honStatusBooleanReadOnly, v, null); }

  //endregion Property "honStatusBooleanReadOnly"

  //region Property "honStatusEnum"

  /**
   * Slot for the {@code honStatusEnum} property.
   * @see #getHonStatusEnum
   * @see #setHonStatusEnum
   */
  public static final Property honStatusEnum = newProperty(Flags.SUMMARY, new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusEnum} property.
   * @see #honStatusEnum
   */
  public BHonStatusEnum getHonStatusEnum() { return (BHonStatusEnum)get(honStatusEnum); }

  /**
   * Set the {@code honStatusEnum} property.
   * @see #honStatusEnum
   */
  public void setHonStatusEnum(BHonStatusEnum v) { set(honStatusEnum, v, null); }

  //endregion Property "honStatusEnum"

  //region Property "honStatusEnumReadOnly"

  /**
   * Slot for the {@code honStatusEnumReadOnly} property.
   * @see #getHonStatusEnumReadOnly
   * @see #setHonStatusEnumReadOnly
   */
  public static final Property honStatusEnumReadOnly = newProperty(Flags.SUMMARY | Flags.READONLY, new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus), null);

  /**
   * Get the {@code honStatusEnumReadOnly} property.
   * @see #honStatusEnumReadOnly
   */
  public BHonStatusEnum getHonStatusEnumReadOnly() { return (BHonStatusEnum)get(honStatusEnumReadOnly); }

  /**
   * Set the {@code honStatusEnumReadOnly} property.
   * @see #honStatusEnumReadOnly
   */
  public void setHonStatusEnumReadOnly(BHonStatusEnum v) { set(honStatusEnumReadOnly, v, null); }

  //endregion Property "honStatusEnumReadOnly"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusValueFETestData.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
