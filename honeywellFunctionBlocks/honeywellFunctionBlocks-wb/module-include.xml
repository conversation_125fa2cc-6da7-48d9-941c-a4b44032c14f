<!-- Module Include File -->
<!-- Types -->
<types>
  <!--com.honeywell.honfunctionblocks.datatypes.fieldeditor-->
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.BHonStatusValueFE" name="HonStatusValueFE">
    <agent requiredPermissions="w">
      <on type="honeywellFunctionBlocks:HonStatusNumeric"/>
      <on type="honeywellFunctionBlocks:HonStatusBoolean"/>
      <on type="honeywellFunctionBlocks:FiniteStatusBoolean"/>
      <on type="honeywellFunctionBlocks:HonStatusEnum"/>
    </agent>
  </type>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.BNegatableStatusValueFE" name="NegatableStatusValueFE">
    <agent requiredPermissions="w">
      <on type="honeywellFunctionBlocks:NegatableStatusBoolean"/>
      <on type="honeywellFunctionBlocks:NegatableFiniteStatusBoolean"/>
      <on type="honeywellFunctionBlocks:NegatableHonStatusNumeric"/>
    </agent>
  </type>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.BNegatableStatusValueOutSlotFE" name="NegatableStatusValueOutSlotFE"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.BStatusValueOutSaveOnlyFE" name="StatusValueOutSaveOnlyFE"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.BStatusValueOutSlotFE" name="StatusValueOutSlotFE"/>
</types>