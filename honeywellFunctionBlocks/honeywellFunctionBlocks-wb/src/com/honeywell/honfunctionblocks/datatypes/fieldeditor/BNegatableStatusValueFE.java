/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor;

import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BToggleButton;
import javax.baja.ui.ToggleCommand;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.ui.pane.BFlowPane;

import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * Workbench field editors for INegatableStatusValue. This field editor will
 * enable user to configure NEGATE option
 * 
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 27, 2017
 */
@NiagaraType(
	agent = @AgentOn(
		types = {"honeywellFunctionBlocks:NegatableStatusBoolean", "honeywellFunctionBlocks:NegatableFiniteStatusBoolean",
				"honeywellFunctionBlocks:NegatableHonStatusNumeric"},
		requiredPermissions="w"
	)
)
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160"})

public class BNegatableStatusValueFE extends BHonStatusValueFE {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.BNegatableStatusValueFE(775338915)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusValueFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	////////////////////////////////////////////////////////////////
	// Construction
	////////////////////////////////////////////////////////////////

	public BNegatableStatusValueFE() {
		super();
		// init negate checkbox
		negateToggle = new ToggleCommand(this, "negate");
		isNegate.setCommand(negateToggle, false, false);
	}

	@Override
	protected void doLoadValue(BObject raw, Context cx) throws Exception {
		super.doLoadValue(raw, cx);
		INegatableStatusValue nsb = (INegatableStatusValue) raw;
		
		if (editorPane==null) {
			BExpandablePane expansionPane = (BExpandablePane) this.getContent();
			editorPane = (BFlowPane) expansionPane.getExpansion();
			// changes on negate checkbox result in modified event
			linkTo("lk3", isNegate, BToggleButton.selected, setModified);

			// put it all together
			editorPane.set("negate", isNegate);
		}
		
		// load negate checkbox
		isNegate.setSelected(nsb.getNegate());
	}

	@Override
	protected BObject doSaveValue(BObject raw, Context cx) throws Exception {
		BObject rawTemp = super.doSaveValue(raw, cx);

		// save negate
		((INegatableStatusValue) rawTemp).setNegate(isNegate.isSelected());
		return rawTemp;
	}

	////////////////////////////////////////////////////////////////
	// Attributes
	////////////////////////////////////////////////////////////////

	private BFlowPane editorPane;
	private BCheckBox isNegate = new BCheckBox(FunctionBlocksLexicon.getLexicon().get("widget.negate") + "     ");
	private ToggleCommand negateToggle;
}
