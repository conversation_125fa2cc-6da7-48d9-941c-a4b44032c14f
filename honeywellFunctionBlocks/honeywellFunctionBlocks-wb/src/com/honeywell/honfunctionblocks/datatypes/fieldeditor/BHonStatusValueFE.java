/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor;

import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusValue;
import javax.baja.sys.BComponent;
import javax.baja.sys.BObject;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BTextField;
import javax.baja.ui.BToggleButton;
import javax.baja.ui.BWidget;
import javax.baja.ui.ToggleCommand;
import javax.baja.ui.enums.BHalign;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.ui.pane.BFlowPane;
import javax.baja.workbench.BWbPlugin;
import javax.baja.workbench.fieldeditor.BWbFieldEditor;

import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * Workbench field editor for all HonStatusValues like HonStatusNumeric,
 * HonStatusBoolean, FiniteStatusBoolean, HonStatusEnum fields
 * </br>
 * This custom field editor is required to handle IN_ONLY slots
 * </br>
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 16, 2018
 */

@NiagaraType(
	agent = @AgentOn(
		types = { "honeywellFunctionBlocks:HonStatusNumeric", "honeywellFunctionBlocks:HonStatusBoolean", 
				"honeywellFunctionBlocks:FiniteStatusBoolean","honeywellFunctionBlocks:HonStatusEnum"},
		requiredPermissions = "w"
		)
	)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160"})

public class BHonStatusValueFE extends BWbFieldEditor {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.BHonStatusValueFE(3998784807)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusValueFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	public BHonStatusValueFE() {
		// init null checkbox
		nullToggle = new NullToggle(this);
		isNull.setCommand(nullToggle, false, false);
		
		// summary is a simple readonly text field
		summary = new BTextField("", FIELD_WIDTH, false);

		// edit pane will contain null checkbox and value
		// editor plugin; initialized in doLoadValue()
		editPane = new BFlowPane(BHalign.left);
		
		// use an expander pane for the content
		expander = new BExpandablePane(summary, editPane);
	}

	@Override
	protected void doLoadValue(BObject raw, Context cx) throws Exception {
		BStatusValue se = (BStatusValue) raw;
		BObject v = se.getValueValue();

		setContent(expander);
		// first time thru, do some initialization
		if (valueEditor == null) {
			// build a plugin for the value
			valueEditor = BWbFieldEditor.makeFor(v);

			// chain action and modified events
			linkTo("lk0", valueEditor, BWbPlugin.pluginModified, setModified);
			linkTo("lk1", valueEditor, BWbPlugin.actionPerformed, actionPerformed);

			// changes on null checkbox result in modified event
			linkTo("lk2", isNull, BToggleButton.selected, setModified);
			
			// put it all together
			//NEGATE option should be shown first, so adding this NEGATE place holder in parent fieldEditor
			editPane.add("negate", BString.make(""));
			 
			editPane.add("null", isNull);
			editPane.add("editor", valueEditor);
		}

		// load value editor
		valueEditor.loadValue(v, cx);
		
		// load null checkbox
		isNull.setSelected(se.getStatus().isNull());
		
		// load summary textfield
		summary.setText(se.toString(cx));

		//If given slot is userDefined1 or readonly, user will not be allowed to edit  
		BComponent parent = (BComponent) se.getParent();
		if(isReadOnly(parent,se)){
			valueEditor.setReadonly(true);
			isNull.setEnabled(false);
		}
	}

	private boolean isReadOnly(BComponent parent, BStatusValue se){
		if(parent == null)
			return false;
		Slot currentSlot = parent.getSlot(se.getName());
		return Flags.isReadonly(parent,currentSlot);
	}

	@Override
	protected BObject doSaveValue(BObject raw, Context cx) throws Exception {
		// save to object passed in

		// save editor to value
		BValue v = (BValue) valueEditor.saveValue(cx);

		// save null check to status
		BStatus status = isNull.isSelected() ? BStatus.nullStatus : BStatus.ok;

		// important to pass-thru context in sets
		((BStatusValue) raw).set(((BStatusValue) raw).getValueProperty(), v, cx);
		((BStatusValue) raw).set(BStatusValue.status, status, cx);
		return raw;
	}

	class NullToggle extends ToggleCommand {
		NullToggle(BWidget owner) {
			super(owner, FunctionBlocksLexicon.getLexicon().get("widget.null"));
		}

		@Override
		public void setSelected(boolean s) {
			super.setSelected(s);

			// set the value plugin to readonly if null selected
			if (valueEditor != null)
				valueEditor.setReadonly(s);
		}
	}

	private BExpandablePane expander;
	private BTextField summary;
	private BFlowPane editPane;
	private BCheckBox isNull = new BCheckBox(FunctionBlocksLexicon.getLexicon().get("widget.null"));
	private NullToggle nullToggle;
	private BWbFieldEditor valueEditor;
	private static final int FIELD_WIDTH = 40;
}
