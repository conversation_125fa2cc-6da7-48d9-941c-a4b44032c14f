/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.ui.pane.BFlowPane;

/**
 * Workbench field editor for all OUTPUT slots. This is to display OUT_SAVE only
 * option for selected output slots.
 * Eg., BOccupancyArbitrator.MANUAL_OVERRIDE_STATE
 * 
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON><PERSON>
 *
 */
@NiagaraType
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160"})
public class BStatusValueOutSaveOnlyFE extends BStatusValueOutSlotFE {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.BStatusValueOutSaveOnlyFE(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusValueOutSaveOnlyFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BStatusValueOutSaveOnlyFE() {
    super();
  }

  @Override
  protected void doLoadValue(BObject raw, Context context) throws Exception {
    super.doLoadValue(raw,context);
    if(editorPane == null){
      BExpandablePane expansionPane = (BExpandablePane) this.getContent();
      editorPane = (BFlowPane) expansionPane.getExpansion();
      ((BWidget)editorPane.get("OUT_SAVE")).setEnabled(false);
    }
  }
  
  private BFlowPane editorPane;
}
