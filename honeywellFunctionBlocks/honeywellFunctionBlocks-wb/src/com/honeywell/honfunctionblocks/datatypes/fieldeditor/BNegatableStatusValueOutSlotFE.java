/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.fieldeditor;

import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComplex;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BCheckBox;
import javax.baja.ui.BToggleButton;
import javax.baja.ui.BWidget;
import javax.baja.ui.pane.BExpandablePane;
import javax.baja.ui.pane.BFlowPane;

/**
 * Workbench field editors for INegatableStatusValue. This field editor will
 * enable user to configure NEGATE and OUT_SAVE option
 * 
 * <AUTHOR> - RSH.<PERSON><PERSON>nan
 * @since May 8, 2018
 */
@NiagaraType
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160"})
public class BNegatableStatusValueOutSlotFE extends BNegatableStatusValueFE {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.fieldeditor.BNegatableStatusValueOutSlotFE(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:43 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusValueOutSlotFE.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BNegatableStatusValueOutSlotFE(){
    super();
    //init outSave Checkbox
    outSaveCheckBox = new BCheckBox(FunctionBlocksLexicon.getLexicon().get("widget.outsave"));
  }

  @Override
  protected void doLoadValue(BObject raw, Context cx) throws Exception {
    super.doLoadValue(raw, cx);
    if(editorPane == null){
      BExpandablePane expansionPane = (BExpandablePane) this.getContent();
      editorPane = (BFlowPane) expansionPane.getExpansion();
      //changes on outSave checkbox result in modified event
      editorPane.add("OUT_SAVE", outSaveCheckBox);
      linkTo(null, outSaveCheckBox,BToggleButton.selected, setModified);
      ((BWidget)editorPane.get("null")).setEnabled(false);
      ((BWidget)editorPane.get("editor")).setEnabled(false);
    }
    BComplex slot = raw.asComplex();
    BComplex slotParent = slot.getParent();
    if(null != slotParent) {
	    boolean isNotOutSave = Flags.isTransient(slotParent,slotParent.getSlot(slot.getName()));
	    outSaveCheckBox.setSelected(!isNotOutSave);
    }

  }

  @Override
  protected BObject doSaveValue(BObject raw, Context cx) throws Exception {
    boolean isOutSaveValue = outSaveCheckBox.isSelected();
    BComplex slot = raw.asComplex();
    BComplex slotParent = slot.getParent();
    int flags = slotParent.getFlags(slotParent.getSlot(slot.getName()));
    if(isOutSaveValue){
      flags &= ~Flags.TRANSIENT;
      slotParent.setFlags(slotParent.getSlot(slot.getName()),flags);
    }else{
      slotParent.setFlags(slotParent.getSlot(slot.getName()),flags | Flags.TRANSIENT);
    }
    return super.doSaveValue(raw, cx);
  }

  private BCheckBox outSaveCheckBox;
  private BFlowPane editorPane;
}
