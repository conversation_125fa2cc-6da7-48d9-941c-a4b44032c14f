<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" output="build/classes/java/test" path="test">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/moduleTest" path="srcTest">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/resources/test" path="src/test/resources">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/resources/moduleTest" path="src/moduleTest/resources">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/resources/main" path="src/main/resources">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="test/resources/|moduleTest/resources/|main/resources/" kind="src" output="build/classes/java/main" path="src">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/test" path="build/generated/sources/annotationProcessor/java/test">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/moduleTest" path="build/generated/sources/annotationProcessor/java/moduleTest">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/main" path="build/generated/sources/annotationProcessor/java/main">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/Java SE">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/bin/ext/nre.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/modules/honeywellFunctionBlocks-rt.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/modules/workbench-wb.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/modules/baja.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/modules/bajaui-wb.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Niagara/Niagara-*********/modules/test-wb.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="bin"/>
</classpath>
