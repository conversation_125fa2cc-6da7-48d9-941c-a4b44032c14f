<!-- Module Include File -->
<!-- Types -->
<types>
  <!-- Type Example:<type name="MyClass" class="com.acme.BMyClass"/> -->
  <!--com.honeywell.honfunctionblocks.datatypes.fieldeditor.test-->
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BHonStatusValueFETest" name="HonStatusValueFETest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BHonStatusValueFETestData" name="HonStatusValueFETestData"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BNegatableStatusValueFETest" name="NegatableStatusValueFETest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BNegatableStatusValueFETestData" name="NegatableStatusValueFETestData"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BNegatableStatusValueOutSlotFETest" name="NegatableStatusValueOutSlotFETest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BStatusValueOutSlotFETest" name="StatusValueOutSlotFETest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.fieldeditor.test.BStatusValueOutSaveOnlyFETest" name="StatusValueOutSaveOnlyFETest"/>
</types>