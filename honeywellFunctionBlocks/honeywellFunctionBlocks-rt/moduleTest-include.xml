<!-- Module Include File -->
<!-- Types -->
<types>
  <!-- Type Example:<type name="MyClass" class="com.acme.BMyClass"/> -->
  <!--com.honeywell.honfunctionblocks.fbs.logic.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.logic.test.BAndTest" name="AndTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.test.BOrTest" name="OrTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.test.BXorTest" name="XorTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.test.BOneShotTest" name="OneShotTest"/>
  <!--com.honeywell.honfunctionblocks.converters.test-->
  <type class="com.honeywell.honfunctionblocks.converters.test.BBooleanToFiniteStatusBooleanConverterTest" name="BooleanToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BEnumToFiniteStatusBooleanConverterTest" name="EnumToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BNumberToFiniteStatusBooleanConverterTest" name="NumberToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToFiniteStatusBooleanConveterTest" name="StatusBooleanToFiniteStatusBooleanConveterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToFiniteStatusBooleanConverterTest" name="StatusEnumToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToFiniteStatusBooleanConverterTest" name="StatusNumericToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusStringToFiniteStatusBooleanConverterTest" name="StatusStringToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStringToFiniteStatusNumericConverterTest" name="StringToFiniteStatusNumericConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToHonStatusNumericConverterTest" name="StatusNumericToHonStatusNumericConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToNegatableHonStatusNumericConverterTest" name="StatusNumericToNegatableHonStatusNumericConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStringToFiniteStatusBooleanConverterTest" name="StringToFiniteStatusBooleanConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToGenSetpointCalcEnumConverterTest" name="StatusNumericToGenSetpointCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToGenSetpointCalcEnumConverterTest" name="StatusEnumToGenSetpointCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToGenSetpointCalcEnumConverterTest" name="HonStatusNumericToGenSetpointCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToStatusOccupancyStateEnumConverterTest" name="HonStatusNumericToStatusOccupancyStateEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToStatusOccupancyStateEnumConverterTest" name="StatusEnumToStatusOccupancyStateEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToStatusOccupancyStateEnumConverterTest" name="StatusNumericToStatusOccupancyStateEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToStatusSetTemperatureModeEnumConveterTest" name="HonStatusNumericToStatusSetTemperatureModeEnumConveterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToStatusSetTemperatureModeEnumConveterTest" name="StatusNumericToStatusSetTemperatureModeEnumConveterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToStatusSetTemperatureModeEnumConveterTest" name="StatusEnumToStatusSetTemperatureModeEnumConveterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToHonStatusBooleanTest" name="StatusBooleanToHonStatusBooleanTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToNegatableStatusBooleanTest" name="StatusBooleanToNegatableStatusBooleanTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToStatusSyncEdgeTriggerEnumConverterTest" name="HonStatusNumericToStatusSyncEdgeTriggerEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToStatusSyncEdgeTriggerEnumConverterTest" name="StatusEnumToStatusSyncEdgeTriggerEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToStatusSyncEdgeTriggerEnumConverterTest" name="StatusNumericToStatusSyncEdgeTriggerEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusNumericToTempSetptCalcEnumConverterTest" name="StatusNumericToTempSetptCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BStatusEnumToTempSetptCalcEnumConverterTest" name="StatusEnumToTempSetptCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToTempSetptCalcEnumConverterTest" name="HonStatusNumericToTempSetptCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BHonStatusEnumToTempSetptCalcEnumConverterTest" name="HonStatusEnumToTempSetptCalcEnumConverterTest"/>
  <type class="com.honeywell.honfunctionblocks.converters.test.BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverterTest" name="NegatableStatusBooleanToNegatableFiniteStatusBooleanConverterTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.analog.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BMaximumTest" name="MaximumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BMinimumTest" name="MinimumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BEncodeTest" name="EncodeTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BHystereticRelayTest" name="HystereticRelayTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BSelectTest" name="SelectTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BAnalogLatchTest" name="AnalogLatchTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BPrioritySelectTest" name="PrioritySelectTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BSwitchTest" name="SwitchTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BCompareTest" name="CompareTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BEdgeTest" name="EdgeTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BAverageTest" name="AverageTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.control.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BPidTest" name="PidTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BFlowControlTest" name="FlowControlTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BFlowControlUnitEnumTest" name="FlowControlUnitEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BHVACOverrideEnumTest" name="HVACOverrideEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BRateLimitTest" name="RateLimitTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BDecisionBoxTest" name="DecisionBoxTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BDecisionOperationEnumTest" name="DecisionOperationEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.test.BCompareOperationEnumTest" name="CompareOperationEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BStagerTest" name="StagerTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BAiaTest" name="AiaTest"/>
  <!--com.honeywell.honfunctionblocks.datatypes.test-->
  <type class="com.honeywell.honfunctionblocks.datatypes.test.BHonStatusNumericTest" name="HonStatusNumericTest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.test.BHonStatusBooleanTest" name="HonStatusBooleanTest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.test.BNegatableStatusBooleanTest" name="NegatableStatusBooleanTest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.test.BNegatableFiniteStatusBooleanTest" name="NegatableFiniteStatusBooleanTest"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.test.BNegatableHonStatusNumericTest" name="NegatableHonStatusNumericTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BStageDriverTest" name="StageDriverTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BCyclerTest" name="CyclerTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.test.BDirectReverseSignOfTREnumTest" name="DirectReverseSignOfTREnumTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.math.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BMultiplyTest" name="MultiplyTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BDivideTest" name="DivideTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BDivOperationEnumTest" name="DivOperationEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BSubtractTest" name="SubtractTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BTailOperationEnumTest" name="TailOperationEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BAddTest" name="AddTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BLimitTest" name="LimitTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BLogarithmTest" name="LogarithmTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BLogTypeEnumTest" name="LogTypeEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BSquareRootTest" name="SquareRootTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BRatioTest" name="RatioTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BDigitalFilterTest" name="DigitalFilterTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BFlowVelocityTest" name="FlowVelocityTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BResetTest" name="ResetTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BEnthalpyTest" name="EnthalpyTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.test.BExponentialTest" name="ExponentialTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.datafunction.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.test.BOverrideTest" name="OverrideTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.test.BRuntimeAccumulateTest" name="RuntimeAccumulateTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.test.BCounterTest" name="CounterTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.zonecontrol.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BGeneralSetpointCalculatorTest" name="GeneralSetpointCalculatorTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BEffectiveOccupancyEnumTest" name="EffectiveOccupancyEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BSetTemperatureModeTest" name="SetTemperatureModeTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BNetworkLastInWinsEnumTest" name="NetworkLastInWinsEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BOccupancyArbitratorTest" name="OccupancyArbitratorTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.enums.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.enums.test.BOccupancyEnumTest" name="OccupancyEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.enums.test.BCompareEnumTest" name="CompareEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BOccupancySensorOperationEnumTest" name="OccupancySensorOperationEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BOccupancySensorStateEnumTest" name="OccupancySensorStateEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BScheduledStateEnumTest" name="ScheduledStateEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BTemperatureSetpointCalculatorTest" name="TemperatureSetpointCalculatorTest"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BSetpointTypeEnumTest" name="SetpointTypeEnumTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.math.enums.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.math.enums.test.BRatioOperationEnumTest" name="RatioOperationEnumTest"/>
  <!--com.honeywell.honfunctionblocks.utils.test-->
  <type class="com.honeywell.honfunctionblocks.utils.test.BPassThruTest" name="PassThruTest"/>
  <!--com.honeywell.honfunctionblocks.enums.test-->
  <type class="com.honeywell.honfunctionblocks.enums.test.BAiaDirectionEnumTest" name="AiaDirectionEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.enums.test.BLeadLagStrategyEnumTest" name="LeadLagStrategyEnumTest"/>
  <type class="com.honeywell.honfunctionblocks.enums.test.BPidOutputRangeEnumTest" name="PidOutputRangeEnumTest"/>
  <!--com.honeywell.honfunctionblocks.fbs-->
  <type class="com.honeywell.honfunctionblocks.fbs.BTestFb" name="TestFb"/>
  <type class="com.honeywell.honfunctionblocks.fbs.BFunctionalBlockTest" name="FunctionalBlockTest"/>
  <!--com.honeywell.honfunctionblocks.engine.test-->
  <type class="com.honeywell.honfunctionblocks.engine.test.BEngineFolderComponent" name="EngineFolderComponent"/>
  <type class="com.honeywell.honfunctionblocks.engine.test.BHoneywellFunctionBlockEngineTest" name="HoneywellFunctionBlockEngineTest"/>
  <!--com.honeywell.honfunctionblocks.fbs.builtin.test-->
  <type class="com.honeywell.honfunctionblocks.fbs.builtin.test.BTuncosTest" name="TuncosTest"/>
</types>