/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.constants;

/**
 * Controller specific flags will be created under this class. 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Jan 16, 2018
 */
@SuppressWarnings({"squid:S00103"})
public final class FBSlotConstants {
    
    //FACET to save value for NEGATE status of a function block property
    public static final String NEGATE = "negate";
    
    public static final String MINUTE_UNIT = "minute";
    
    //Field editor constants to toggle slot for OUT AND OUT_SAVE
    public static final String NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE = "honeywellFunctionBlocks:NegatableStatusValueOutSlotFE";
    public static final String NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE = "honeywellFunctionBlocks:NegatableOutSlotWidget";
    public static final String STATUS_VALUE_OUTSLOT_AX_FE = "honeywellFunctionBlocks:StatusValueOutSlotFE";
    public static final String STATUS_VALUE_OUTSLOT_UX_FE =  "honeywellFunctionBlocks:OutSlotWidget";
    public static final String STATUS_VALUE_OUTSAVE_ONLY_AX_FE =  "honeywellFunctionBlocks:StatusValueOutSaveOnlyFE";
    public static final String STATUS_VALUE_OUTSAVE_ONLY_UX_FE =  "honeywellFunctionBlocks:OutSaveOnlyWidget";
    public static final String STATUS_NUMERIC_OUTSLOT_AX_FE = "honeywellFunctionBlocks:StatusNumericOutSlotFE"; 
    public static final String INT_UX_FE = "honeywellFunctionBlocks:IntDataTypeWidget";
    public static final String OVERRIDE = "Override";
    public static final String AUTO = "Auto";
    public static final String STATUS_VALUE_AX_FE = "workbench:StatusValueFE";
    public static final String STATUS_VALUE_UX_FE = "webEditors:StatusValueEditor";
    
    ///CLOVER:OFF 
    private FBSlotConstants() {}
    ///CLOVER:ON

}
