/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.constants;

/**
 * Constants class to store only UnitNames
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON><PERSON>
 * @since Nov 24, 2017
 */
public final class UnitConstants {
	public static final String SECOND = "second";
	public static final String MINUTE = "minute";
	public static final String PERCENT = "percent";
	public static final String MILLI_SECOND = "millisecond";
	public static final String INCHES_OF_WATER="inches of water";
	public static final String SQUARE_FOOT="square foot";
	public static final String CFM="cubic feet per minute";
	public static final String FEET_PER_MIN="feet per minute";
	public static final String CHG_PER_SECOND = "change per second";
	public static final String PERCENT_PER_SECOND = "percent per second";
	public static final String HOUR = "hour";
	public static final String FAHRENHEIT = "fahrenheit";

	public static final int THOUSAND_MILLI_SECOND = 1000;
	public static final float SIXTY_SECOND = 60.0f;
	public static final float ONEDAY_IN_MINUTE = 1440.0f;


	///CLOVER:OFF 
	private UnitConstants() {
		throw new IllegalAccessError("Utility class");
	}
	///CLOVER:ON
	
}
