/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;

import javax.baja.sys.BEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public class BCompareStatusEnum extends BHonStatusEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.enums.BCompareStatusEnum(2979906276)1.0$ @*/
/* Generated Tue Aug 06 14:31:30 IST 2019 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareStatusEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  public BCompareStatusEnum() {
	  super();
  }

  public BCompareStatusEnum(BEnum value) {
	  super(value);
  }

  public BCompareStatusEnum(BEnum value, BStatus status) {
	  super(value, status);
  }
}
