/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of OccupancyArbitrator block implementation
 * This ENUM is used for WMOverride, NetworkManOcc, MANUAL_OVERRIDE_STATE slot of OccupancyArbitrator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshmi<PERSON>ayanan
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range(value="Occupied", ordinal=0), 
		@Range(value="Unoccupied", ordinal=1),
		@Range(value="Bypass", ordinal=2),
		@Range(value="Standby", ordinal=3),
		@Range(value="Null", ordinal=255)
		}, defaultValue = "Null")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BOccupancyEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonearbitrator.BOccupancyEnum(4146279291)1.0$ @*/
/* Generated Fri Feb 02 12:27:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for Occupied. */
  public static final int OCCUPIED = 0;
  /** Ordinal value for Unoccupied. */
  public static final int UNOCCUPIED = 1;
  /** Ordinal value for Bypass. */
  public static final int BYPASS = 2;
  /** Ordinal value for Standby. */
  public static final int STANDBY = 3;
  /** Ordinal value for Null. */
  public static final int NULL = 255;
  
  /** BOccupancyEnum constant for Occupied. */
  public static final BOccupancyEnum Occupied = new BOccupancyEnum(OCCUPIED);
  /** BOccupancyEnum constant for Unoccupied. */
  public static final BOccupancyEnum Unoccupied = new BOccupancyEnum(UNOCCUPIED);
  /** BOccupancyEnum constant for Bypass. */
  public static final BOccupancyEnum Bypass = new BOccupancyEnum(BYPASS);
  /** BOccupancyEnum constant for Standby. */
  public static final BOccupancyEnum Standby = new BOccupancyEnum(STANDBY);
  /** BOccupancyEnum constant for Null. */
  public static final BOccupancyEnum Null = new BOccupancyEnum(NULL);
  
  /** Factory method with ordinal. */
  public static BOccupancyEnum make(int ordinal)
  {
    return (BOccupancyEnum)Occupied.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BOccupancyEnum make(String tag)
  {
    return (BOccupancyEnum)Occupied.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BOccupancyEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BOccupancyEnum DEFAULT = Null;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOccupancyEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
