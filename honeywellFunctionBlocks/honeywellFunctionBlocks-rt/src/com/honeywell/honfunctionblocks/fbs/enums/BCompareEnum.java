/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
@NiagaraEnum(range = {
		@Range(value="False", ordinal=0), 
		@Range(value="True", ordinal=1),
		@Range(value="Null", ordinal=255)
		}, defaultValue = "Null")
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public final class BCompareEnum extends BFrozenEnum {
//region /*+ ------------ <PERSON>EGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.enums.BCompareEnum(2371914935)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for False. */
  public static final int FALSE = 0;
  /** Ordinal value for True. */
  public static final int TRUE = 1;
  /** Ordinal value for Null. */
  public static final int NULL = 255;

  /** BCompareEnum constant for False. */
  public static final BCompareEnum False = new BCompareEnum(FALSE);
  /** BCompareEnum constant for True. */
  public static final BCompareEnum True = new BCompareEnum(TRUE);
  /** BCompareEnum constant for Null. */
  public static final BCompareEnum Null = new BCompareEnum(NULL);

  /** Factory method with ordinal. */
  public static BCompareEnum make(int ordinal)
  {
    return (BCompareEnum)False.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BCompareEnum make(String tag)
  {
    return (BCompareEnum)False.getRange().get(tag);
  }

  /** Private constructor. */
  private BCompareEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BCompareEnum DEFAULT = Null;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
