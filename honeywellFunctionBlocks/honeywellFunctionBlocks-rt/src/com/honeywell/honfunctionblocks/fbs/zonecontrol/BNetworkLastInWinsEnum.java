/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of OccupancyArbitrator block implementation
 * This ENUM is used for netLastInWins config slot of OccupancyArbitrator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range("NetworkWins"), 
		@Range("LastInWins"),
		}, defaultValue = "NetworkWins")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BNetworkLastInWinsEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonearbitrator.BNetworkLastInWinsEnums(1664620339)1.0$ @*/
/* Generated Fri Feb 02 12:27:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for NetworkWins. */
  public static final int NETWORK_WINS = 0;
  /** Ordinal value for LastInWins. */
  public static final int LAST_IN_WINS = 1;
  
  /** BNetworkLastInWinsEnums constant for NetworkWins. */
  public static final BNetworkLastInWinsEnum NetworkWins = new BNetworkLastInWinsEnum(NETWORK_WINS);
  /** BNetworkLastInWinsEnums constant for LastInWins. */
  public static final BNetworkLastInWinsEnum LastInWins = new BNetworkLastInWinsEnum(LAST_IN_WINS);
  
  /** Factory method with ordinal. */
  public static BNetworkLastInWinsEnum make(int ordinal)
  {
    return (BNetworkLastInWinsEnum)NetworkWins.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BNetworkLastInWinsEnum make(String tag)
  {
    return (BNetworkLastInWinsEnum)NetworkWins.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BNetworkLastInWinsEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BNetworkLastInWinsEnum DEFAULT = NetworkWins;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNetworkLastInWinsEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
