/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation Set Temperature Mode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 8, 2018
 */

@NiagaraType
@NiagaraEnum (
	range = {
			@Range(value="Cvahu", ordinal=0),
			@Range(value="Vav", ordinal=1)
	},
	defaultValue="Cvahu"
)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public final class BControlTypeEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BControlTypeEnum(2611160062)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Cvahu. */
  public static final int CVAHU = 0;
  /** Ordinal value for Vav. */
  public static final int VAV = 1;

  /** BControlTypeEnum constant for Cvahu. */
  public static final BControlTypeEnum Cvahu = new BControlTypeEnum(CVAHU);
  /** BControlTypeEnum constant for Vav. */
  public static final BControlTypeEnum Vav = new BControlTypeEnum(VAV);

  /** Factory method with ordinal. */
  public static BControlTypeEnum make(int ordinal)
  {
    return (BControlTypeEnum)Cvahu.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BControlTypeEnum make(String tag)
  {
    return (BControlTypeEnum)Cvahu.getRange().get(tag);
  }

  /** Private constructor. */
  private BControlTypeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BControlTypeEnum DEFAULT = Cvahu;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BControlTypeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
