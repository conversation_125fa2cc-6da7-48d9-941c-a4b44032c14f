/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of TemperatureSetpointCalculator block implementation
 * This ENUM is used for SetpointType config slot of TemperatureSetpointCalculator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 * @since Feb 16, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range("Global"), 
		@Range("Custom"),
		}, defaultValue = "Global")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1845","squid:S1213"})

public final class BSetpointTypeEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetpointTypeEnum(3162241363)1.0$ @*/
/* Generated Fri Feb 16 15:57:33 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for Global. */
  public static final int GLOBAL = 0;
  /** Ordinal value for Custom. */
  public static final int CUSTOM = 1;
  
  /** BSetpointTypeEnum constant for Global. */
  public static final BSetpointTypeEnum Global = new BSetpointTypeEnum(GLOBAL);
  /** BSetpointTypeEnum constant for Custom. */
  public static final BSetpointTypeEnum Custom = new BSetpointTypeEnum(CUSTOM);
  
  /** Factory method with ordinal. */
  public static BSetpointTypeEnum make(int ordinal)
  {
    return (BSetpointTypeEnum)Global.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BSetpointTypeEnum make(String tag)
  {
    return (BSetpointTypeEnum)Global.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BSetpointTypeEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BSetpointTypeEnum DEFAULT = Global;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSetpointTypeEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
