/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation Set Temperature Mode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 7, 2018
 */

@NiagaraType
@NiagaraEnum (
	range = {
			@Range(value="Ss_Auto", ordinal=0),
			@Range(value="Ss_Cool", ordinal=1),
			@Range(value="Ss_Heat", ordinal=2),
			@Range(value="Ss_Emerg_Heat", ordinal=3),
			@Range(value="Ss_Eff_Heat_Mode", ordinal=4),
			@Range(value="Ss_Others", ordinal=254),
			@Range(value="Ss_Off", ordinal=255)
	},
	defaultValue="Ss_Auto"
)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public final class BSystemSwitchEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum(2779184107)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Ss_Auto. */
  public static final int SS_AUTO = 0;
  /** Ordinal value for Ss_Cool. */
  public static final int SS_COOL = 1;
  /** Ordinal value for Ss_Heat. */
  public static final int SS_HEAT = 2;
  /** Ordinal value for Ss_Emerg_Heat. */
  public static final int SS_EMERG_HEAT = 3;
  /** Ordinal value for Ss_Eff_Heat_Mode. */
  public static final int SS_EFF_HEAT_MODE = 4;
  /** Ordinal value for Ss_Others. */
  public static final int SS_OTHERS = 254;
  /** Ordinal value for Ss_Off. */
  public static final int SS_OFF = 255;

  /** BSystemSwitchEnum constant for Ss_Auto. */
  public static final BSystemSwitchEnum Ss_Auto = new BSystemSwitchEnum(SS_AUTO);
  /** BSystemSwitchEnum constant for Ss_Cool. */
  public static final BSystemSwitchEnum Ss_Cool = new BSystemSwitchEnum(SS_COOL);
  /** BSystemSwitchEnum constant for Ss_Heat. */
  public static final BSystemSwitchEnum Ss_Heat = new BSystemSwitchEnum(SS_HEAT);
  /** BSystemSwitchEnum constant for Ss_Emerg_Heat. */
  public static final BSystemSwitchEnum Ss_Emerg_Heat = new BSystemSwitchEnum(SS_EMERG_HEAT);
  /** BSystemSwitchEnum constant for Ss_Eff_Heat_Mode. */
  public static final BSystemSwitchEnum Ss_Eff_Heat_Mode = new BSystemSwitchEnum(SS_EFF_HEAT_MODE);
  /** BSystemSwitchEnum constant for Ss_Others. */
  public static final BSystemSwitchEnum Ss_Others = new BSystemSwitchEnum(SS_OTHERS);
  /** BSystemSwitchEnum constant for Ss_Off. */
  public static final BSystemSwitchEnum Ss_Off = new BSystemSwitchEnum(SS_OFF);

  /** Factory method with ordinal. */
  public static BSystemSwitchEnum make(int ordinal)
  {
    return (BSystemSwitchEnum)Ss_Auto.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BSystemSwitchEnum make(String tag)
  {
    return (BSystemSwitchEnum)Ss_Auto.getRange().get(tag);
  }

  /** Private constructor. */
  private BSystemSwitchEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BSystemSwitchEnum DEFAULT = Ss_Auto;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSystemSwitchEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
