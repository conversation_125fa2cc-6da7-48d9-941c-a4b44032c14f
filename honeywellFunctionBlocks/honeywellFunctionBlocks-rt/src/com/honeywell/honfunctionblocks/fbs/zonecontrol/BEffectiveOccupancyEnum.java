/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of OccupancyArbitrator block implementation
 * This ENUM is used for EFF_OCC_CURRENT_STATE output slot of OccupancyArbitrator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON>
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range(value="Occupied", ordinal=0), 
		@Range(value="Unoccupied", ordinal=1),
		@Range(value="Bypass", ordinal=2),
		@Range(value="Standby", ordinal=3),
		}, defaultValue = "Occupied")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BEffectiveOccupancyEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum(1161511008)1.0$ @*/
/* Generated Tue Feb 06 14:31:30 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for Occupied. */
  public static final int OCCUPIED = 0;
  /** Ordinal value for Unoccupied. */
  public static final int UNOCCUPIED = 1;
  /** Ordinal value for Bypass. */
  public static final int BYPASS = 2;
  /** Ordinal value for Standby. */
  public static final int STANDBY = 3;
  
  /** BEffectiveOccupancyEnum constant for Occupied. */
  public static final BEffectiveOccupancyEnum Occupied = new BEffectiveOccupancyEnum(OCCUPIED);
  /** BEffectiveOccupancyEnum constant for Unoccupied. */
  public static final BEffectiveOccupancyEnum Unoccupied = new BEffectiveOccupancyEnum(UNOCCUPIED);
  /** BEffectiveOccupancyEnum constant for Bypass. */
  public static final BEffectiveOccupancyEnum Bypass = new BEffectiveOccupancyEnum(BYPASS);
  /** BEffectiveOccupancyEnum constant for Standby. */
  public static final BEffectiveOccupancyEnum Standby = new BEffectiveOccupancyEnum(STANDBY);
  
  /** Factory method with ordinal. */
  public static BEffectiveOccupancyEnum make(int ordinal)
  {
    return (BEffectiveOccupancyEnum)Occupied.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BEffectiveOccupancyEnum make(String tag)
  {
    return (BEffectiveOccupancyEnum)Occupied.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BEffectiveOccupancyEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BEffectiveOccupancyEnum DEFAULT = Occupied;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEffectiveOccupancyEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
