/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import java.util.ArrayList;
import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BStatusGenSetpointCalcEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.control.XYLineCalculator;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Implementation of General Setpoint Calculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * <AUTHOR> - Lavanya B.
 * @since Feb 12, 2018
 */

@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"general_setpoint_calculator.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@NiagaraProperty(name="effOccuCurrentState", type="BStatusGenSetpointCalcEnum", defaultValue="new BStatusGenSetpointCalcEnum(BOccupancyEnum.Occupied, BStatus.nullStatus)", flags=Flags.SUMMARY,
	facets = { @Facet(name = "BFacets.RANGE", value = "BOccupancyEnum.Occupied.getRange()") }
)

@NiagaraProperty(name = "ResetInput", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "Reset0Pct", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "Reset100Pct", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "ResetAmount", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "OccupiedSetpoint", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "StandbySetpoint", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "UnoccupiedSetpoint", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="EFF_SETPT", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.POSITIVE_INFINITY)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") ,
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103", "squid:S00100" })

public class BGeneralSetpointCalculator extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BGeneralSetpointCalculator(1427942591)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "general_setpoint_calculator.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "effOccuCurrentState"

  /**
   * Slot for the {@code effOccuCurrentState} property.
   * @see #getEffOccuCurrentState
   * @see #setEffOccuCurrentState
   */
  public static final Property effOccuCurrentState = newProperty(Flags.SUMMARY, new BStatusGenSetpointCalcEnum(BOccupancyEnum.Occupied, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BOccupancyEnum.Occupied.getRange()));

  /**
   * Get the {@code effOccuCurrentState} property.
   * @see #effOccuCurrentState
   */
  public BStatusGenSetpointCalcEnum getEffOccuCurrentState() { return (BStatusGenSetpointCalcEnum)get(effOccuCurrentState); }

  /**
   * Set the {@code effOccuCurrentState} property.
   * @see #effOccuCurrentState
   */
  public void setEffOccuCurrentState(BStatusGenSetpointCalcEnum v) { set(effOccuCurrentState, v, null); }

  //endregion Property "effOccuCurrentState"

  //region Property "ResetInput"

  /**
   * Slot for the {@code ResetInput} property.
   * @see #getResetInput
   * @see #setResetInput
   */
  public static final Property ResetInput = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code ResetInput} property.
   * @see #ResetInput
   */
  public BHonStatusNumeric getResetInput() { return (BHonStatusNumeric)get(ResetInput); }

  /**
   * Set the {@code ResetInput} property.
   * @see #ResetInput
   */
  public void setResetInput(BHonStatusNumeric v) { set(ResetInput, v, null); }

  //endregion Property "ResetInput"

  //region Property "Reset0Pct"

  /**
   * Slot for the {@code Reset0Pct} property.
   * @see #getReset0Pct
   * @see #setReset0Pct
   */
  public static final Property Reset0Pct = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code Reset0Pct} property.
   * @see #Reset0Pct
   */
  public BHonStatusNumeric getReset0Pct() { return (BHonStatusNumeric)get(Reset0Pct); }

  /**
   * Set the {@code Reset0Pct} property.
   * @see #Reset0Pct
   */
  public void setReset0Pct(BHonStatusNumeric v) { set(Reset0Pct, v, null); }

  //endregion Property "Reset0Pct"

  //region Property "Reset100Pct"

  /**
   * Slot for the {@code Reset100Pct} property.
   * @see #getReset100Pct
   * @see #setReset100Pct
   */
  public static final Property Reset100Pct = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code Reset100Pct} property.
   * @see #Reset100Pct
   */
  public BHonStatusNumeric getReset100Pct() { return (BHonStatusNumeric)get(Reset100Pct); }

  /**
   * Set the {@code Reset100Pct} property.
   * @see #Reset100Pct
   */
  public void setReset100Pct(BHonStatusNumeric v) { set(Reset100Pct, v, null); }

  //endregion Property "Reset100Pct"

  //region Property "ResetAmount"

  /**
   * Slot for the {@code ResetAmount} property.
   * @see #getResetAmount
   * @see #setResetAmount
   */
  public static final Property ResetAmount = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code ResetAmount} property.
   * @see #ResetAmount
   */
  public BHonStatusNumeric getResetAmount() { return (BHonStatusNumeric)get(ResetAmount); }

  /**
   * Set the {@code ResetAmount} property.
   * @see #ResetAmount
   */
  public void setResetAmount(BHonStatusNumeric v) { set(ResetAmount, v, null); }

  //endregion Property "ResetAmount"

  //region Property "OccupiedSetpoint"

  /**
   * Slot for the {@code OccupiedSetpoint} property.
   * @see #getOccupiedSetpoint
   * @see #setOccupiedSetpoint
   */
  public static final Property OccupiedSetpoint = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code OccupiedSetpoint} property.
   * @see #OccupiedSetpoint
   */
  public BHonStatusNumeric getOccupiedSetpoint() { return (BHonStatusNumeric)get(OccupiedSetpoint); }

  /**
   * Set the {@code OccupiedSetpoint} property.
   * @see #OccupiedSetpoint
   */
  public void setOccupiedSetpoint(BHonStatusNumeric v) { set(OccupiedSetpoint, v, null); }

  //endregion Property "OccupiedSetpoint"

  //region Property "StandbySetpoint"

  /**
   * Slot for the {@code StandbySetpoint} property.
   * @see #getStandbySetpoint
   * @see #setStandbySetpoint
   */
  public static final Property StandbySetpoint = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code StandbySetpoint} property.
   * @see #StandbySetpoint
   */
  public BHonStatusNumeric getStandbySetpoint() { return (BHonStatusNumeric)get(StandbySetpoint); }

  /**
   * Set the {@code StandbySetpoint} property.
   * @see #StandbySetpoint
   */
  public void setStandbySetpoint(BHonStatusNumeric v) { set(StandbySetpoint, v, null); }

  //endregion Property "StandbySetpoint"

  //region Property "UnoccupiedSetpoint"

  /**
   * Slot for the {@code UnoccupiedSetpoint} property.
   * @see #getUnoccupiedSetpoint
   * @see #setUnoccupiedSetpoint
   */
  public static final Property UnoccupiedSetpoint = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code UnoccupiedSetpoint} property.
   * @see #UnoccupiedSetpoint
   */
  public BHonStatusNumeric getUnoccupiedSetpoint() { return (BHonStatusNumeric)get(UnoccupiedSetpoint); }

  /**
   * Set the {@code UnoccupiedSetpoint} property.
   * @see #UnoccupiedSetpoint
   */
  public void setUnoccupiedSetpoint(BHonStatusNumeric v) { set(UnoccupiedSetpoint, v, null); }

  //endregion Property "UnoccupiedSetpoint"

  //region Property "EFF_SETPT"

  /**
   * Slot for the {@code EFF_SETPT} property.
   * @see #getEFF_SETPT
   * @see #setEFF_SETPT
   */
  public static final Property EFF_SETPT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.POSITIVE_INFINITY))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_SETPT} property.
   * @see #EFF_SETPT
   */
  public BHonStatusNumeric getEFF_SETPT() { return (BHonStatusNumeric)get(EFF_SETPT); }

  /**
   * Set the {@code EFF_SETPT} property.
   * @see #EFF_SETPT
   */
  public void setEFF_SETPT(BHonStatusNumeric v) { set(EFF_SETPT, v, null); }

  //endregion Property "EFF_SETPT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BGeneralSetpointCalculator.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeGSC();
	}
	
	private void executeGSC() {
		BOccupancyEnum effectiveOccCurrentState = getEffectiveOccStateValAfterValidate();
		
		double effectiveSetpoint;
		
		switch(effectiveOccCurrentState.getOrdinal()) {
			case BOccupancyEnum.UNOCCUPIED:
				effectiveSetpoint = getInValueAfterValidate(UnoccupiedSetpoint);
				break;
				
			case BOccupancyEnum.STANDBY:
				effectiveSetpoint = getInValueAfterValidate(StandbySetpoint);
				break;
				
			case BOccupancyEnum.OCCUPIED:
			case BOccupancyEnum.BYPASS:
				///CLOVER:OFF
				default:
				///CLOVER:ON
					effectiveSetpoint = computeEffectiveSetpointForOccupiedStatus();
					break;
		}
		
		
		getEFF_SETPT().setValue(effectiveSetpoint);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(effOccuCurrentState);
		propertyArrayList.addAll(getInputResetPropertiesList());
		propertyArrayList.add(OccupiedSetpoint);
		propertyArrayList.add(UnoccupiedSetpoint);
		propertyArrayList.add(StandbySetpoint);
		return propertyArrayList;
	}

	private List<Property> getInputResetPropertiesList() {
		List<Property> propertyArrayList = new ArrayList<>();
		propertyArrayList.add(ResetInput);
		propertyArrayList.add(Reset0Pct);
		propertyArrayList.add(Reset100Pct);
		propertyArrayList.add(ResetAmount);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(EFF_SETPT);
		return properties;
	}
	
	private double computeEffectiveSetpointForOccupiedStatus() {
		double resetValue;
		
		if(hasAnyInvalidResetInput())
			resetValue = 0.0;
		else {
			double resetInputValue = getInValueAfterValidate(ResetInput);
			double reset0PctValue=getInValueAfterValidate(Reset0Pct);
			double reset100PctValue=getInValueAfterValidate(Reset100Pct);
			double resetAmtValue=getInValueAfterValidate(ResetAmount);
			resetValue = XYLineCalculator.xyline(resetInputValue, reset0PctValue, reset100PctValue, 0.0, resetAmtValue, true);
		}
		
		if(Double.isNaN(resetValue))
			resetValue = Double.POSITIVE_INFINITY;
		
		return getInValueAfterValidate(OccupiedSetpoint) + resetValue;
	}
	
	private double getInValueAfterValidate(Property property) {
		  double val = Double.POSITIVE_INFINITY;
		  if(isSlotValueValid(property)) {
			  BHonStatusNumeric bval = (BHonStatusNumeric) get(property);
			  val = bval.getValue();
		  }  
		  return val;	  
	}
	
	private BOccupancyEnum getEffectiveOccStateValAfterValidate() {
		if(isConfigured(effOccuCurrentState))
			return BOccupancyEnum.make(getEffOccuCurrentState().getEnum().getOrdinal());
			
		return BOccupancyEnum.Occupied; 
	}
	
	/**
	 * Check whether this block has any Invalid Input
	 * @return
	 */
	public boolean hasAnyInvalidResetInput() {
		boolean invalid = false; 
		List<Property> propertiesList = getInputResetPropertiesList();
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(!isConfigured(property) || isInvalidValue(property)) {
				invalid = true;
				break;
			}
		}
		return invalid;
	}	
	  
}
