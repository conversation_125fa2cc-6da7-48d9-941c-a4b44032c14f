/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;

/**
 * This class handles datatype for all Temperature Setpoint type enums
 * <AUTHOR> - <PERSON>vanya
 * @since Jun 25, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStatusTempSetpointCalcEnum extends BHonStatusEnum {
/*+ ------------ <PERSON>EG<PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BStatusTempSetpointCalcEnum(2979906276)1.0$ @*/
/* Generated Mon Jun 25 17:23:31 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusTempSetpointCalcEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  public BStatusTempSetpointCalcEnum() {
	  super();
  }

  public BStatusTempSetpointCalcEnum(BEnum value) {
	  super(value);
  }

  public BStatusTempSetpointCalcEnum(BEnum value, BStatus status) {
	  super(value, status);
  }


}
