/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation Set Temperature Mode of EffTempMode Enum as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 7, 2018
 */

@NiagaraType
@NiagaraEnum (
	range = {
			@Range(value="Cool_Mode", ordinal=0),
			@Range(value="Reheat_Mode", ordinal=1),
			@Range(value="Heat_Mode", ordinal=2),
			@Range(value="Emerg_Heat", ordinal=3),
			@Range(value="Off_Mode", ordinal=255)
	},
	defaultValue = "Cool_Mode"
)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public final class BEffTempModeEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BEffTempModeEnum(575924814)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Cool_Mode. */
  public static final int COOL_MODE = 0;
  /** Ordinal value for Reheat_Mode. */
  public static final int REHEAT_MODE = 1;
  /** Ordinal value for Heat_Mode. */
  public static final int HEAT_MODE = 2;
  /** Ordinal value for Emerg_Heat. */
  public static final int EMERG_HEAT = 3;
  /** Ordinal value for Off_Mode. */
  public static final int OFF_MODE = 255;

  /** BEffTempModeEnum constant for Cool_Mode. */
  public static final BEffTempModeEnum Cool_Mode = new BEffTempModeEnum(COOL_MODE);
  /** BEffTempModeEnum constant for Reheat_Mode. */
  public static final BEffTempModeEnum Reheat_Mode = new BEffTempModeEnum(REHEAT_MODE);
  /** BEffTempModeEnum constant for Heat_Mode. */
  public static final BEffTempModeEnum Heat_Mode = new BEffTempModeEnum(HEAT_MODE);
  /** BEffTempModeEnum constant for Emerg_Heat. */
  public static final BEffTempModeEnum Emerg_Heat = new BEffTempModeEnum(EMERG_HEAT);
  /** BEffTempModeEnum constant for Off_Mode. */
  public static final BEffTempModeEnum Off_Mode = new BEffTempModeEnum(OFF_MODE);

  /** Factory method with ordinal. */
  public static BEffTempModeEnum make(int ordinal)
  {
    return (BEffTempModeEnum)Cool_Mode.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BEffTempModeEnum make(String tag)
  {
    return (BEffTempModeEnum)Cool_Mode.getRange().get(tag);
  }

  /** Private constructor. */
  private BEffTempModeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BEffTempModeEnum DEFAULT = Cool_Mode;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEffTempModeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
