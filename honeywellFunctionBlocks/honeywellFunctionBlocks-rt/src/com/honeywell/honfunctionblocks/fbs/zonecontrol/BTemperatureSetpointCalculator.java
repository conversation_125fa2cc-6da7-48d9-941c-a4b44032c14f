/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Implementation of TemperatureSetpointCalculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405 
 * Testcase ID: F1PLT-ATC-265
 * 
 * Functionality: Determine the effective heating and cooling setpoints. This
 * routine calculates the effective heating and cooling setpoints given the
 * effective occupancy current state, next state and TUNCOS, setpoints,heat and
 * cool ramp rates and manual override state. It limits all inputs to legal
 * values. It calculates the recovery. See SDS Temperature Setpoint Calculator
 * Function Block
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 9, 2018
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"temperature_setpoint_calculator.png\")", flags=Flags.HIDDEN|Flags.READONLY)

//Input slots
@NiagaraProperty(name="EffOccuCurrentState", type="BStatusTempSetpointCalcEnum", defaultValue="new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BEffectiveOccupancyEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="ScheduleNextState", type="BStatusTempSetpointCalcEnum", defaultValue="new BStatusTempSetpointCalcEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BScheduledStateEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="ScheduleTUNCOS", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(11520F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(11520)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="Setpoint", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="HeatRampRate", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="CoolRampRate", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="ManualOverrideState", type="BStatusTempSetpointCalcEnum", defaultValue="new BStatusTempSetpointCalcEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BOccupancyEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="occupiedCool", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(75.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="standbyCool", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(78.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="unoccupiedCool", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(85.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="occupiedHeat", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(70.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="standbyHeat", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(67.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="unoccupiedHeat", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(55.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})

//Config slots
@NiagaraProperty(name="SetpointType", type="BSetpointTypeEnum", defaultValue="BSetpointTypeEnum.DEFAULT", flags=Flags.HIDDEN)

//Output slots
@NiagaraProperty(name="EFF_HEAT_SETPT", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.POSITIVE_INFINITY)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
  @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
  @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="EFF_COOL_SETPT", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.POSITIVE_INFINITY)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
  @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
  @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103", "squid:S00100"})
public class BTemperatureSetpointCalculator extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BTemperatureSetpointCalculator(3644964683)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "temperature_setpoint_calculator.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "EffOccuCurrentState"

  /**
   * Slot for the {@code EffOccuCurrentState} property.
   * Input slots
   * @see #getEffOccuCurrentState
   * @see #setEffOccuCurrentState
   */
  public static final Property EffOccuCurrentState = newProperty(Flags.SUMMARY, new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BEffectiveOccupancyEnum.DEFAULT.getRange()));

  /**
   * Get the {@code EffOccuCurrentState} property.
   * Input slots
   * @see #EffOccuCurrentState
   */
  public BStatusTempSetpointCalcEnum getEffOccuCurrentState() { return (BStatusTempSetpointCalcEnum)get(EffOccuCurrentState); }

  /**
   * Set the {@code EffOccuCurrentState} property.
   * Input slots
   * @see #EffOccuCurrentState
   */
  public void setEffOccuCurrentState(BStatusTempSetpointCalcEnum v) { set(EffOccuCurrentState, v, null); }

  //endregion Property "EffOccuCurrentState"

  //region Property "ScheduleNextState"

  /**
   * Slot for the {@code ScheduleNextState} property.
   * @see #getScheduleNextState
   * @see #setScheduleNextState
   */
  public static final Property ScheduleNextState = newProperty(Flags.SUMMARY, new BStatusTempSetpointCalcEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BScheduledStateEnum.DEFAULT.getRange()));

  /**
   * Get the {@code ScheduleNextState} property.
   * @see #ScheduleNextState
   */
  public BStatusTempSetpointCalcEnum getScheduleNextState() { return (BStatusTempSetpointCalcEnum)get(ScheduleNextState); }

  /**
   * Set the {@code ScheduleNextState} property.
   * @see #ScheduleNextState
   */
  public void setScheduleNextState(BStatusTempSetpointCalcEnum v) { set(ScheduleNextState, v, null); }

  //endregion Property "ScheduleNextState"

  //region Property "ScheduleTUNCOS"

  /**
   * Slot for the {@code ScheduleTUNCOS} property.
   * @see #getScheduleTUNCOS
   * @see #setScheduleTUNCOS
   */
  public static final Property ScheduleTUNCOS = newProperty(Flags.SUMMARY, new BHonStatusNumeric(11520F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(11520))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code ScheduleTUNCOS} property.
   * @see #ScheduleTUNCOS
   */
  public BHonStatusNumeric getScheduleTUNCOS() { return (BHonStatusNumeric)get(ScheduleTUNCOS); }

  /**
   * Set the {@code ScheduleTUNCOS} property.
   * @see #ScheduleTUNCOS
   */
  public void setScheduleTUNCOS(BHonStatusNumeric v) { set(ScheduleTUNCOS, v, null); }

  //endregion Property "ScheduleTUNCOS"

  //region Property "Setpoint"

  /**
   * Slot for the {@code Setpoint} property.
   * @see #getSetpoint
   * @see #setSetpoint
   */
  public static final Property Setpoint = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code Setpoint} property.
   * @see #Setpoint
   */
  public BHonStatusNumeric getSetpoint() { return (BHonStatusNumeric)get(Setpoint); }

  /**
   * Set the {@code Setpoint} property.
   * @see #Setpoint
   */
  public void setSetpoint(BHonStatusNumeric v) { set(Setpoint, v, null); }

  //endregion Property "Setpoint"

  //region Property "HeatRampRate"

  /**
   * Slot for the {@code HeatRampRate} property.
   * @see #getHeatRampRate
   * @see #setHeatRampRate
   */
  public static final Property HeatRampRate = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code HeatRampRate} property.
   * @see #HeatRampRate
   */
  public BHonStatusNumeric getHeatRampRate() { return (BHonStatusNumeric)get(HeatRampRate); }

  /**
   * Set the {@code HeatRampRate} property.
   * @see #HeatRampRate
   */
  public void setHeatRampRate(BHonStatusNumeric v) { set(HeatRampRate, v, null); }

  //endregion Property "HeatRampRate"

  //region Property "CoolRampRate"

  /**
   * Slot for the {@code CoolRampRate} property.
   * @see #getCoolRampRate
   * @see #setCoolRampRate
   */
  public static final Property CoolRampRate = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code CoolRampRate} property.
   * @see #CoolRampRate
   */
  public BHonStatusNumeric getCoolRampRate() { return (BHonStatusNumeric)get(CoolRampRate); }

  /**
   * Set the {@code CoolRampRate} property.
   * @see #CoolRampRate
   */
  public void setCoolRampRate(BHonStatusNumeric v) { set(CoolRampRate, v, null); }

  //endregion Property "CoolRampRate"

  //region Property "ManualOverrideState"

  /**
   * Slot for the {@code ManualOverrideState} property.
   * @see #getManualOverrideState
   * @see #setManualOverrideState
   */
  public static final Property ManualOverrideState = newProperty(Flags.SUMMARY, new BStatusTempSetpointCalcEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BOccupancyEnum.DEFAULT.getRange()));

  /**
   * Get the {@code ManualOverrideState} property.
   * @see #ManualOverrideState
   */
  public BStatusTempSetpointCalcEnum getManualOverrideState() { return (BStatusTempSetpointCalcEnum)get(ManualOverrideState); }

  /**
   * Set the {@code ManualOverrideState} property.
   * @see #ManualOverrideState
   */
  public void setManualOverrideState(BStatusTempSetpointCalcEnum v) { set(ManualOverrideState, v, null); }

  //endregion Property "ManualOverrideState"

  //region Property "occupiedCool"

  /**
   * Slot for the {@code occupiedCool} property.
   * @see #getOccupiedCool
   * @see #setOccupiedCool
   */
  public static final Property occupiedCool = newProperty(Flags.SUMMARY, new BHonStatusNumeric(75.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code occupiedCool} property.
   * @see #occupiedCool
   */
  public BHonStatusNumeric getOccupiedCool() { return (BHonStatusNumeric)get(occupiedCool); }

  /**
   * Set the {@code occupiedCool} property.
   * @see #occupiedCool
   */
  public void setOccupiedCool(BHonStatusNumeric v) { set(occupiedCool, v, null); }

  //endregion Property "occupiedCool"

  //region Property "standbyCool"

  /**
   * Slot for the {@code standbyCool} property.
   * @see #getStandbyCool
   * @see #setStandbyCool
   */
  public static final Property standbyCool = newProperty(Flags.SUMMARY, new BHonStatusNumeric(78.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code standbyCool} property.
   * @see #standbyCool
   */
  public BHonStatusNumeric getStandbyCool() { return (BHonStatusNumeric)get(standbyCool); }

  /**
   * Set the {@code standbyCool} property.
   * @see #standbyCool
   */
  public void setStandbyCool(BHonStatusNumeric v) { set(standbyCool, v, null); }

  //endregion Property "standbyCool"

  //region Property "unoccupiedCool"

  /**
   * Slot for the {@code unoccupiedCool} property.
   * @see #getUnoccupiedCool
   * @see #setUnoccupiedCool
   */
  public static final Property unoccupiedCool = newProperty(Flags.SUMMARY, new BHonStatusNumeric(85.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code unoccupiedCool} property.
   * @see #unoccupiedCool
   */
  public BHonStatusNumeric getUnoccupiedCool() { return (BHonStatusNumeric)get(unoccupiedCool); }

  /**
   * Set the {@code unoccupiedCool} property.
   * @see #unoccupiedCool
   */
  public void setUnoccupiedCool(BHonStatusNumeric v) { set(unoccupiedCool, v, null); }

  //endregion Property "unoccupiedCool"

  //region Property "occupiedHeat"

  /**
   * Slot for the {@code occupiedHeat} property.
   * @see #getOccupiedHeat
   * @see #setOccupiedHeat
   */
  public static final Property occupiedHeat = newProperty(Flags.SUMMARY, new BHonStatusNumeric(70.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code occupiedHeat} property.
   * @see #occupiedHeat
   */
  public BHonStatusNumeric getOccupiedHeat() { return (BHonStatusNumeric)get(occupiedHeat); }

  /**
   * Set the {@code occupiedHeat} property.
   * @see #occupiedHeat
   */
  public void setOccupiedHeat(BHonStatusNumeric v) { set(occupiedHeat, v, null); }

  //endregion Property "occupiedHeat"

  //region Property "standbyHeat"

  /**
   * Slot for the {@code standbyHeat} property.
   * @see #getStandbyHeat
   * @see #setStandbyHeat
   */
  public static final Property standbyHeat = newProperty(Flags.SUMMARY, new BHonStatusNumeric(67.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code standbyHeat} property.
   * @see #standbyHeat
   */
  public BHonStatusNumeric getStandbyHeat() { return (BHonStatusNumeric)get(standbyHeat); }

  /**
   * Set the {@code standbyHeat} property.
   * @see #standbyHeat
   */
  public void setStandbyHeat(BHonStatusNumeric v) { set(standbyHeat, v, null); }

  //endregion Property "standbyHeat"

  //region Property "unoccupiedHeat"

  /**
   * Slot for the {@code unoccupiedHeat} property.
   * @see #getUnoccupiedHeat
   * @see #setUnoccupiedHeat
   */
  public static final Property unoccupiedHeat = newProperty(Flags.SUMMARY, new BHonStatusNumeric(55.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code unoccupiedHeat} property.
   * @see #unoccupiedHeat
   */
  public BHonStatusNumeric getUnoccupiedHeat() { return (BHonStatusNumeric)get(unoccupiedHeat); }

  /**
   * Set the {@code unoccupiedHeat} property.
   * @see #unoccupiedHeat
   */
  public void setUnoccupiedHeat(BHonStatusNumeric v) { set(unoccupiedHeat, v, null); }

  //endregion Property "unoccupiedHeat"

  //region Property "SetpointType"

  /**
   * Slot for the {@code SetpointType} property.
   * Config slots
   * @see #getSetpointType
   * @see #setSetpointType
   */
  public static final Property SetpointType = newProperty(Flags.HIDDEN, BSetpointTypeEnum.DEFAULT, null);

  /**
   * Get the {@code SetpointType} property.
   * Config slots
   * @see #SetpointType
   */
  public BSetpointTypeEnum getSetpointType() { return (BSetpointTypeEnum)get(SetpointType); }

  /**
   * Set the {@code SetpointType} property.
   * Config slots
   * @see #SetpointType
   */
  public void setSetpointType(BSetpointTypeEnum v) { set(SetpointType, v, null); }

  //endregion Property "SetpointType"

  //region Property "EFF_HEAT_SETPT"

  /**
   * Slot for the {@code EFF_HEAT_SETPT} property.
   * Output slots
   * @see #getEFF_HEAT_SETPT
   * @see #setEFF_HEAT_SETPT
   */
  public static final Property EFF_HEAT_SETPT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.POSITIVE_INFINITY))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_HEAT_SETPT} property.
   * Output slots
   * @see #EFF_HEAT_SETPT
   */
  public BHonStatusNumeric getEFF_HEAT_SETPT() { return (BHonStatusNumeric)get(EFF_HEAT_SETPT); }

  /**
   * Set the {@code EFF_HEAT_SETPT} property.
   * Output slots
   * @see #EFF_HEAT_SETPT
   */
  public void setEFF_HEAT_SETPT(BHonStatusNumeric v) { set(EFF_HEAT_SETPT, v, null); }

  //endregion Property "EFF_HEAT_SETPT"

  //region Property "EFF_COOL_SETPT"

  /**
   * Slot for the {@code EFF_COOL_SETPT} property.
   * @see #getEFF_COOL_SETPT
   * @see #setEFF_COOL_SETPT
   */
  public static final Property EFF_COOL_SETPT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.POSITIVE_INFINITY))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_COOL_SETPT} property.
   * @see #EFF_COOL_SETPT
   */
  public BHonStatusNumeric getEFF_COOL_SETPT() { return (BHonStatusNumeric)get(EFF_COOL_SETPT); }

  /**
   * Set the {@code EFF_COOL_SETPT} property.
   * @see #EFF_COOL_SETPT
   */
  public void setEFF_COOL_SETPT(BHonStatusNumeric v) { set(EFF_COOL_SETPT, v, null); }

  //endregion Property "EFF_COOL_SETPT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTemperatureSetpointCalculator.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeTSC();
	}
	
	private void executeTSC() {
		Setpoint setpoints = getOffsetOrCenterSetpoints(getUserGivenSetpointValuesAfterValidation());
		BEffectiveOccupancyEnum effOccCurrentState = BEffectiveOccupancyEnum.make(getEffOccuCurrentStateAfterValidation());
		
		//Set the current setpoints based on the current occupancy mode
		switch (effOccCurrentState.getOrdinal()) {
		case BEffectiveOccupancyEnum.UNOCCUPIED:
			calculateAndSetSetpointsForUnoccupiedState(setpoints);
			break;

		case BEffectiveOccupancyEnum.STANDBY:
			getEFF_COOL_SETPT().setValue(setpoints.getStandbyCool());
			getEFF_HEAT_SETPT().setValue(setpoints.getStandbyHeat());
			break;
			
		case BEffectiveOccupancyEnum.BYPASS:	//Bypass and Occ are the same
		case BEffectiveOccupancyEnum.OCCUPIED: 
			///CLOVER:OFF
		default:	//FYI: current state is never Null as it is checked by converters
			///CLOVER:ON
			getEFF_COOL_SETPT().setValue(setpoints.getOccupiedCool());
			getEFF_HEAT_SETPT().setValue(setpoints.getOccupiedHeat());
			break;
		}
	}

	private Setpoint getOffsetOrCenterSetpoints(final Setpoint userGivenSetpoints) {
		double userSetpointValue = getUserSetpointAfterValidation();
		Setpoint setpoints = new Setpoint();
		
		// If the setpoint input is less than 10(OFFSET_LIMIT), this algorithm assumes it is an offset
		// setpoint and adds it to the programmed setpoints
		// If it is 10(OFFSET_LIMIT) or greater, then it is assumed to be a center (absolute)
		// setpoint. Half of the zeb(ZeroEnergyBand) is added and subtracted from it to get the
		// occ/standby setpoints
		if(userSetpointValue < OFFSET_LIMIT) {
			setpoints.setOccupiedCool(userGivenSetpoints.getOccupiedCool() + userSetpointValue);
			setpoints.setOccupiedHeat(userGivenSetpoints.getOccupiedHeat() + userSetpointValue);
			setpoints.setStandbyCool(userGivenSetpoints.getStandbyCool() + userSetpointValue);
			setpoints.setStandbyHeat(userGivenSetpoints.getStandbyHeat() + userSetpointValue);
		} else {
			double halfZeroEnergyBand = (userGivenSetpoints.getOccupiedCool() - userGivenSetpoints.getOccupiedHeat())/AVERAGE_OF_TWO;
			setpoints.setOccupiedCool(userSetpointValue + halfZeroEnergyBand);
			setpoints.setOccupiedHeat(userSetpointValue - halfZeroEnergyBand);
			
			halfZeroEnergyBand = (userGivenSetpoints.getStandbyCool() - userGivenSetpoints.getStandbyHeat())/AVERAGE_OF_TWO;
			setpoints.setStandbyCool(userSetpointValue + halfZeroEnergyBand);
			setpoints.setStandbyHeat(userSetpointValue - halfZeroEnergyBand);
		}
		
		setpoints.setUnoccupiedCool(userGivenSetpoints.getUnoccupiedCool());
		setpoints.setUnoccupiedHeat(userGivenSetpoints.getUnoccupiedHeat());
		return setpoints;
	}

	private void calculateAndSetSetpointsForUnoccupiedState(final Setpoint setpoints) {
		BOccupancyEnum manOverrideState = BOccupancyEnum.make(getManualOverrideStateAfterValidation());
		
		// Only recover setpoint when going from:
        //  Unoccupied to standby,
        //  Unoccupied to occupied.
        // (Do not do recovery if manual occupancy is in effect.)
		if(BOccupancyEnum.Null.equals(manOverrideState.getEnum())) {
			BScheduledStateEnum schedNextState = BScheduledStateEnum.make(getScheduleNextStateAfterValidation());
			
			//Check what our next state is. If it's Occ or Standby, then do recovery.
			switch(schedNextState.getOrdinal()) {
			case BScheduledStateEnum.OCCUPIED:
				//Set the target setpoints to either the occupied ones
				calculateAndSetSetpointsForRecovery(setpoints, setpoints.getOccupiedCool(), setpoints.getOccupiedHeat());
				break;
			case BScheduledStateEnum.STANDBY:
				//Set the target setpoints to either the standby ones
				calculateAndSetSetpointsForRecovery(setpoints, setpoints.getStandbyCool(), setpoints.getStandbyHeat());
				break;
			default:
				getEFF_COOL_SETPT().setValue(setpoints.getUnoccupiedCool());
				getEFF_HEAT_SETPT().setValue(setpoints.getUnoccupiedHeat());
				break;
			}
		} else {// manual override is in effect so don't recover the setpoints
			getEFF_COOL_SETPT().setValue(setpoints.getUnoccupiedCool());
			getEFF_HEAT_SETPT().setValue(setpoints.getUnoccupiedHeat());
		}
	}
	
	private void calculateAndSetSetpointsForRecovery(final Setpoint setpoints, final double targetClSP, final double targetHtSP) {
		double coolRampRate = getCoolRampRateAfterValidation();
		double heatRampRate = getHeatRampRateAfterValidation();
		double tuncosMesa = calculateTuncosMesa();
		double delta;
		double diff;
		
        // If the recovery rate is non zero, then calculate the delta:
        //  delta = tuncos(min) * rate(units/HR) / 60 (min/Hr)
        // If the recovery rate is zero then do a step change at the change of state.
        if (coolRampRate > 0) {
           delta = tuncosMesa * coolRampRate / UnitConstants.SIXTY_SECOND;

           // Insure the delta doesn't exceed the difference between
           //  the unoccupied and target setpoints.
           //Then add the delta to the target setpoint.
           diff = setpoints.getUnoccupiedCool() - targetClSP;
           if (delta > diff)
              delta = diff;
           getEFF_COOL_SETPT().setValue(targetClSP + delta);
        } else {
        	getEFF_COOL_SETPT().setValue(setpoints.getUnoccupiedCool());
        }

        //Do the same for heat, but subtract the delta from the target.
        if (heatRampRate > 0) {
           delta = tuncosMesa * heatRampRate / UnitConstants.SIXTY_SECOND;

           diff = targetHtSP - setpoints.getUnoccupiedHeat();
           if (delta > diff)
              delta = diff;
           getEFF_HEAT_SETPT().setValue(targetHtSP - delta);
        } else {
        	getEFF_HEAT_SETPT().setValue(setpoints.getUnoccupiedHeat());
        }
	}

	private double calculateTuncosMesa() {
		double schedTuncos;
		double tuncosMesa;
		if(isConfigured(ScheduleTUNCOS)) {
			schedTuncos = limitInput(getScheduleTUNCOS().getValue(), 0, MAX_TUNCOS, MAX_TUNCOS);
		} else {
			schedTuncos = MAX_TUNCOS;
		}
		
		//Subtract 10 minutes from the TUNCOS to make sure recovery makes it by the event time.
        //(A similar technique was done in the T7300 series 1000/2000 and T7350)
		if (schedTuncos > TUNCOS_MESA)
           tuncosMesa = schedTuncos - TUNCOS_MESA;
        else
           tuncosMesa = 0;
		return tuncosMesa;
	}
	
	
	//Input validation logic [starts here] 
	private Setpoint getUserGivenSetpointValuesAfterValidation() {
		Setpoint setpoints = new Setpoint();
		if(isSlotValueValid(occupiedCool)) {
			setpoints.setOccupiedCool(getOccupiedCool().getValue());
		} else {
			setpoints.setOccupiedCool(DEFAULT_OCCUPIED_COOL);
		}
		
		if(isSlotValueValid(standbyCool)) {
			setpoints.setStandbyCool(getStandbyCool().getValue());
		} else {
			setpoints.setStandbyCool(DEFAULT_STANDBY_COOL);
		}
		
		if(isSlotValueValid(unoccupiedCool)) {
			setpoints.setUnoccupiedCool(getUnoccupiedCool().getValue());
		} else {
			setpoints.setUnoccupiedCool(DEFAULT_UNOCCUPIED_COOL);
		}
		
		if(isSlotValueValid(occupiedHeat)) {
			setpoints.setOccupiedHeat(getOccupiedHeat().getValue());
		} else {
			setpoints.setOccupiedHeat(DEFAULT_OCCUPIED_HEAT);
		}
		
		if(isSlotValueValid(standbyHeat)) {
			setpoints.setStandbyHeat(getStandbyHeat().getValue());
		} else {
			setpoints.setStandbyHeat(DEFAULT_STANDBY_HEAT);
		}
		
		if(isSlotValueValid(unoccupiedHeat)) {
			setpoints.setUnoccupiedHeat(getUnoccupiedHeat().getValue());
		} else {
			setpoints.setUnoccupiedHeat(DEFAULT_UNOCCUPIED_HEAT);
		}
		
		return setpoints;
	}
	
	private double getCoolRampRateAfterValidation() {
		if(isSlotValueValid(CoolRampRate)) {
			return getCoolRampRate().getValue();
		}
		
		return 0.0;
	}
	
	private double getHeatRampRateAfterValidation() {
		if(isSlotValueValid(HeatRampRate)) {
			return getHeatRampRate().getValue();
		}
		
		return 0.0;
	}
	
	private double getUserSetpointAfterValidation() {
		if(isSlotValueValid(Setpoint)) {
			return  getSetpoint().getValue();
		} 
		return 0.0;
	}
	
	private int getEffOccuCurrentStateAfterValidation() {
		if(isConfigured(EffOccuCurrentState)) {
			return getEffOccuCurrentState().getEnum().getOrdinal();
		}
		
		return BEffectiveOccupancyEnum.OCCUPIED;
	}
	
	private int getManualOverrideStateAfterValidation() {
		if(isConfigured(ManualOverrideState)) {
			return getManualOverrideState().getEnum().getOrdinal();
		}
		
		return BOccupancyEnum.NULL;
	}
	
	private int getScheduleNextStateAfterValidation() {
		if(isConfigured(ScheduleNextState)) {
			return getScheduleNextState().getEnum().getOrdinal();
		}
		
		return BScheduledStateEnum.NULL;
	}
	//Input validation logic [ends here]

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(EffOccuCurrentState);
		propertyArrayList.add(ScheduleNextState);
		propertyArrayList.add(ScheduleTUNCOS);
		propertyArrayList.add(Setpoint);
		propertyArrayList.add(HeatRampRate);
		propertyArrayList.add(CoolRampRate);
		propertyArrayList.add(ManualOverrideState);
		propertyArrayList.add(occupiedCool);
		propertyArrayList.add(occupiedHeat);
		propertyArrayList.add(standbyCool);
		propertyArrayList.add(standbyHeat);
		propertyArrayList.add(unoccupiedCool);
		propertyArrayList.add(unoccupiedHeat);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(EFF_COOL_SETPT);
		propertyArrayList.add(EFF_HEAT_SETPT);
		return propertyArrayList;
	}
	
	/**
	 * Values less than this are assumed to be offset (relative) setpoints
	 */
	private static final int OFFSET_LIMIT	= 10;
	/**
	 * Maximum TUNCOS allowed by algorithm (8 days)
	 */
	private static final int MAX_TUNCOS		= 11520;
	/**
	 * Amount of time in minutes TUNCOS zeroes out before the event time. (It was
	 * named the "Smith Mesa" by Jeff Meyer after Gary Smith who put implemented it
	 * In the T7300 series 1000. It was added to insure the HVAC equipment gets the
	 * space temperature up to setpoint by the occupied time.)
	 */
	private static final int TUNCOS_MESA	= 10;
	
	private static final int AVERAGE_OF_TWO = 2;
	
	private static final int DEFAULT_OCCUPIED_COOL		= 75;
	private static final int DEFAULT_STANDBY_COOL		= 78;
	private static final int DEFAULT_UNOCCUPIED_COOL	= 85;
	private static final int DEFAULT_OCCUPIED_HEAT		= 70;
	private static final int DEFAULT_STANDBY_HEAT		= 67;
	private static final int DEFAULT_UNOCCUPIED_HEAT	= 55;
}

class Setpoint{
	private double occupiedCool;
	private double standbyCool;
	private double unoccupiedCool;
	private double occupiedHeat;
	private double standbyHeat;
	private double unoccupiedHeat;

	public double getOccupiedCool() {
		return occupiedCool;
	}

	public void setOccupiedCool(double occupiedCool) {
		this.occupiedCool = occupiedCool;
	}

	public double getStandbyCool() {
		return standbyCool;
	}

	public void setStandbyCool(double standbyCool) {
		this.standbyCool = standbyCool;
	}

	public double getUnoccupiedCool() {
		return unoccupiedCool;
	}

	public void setUnoccupiedCool(double unoccupiedCool) {
		this.unoccupiedCool = unoccupiedCool;
	}

	public double getOccupiedHeat() {
		return occupiedHeat;
	}

	public void setOccupiedHeat(double occupiedHeat) {
		this.occupiedHeat = occupiedHeat;
	}

	public double getStandbyHeat() {
		return standbyHeat;
	}

	public void setStandbyHeat(double standbyHeat) {
		this.standbyHeat = standbyHeat;
	}

	public double getUnoccupiedHeat() {
		return unoccupiedHeat;
	}

	public void setUnoccupiedHeat(double unoccupiedHeat) {
		this.unoccupiedHeat = unoccupiedHeat;
	}
	
}
