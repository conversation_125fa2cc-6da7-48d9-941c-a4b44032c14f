/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.zonecontrol.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation Set Temperature Mode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Aug 30, 2019
 */


@NiagaraType
@NiagaraEnum (
	range = {
			@Range(value="legacy", ordinal=0),
			@Range(value="enhanced", ordinal=1)
	},
	defaultValue="legacy"
)

@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})
public final class BBehaviorTypeEnum  extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BBehaviorTypeEnum(3238973548)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for legacy. */
  public static final int LEGACY = 0;
  /** Ordinal value for enhanced. */
  public static final int ENHANCED = 1;

  /** BBehaviorTypeEnum constant for legacy. */
  public static final BBehaviorTypeEnum legacy = new BBehaviorTypeEnum(LEGACY);
  /** BBehaviorTypeEnum constant for enhanced. */
  public static final BBehaviorTypeEnum enhanced = new BBehaviorTypeEnum(ENHANCED);

  /** Factory method with ordinal. */
  public static BBehaviorTypeEnum make(int ordinal)
  {
    return (BBehaviorTypeEnum)legacy.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BBehaviorTypeEnum make(String tag)
  {
    return (BBehaviorTypeEnum)legacy.getRange().get(tag);
  }

  /** Private constructor. */
  private BBehaviorTypeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BBehaviorTypeEnum DEFAULT = legacy;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BBehaviorTypeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
