/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of OccupancyArbitrator block implementation
 * This ENUM is used for occSensorOper config slot of OccupancyArbitrator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range("ConferenceRoom"), 
		@Range("UnoccupiedCleaningCrew"),
		@Range("UnoccupiedTenant")
		}, defaultValue = "ConferenceRoom")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BOccupancySensorOperationEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonearbitrator.BOccupancySensorOperationEnum(3693212089)1.0$ @*/
/* Generated Fri Feb 02 12:27:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for ConferenceRoom. */
  public static final int CONFERENCE_ROOM = 0;
  /** Ordinal value for UnoccupiedCleaningCrew. */
  public static final int UNOCCUPIED_CLEANING_CREW = 1;
  /** Ordinal value for UnoccupiedTenant. */
  public static final int UNOCCUPIED_TENANT = 2;
  
  /** BOccupancySensorOperationEnum constant for ConferenceRoom. */
  public static final BOccupancySensorOperationEnum ConferenceRoom = new BOccupancySensorOperationEnum(CONFERENCE_ROOM);
  /** BOccupancySensorOperationEnum constant for UnoccupiedCleaningCrew. */
  public static final BOccupancySensorOperationEnum UnoccupiedCleaningCrew = new BOccupancySensorOperationEnum(UNOCCUPIED_CLEANING_CREW);
  /** BOccupancySensorOperationEnum constant for UnoccupiedTenant. */
  public static final BOccupancySensorOperationEnum UnoccupiedTenant = new BOccupancySensorOperationEnum(UNOCCUPIED_TENANT);
  
  /** Factory method with ordinal. */
  public static BOccupancySensorOperationEnum make(int ordinal)
  {
    return (BOccupancySensorOperationEnum)ConferenceRoom.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BOccupancySensorOperationEnum make(String tag)
  {
    return (BOccupancySensorOperationEnum)ConferenceRoom.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BOccupancySensorOperationEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BOccupancySensorOperationEnum DEFAULT = ConferenceRoom;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOccupancySensorOperationEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
