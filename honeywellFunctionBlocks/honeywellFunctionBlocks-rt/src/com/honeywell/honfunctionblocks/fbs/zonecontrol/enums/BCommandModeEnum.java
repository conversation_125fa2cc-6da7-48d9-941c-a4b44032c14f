/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation Set Temperature Mode  of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 7, 2018
 */

@NiagaraType
@NiagaraEnum (
	range = {
			@Range(value="Cmd_Auto_Mode", ordinal=0),
			@Range(value="Cmd_Heat_Mode", ordinal=1),
			@Range(value="Cmd_Cool_Mode", ordinal=2),
			@Range(value="Cmd_Off_Mode", ordinal=3),
			@Range(value="Cmd_Emerg_Heat_Mode", ordinal=4),
			@Range(value="Cmd_Eff_Heat_Mode", ordinal=5),
			@Range(value="Cmd_Nul_Mode", ordinal=255)
	},
	defaultValue="Cmd_Auto_Mode"
)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public final class BCommandModeEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum(860330974)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Cmd_Auto_Mode. */
  public static final int CMD_AUTO_MODE = 0;
  /** Ordinal value for Cmd_Heat_Mode. */
  public static final int CMD_HEAT_MODE = 1;
  /** Ordinal value for Cmd_Cool_Mode. */
  public static final int CMD_COOL_MODE = 2;
  /** Ordinal value for Cmd_Off_Mode. */
  public static final int CMD_OFF_MODE = 3;
  /** Ordinal value for Cmd_Emerg_Heat_Mode. */
  public static final int CMD_EMERG_HEAT_MODE = 4;
  /** Ordinal value for Cmd_Eff_Heat_Mode. */
  public static final int CMD_EFF_HEAT_MODE = 5;
  /** Ordinal value for Cmd_Nul_Mode. */
  public static final int CMD_NUL_MODE = 255;

  /** BCommandModeEnum constant for Cmd_Auto_Mode. */
  public static final BCommandModeEnum Cmd_Auto_Mode = new BCommandModeEnum(CMD_AUTO_MODE);
  /** BCommandModeEnum constant for Cmd_Heat_Mode. */
  public static final BCommandModeEnum Cmd_Heat_Mode = new BCommandModeEnum(CMD_HEAT_MODE);
  /** BCommandModeEnum constant for Cmd_Cool_Mode. */
  public static final BCommandModeEnum Cmd_Cool_Mode = new BCommandModeEnum(CMD_COOL_MODE);
  /** BCommandModeEnum constant for Cmd_Off_Mode. */
  public static final BCommandModeEnum Cmd_Off_Mode = new BCommandModeEnum(CMD_OFF_MODE);
  /** BCommandModeEnum constant for Cmd_Emerg_Heat_Mode. */
  public static final BCommandModeEnum Cmd_Emerg_Heat_Mode = new BCommandModeEnum(CMD_EMERG_HEAT_MODE);
  /** BCommandModeEnum constant for Cmd_Eff_Heat_Mode. */
  public static final BCommandModeEnum Cmd_Eff_Heat_Mode = new BCommandModeEnum(CMD_EFF_HEAT_MODE);
  /** BCommandModeEnum constant for Cmd_Nul_Mode. */
  public static final BCommandModeEnum Cmd_Nul_Mode = new BCommandModeEnum(CMD_NUL_MODE);

  /** Factory method with ordinal. */
  public static BCommandModeEnum make(int ordinal)
  {
    return (BCommandModeEnum)Cmd_Auto_Mode.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BCommandModeEnum make(String tag)
  {
    return (BCommandModeEnum)Cmd_Auto_Mode.getRange().get(tag);
  }

  /** Private constructor. */
  private BCommandModeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BCommandModeEnum DEFAULT = Cmd_Auto_Mode;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCommandModeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
