/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of OccupancyArbitrator block implementation
 * This ENUM is used for ScheduledCurrentState slot of OccupancyArbitrator as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range(value="Occupied", ordinal=0), 
		@Range(value="Unoccupied", ordinal=1),
		@Range(value="Standby", ordinal=3),
		@Range(value="Null", ordinal=255)
		}, defaultValue = "Null")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BScheduledStateEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonearbitrator.BScheduledStateEnum(1503234298)1.0$ @*/
/* Generated Fri Feb 02 12:27:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for Occupied. */
  public static final int OCCUPIED = 0;
  /** Ordinal value for Unoccupied. */
  public static final int UNOCCUPIED = 1;
  /** Ordinal value for Standby. */
  public static final int STANDBY = 3;
  /** Ordinal value for Null. */
  public static final int NULL = 255;
  
  /** BScheduledStateEnum constant for Occupied. */
  public static final BScheduledStateEnum Occupied = new BScheduledStateEnum(OCCUPIED);
  /** BScheduledStateEnum constant for Unoccupied. */
  public static final BScheduledStateEnum Unoccupied = new BScheduledStateEnum(UNOCCUPIED);
  /** BScheduledStateEnum constant for Standby. */
  public static final BScheduledStateEnum Standby = new BScheduledStateEnum(STANDBY);
  /** BScheduledStateEnum constant for Null. */
  public static final BScheduledStateEnum Null = new BScheduledStateEnum(NULL);
  
  /** Factory method with ordinal. */
  public static BScheduledStateEnum make(int ordinal)
  {
    return (BScheduledStateEnum)Occupied.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BScheduledStateEnum make(String tag)
  {
    return (BScheduledStateEnum)Occupied.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BScheduledStateEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BScheduledStateEnum DEFAULT = Null;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BScheduledStateEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/}
