/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;
import com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Implementation of OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 31, 2018
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"occupancy_arbitrator.png\")", flags=Flags.HIDDEN|Flags.READONLY)

//Input slots
@NiagaraProperty(name="scheduleCurrentState", type="BStatusOccupancyStateEnum", defaultValue="new BStatusOccupancyStateEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BScheduledStateEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="WMOverride", type="BStatusOccupancyStateEnum", defaultValue="new BStatusOccupancyStateEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BOccupancyEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="NetworkManOcc", type="BStatusOccupancyStateEnum", defaultValue="new BStatusOccupancyStateEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BOccupancyEnum.DEFAULT.getRange()") }
	)
@NiagaraProperty(name="OccSensorState", type="BStatusOccupancyStateEnum", defaultValue="new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BOccupancySensorStateEnum.DEFAULT.getRange()") }
	)

//Config slots
@NiagaraProperty(name="netLastInWins", type="BNetworkLastInWinsEnum", defaultValue="BNetworkLastInWinsEnum.DEFAULT")
@NiagaraProperty(name="occSensorOper", type="BOccupancySensorOperationEnum", defaultValue="BOccupancySensorOperationEnum.DEFAULT")

//Output slots
@NiagaraProperty(name="EFF_OCC_CURRENT_STATE", type="BHonStatusEnum", defaultValue="new BHonStatusEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.ok)", flags=Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="MANUAL_OVERRIDE_STATE", type="BHonStatusEnum", defaultValue="new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")
})

//LoopStaticVariables
@NiagaraProperty(name="prevNetworkManOccState", type="BOccupancyEnum", defaultValue="BOccupancyEnum.DEFAULT", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="prevWMOverrideState", type="BOccupancyEnum", defaultValue="BOccupancyEnum.DEFAULT", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103", "squid:S00100"})
public class BOccupancyArbitrator extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancyArbitrator(2966680464)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "occupancy_arbitrator.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "scheduleCurrentState"

  /**
   * Slot for the {@code scheduleCurrentState} property.
   * Input slots
   * @see #getScheduleCurrentState
   * @see #setScheduleCurrentState
   */
  public static final Property scheduleCurrentState = newProperty(Flags.SUMMARY, new BStatusOccupancyStateEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BScheduledStateEnum.DEFAULT.getRange()));

  /**
   * Get the {@code scheduleCurrentState} property.
   * Input slots
   * @see #scheduleCurrentState
   */
  public BStatusOccupancyStateEnum getScheduleCurrentState() { return (BStatusOccupancyStateEnum)get(scheduleCurrentState); }

  /**
   * Set the {@code scheduleCurrentState} property.
   * Input slots
   * @see #scheduleCurrentState
   */
  public void setScheduleCurrentState(BStatusOccupancyStateEnum v) { set(scheduleCurrentState, v, null); }

  //endregion Property "scheduleCurrentState"

  //region Property "WMOverride"

  /**
   * Slot for the {@code WMOverride} property.
   * @see #getWMOverride
   * @see #setWMOverride
   */
  public static final Property WMOverride = newProperty(Flags.SUMMARY, new BStatusOccupancyStateEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BOccupancyEnum.DEFAULT.getRange()));

  /**
   * Get the {@code WMOverride} property.
   * @see #WMOverride
   */
  public BStatusOccupancyStateEnum getWMOverride() { return (BStatusOccupancyStateEnum)get(WMOverride); }

  /**
   * Set the {@code WMOverride} property.
   * @see #WMOverride
   */
  public void setWMOverride(BStatusOccupancyStateEnum v) { set(WMOverride, v, null); }

  //endregion Property "WMOverride"

  //region Property "NetworkManOcc"

  /**
   * Slot for the {@code NetworkManOcc} property.
   * @see #getNetworkManOcc
   * @see #setNetworkManOcc
   */
  public static final Property NetworkManOcc = newProperty(Flags.SUMMARY, new BStatusOccupancyStateEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BOccupancyEnum.DEFAULT.getRange()));

  /**
   * Get the {@code NetworkManOcc} property.
   * @see #NetworkManOcc
   */
  public BStatusOccupancyStateEnum getNetworkManOcc() { return (BStatusOccupancyStateEnum)get(NetworkManOcc); }

  /**
   * Set the {@code NetworkManOcc} property.
   * @see #NetworkManOcc
   */
  public void setNetworkManOcc(BStatusOccupancyStateEnum v) { set(NetworkManOcc, v, null); }

  //endregion Property "NetworkManOcc"

  //region Property "OccSensorState"

  /**
   * Slot for the {@code OccSensorState} property.
   * @see #getOccSensorState
   * @see #setOccSensorState
   */
  public static final Property OccSensorState = newProperty(Flags.SUMMARY, new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BOccupancySensorStateEnum.DEFAULT.getRange()));

  /**
   * Get the {@code OccSensorState} property.
   * @see #OccSensorState
   */
  public BStatusOccupancyStateEnum getOccSensorState() { return (BStatusOccupancyStateEnum)get(OccSensorState); }

  /**
   * Set the {@code OccSensorState} property.
   * @see #OccSensorState
   */
  public void setOccSensorState(BStatusOccupancyStateEnum v) { set(OccSensorState, v, null); }

  //endregion Property "OccSensorState"

  //region Property "netLastInWins"

  /**
   * Slot for the {@code netLastInWins} property.
   * Config slots
   * @see #getNetLastInWins
   * @see #setNetLastInWins
   */
  public static final Property netLastInWins = newProperty(0, BNetworkLastInWinsEnum.DEFAULT, null);

  /**
   * Get the {@code netLastInWins} property.
   * Config slots
   * @see #netLastInWins
   */
  public BNetworkLastInWinsEnum getNetLastInWins() { return (BNetworkLastInWinsEnum)get(netLastInWins); }

  /**
   * Set the {@code netLastInWins} property.
   * Config slots
   * @see #netLastInWins
   */
  public void setNetLastInWins(BNetworkLastInWinsEnum v) { set(netLastInWins, v, null); }

  //endregion Property "netLastInWins"

  //region Property "occSensorOper"

  /**
   * Slot for the {@code occSensorOper} property.
   * @see #getOccSensorOper
   * @see #setOccSensorOper
   */
  public static final Property occSensorOper = newProperty(0, BOccupancySensorOperationEnum.DEFAULT, null);

  /**
   * Get the {@code occSensorOper} property.
   * @see #occSensorOper
   */
  public BOccupancySensorOperationEnum getOccSensorOper() { return (BOccupancySensorOperationEnum)get(occSensorOper); }

  /**
   * Set the {@code occSensorOper} property.
   * @see #occSensorOper
   */
  public void setOccSensorOper(BOccupancySensorOperationEnum v) { set(occSensorOper, v, null); }

  //endregion Property "occSensorOper"

  //region Property "EFF_OCC_CURRENT_STATE"

  /**
   * Slot for the {@code EFF_OCC_CURRENT_STATE} property.
   * Output slots
   * @see #getEFF_OCC_CURRENT_STATE
   * @see #setEFF_OCC_CURRENT_STATE
   */
  public static final Property EFF_OCC_CURRENT_STATE = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_OCC_CURRENT_STATE} property.
   * Output slots
   * @see #EFF_OCC_CURRENT_STATE
   */
  public BHonStatusEnum getEFF_OCC_CURRENT_STATE() { return (BHonStatusEnum)get(EFF_OCC_CURRENT_STATE); }

  /**
   * Set the {@code EFF_OCC_CURRENT_STATE} property.
   * Output slots
   * @see #EFF_OCC_CURRENT_STATE
   */
  public void setEFF_OCC_CURRENT_STATE(BHonStatusEnum v) { set(EFF_OCC_CURRENT_STATE, v, null); }

  //endregion Property "EFF_OCC_CURRENT_STATE"

  //region Property "MANUAL_OVERRIDE_STATE"

  /**
   * Slot for the {@code MANUAL_OVERRIDE_STATE} property.
   * @see #getMANUAL_OVERRIDE_STATE
   * @see #setMANUAL_OVERRIDE_STATE
   */
  public static final Property MANUAL_OVERRIDE_STATE = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusEnum(BOccupancyEnum.DEFAULT, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE)));

  /**
   * Get the {@code MANUAL_OVERRIDE_STATE} property.
   * @see #MANUAL_OVERRIDE_STATE
   */
  public BHonStatusEnum getMANUAL_OVERRIDE_STATE() { return (BHonStatusEnum)get(MANUAL_OVERRIDE_STATE); }

  /**
   * Set the {@code MANUAL_OVERRIDE_STATE} property.
   * @see #MANUAL_OVERRIDE_STATE
   */
  public void setMANUAL_OVERRIDE_STATE(BHonStatusEnum v) { set(MANUAL_OVERRIDE_STATE, v, null); }

  //endregion Property "MANUAL_OVERRIDE_STATE"

  //region Property "prevNetworkManOccState"

  /**
   * Slot for the {@code prevNetworkManOccState} property.
   * LoopStaticVariables
   * @see #getPrevNetworkManOccState
   * @see #setPrevNetworkManOccState
   */
  public static final Property prevNetworkManOccState = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, BOccupancyEnum.DEFAULT, null);

  /**
   * Get the {@code prevNetworkManOccState} property.
   * LoopStaticVariables
   * @see #prevNetworkManOccState
   */
  public BOccupancyEnum getPrevNetworkManOccState() { return (BOccupancyEnum)get(prevNetworkManOccState); }

  /**
   * Set the {@code prevNetworkManOccState} property.
   * LoopStaticVariables
   * @see #prevNetworkManOccState
   */
  public void setPrevNetworkManOccState(BOccupancyEnum v) { set(prevNetworkManOccState, v, null); }

  //endregion Property "prevNetworkManOccState"

  //region Property "prevWMOverrideState"

  /**
   * Slot for the {@code prevWMOverrideState} property.
   * @see #getPrevWMOverrideState
   * @see #setPrevWMOverrideState
   */
  public static final Property prevWMOverrideState = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, BOccupancyEnum.DEFAULT, null);

  /**
   * Get the {@code prevWMOverrideState} property.
   * @see #prevWMOverrideState
   */
  public BOccupancyEnum getPrevWMOverrideState() { return (BOccupancyEnum)get(prevWMOverrideState); }

  /**
   * Set the {@code prevWMOverrideState} property.
   * @see #prevWMOverrideState
   */
  public void setPrevWMOverrideState(BOccupancyEnum v) { set(prevWMOverrideState, v, null); }

  //endregion Property "prevWMOverrideState"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOccupancyArbitrator.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		
		setPrevNetworkManOccState(BOccupancyEnum.Null);
		setPrevWMOverrideState(BOccupancyEnum.Null);
		manualOverrideState = BOccupancyEnum.Null;
	}
  
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeOccArbitrator();
	}
	
	private void executeOccArbitrator() {
		manualOverrideState = BOccupancyEnum.make(getMANUAL_OVERRIDE_STATE().getEnum().getOrdinal());
		
		computeManualOverrideState();		
		BEffectiveOccupancyEnum effOccEnum = BEffectiveOccupancyEnum.make(computeEffectiveOccupancyState().getOrdinal());
		
		getMANUAL_OVERRIDE_STATE().setValue(manualOverrideState);
		getEFF_OCC_CURRENT_STATE().setValue(effOccEnum);		
	}
	
	private BEffectiveOccupancyEnum computeEffectiveOccupancyState() {
		BEffectiveOccupancyEnum effOccEnum;
		
		BScheduledStateEnum schedCurrentState = BScheduledStateEnum.make(getScheduleCurrentStateValue());
		BOccupancySensorStateEnum occupancySensorState = BOccupancySensorStateEnum.make(getOccSensorStateValue());
		
		// Combine the manual override, schedule, and occupancy sensor per the SDS.
		// Manual override has priority
		switch(manualOverrideState.getOrdinal()) {
			case BOccupancyEnum.OCCUPIED:			
			case BOccupancyEnum.STANDBY:
			case BOccupancyEnum.UNOCCUPIED:
				effOccEnum = BEffectiveOccupancyEnum.make(manualOverrideState.getOrdinal());
				break;

			case BOccupancyEnum.BYPASS:
				effOccEnum = BEffectiveOccupancyEnum.make(getEffOccWhenManualOccIsBypass(schedCurrentState,occupancySensorState).getOrdinal());
				break;

			case BOccupancyEnum.NULL:
				///CLOVER:OFF
			default:
				///CLOVER:ON
				effOccEnum = BEffectiveOccupancyEnum.make(getEffOccBasedOnSchedule(schedCurrentState,occupancySensorState).getOrdinal());
		}
		
		return effOccEnum;
	}
	
	
	private void computeManualOverrideState() {		
		BOccupancyEnum wmOverrideState = BOccupancyEnum.make(getWMOverrideValue());
		BOccupancyEnum netManOcc = BOccupancyEnum.make(getNetworkManOccVal());	
		
		if(getNetLastInWins().equals(BNetworkLastInWinsEnum.NetworkWins)) {					
			if(BOccupancyEnum.Null.equals(netManOcc))
				manualOverrideState = wmOverrideState;
			else
				manualOverrideState = netManOcc;
		}else {
			if(!(netManOcc.equals(getPrevNetworkManOccState()))) {
				setPrevNetworkManOccState(netManOcc);
				manualOverrideState = netManOcc;
			}else if(!(wmOverrideState.equals(getPrevWMOverrideState()))) {
				setPrevWMOverrideState(wmOverrideState);
				manualOverrideState = wmOverrideState;
			}
		}
	}
	
	private BEffectiveOccupancyEnum getEffOccWhenManualOccIsBypass(BScheduledStateEnum schedCurrentState,BOccupancySensorStateEnum occupancySensorState) {
		BEffectiveOccupancyEnum effOccEnum;
		if(BScheduledStateEnum.Standby.equals(schedCurrentState) || BScheduledStateEnum.Unoccupied.equals(schedCurrentState) ||
			(BScheduledStateEnum.Null.equals(schedCurrentState) && BOccupancySensorStateEnum.Unoccupied.equals(occupancySensorState)))
				effOccEnum = BEffectiveOccupancyEnum.Bypass;
			else
				effOccEnum = BEffectiveOccupancyEnum.Occupied;
		return effOccEnum;
	}
	
	private BEffectiveOccupancyEnum getEffOccBasedOnSchedule(BScheduledStateEnum schedCurrentState,BOccupancySensorStateEnum occupancySensorState) {
		BEffectiveOccupancyEnum effOccEnum;		
		if(BScheduledStateEnum.Standby.equals(schedCurrentState))
			effOccEnum = BEffectiveOccupancyEnum.Standby;
		else if(BScheduledStateEnum.Occupied.equals(schedCurrentState)) {
			if(BOccupancySensorStateEnum.Unoccupied.equals(occupancySensorState))
				effOccEnum = BEffectiveOccupancyEnum.Standby;
			else
				effOccEnum = BEffectiveOccupancyEnum.Occupied;
		}else if(BScheduledStateEnum.Unoccupied.equals(schedCurrentState)) {
			if(BOccupancySensorStateEnum.Occupied.equals(occupancySensorState)) {
				effOccEnum = getEffOccEnumBasedOnOccSensorOperation();
			}else {
				effOccEnum = BEffectiveOccupancyEnum.Unoccupied;
			}
		}else if(getOccSensorStateValue()==BOccupancySensorStateEnum.UNOCCUPIED) { 
			effOccEnum = BEffectiveOccupancyEnum.Unoccupied;			
		}else {
			effOccEnum = BEffectiveOccupancyEnum.Occupied;
		}
			
		return effOccEnum;
	}	
	
	
	private BEffectiveOccupancyEnum getEffOccEnumBasedOnOccSensorOperation() {
		BEffectiveOccupancyEnum effOccEnum;
		BOccupancySensorOperationEnum occSensorOperationEnum = getOccSensorOper();
		switch(occSensorOperationEnum.getOrdinal()) {
			case BOccupancySensorOperationEnum.CONFERENCE_ROOM:
				effOccEnum =  BEffectiveOccupancyEnum.Unoccupied;
				break;
				
			case BOccupancySensorOperationEnum.UNOCCUPIED_CLEANING_CREW:
				effOccEnum =  BEffectiveOccupancyEnum.Standby;
				break;
				
			case BOccupancySensorOperationEnum.UNOCCUPIED_TENANT:
				///CLOVER:OFF				
			default:
				///CLOVER:ON
				effOccEnum = BEffectiveOccupancyEnum.Occupied;
				break;				
		}
		
		return effOccEnum;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(scheduleCurrentState);
		propertyArrayList.add(WMOverride);
		propertyArrayList.add(NetworkManOcc);
		propertyArrayList.add(OccSensorState);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(EFF_OCC_CURRENT_STATE);
		properties.add(MANUAL_OVERRIDE_STATE);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(netLastInWins);
		properties.add(occSensorOper);
		return properties;
	}
	
	private int getWMOverrideValue() {		
		if(!isConfigured(WMOverride))
			return BOccupancyEnum.NULL;
		
		return getWMOverride().getEnum().getOrdinal();		
	}
	
	private int getScheduleCurrentStateValue() {		
		if(!isConfigured(scheduleCurrentState))
			return BScheduledStateEnum.NULL;
		
		return getScheduleCurrentState().getEnum().getOrdinal();		
	}
	
	private int getNetworkManOccVal() {		
		if(!isConfigured(NetworkManOcc))
			return BOccupancyEnum.NULL;
		
		return getNetworkManOcc().getEnum().getOrdinal();		
	}
	
	private int getOccSensorStateValue() {		
		if(!isConfigured(OccSensorState))
			return BOccupancySensorStateEnum.NULL;
		
		return getOccSensorState().getEnum().getOrdinal();		
	}

	private BOccupancyEnum manualOverrideState;	
}
