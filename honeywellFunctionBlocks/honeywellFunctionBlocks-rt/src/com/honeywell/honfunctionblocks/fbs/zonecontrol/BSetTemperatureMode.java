/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BBehaviorTypeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BControlTypeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BEffTempModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BStatusSetTemperatureModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;

/**
 * Implementation SetTemperatureMode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 7, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"set_temperature_mode.png\")", flags=Flags.HIDDEN|Flags.READONLY)

//Input slots
@NiagaraProperty(name="sysSwitch", type="BStatusSetTemperatureModeEnum", defaultValue="new BStatusSetTemperatureModeEnum(BSystemSwitchEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BSystemSwitchEnum.DEFAULT.getRange()") }
	)

@NiagaraProperty(name="cmdMode", type="BStatusSetTemperatureModeEnum", defaultValue="new BStatusSetTemperatureModeEnum(BCommandModeEnum.DEFAULT, BStatus.nullStatus)", flags=Flags.SUMMARY,
		facets = { @Facet(name="BFacets.RANGE", value="BCommandModeEnum.DEFAULT.getRange()") }
	)

@NiagaraProperty(name = "supplyTemp", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "spaceTemp", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "effHeatSP", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "effCoolSP", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "allowAutoChange", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(true, BStatus.ok)", flags = Flags.SUMMARY)

//Config slots
@NiagaraProperty(name = "controlType", type = "BControlTypeEnum", defaultValue = "BControlTypeEnum.DEFAULT")


@NiagaraProperty(name = "behaviorType", type = "BBehaviorTypeEnum", defaultValue = "BBehaviorTypeEnum.DEFAULT")

//Output slots
@NiagaraProperty(name = "EFF_SETPT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })
@NiagaraProperty(name = "EFF_TEMP_MODE", type = "BStatusSetTemperatureModeEnum", defaultValue = "new BStatusSetTemperatureModeEnum(BEffTempModeEnum.DEFAULT, BStatus.nullStatus)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")
})

//LoopStaticVariables
@NiagaraProperty(name="prevModeArb", type="BCommandModeEnum", defaultValue="BCommandModeEnum.DEFAULT", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103", "squid:S00100", "squid:S6208"})
public class BSetTemperatureMode extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetTemperatureMode(245993181)1.0$ @*/
/* Generated Mon Aug 25 22:21:35 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "set_temperature_mode.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "sysSwitch"

  /**
   * Slot for the {@code sysSwitch} property.
   * Input slots
   * @see #getSysSwitch
   * @see #setSysSwitch
   */
  public static final Property sysSwitch = newProperty(Flags.SUMMARY, new BStatusSetTemperatureModeEnum(BSystemSwitchEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BSystemSwitchEnum.DEFAULT.getRange()));

  /**
   * Get the {@code sysSwitch} property.
   * Input slots
   * @see #sysSwitch
   */
  public BStatusSetTemperatureModeEnum getSysSwitch() { return (BStatusSetTemperatureModeEnum)get(sysSwitch); }

  /**
   * Set the {@code sysSwitch} property.
   * Input slots
   * @see #sysSwitch
   */
  public void setSysSwitch(BStatusSetTemperatureModeEnum v) { set(sysSwitch, v, null); }

  //endregion Property "sysSwitch"

  //region Property "cmdMode"

  /**
   * Slot for the {@code cmdMode} property.
   * @see #getCmdMode
   * @see #setCmdMode
   */
  public static final Property cmdMode = newProperty(Flags.SUMMARY, new BStatusSetTemperatureModeEnum(BCommandModeEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.RANGE, BCommandModeEnum.DEFAULT.getRange()));

  /**
   * Get the {@code cmdMode} property.
   * @see #cmdMode
   */
  public BStatusSetTemperatureModeEnum getCmdMode() { return (BStatusSetTemperatureModeEnum)get(cmdMode); }

  /**
   * Set the {@code cmdMode} property.
   * @see #cmdMode
   */
  public void setCmdMode(BStatusSetTemperatureModeEnum v) { set(cmdMode, v, null); }

  //endregion Property "cmdMode"

  //region Property "supplyTemp"

  /**
   * Slot for the {@code supplyTemp} property.
   * @see #getSupplyTemp
   * @see #setSupplyTemp
   */
  public static final Property supplyTemp = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code supplyTemp} property.
   * @see #supplyTemp
   */
  public BHonStatusNumeric getSupplyTemp() { return (BHonStatusNumeric)get(supplyTemp); }

  /**
   * Set the {@code supplyTemp} property.
   * @see #supplyTemp
   */
  public void setSupplyTemp(BHonStatusNumeric v) { set(supplyTemp, v, null); }

  //endregion Property "supplyTemp"

  //region Property "spaceTemp"

  /**
   * Slot for the {@code spaceTemp} property.
   * @see #getSpaceTemp
   * @see #setSpaceTemp
   */
  public static final Property spaceTemp = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code spaceTemp} property.
   * @see #spaceTemp
   */
  public BHonStatusNumeric getSpaceTemp() { return (BHonStatusNumeric)get(spaceTemp); }

  /**
   * Set the {@code spaceTemp} property.
   * @see #spaceTemp
   */
  public void setSpaceTemp(BHonStatusNumeric v) { set(spaceTemp, v, null); }

  //endregion Property "spaceTemp"

  //region Property "effHeatSP"

  /**
   * Slot for the {@code effHeatSP} property.
   * @see #getEffHeatSP
   * @see #setEffHeatSP
   */
  public static final Property effHeatSP = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code effHeatSP} property.
   * @see #effHeatSP
   */
  public BHonStatusNumeric getEffHeatSP() { return (BHonStatusNumeric)get(effHeatSP); }

  /**
   * Set the {@code effHeatSP} property.
   * @see #effHeatSP
   */
  public void setEffHeatSP(BHonStatusNumeric v) { set(effHeatSP, v, null); }

  //endregion Property "effHeatSP"

  //region Property "effCoolSP"

  /**
   * Slot for the {@code effCoolSP} property.
   * @see #getEffCoolSP
   * @see #setEffCoolSP
   */
  public static final Property effCoolSP = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code effCoolSP} property.
   * @see #effCoolSP
   */
  public BHonStatusNumeric getEffCoolSP() { return (BHonStatusNumeric)get(effCoolSP); }

  /**
   * Set the {@code effCoolSP} property.
   * @see #effCoolSP
   */
  public void setEffCoolSP(BHonStatusNumeric v) { set(effCoolSP, v, null); }

  //endregion Property "effCoolSP"

  //region Property "allowAutoChange"

  /**
   * Slot for the {@code allowAutoChange} property.
   * @see #getAllowAutoChange
   * @see #setAllowAutoChange
   */
  public static final Property allowAutoChange = newProperty(Flags.SUMMARY, new BHonStatusBoolean(true, BStatus.ok), null);

  /**
   * Get the {@code allowAutoChange} property.
   * @see #allowAutoChange
   */
  public BHonStatusBoolean getAllowAutoChange() { return (BHonStatusBoolean)get(allowAutoChange); }

  /**
   * Set the {@code allowAutoChange} property.
   * @see #allowAutoChange
   */
  public void setAllowAutoChange(BHonStatusBoolean v) { set(allowAutoChange, v, null); }

  //endregion Property "allowAutoChange"

  //region Property "controlType"

  /**
   * Slot for the {@code controlType} property.
   * Config slots
   * @see #getControlType
   * @see #setControlType
   */
  public static final Property controlType = newProperty(0, BControlTypeEnum.DEFAULT, null);

  /**
   * Get the {@code controlType} property.
   * Config slots
   * @see #controlType
   */
  public BControlTypeEnum getControlType() { return (BControlTypeEnum)get(controlType); }

  /**
   * Set the {@code controlType} property.
   * Config slots
   * @see #controlType
   */
  public void setControlType(BControlTypeEnum v) { set(controlType, v, null); }

  //endregion Property "controlType"

  //region Property "behaviorType"

  /**
   * Slot for the {@code behaviorType} property.
   * @see #getBehaviorType
   * @see #setBehaviorType
   */
  public static final Property behaviorType = newProperty(0, BBehaviorTypeEnum.DEFAULT, null);

  /**
   * Get the {@code behaviorType} property.
   * @see #behaviorType
   */
  public BBehaviorTypeEnum getBehaviorType() { return (BBehaviorTypeEnum)get(behaviorType); }

  /**
   * Set the {@code behaviorType} property.
   * @see #behaviorType
   */
  public void setBehaviorType(BBehaviorTypeEnum v) { set(behaviorType, v, null); }

  //endregion Property "behaviorType"

  //region Property "EFF_SETPT"

  /**
   * Slot for the {@code EFF_SETPT} property.
   * Output slots
   * @see #getEFF_SETPT
   * @see #setEFF_SETPT
   */
  public static final Property EFF_SETPT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_SETPT} property.
   * Output slots
   * @see #EFF_SETPT
   */
  public BHonStatusNumeric getEFF_SETPT() { return (BHonStatusNumeric)get(EFF_SETPT); }

  /**
   * Set the {@code EFF_SETPT} property.
   * Output slots
   * @see #EFF_SETPT
   */
  public void setEFF_SETPT(BHonStatusNumeric v) { set(EFF_SETPT, v, null); }

  //endregion Property "EFF_SETPT"

  //region Property "EFF_TEMP_MODE"

  /**
   * Slot for the {@code EFF_TEMP_MODE} property.
   * @see #getEFF_TEMP_MODE
   * @see #setEFF_TEMP_MODE
   */
  public static final Property EFF_TEMP_MODE = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BStatusSetTemperatureModeEnum(BEffTempModeEnum.DEFAULT, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE)));

  /**
   * Get the {@code EFF_TEMP_MODE} property.
   * @see #EFF_TEMP_MODE
   */
  public BStatusSetTemperatureModeEnum getEFF_TEMP_MODE() { return (BStatusSetTemperatureModeEnum)get(EFF_TEMP_MODE); }

  /**
   * Set the {@code EFF_TEMP_MODE} property.
   * @see #EFF_TEMP_MODE
   */
  public void setEFF_TEMP_MODE(BStatusSetTemperatureModeEnum v) { set(EFF_TEMP_MODE, v, null); }

  //endregion Property "EFF_TEMP_MODE"

  //region Property "prevModeArb"

  /**
   * Slot for the {@code prevModeArb} property.
   * LoopStaticVariables
   * @see #getPrevModeArb
   * @see #setPrevModeArb
   */
  public static final Property prevModeArb = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, BCommandModeEnum.DEFAULT, null);

  /**
   * Get the {@code prevModeArb} property.
   * LoopStaticVariables
   * @see #prevModeArb
   */
  public BCommandModeEnum getPrevModeArb() { return (BCommandModeEnum)get(prevModeArb); }

  /**
   * Set the {@code prevModeArb} property.
   * LoopStaticVariables
   * @see #prevModeArb
   */
  public void setPrevModeArb(BCommandModeEnum v) { set(prevModeArb, v, null); }

  //endregion Property "prevModeArb"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSetTemperatureMode.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		if (getControlType().equals(BControlTypeEnum.Vav)) {
			performVavOperation();
		} else {
			performCvahuOperation();
		}
	}

	private void performCvahuOperation() {
		BCommandModeEnum cmdMode1 = getCmdModePropertyValue();
		BCommandModeEnum effModeArb;
		double effCoolSetPt = getEffCoolSPPropertyValue();
		double effHeatSetPt = getEffHeatSPPropertyValue();

		effModeArb = getPrevModeArb();
		BEffTempModeEnum effTempMode;
		effTempMode = BEffTempModeEnum.make(getEFF_TEMP_MODE().getValue().getOrdinal());
		
		if (cmdMode1.getOrdinal() == BCommandModeEnum.CMD_AUTO_MODE && (effTempMode.getOrdinal() != BEffTempModeEnum.COOL_MODE && effTempMode.getOrdinal() != BEffTempModeEnum.HEAT_MODE))
			effTempMode = BEffTempModeEnum.Heat_Mode;
		if (cmdMode1.getOrdinal() > BCommandModeEnum.CMD_AUTO_MODE && cmdMode1.getOrdinal() <= BCommandModeEnum.CMD_EMERG_HEAT_MODE) {
			effTempMode = updateEffTempModeForCommandModeInRangeForCvahu(cmdMode1, effTempMode);
		} else if (cmdMode1.getOrdinal() > BCommandModeEnum.CMD_EMERG_HEAT_MODE && cmdMode1.getOrdinal() != BCommandModeEnum.CMD_NUL_MODE) {
			effTempMode = BEffTempModeEnum.Heat_Mode;
		} else {
			effTempMode = updateEffTempModeForCommandModeForCvahu(effModeArb, effTempMode);
			if(null == effTempMode)
				return;
		}
		updateOutput(effModeArb, effCoolSetPt, effHeatSetPt, effTempMode);
	}

	private void updateOutput(BCommandModeEnum effModeArb, double effCoolSetPt, double effHeatSetPt, BEffTempModeEnum effTempMode) {
		if (effTempMode.getOrdinal() == BEffTempModeEnum.COOL_MODE)
			getEFF_SETPT().setValue(effCoolSetPt);
		else
			getEFF_SETPT().setValue(effHeatSetPt);

		setPrevModeArb(effModeArb);
		setEFF_TEMP_MODE(new BStatusSetTemperatureModeEnum(effTempMode));
	}

	private BEffTempModeEnum updateEffTempModeForCommandModeForCvahu(BCommandModeEnum effModeArb, BEffTempModeEnum effTempMode1) {
		BEffTempModeEnum effTempMode = effTempMode1;
		BSystemSwitchEnum sysSwitch1;
		sysSwitch1 = getSysSwitchPropertyValueForCvahu();
		if (sysSwitch1.getOrdinal() == BSystemSwitchEnum.SS_AUTO && (effTempMode.getOrdinal() != BEffTempModeEnum.COOL_MODE && effTempMode.getOrdinal() != BEffTempModeEnum.HEAT_MODE))
			effTempMode = BEffTempModeEnum.Heat_Mode;
		if (sysSwitch1.getOrdinal() == BSystemSwitchEnum.SS_COOL)
			effTempMode = BEffTempModeEnum.Cool_Mode;
		else if (sysSwitch1.getOrdinal() == BSystemSwitchEnum.SS_HEAT)
			effTempMode = BEffTempModeEnum.Heat_Mode;
		else if (sysSwitch1.getOrdinal() == BSystemSwitchEnum.SS_EMERG_HEAT)
			effTempMode = BEffTempModeEnum.Emerg_Heat;
		else if (sysSwitch1.getOrdinal() == BSystemSwitchEnum.SS_OFF)
			effTempMode = BEffTempModeEnum.Off_Mode;
		else {
			updateEffTempModeForSystemSwitchForCvahu(effModeArb, effTempMode);
			return null;
		}
		return effTempMode;
	}

	private BEffTempModeEnum updateEffTempModeForSystemSwitchForCvahu(BCommandModeEnum effModeArb, BEffTempModeEnum effTempMode1) {
		BEffTempModeEnum effTempMode = effTempMode1;
		double spaceTemp1;
		boolean allowAutoChange1;
		double effCoolSetPt = getEffCoolSPPropertyValue();
		double effHeatSetPt = getEffHeatSPPropertyValue();
		spaceTemp1 = getSpaceTempPropertyValue();
		allowAutoChange1 = getAllowAutoChangePropertyValue();
		if (Double.isInfinite(spaceTemp1) && spaceTemp1 > 0)
			effTempMode = BEffTempModeEnum.Heat_Mode;
		else if (allowAutoChange1) {
			if (effHeatSetPt > effCoolSetPt)
				effHeatSetPt = effCoolSetPt;
			effTempMode = updateEffTempModeForCvahu(effTempMode, spaceTemp1, effCoolSetPt, effHeatSetPt);
		}
		updateOutput(effModeArb, effCoolSetPt, effHeatSetPt, effTempMode);
		return effTempMode;
	}

	private BEffTempModeEnum updateEffTempModeForCommandModeInRangeForCvahu(BCommandModeEnum cmdMode1, BEffTempModeEnum effTempMode1) {
		BEffTempModeEnum effTempMode = effTempMode1; 
		switch (cmdMode1.getOrdinal()) {
			case BCommandModeEnum.CMD_HEAT_MODE:
				effTempMode = BEffTempModeEnum.Heat_Mode;
				break;
			case BCommandModeEnum.CMD_COOL_MODE:
				effTempMode = BEffTempModeEnum.Cool_Mode;
				break;
			case BCommandModeEnum.CMD_OFF_MODE:
				effTempMode = BEffTempModeEnum.Off_Mode;
				break;
			case BCommandModeEnum.CMD_EMERG_HEAT_MODE:
				effTempMode = BEffTempModeEnum.Emerg_Heat;
				break;
			///CLOVER:OFF
			default:
				break;
			///CLOVER:ON
		}
		return effTempMode;
	}

	private void performVavOperation() {
		BCommandModeEnum cmdMode1;
		BCommandModeEnum effModeArb;
		double effCoolSetPt;
		double effHeatSetPt;
	
		effModeArb = getPrevModeArb();
		effCoolSetPt = getEffCoolSPPropertyValue();
		effHeatSetPt = getEffHeatSPPropertyValue();
		cmdMode1 = getCmdModePropertyValue();
	
		BEffTempModeEnum effTempMode;
		double supplyTemp1;
		effTempMode = BEffTempModeEnum.make(getEFF_TEMP_MODE().getValue().getOrdinal());
	
		if (cmdMode1.getOrdinal() == BCommandModeEnum.CMD_AUTO_MODE && (effTempMode.getOrdinal() != BEffTempModeEnum.COOL_MODE && effTempMode.getOrdinal() != BEffTempModeEnum.REHEAT_MODE))
			effTempMode = BEffTempModeEnum.Cool_Mode;
	
		if (cmdMode1.getOrdinal() == BCommandModeEnum.CMD_OFF_MODE)
			effTempMode = BEffTempModeEnum.Off_Mode;
		else if (cmdMode1.getOrdinal() == BCommandModeEnum.CMD_EMERG_HEAT_MODE)
			effTempMode = BEffTempModeEnum.Heat_Mode;
		else if (cmdMode1.getOrdinal() > BCommandModeEnum.CMD_EMERG_HEAT_MODE && cmdMode1.getOrdinal() != BCommandModeEnum.CMD_NUL_MODE)
			effTempMode = BEffTempModeEnum.Cool_Mode;
		else {
			supplyTemp1 = getSupplyTempPropertyValue();
			effModeArb = updateEffModeArbForVav(cmdMode1, effModeArb, supplyTemp1);
			
			effTempMode = updateEffTempModeForVav(effTempMode, effModeArb, effCoolSetPt, effHeatSetPt, supplyTemp1);
		}
		updateOutput(effModeArb, effCoolSetPt, effHeatSetPt, effTempMode);
	}
	
	private BEffTempModeEnum updateEffTempModeForVav(BEffTempModeEnum effTempMode1, BCommandModeEnum effModeArb, double effCoolSetPt, double effHeatSetPt, double supplyTemp1) {
		BEffTempModeEnum effTempMode = effTempMode1;
		double spaceTemp1;
		boolean allowAutoChange1;
		effTempMode = updateEffTempModeForVav(effModeArb, effTempMode);

		spaceTemp1 = getSpaceTempPropertyValue();
		allowAutoChange1 = getAllowAutoChangePropertyValue();

		if (Double.isInfinite(spaceTemp1) && supplyTemp1 > 0)
			effTempMode = BEffTempModeEnum.Cool_Mode;
		else if (allowAutoChange1) {
			effTempMode = updateEffTempModeForVav(effTempMode, effModeArb, spaceTemp1, effCoolSetPt, effHeatSetPt, allowAutoChange1);
		}
		return effTempMode;
	}
	private BEffTempModeEnum updateEffTempModeForVav(BCommandModeEnum effModeArb, BEffTempModeEnum effTempMode1) {
		BEffTempModeEnum effTempMode = effTempMode1;
		if (effModeArb.getOrdinal() == BCommandModeEnum.CMD_HEAT_MODE)
			effTempMode = BEffTempModeEnum.Heat_Mode;
		else if (effModeArb.getOrdinal() == BCommandModeEnum.CMD_COOL_MODE || (effTempMode.getOrdinal() != BEffTempModeEnum.REHEAT_MODE && effTempMode.getOrdinal() != BEffTempModeEnum.COOL_MODE))
			effTempMode = BEffTempModeEnum.Cool_Mode;
		else if (effModeArb.getOrdinal() == BCommandModeEnum.CMD_OFF_MODE)
			effTempMode = BEffTempModeEnum.Off_Mode;
		return effTempMode;
	}

	private BCommandModeEnum updateEffModeArbForVav(BCommandModeEnum cmdMode1, BCommandModeEnum effModeArb1, double supplyTemp1) {
		BCommandModeEnum effModeArb = effModeArb1; 
		BSystemSwitchEnum sysSwitch1;
		if (!(Double.isInfinite(supplyTemp1) && supplyTemp1 > 0)) {
			effModeArb = updateEffModeArbForValidSTForVav(cmdMode1, effModeArb, supplyTemp1);
		
		}else {
			switch (cmdMode1.getOrdinal()) {
				case BCommandModeEnum.CMD_AUTO_MODE:
					sysSwitch1 = getSysSwitchPropertyValueForVav();
					effModeArb = updateEffModeArbForVav(sysSwitch1);
					break;
				case BCommandModeEnum.CMD_HEAT_MODE:
				case BCommandModeEnum.CMD_COOL_MODE:
					effModeArb = cmdMode1;
					break;
				default:
					effModeArb = BCommandModeEnum.Cmd_Auto_Mode;
					break;
			}
		}
		return effModeArb;
	}

	private BEffTempModeEnum updateEffTempModeForCvahu(BEffTempModeEnum effTempMode1, double spaceTemp1, double effCoolSetPt, double effHeatSetPt) {
		BEffTempModeEnum effTempMode = effTempMode1;
		if (effTempMode.getOrdinal() == BEffTempModeEnum.COOL_MODE && spaceTemp1 < effHeatSetPt) {
				return BEffTempModeEnum.Heat_Mode;
		} 

		if (effTempMode.getOrdinal() == BEffTempModeEnum.HEAT_MODE && spaceTemp1 > effCoolSetPt) {
				return BEffTempModeEnum.Cool_Mode;
		}
		return effTempMode;
	}

	private BEffTempModeEnum updateEffTempModeForVav(BEffTempModeEnum effTempMode1, BCommandModeEnum effModeArb, double spaceTemp1, double effCoolSetPt, double effHeatSetPt, boolean allowAutoChange1) {
		BEffTempModeEnum effTempMode = effTempMode1;
		if (condition1(effTempMode, effModeArb) && allowAutoChange1 && spaceTemp1 < effHeatSetPt && spaceTemp1 < effCoolSetPt - 1.0)
			effTempMode = BEffTempModeEnum.Reheat_Mode;

		if (effTempMode.getOrdinal() == BEffTempModeEnum.REHEAT_MODE && spaceTemp1 > effCoolSetPt && spaceTemp1 > effHeatSetPt + 1.0)
			effTempMode = BEffTempModeEnum.Cool_Mode;
		return effTempMode;
	}

	private boolean condition1(BEffTempModeEnum effTempMode, BCommandModeEnum effModeArb) {
		return effTempMode.getOrdinal() == BEffTempModeEnum.COOL_MODE && effModeArb.getOrdinal() != BCommandModeEnum.CMD_COOL_MODE;
	}

	private BCommandModeEnum updateEffModeArbForVav(BSystemSwitchEnum sysSwitch1) {
		BCommandModeEnum effModeArb;
		switch (sysSwitch1.getOrdinal() ) {
			case BSystemSwitchEnum.SS_COOL:
				effModeArb = BCommandModeEnum.Cmd_Cool_Mode;
				break;
			case BSystemSwitchEnum.SS_HEAT:
			case BSystemSwitchEnum.SS_EMERG_HEAT:
				effModeArb = BCommandModeEnum.Cmd_Heat_Mode;
				break;
			case BSystemSwitchEnum.SS_OFF:
				effModeArb = BCommandModeEnum.Cmd_Off_Mode;
				break;
			default:
				effModeArb = BCommandModeEnum.Cmd_Auto_Mode;
				break;
		}
		return effModeArb;
	}

	private BCommandModeEnum updateEffModeArbForValidSTForVav(BCommandModeEnum cmdMode1, BCommandModeEnum effModeArb1, double supplyTemp1) {
		BCommandModeEnum effModeArb = effModeArb1;
		if (supplyTemp1 < SEVENTY) {
			if (cmdMode1.getOrdinal() == BCommandModeEnum.CMD_COOL_MODE)
				effModeArb = BCommandModeEnum.Cmd_Cool_Mode;
			else
				effModeArb = BCommandModeEnum.Cmd_Auto_Mode;
		} else if (supplyTemp1 > DEFAULTEFFCOOLSP) {
			effModeArb = BCommandModeEnum.Cmd_Heat_Mode;
		}
		return effModeArb;
	}

	private BSystemSwitchEnum getSysSwitchPropertyValueForVav() {
		if(!isConfigured(sysSwitch)) {
			return BSystemSwitchEnum.Ss_Auto;
		}
		if(getSysSwitch().getValue().getOrdinal() == BSystemSwitchEnum.SS_OTHERS) 
			return BSystemSwitchEnum.Ss_Auto;

		return BSystemSwitchEnum.make(getSysSwitch().getValue().getTag());
	}
	
	private BSystemSwitchEnum getSysSwitchPropertyValueForCvahu() {
		if (!isConfigured(sysSwitch)) {
			if (getBehaviorType().equals(BBehaviorTypeEnum.enhanced)) {
				return BSystemSwitchEnum.Ss_Auto;
			} else {
				return BSystemSwitchEnum.Ss_Off;
			}
		}

		if (getSysSwitch().getValue().getOrdinal() == BSystemSwitchEnum.SS_OTHERS)
			return BSystemSwitchEnum.Ss_Off;
		else if (getSysSwitch().getValue().getOrdinal() == BSystemSwitchEnum.SS_EFF_HEAT_MODE)
			return BSystemSwitchEnum.Ss_Heat;

		return BSystemSwitchEnum.make(getSysSwitch().getValue().getOrdinal());
	}
	
	private BCommandModeEnum getCmdModePropertyValue() {
		if(!isConfigured(cmdMode)) {
			return BCommandModeEnum.Cmd_Auto_Mode;
		}
		return BCommandModeEnum.make(getCmdMode().getValue().getTag());
	}
	
	private double getSupplyTempPropertyValue() {
		if(!isConfigured(supplyTemp)) {
			return Double.POSITIVE_INFINITY;
		}
		double supplyTemp1 = getSupplyTemp().getValue();
		if (LimitCheckUtil.isInvalidValue(supplyTemp1)) {
			return Double.POSITIVE_INFINITY;
		}
		return limitInput(supplyTemp1, 0, BYTE, 0);
	}
	
	private double getSpaceTempPropertyValue() {
		if(!isConfigured(spaceTemp)) {
			return Double.POSITIVE_INFINITY;
		}
		double spaceTemp1 = getSpaceTemp().getValue();
		if (LimitCheckUtil.isInvalidValue(spaceTemp1)) {
			return Double.POSITIVE_INFINITY;
		}
		return limitInput(spaceTemp1, 0, BYTE, 0);
	}
	
	private double getEffCoolSPPropertyValue() {
		if(!isConfigured(effCoolSP)) {
			return DEFAULTEFFCOOLSP;
		}
		double effCoolSP1 = getEffCoolSP().getValue();
		if (LimitCheckUtil.isInvalidValue(effCoolSP1)) {
			return DEFAULTEFFCOOLSP;
		}
		return effCoolSP1;
	}
	
	private double getEffHeatSPPropertyValue() {
		if(!isConfigured(effHeatSP)) {
			return DEFAULTEFFHEATSP;
		}
		double effHeatSP1 = getEffHeatSP().getValue();
		if (LimitCheckUtil.isInvalidValue(effHeatSP1)) {
			return DEFAULTEFFHEATSP;
		}
		return effHeatSP1;
	}
	
	private boolean getAllowAutoChangePropertyValue() {
		if(!isConfigured(allowAutoChange)) {
			return true;
		}
		return getAllowAutoChange().getValue();
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
    	List<Property> properties = super.getInputPropertiesList();
    	properties.add(sysSwitch);
    	properties.add(cmdMode);
    	properties.add(supplyTemp);
    	properties.add(spaceTemp);
    	properties.add(effHeatSP);
    	properties.add(effCoolSP);
    	properties.add(allowAutoChange);
    	return properties;
	}

	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(EFF_SETPT);
		properties.add(EFF_TEMP_MODE);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList =  super.getConfigPropertiesList();
		propertyArrayList.add(controlType);
		propertyArrayList.add(behaviorType);
		return propertyArrayList;
	}

	private static final double DEFAULTEFFHEATSP = 68;  
	private static final double DEFAULTEFFCOOLSP = 75;
	private static final double SEVENTY = 70;
	private static final double BYTE = 255;
	
}
