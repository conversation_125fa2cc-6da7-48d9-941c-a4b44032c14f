/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

/**
 * This class holds all the ouput property and shows in Override action dialog.
 * <AUTHOR> -<PERSON><PERSON><PERSON><PERSON>
 * @since Sep 24, 2018
 */
package com.honeywell.honfunctionblocks.fbs;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BRelTime;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
@NiagaraProperty(name="OverrideDuration",  defaultValue="BRelTime.makeMinutes(1)", type = "BRelTime")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845","squid:S00103"})
public class BFBOverrideProperties extends  BComponent{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.BFBOverrideProperties(1442866405)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "OverrideDuration"

  /**
   * Slot for the {@code OverrideDuration} property.
   * @see #getOverrideDuration
   * @see #setOverrideDuration
   */
  public static final Property OverrideDuration = newProperty(0, BRelTime.makeMinutes(1), null);

  /**
   * Get the {@code OverrideDuration} property.
   * @see #OverrideDuration
   */
  public BRelTime getOverrideDuration() { return (BRelTime)get(OverrideDuration); }

  /**
   * Set the {@code OverrideDuration} property.
   * @see #OverrideDuration
   */
  public void setOverrideDuration(BRelTime v) { set(OverrideDuration, v, null); }

  //endregion Property "OverrideDuration"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFBOverrideProperties.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
