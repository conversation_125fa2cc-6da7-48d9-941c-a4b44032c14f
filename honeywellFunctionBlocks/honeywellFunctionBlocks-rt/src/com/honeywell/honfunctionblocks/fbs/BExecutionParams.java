/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BFacets;
import javax.baja.sys.BStruct;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.UnitConstants;


/**
 * Parameters required to execute a block, will be passed to the each functional block by the DDC engine
 * 
 * <AUTHOR> - Ravi <PERSON> .K
 * @since Nov 23, 2017
 */

@NiagaraType
@NiagaraProperty(name = "iterationInterval", type = "int", defaultValue = "UnitConstants.THOUSAND_MILLI_SECOND",
facets = { 
	@Facet(name = "BFacets.MIN", value = "100"),
	@Facet(name = "BFacets.MAX", value = "65535"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(\"millisecond\")"), 
	@Facet(name = "BFacets.PRECISION", value = "0") })
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BExecutionParams extends BStruct {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.BExecutionParams(2439916077)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "iterationInterval"

  /**
   * Slot for the {@code iterationInterval} property.
   * @see #getIterationInterval
   * @see #setIterationInterval
   */
  public static final Property iterationInterval = newProperty(0, UnitConstants.THOUSAND_MILLI_SECOND, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 100), BFacets.make(BFacets.MAX, 65535)), BFacets.make(BFacets.UNITS, BUnit.getUnit("millisecond"))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code iterationInterval} property.
   * @see #iterationInterval
   */
  public int getIterationInterval() { return getInt(iterationInterval); }

  /**
   * Set the {@code iterationInterval} property.
   * @see #iterationInterval
   */
  public void setIterationInterval(int v) { setInt(iterationInterval, v, null); }

  //endregion Property "iterationInterval"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BExecutionParams.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
}
