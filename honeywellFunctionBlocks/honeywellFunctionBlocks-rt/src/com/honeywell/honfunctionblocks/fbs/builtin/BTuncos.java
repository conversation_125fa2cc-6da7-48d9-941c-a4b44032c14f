/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.builtin;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.BRelTime;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of TUNCOS option of schedule block of spyder required for migration
 * <AUTHOR> - Sugandhika Parida
 * @since Jul 26, 2018
 *
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"tuncos.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "nextTime", type = "BAbsTime", defaultValue = "BAbsTime.DEFAULT", flags = Flags.READONLY | Flags.SUMMARY)
@NiagaraProperty(name = "TUNCOS", type = "BStatusNumeric", defaultValue = "new BStatusNumeric(0, BStatus.ok)", flags = Flags.READONLY | Flags.SUMMARY, 
facets = { 	@Facet(name = "BFacets.PRECISION", value = "0"),
			@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(FBSlotConstants.MINUTE_UNIT)")
		})
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103"})
public class BTuncos  extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.builtin.BTuncos(215490024)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "tuncos.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "nextTime"

  /**
   * Slot for the {@code nextTime} property.
   * @see #getNextTime
   * @see #setNextTime
   */
  public static final Property nextTime = newProperty(Flags.READONLY | Flags.SUMMARY, BAbsTime.DEFAULT, null);

  /**
   * Get the {@code nextTime} property.
   * @see #nextTime
   */
  public BAbsTime getNextTime() { return (BAbsTime)get(nextTime); }

  /**
   * Set the {@code nextTime} property.
   * @see #nextTime
   */
  public void setNextTime(BAbsTime v) { set(nextTime, v, null); }

  //endregion Property "nextTime"

  //region Property "TUNCOS"

  /**
   * Slot for the {@code TUNCOS} property.
   * @see #getTUNCOS
   * @see #setTUNCOS
   */
  public static final Property TUNCOS = newProperty(Flags.READONLY | Flags.SUMMARY, new BStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.PRECISION, 0), BFacets.make(BFacets.UNITS, BUnit.getUnit(FBSlotConstants.MINUTE_UNIT))));

  /**
   * Get the {@code TUNCOS} property.
   * @see #TUNCOS
   */
  public BStatusNumeric getTUNCOS() { return (BStatusNumeric)get(TUNCOS); }

  /**
   * Set the {@code TUNCOS} property.
   * @see #TUNCOS
   */
  public void setTUNCOS(BStatusNumeric v) { set(TUNCOS, v, null); }

  //endregion Property "TUNCOS"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTuncos.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	  	super.executeHoneywellComponent(executionParams);
	  
		BRelTime tuncos = BAbsTime.now().delta(getNextTime());
		getTUNCOS().setValue(limitingTuncos(tuncos));
		getTUNCOS().setStatus(BStatus.ok);

	}
  
	private double limitingTuncos(BRelTime tuncos) {
		if(tuncos.getMinutes() < 0) {
			return 0;
		} else if(tuncos.getMinutes() > TUNCOS_MAX_LIMIT) {
			return TUNCOS_MAX_LIMIT;
		}
		return tuncos.getMinutes();
	}
	
	private static final int TUNCOS_MAX_LIMIT = 11520;

}
