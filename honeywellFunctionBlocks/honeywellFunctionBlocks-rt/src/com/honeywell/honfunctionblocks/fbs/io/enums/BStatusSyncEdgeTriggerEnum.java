/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.io.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum;

/**
 * <AUTHOR> - <PERSON><PERSON>
 * @since 12 June,2018
 */

@NiagaraType
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public class BStatusSyncEdgeTriggerEnum extends BHonStatusEnum {
/*+ ------------ <PERSON><PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.io.enums.BStatusSyncEdgeTriggerEnum(2979906276)1.0$ @*/
/* Generated Tue Jun 12 14:14:58 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusSyncEdgeTriggerEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BStatusSyncEdgeTriggerEnum() {
    super();
  }

  public BStatusSyncEdgeTriggerEnum(BEnum value) {
    super(value);
  }

  public BStatusSyncEdgeTriggerEnum(BEnum value, BStatus status) {
    super(value, status);
  }
}
