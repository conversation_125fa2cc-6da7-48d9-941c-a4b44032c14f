/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.io.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Enum for FloatingOutput
 * Requirement ID: F1PLT-ADR-1790
 * <AUTHOR> - <PERSON><PERSON>
 * @since 31 May,2018
 */

@NiagaraType
@NiagaraEnum(range = {
  @Range("None"),
  @Range("SyncClosed"),
  @Range("SyncOpen")},
  defaultValue = "None")
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public final class BSyncTypeEnum extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.io.enums.BSyncTypeEnum(3217109180)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for None. */
  public static final int NONE = 0;
  /** Ordinal value for SyncClosed. */
  public static final int SYNC_CLOSED = 1;
  /** Ordinal value for SyncOpen. */
  public static final int SYNC_OPEN = 2;

  /** BSyncTypeEnum constant for None. */
  public static final BSyncTypeEnum None = new BSyncTypeEnum(NONE);
  /** BSyncTypeEnum constant for SyncClosed. */
  public static final BSyncTypeEnum SyncClosed = new BSyncTypeEnum(SYNC_CLOSED);
  /** BSyncTypeEnum constant for SyncOpen. */
  public static final BSyncTypeEnum SyncOpen = new BSyncTypeEnum(SYNC_OPEN);

  /** Factory method with ordinal. */
  public static BSyncTypeEnum make(int ordinal)
  {
    return (BSyncTypeEnum)None.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BSyncTypeEnum make(String tag)
  {
    return (BSyncTypeEnum)None.getRange().get(tag);
  }

  /** Private constructor. */
  private BSyncTypeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BSyncTypeEnum DEFAULT = None;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSyncTypeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
