/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.io.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Enum for FloatingOutput
 * Requirement ID: F1PLT-ADR-1790
 * <AUTHOR> - <PERSON><PERSON>
 * @since 31 May,2018
 */

@NiagaraType
@NiagaraEnum(range = {
    @Range("noEffect"),
    @Range("SyncClosed"),
    @Range("SyncOpen")},
  defaultValue = "noEffect")
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public final class BSyncEdgeTriggerEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.io.enums.BSyncEdgeTriggerEnum(764224370)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for noEffect. */
  public static final int NO_EFFECT = 0;
  /** Ordinal value for SyncClosed. */
  public static final int SYNC_CLOSED = 1;
  /** Ordinal value for SyncOpen. */
  public static final int SYNC_OPEN = 2;

  /** BSyncEdgeTriggerEnum constant for noEffect. */
  public static final BSyncEdgeTriggerEnum noEffect = new BSyncEdgeTriggerEnum(NO_EFFECT);
  /** BSyncEdgeTriggerEnum constant for SyncClosed. */
  public static final BSyncEdgeTriggerEnum SyncClosed = new BSyncEdgeTriggerEnum(SYNC_CLOSED);
  /** BSyncEdgeTriggerEnum constant for SyncOpen. */
  public static final BSyncEdgeTriggerEnum SyncOpen = new BSyncEdgeTriggerEnum(SYNC_OPEN);

  /** Factory method with ordinal. */
  public static BSyncEdgeTriggerEnum make(int ordinal)
  {
    return (BSyncEdgeTriggerEnum)noEffect.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BSyncEdgeTriggerEnum make(String tag)
  {
    return (BSyncEdgeTriggerEnum)noEffect.getRange().get(tag);
  }

  /** Private constructor. */
  private BSyncEdgeTriggerEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BSyncEdgeTriggerEnum DEFAULT = noEffect;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSyncEdgeTriggerEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
