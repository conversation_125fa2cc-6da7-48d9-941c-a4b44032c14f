/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.io.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Enum for FloatingOutput
 * Specifies direction in which to rotate the damper
 * Requirement ID: F1PLT-ADR-1790
 * <AUTHOR> - <PERSON><PERSON>
 * @since 31 May,2018
 */

@NiagaraType
@NiagaraEnum(range = {
  @Range("Direct"),
  @Range("Reverse")},
  defaultValue = "Direct")
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public final class BMotorActionEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.io.enums.BMotorActionEnum(2737500631)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Direct. */
  public static final int DIRECT = 0;
  /** Ordinal value for Reverse. */
  public static final int REVERSE = 1;

  /** BMotorActionEnum constant for Direct. */
  public static final BMotorActionEnum Direct = new BMotorActionEnum(DIRECT);
  /** BMotorActionEnum constant for Reverse. */
  public static final BMotorActionEnum Reverse = new BMotorActionEnum(REVERSE);

  /** Factory method with ordinal. */
  public static BMotorActionEnum make(int ordinal)
  {
    return (BMotorActionEnum)Direct.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BMotorActionEnum make(String tag)
  {
    return (BMotorActionEnum)Direct.getRange().get(tag);
  }

  /** Private constructor. */
  private BMotorActionEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BMotorActionEnum DEFAULT = Direct;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMotorActionEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
