/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;

import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;

/**
 *
 * <AUTHOR> - <PERSON> .K
 * @since Oct 31, 2017
 * Any component which needs to be added to the deterministic folder shall implement this interface
 * so that deterministic DDC engine will execute this block on the consistent time slot 
 */
public interface IHoneywellComponent {
	/**
	 * Method to execute the Honeywell component to get the deterministic behavior
	 *
	 * @since Oct 31, 2017
	 * @return {@link int} - Users can report back if the execution of the block was success
	 */
	void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException;
	
	/**
	 * Method to initialize the Honeywell component before executing this block.
	 * Any Honeywell component will be initialized before staring the DDC engine
	 * @param executionParams
	 *
	 * @since Nov 23, 2017
	 * @throws BlockInitializationException
	 * @return {@link void}
	 */
	void initHoneywellComponent(BExecutionParams executionParams)  throws BlockInitializationException;
	
	/**
	 * To know if any of the output properties is overridden then do not execute the block
	 * @return
	 */
	boolean isOutputPropertiesOverridden();
}
