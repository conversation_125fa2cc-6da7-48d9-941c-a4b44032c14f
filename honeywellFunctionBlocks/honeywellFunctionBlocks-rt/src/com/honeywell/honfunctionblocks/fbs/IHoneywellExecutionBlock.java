/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;


/**
 * All blocks/containers that need to have executionOrder implement this interface 
 * <AUTHOR>
 *
 */
@SuppressWarnings("squid:S1609")	//Required as Niagara doesn't recognize @FunctionalInterface annotation
public interface IHoneywellExecutionBlock {	
	/**
	 * Update Execution Order for all Functional Blocks
	 * @param executionOrder
	 */
	void updateBlockExecutionOrder(int executionOrder);

}
