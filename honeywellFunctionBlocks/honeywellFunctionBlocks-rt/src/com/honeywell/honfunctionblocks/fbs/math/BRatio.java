/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.ArrayList;
import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.control.XYLineCalculator;
import com.honeywell.honfunctionblocks.fbs.math.enums.BRatioOperationEnum;

/**
 * Implementation of Ratio block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 9, 2018
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_ratio.png\")", flags=Flags.HIDDEN|Flags.READONLY)

@NiagaraProperty(name = "x", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "y1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "y2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "operation", type="BRatioOperationEnum", defaultValue="BRatioOperationEnum.DEFAULT")

@NiagaraProperty(name = "ignoreInvalidInput", type="boolean", defaultValue="true")

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })




public class BRatio extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BRatio(3646383797)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_ratio.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "x1"

  /**
   * Slot for the {@code x1} property.
   * @see #getX1
   * @see #setX1
   */
  public static final Property x1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x1} property.
   * @see #x1
   */
  public BHonStatusNumeric getX1() { return (BHonStatusNumeric)get(x1); }

  /**
   * Set the {@code x1} property.
   * @see #x1
   */
  public void setX1(BHonStatusNumeric v) { set(x1, v, null); }

  //endregion Property "x1"

  //region Property "y1"

  /**
   * Slot for the {@code y1} property.
   * @see #getY1
   * @see #setY1
   */
  public static final Property y1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code y1} property.
   * @see #y1
   */
  public BHonStatusNumeric getY1() { return (BHonStatusNumeric)get(y1); }

  /**
   * Set the {@code y1} property.
   * @see #y1
   */
  public void setY1(BHonStatusNumeric v) { set(y1, v, null); }

  //endregion Property "y1"

  //region Property "x2"

  /**
   * Slot for the {@code x2} property.
   * @see #getX2
   * @see #setX2
   */
  public static final Property x2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x2} property.
   * @see #x2
   */
  public BHonStatusNumeric getX2() { return (BHonStatusNumeric)get(x2); }

  /**
   * Set the {@code x2} property.
   * @see #x2
   */
  public void setX2(BHonStatusNumeric v) { set(x2, v, null); }

  //endregion Property "x2"

  //region Property "y2"

  /**
   * Slot for the {@code y2} property.
   * @see #getY2
   * @see #setY2
   */
  public static final Property y2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code y2} property.
   * @see #y2
   */
  public BHonStatusNumeric getY2() { return (BHonStatusNumeric)get(y2); }

  /**
   * Set the {@code y2} property.
   * @see #y2
   */
  public void setY2(BHonStatusNumeric v) { set(y2, v, null); }

  //endregion Property "y2"

  //region Property "operation"

  /**
   * Slot for the {@code operation} property.
   * @see #getOperation
   * @see #setOperation
   */
  public static final Property operation = newProperty(0, BRatioOperationEnum.DEFAULT, null);

  /**
   * Get the {@code operation} property.
   * @see #operation
   */
  public BRatioOperationEnum getOperation() { return (BRatioOperationEnum)get(operation); }

  /**
   * Set the {@code operation} property.
   * @see #operation
   */
  public void setOperation(BRatioOperationEnum v) { set(operation, v, null); }

  //endregion Property "operation"

  //region Property "ignoreInvalidInput"

  /**
   * Slot for the {@code ignoreInvalidInput} property.
   * @see #getIgnoreInvalidInput
   * @see #setIgnoreInvalidInput
   */
  public static final Property ignoreInvalidInput = newProperty(0, true, null);

  /**
   * Get the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public boolean getIgnoreInvalidInput() { return getBoolean(ignoreInvalidInput); }

  /**
   * Set the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public void setIgnoreInvalidInput(boolean v) { setBoolean(ignoreInvalidInput, v, null); }

  //endregion Property "ignoreInvalidInput"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRatio.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		double output = Double.POSITIVE_INFINITY;
		boolean isAllInputsConfigured = isAllInputsConfigured();
		
		//If all inputs are configured then calculate ratio
		if(isAllInputsConfigured) {
			boolean isAllInputValid = isAllInputsValid();

			//All inputs must be valid or the result is invalid
			if(isAllInputValid) {
				output = computeOutput();
			} else if(getIgnoreInvalidInput()) {
				//If any input is invalid && ignoreInvalidFlag is set it goes to the minimum value(i.e. y1)
				output = getY1().getValue();
			}
		}
		
		if(Double.isNaN(output)){
			output = Double.POSITIVE_INFINITY;
		}
		getOUTPUT().setValue(output);
	}

	private boolean isAllInputsValid() {
		boolean allInputsValid = true; 
		List<Property> propertiesList = getInputPropertiesList();
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(isInvalidValue(property)) {
				allInputsValid = false;
				break;
			}
		}
		
		return allInputsValid;
	}

	private boolean isAllInputsConfigured() {
		boolean allInputsConfigured = true; 
		List<Property> propertiesList = getInputPropertiesList();
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(!isConfigured(property)) {
				allInputsConfigured = false;
				break;
			}
		}
		
		return allInputsConfigured;
	}

	private double computeOutput() {
		double xValue = getX().getValue();
		double x1Value = getX1().getValue();
	    double y1Value = getY1().getValue();

	    //look for special case for VAVBOX where x is < x1.
        //The line then goes from the x1,y1 point thru the 0,0 point
        if(BRatioOperationEnum.Vav_Flow_Bal.equals(getOperation()) && xValue < x1Value )
            return XYLineCalculator.xyline(xValue, 0.0, x1Value, 0.0, y1Value, false);
        
        double x2Value = getX2().getValue();
        double y2Value = getY2().getValue();
        boolean limitEndPoint = false;
        if(BRatioOperationEnum.Endpt_Limited.equals(getOperation())) {
        	//use limited calculation of y
        	limitEndPoint = true;
        }

        return XYLineCalculator.xyline(xValue, x1Value, x2Value, y1Value, y2Value, limitEndPoint);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = new ArrayList<>();
		properties.add(x);
		properties.add(x1);
		properties.add(x2);
		properties.add(y1);
		properties.add(y2);
		return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(operation);
		properties.add(ignoreInvalidInput);
		return properties;
	}
	
	

}
