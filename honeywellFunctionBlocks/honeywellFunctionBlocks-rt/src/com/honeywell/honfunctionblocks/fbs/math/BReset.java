/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.control.XYLineCalculator;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;

/**
 * Implementation of Reset block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 21, 2018
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
@NiagaraType

@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_reset.png\")", flags=Flags.HIDDEN|Flags.READONLY)

@NiagaraProperty(name = "input", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "sensor", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "zeroPctResetVal", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "hundredPctResetVal", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "resetAmount", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })



public class BReset extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BReset(67939451)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_reset.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "input"

  /**
   * Slot for the {@code input} property.
   * @see #getInput
   * @see #setInput
   */
  public static final Property input = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input} property.
   * @see #input
   */
  public BHonStatusNumeric getInput() { return (BHonStatusNumeric)get(input); }

  /**
   * Set the {@code input} property.
   * @see #input
   */
  public void setInput(BHonStatusNumeric v) { set(input, v, null); }

  //endregion Property "input"

  //region Property "sensor"

  /**
   * Slot for the {@code sensor} property.
   * @see #getSensor
   * @see #setSensor
   */
  public static final Property sensor = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code sensor} property.
   * @see #sensor
   */
  public BHonStatusNumeric getSensor() { return (BHonStatusNumeric)get(sensor); }

  /**
   * Set the {@code sensor} property.
   * @see #sensor
   */
  public void setSensor(BHonStatusNumeric v) { set(sensor, v, null); }

  //endregion Property "sensor"

  //region Property "zeroPctResetVal"

  /**
   * Slot for the {@code zeroPctResetVal} property.
   * @see #getZeroPctResetVal
   * @see #setZeroPctResetVal
   */
  public static final Property zeroPctResetVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code zeroPctResetVal} property.
   * @see #zeroPctResetVal
   */
  public BHonStatusNumeric getZeroPctResetVal() { return (BHonStatusNumeric)get(zeroPctResetVal); }

  /**
   * Set the {@code zeroPctResetVal} property.
   * @see #zeroPctResetVal
   */
  public void setZeroPctResetVal(BHonStatusNumeric v) { set(zeroPctResetVal, v, null); }

  //endregion Property "zeroPctResetVal"

  //region Property "hundredPctResetVal"

  /**
   * Slot for the {@code hundredPctResetVal} property.
   * @see #getHundredPctResetVal
   * @see #setHundredPctResetVal
   */
  public static final Property hundredPctResetVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code hundredPctResetVal} property.
   * @see #hundredPctResetVal
   */
  public BHonStatusNumeric getHundredPctResetVal() { return (BHonStatusNumeric)get(hundredPctResetVal); }

  /**
   * Set the {@code hundredPctResetVal} property.
   * @see #hundredPctResetVal
   */
  public void setHundredPctResetVal(BHonStatusNumeric v) { set(hundredPctResetVal, v, null); }

  //endregion Property "hundredPctResetVal"

  //region Property "resetAmount"

  /**
   * Slot for the {@code resetAmount} property.
   * @see #getResetAmount
   * @see #setResetAmount
   */
  public static final Property resetAmount = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code resetAmount} property.
   * @see #resetAmount
   */
  public BHonStatusNumeric getResetAmount() { return (BHonStatusNumeric)get(resetAmount); }

  /**
   * Set the {@code resetAmount} property.
   * @see #resetAmount
   */
  public void setResetAmount(BHonStatusNumeric v) { set(resetAmount, v, null); }

  //endregion Property "resetAmount"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BReset.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeReset();
	}
	
	private void executeReset() {
	    double input1;
	    double sensor1;
	    double zeroPctResetVal1;
	    double hundredPctResetVal1;
	    double resetAmt;
	    double resetVal;

	    if(isAnyInputUnconfigured(getInputPropertiesList()) || isInvalidValue(input)) {
	    	getOUTPUT().setValue(Double.POSITIVE_INFINITY);
	    	return;
	    }
	    
	    input1 = getInput().getValue();
	    if(isInvalidValue(sensor) || isInvalidValue(zeroPctResetVal) || isInvalidValue(hundredPctResetVal) || isInvalidValue(resetAmount)) {
	    	getOUTPUT().setValue(input1);
	    	return;
	    }
	    
        zeroPctResetVal1 = getZeroPctResetVal().getValue();
        hundredPctResetVal1 = getHundredPctResetVal().getValue();
        if(Double.compare(zeroPctResetVal1, hundredPctResetVal1) == 0) {
	    	getOUTPUT().setValue(input1);
	    	return;
        }
        
        sensor1 = getSensor().getValue();
        resetAmt = getResetAmount().getValue();
        resetVal = XYLineCalculator.xyline(sensor1,zeroPctResetVal1,hundredPctResetVal1,0.0,resetAmt,true);
        if(LimitCheckUtil.isInvalidValue(resetVal)) {
        	getOUTPUT().setValue(Double.POSITIVE_INFINITY);
        }else {
        	getOUTPUT().setValue(input1 + resetVal);
        }
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = super.getInputPropertiesList();
		properties.add(input);
		properties.add(sensor);
		properties.add(zeroPctResetVal);
		properties.add(hundredPctResetVal);
		properties.add(resetAmount);
		return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
}
