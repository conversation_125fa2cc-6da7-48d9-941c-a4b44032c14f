/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Tail Operation used by Math blocks as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON> .K
 * @since Oct 31, 2017
 */

@NiagaraType

@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public abstract class BArithmetic extends BFunctionBlock {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.BArithmetic(2979906276)1.0$ @*/
/* Generated Thu Mar 01 18:37:44 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BArithmetic.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
	protected double doTailOperation(final double output, final BTailOperationEnum tailOperationEnum) {
		if(Double.compare(output, 0.0)==0) {
			return output;
		}
		
		double y = output;
		if (!(Double.isInfinite(y) && y > 0) && !Double.isNaN(y)) {
			y = applyTailOperation(y, tailOperationEnum);
		}
		
		if(Double.isNaN(y)) { 
			y = Double.POSITIVE_INFINITY;
		}

		return y;
	}

	private double applyTailOperation(final double value, final BTailOperationEnum tailOperationEnum) {
		switch (tailOperationEnum.getOrdinal()) {
		case BTailOperationEnum.ABSOLUTE:
			return Math.abs(value);
		case BTailOperationEnum.INTEGER:
			if (value < 0.0) 
				return Math.ceil(value);
			else 
				return Math.floor(value);
		case BTailOperationEnum.FRACTIONAL:
			return Math.abs(value)%1;
		default:
			return value;
		}
	}
	
}
