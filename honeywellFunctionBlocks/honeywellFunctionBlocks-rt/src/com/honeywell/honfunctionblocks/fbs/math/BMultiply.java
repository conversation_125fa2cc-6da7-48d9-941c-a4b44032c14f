/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;

/**
 * Implementation of Multiply as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Jan 29, 2018
 */

@NiagaraType
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S00103" })

@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_multiply.png\")", flags=Flags.HIDDEN|Flags.READONLY)

@NiagaraProperty(name = "x1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "ignoreInvalidInput", type="boolean", defaultValue="true")

@NiagaraProperty(name = "tailOperation", type="honeywellFunctionBlocks:TailOperationEnum", defaultValue="BTailOperationEnum.NoChange")

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })


public class BMultiply extends BArithmetic{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BMultiply(1532664904)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_multiply.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x1"

  /**
   * Slot for the {@code x1} property.
   * @see #getX1
   * @see #setX1
   */
  public static final Property x1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x1} property.
   * @see #x1
   */
  public BHonStatusNumeric getX1() { return (BHonStatusNumeric)get(x1); }

  /**
   * Set the {@code x1} property.
   * @see #x1
   */
  public void setX1(BHonStatusNumeric v) { set(x1, v, null); }

  //endregion Property "x1"

  //region Property "x2"

  /**
   * Slot for the {@code x2} property.
   * @see #getX2
   * @see #setX2
   */
  public static final Property x2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x2} property.
   * @see #x2
   */
  public BHonStatusNumeric getX2() { return (BHonStatusNumeric)get(x2); }

  /**
   * Set the {@code x2} property.
   * @see #x2
   */
  public void setX2(BHonStatusNumeric v) { set(x2, v, null); }

  //endregion Property "x2"

  //region Property "ignoreInvalidInput"

  /**
   * Slot for the {@code ignoreInvalidInput} property.
   * @see #getIgnoreInvalidInput
   * @see #setIgnoreInvalidInput
   */
  public static final Property ignoreInvalidInput = newProperty(0, true, null);

  /**
   * Get the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public boolean getIgnoreInvalidInput() { return getBoolean(ignoreInvalidInput); }

  /**
   * Set the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public void setIgnoreInvalidInput(boolean v) { setBoolean(ignoreInvalidInput, v, null); }

  //endregion Property "ignoreInvalidInput"

  //region Property "tailOperation"

  /**
   * Slot for the {@code tailOperation} property.
   * @see #getTailOperation
   * @see #setTailOperation
   */
  public static final Property tailOperation = newProperty(0, BTailOperationEnum.NoChange, null);

  /**
   * Get the {@code tailOperation} property.
   * @see #tailOperation
   */
  public BTailOperationEnum getTailOperation() { return (BTailOperationEnum)get(tailOperation); }

  /**
   * Set the {@code tailOperation} property.
   * @see #tailOperation
   */
  public void setTailOperation(BTailOperationEnum v) { set(tailOperation, v, null); }

  //endregion Property "tailOperation"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMultiply.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		double x1Val;
		double x2Val;
		
		boolean x1Invalid = false;
		boolean x2Invalid = false;
		
		if(!isConfigured(x1))
			x1Val = 0.0;
		else if(isInvalidValue(x1)) {
			x1Invalid = true;
			x1Val = 1.0;
		}else
			x1Val = getX1().getValue();
		
		if(!getIgnoreInvalidInput() && x1Invalid ) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		if(!isConfigured(x2))
			x2Val = 0.0;
		else if(isInvalidValue(x2)) {
			x2Invalid = true;
			x2Val = 1.0;
		}else
			x2Val = getX2().getValue();
		
		computeAndSetOutput(x1Invalid,x2Invalid,x1Val,x2Val);
	}

	private void computeAndSetOutput(boolean x1Invalid,boolean x2Invalid,double x1Val, double x2Val) {		
		if(!getIgnoreInvalidInput() && (x1Invalid || x2Invalid)) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		if(getIgnoreInvalidInput() && (x1Invalid && x2Invalid)) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		double result = x1Val * x2Val;
		result = doTailOperation(result, getTailOperation());
		getY().setValue(result);		
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = super.getInputPropertiesList();
		properties.add(x1);
		properties.add(x2);
		return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(Y);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(ignoreInvalidInput);
		properties.add(tailOperation);
		return properties;
	}
	
}
