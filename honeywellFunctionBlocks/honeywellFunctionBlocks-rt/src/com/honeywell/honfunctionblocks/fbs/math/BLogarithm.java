/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;
import javax.baja.util.BNameMap;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * Implementation of Logarithm block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405 
 * <AUTHOR> - Lavanya B.
 * @since Feb 15, 2018
 */

@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"math_func_logarithm.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@NiagaraProperty(name = "x", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0.000001f)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "eOR10", type="BLogTypeEnum", defaultValue="BLogTypeEnum.DEFAULT")

@NiagaraProperty(name = "tailOperation", type="honeywellFunctionBlocks:TailOperationEnum", defaultValue="BTailOperationEnum.NoChange")

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

@NiagaraProperty(name="displayNames", type="BNameMap", defaultValue = "BNameMap.make(BNameMap.DEFAULT, \"Y\", \"Y(NATURAL)\")", flags = Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.HIDDEN|Flags.OPERATOR) 


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BLogarithm extends BArithmetic {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BLogarithm(3226613543)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_logarithm.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0.000001f)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "eOR10"

  /**
   * Slot for the {@code eOR10} property.
   * @see #getEOR10
   * @see #setEOR10
   */
  public static final Property eOR10 = newProperty(0, BLogTypeEnum.DEFAULT, null);

  /**
   * Get the {@code eOR10} property.
   * @see #eOR10
   */
  public BLogTypeEnum getEOR10() { return (BLogTypeEnum)get(eOR10); }

  /**
   * Set the {@code eOR10} property.
   * @see #eOR10
   */
  public void setEOR10(BLogTypeEnum v) { set(eOR10, v, null); }

  //endregion Property "eOR10"

  //region Property "tailOperation"

  /**
   * Slot for the {@code tailOperation} property.
   * @see #getTailOperation
   * @see #setTailOperation
   */
  public static final Property tailOperation = newProperty(0, BTailOperationEnum.NoChange, null);

  /**
   * Get the {@code tailOperation} property.
   * @see #tailOperation
   */
  public BTailOperationEnum getTailOperation() { return (BTailOperationEnum)get(tailOperation); }

  /**
   * Set the {@code tailOperation} property.
   * @see #tailOperation
   */
  public void setTailOperation(BTailOperationEnum v) { set(tailOperation, v, null); }

  //endregion Property "tailOperation"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Property "displayNames"

  /**
   * Slot for the {@code displayNames} property.
   * @see #getDisplayNames
   * @see #setDisplayNames
   */
  public static final Property displayNames = newProperty(Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.HIDDEN | Flags.OPERATOR, BNameMap.make(BNameMap.DEFAULT, "Y", "Y(NATURAL)"), null);

  /**
   * Get the {@code displayNames} property.
   * @see #displayNames
   */
  public BNameMap getDisplayNames() { return (BNameMap)get(displayNames); }

  /**
   * Set the {@code displayNames} property.
   * @see #displayNames
   */
  public void setDisplayNames(BNameMap v) { set(displayNames, v, null); }

  //endregion Property "displayNames"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLogarithm.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		if(!isSlotValueValid(x)) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		double yVal;
		double xVal = getX().getValue(); 

		if(getEOR10().equals(BLogTypeEnum.Base10))
			yVal = Math.log10(xVal);
		else
			yVal = Math.log(xVal);	
		
		getY().setValue(doTailOperation(yVal, getTailOperation()));
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(Y);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(eOR10);
		propertyArrayList.add(tailOperation);
		return propertyArrayList;
	}

	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		changeLogTypeSlotDisplayName();
	}
	
	private void changeLogTypeSlotDisplayName() {
		BNameMap nameMap = getDisplayNames();
		if(getEOR10().equals(BLogTypeEnum.Natural))
			this.setDisplayNames(BNameMap.make(nameMap,Y.getName(),FunctionBlocksLexicon.getLexicon().getText("logarithm.displayname.ynatural", new String[] {})));
		else
			this.setDisplayNames(BNameMap.make(nameMap,Y.getName(),FunctionBlocksLexicon.getLexicon().getText("logarithm.displayname.ybase10", new String[] {})));
	}

	@Override
	public void changed(Property property, Context context) {
		if (!Sys.atSteadyState())
			return;
		if(property.equals(eOR10))
			changeLogTypeSlotDisplayName();
		
		super.changed(property, context);
		
	}

}
