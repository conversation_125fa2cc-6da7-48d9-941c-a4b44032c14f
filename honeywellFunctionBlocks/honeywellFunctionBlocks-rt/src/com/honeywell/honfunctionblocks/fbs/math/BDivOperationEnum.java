/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation of Divide block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya B.
 * @since Jan 31, 2018
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range("Divide"),
		@Range("Modulo"),
		}, defaultValue = "Divide")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class BDivOperationEnum extends BF<PERSON>zenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BDivOperationEnum(811574185)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Divide. */
  public static final int DIVIDE = 0;
  /** Ordinal value for Modulo. */
  public static final int MODULO = 1;

  /** BDivOperationEnum constant for Divide. */
  public static final BDivOperationEnum Divide = new BDivOperationEnum(DIVIDE);
  /** BDivOperationEnum constant for Modulo. */
  public static final BDivOperationEnum Modulo = new BDivOperationEnum(MODULO);

  /** Factory method with ordinal. */
  public static BDivOperationEnum make(int ordinal)
  {
    return (BDivOperationEnum)Divide.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BDivOperationEnum make(String tag)
  {
    return (BDivOperationEnum)Divide.getRange().get(tag);
  }

  /** Private constructor. */
  private BDivOperationEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BDivOperationEnum DEFAULT = Divide;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDivOperationEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
