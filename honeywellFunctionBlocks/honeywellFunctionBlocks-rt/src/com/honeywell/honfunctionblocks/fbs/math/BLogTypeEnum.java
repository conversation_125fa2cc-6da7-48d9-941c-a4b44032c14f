/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation of Logarithm block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON>vanya B.
 * @since Feb 15, 2018
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range("Natural"), 
		@Range("Base10"),
		}, defaultValue = "Natural")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class BLogTypeEnum extends BF<PERSON>zenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BLogTypeEnum(670250537)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Natural. */
  public static final int NATURAL = 0;
  /** Ordinal value for Base10. */
  public static final int BASE_10 = 1;

  /** BLogTypeEnum constant for Natural. */
  public static final BLogTypeEnum Natural = new BLogTypeEnum(NATURAL);
  /** BLogTypeEnum constant for Base10. */
  public static final BLogTypeEnum Base10 = new BLogTypeEnum(BASE_10);

  /** Factory method with ordinal. */
  public static BLogTypeEnum make(int ordinal)
  {
    return (BLogTypeEnum)Natural.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BLogTypeEnum make(String tag)
  {
    return (BLogTypeEnum)Natural.getRange().get(tag);
  }

  /** Private constructor. */
  private BLogTypeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BLogTypeEnum DEFAULT = Natural;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLogTypeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
