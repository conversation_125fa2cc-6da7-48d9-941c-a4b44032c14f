/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation of Math blocks as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Ravi Bharathi .K
 * @since Oct 31, 2017
 */

@NiagaraType
@NiagaraEnum (
	range = {
			@Range("NoChange"),
			@Range("Absolute"),
			@Range("Integer"),
			@Range("Fractional")
	},
	defaultValue="NoChange"
)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public final class BTailOperationEnum extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum(2890817118)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for NoChange. */
  public static final int NO_CHANGE = 0;
  /** Ordinal value for Absolute. */
  public static final int ABSOLUTE = 1;
  /** Ordinal value for Integer. */
  public static final int INTEGER = 2;
  /** Ordinal value for Fractional. */
  public static final int FRACTIONAL = 3;

  /** BTailOperationEnum constant for NoChange. */
  public static final BTailOperationEnum NoChange = new BTailOperationEnum(NO_CHANGE);
  /** BTailOperationEnum constant for Absolute. */
  public static final BTailOperationEnum Absolute = new BTailOperationEnum(ABSOLUTE);
  /** BTailOperationEnum constant for Integer. */
  public static final BTailOperationEnum Integer = new BTailOperationEnum(INTEGER);
  /** BTailOperationEnum constant for Fractional. */
  public static final BTailOperationEnum Fractional = new BTailOperationEnum(FRACTIONAL);

  /** Factory method with ordinal. */
  public static BTailOperationEnum make(int ordinal)
  {
    return (BTailOperationEnum)NoChange.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BTailOperationEnum make(String tag)
  {
    return (BTailOperationEnum)NoChange.getRange().get(tag);
  }

  /** Private constructor. */
  private BTailOperationEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BTailOperationEnum DEFAULT = NoChange;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTailOperationEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
