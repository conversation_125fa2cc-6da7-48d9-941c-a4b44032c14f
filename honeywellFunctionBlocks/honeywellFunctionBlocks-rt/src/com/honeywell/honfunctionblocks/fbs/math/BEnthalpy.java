/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation Enthalpy FB of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 22, 2018
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_enthalpy.png\")", flags=Flags.HIDDEN|Flags.READONLY)

@NiagaraProperty(name = "t", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(120)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.FAHRENHEIT)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "rth", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(100)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

public class BEnthalpy extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BEnthalpy(38181707)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_enthalpy.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "t"

  /**
   * Slot for the {@code t} property.
   * @see #getT
   * @see #setT
   */
  public static final Property t = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(120))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.FAHRENHEIT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code t} property.
   * @see #t
   */
  public BHonStatusNumeric getT() { return (BHonStatusNumeric)get(t); }

  /**
   * Set the {@code t} property.
   * @see #t
   */
  public void setT(BHonStatusNumeric v) { set(t, v, null); }

  //endregion Property "t"

  //region Property "rth"

  /**
   * Slot for the {@code rth} property.
   * @see #getRth
   * @see #setRth
   */
  public static final Property rth = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(100))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code rth} property.
   * @see #rth
   */
  public BHonStatusNumeric getRth() { return (BHonStatusNumeric)get(rth); }

  /**
   * Set the {@code rth} property.
   * @see #rth
   */
  public void setRth(BHonStatusNumeric v) { set(rth, v, null); }

  //endregion Property "rth"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEnthalpy.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeEnthaly();
	}
	
	private void executeEnthaly() {
		double pds;
		double temp;
		double rh;
		double absHum;
		double enth;
		double p;

		if (isAnySlotUnconfiguredOrInvalidWithIgnoreLimits(getInputPropertiesList())) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}

		double t1 = getT().getValue();
		double r1 = getRth().getValue();
		temp = limitInput(t1, 0, ONETWENTY, Double.POSITIVE_INFINITY);
		rh = limitInput(r1, 0, HUNDRED, Double.POSITIVE_INFINITY);

		double temp1 = K5 + temp / ONEEIGHTY;
		pds = K4 * Math.pow(temp1, K6);
		pds = pds / TEN;

		p = rh / HUNDRED * pds;
		absHum = K7 * p / (PRESS - p);
		enth = K1 * temp + absHum * (K2 + K3 * temp);
		getY().setValue(enth);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = super.getInputPropertiesList();
		properties.add(t);
		properties.add(rth);
		return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(Y);
		return properties;
	}
	   private static final double K1 = 0.2398;
	   private static final double K2 = 1061.37;
	   private static final double K3 = 0.4443;
	   private static final double K4 = 0.4204;
	   private static final double K5 = 0.9202;
	   private static final double K6 = 8.0;
	   private static final double PRESS = 14.7;
	   private static final double ONEEIGHTY = 180;
	   private static final double TEN = 10;
	   private static final double HUNDRED = 100;
	   private static final double K7 = 0.622;
	   private static final double ONETWENTY = 120;

}
