/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.ArrayList;
import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;

/**
 * Implementation of Exponential block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 24, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_exponential.png\")", flags=Flags.HIDDEN|Flags.READONLY)

@NiagaraProperty(name="x", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name="y", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="negInvalid", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.SUMMARY)

@NiagaraProperty(name = "tailOperation", type="honeywellFunctionBlocks:TailOperationEnum", defaultValue="BTailOperationEnum.NoChange")

@NiagaraProperty(name="Z", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S00103" })

public class BExponential extends BArithmetic{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BExponential(947952426)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_exponential.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "y"

  /**
   * Slot for the {@code y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property y = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code y} property.
   * @see #y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(y); }

  /**
   * Set the {@code y} property.
   * @see #y
   */
  public void setY(BHonStatusNumeric v) { set(y, v, null); }

  //endregion Property "y"

  //region Property "negInvalid"

  /**
   * Slot for the {@code negInvalid} property.
   * @see #getNegInvalid
   * @see #setNegInvalid
   */
  public static final Property negInvalid = newProperty(Flags.SUMMARY, new BHonStatusBoolean(false, BStatus.ok), null);

  /**
   * Get the {@code negInvalid} property.
   * @see #negInvalid
   */
  public BHonStatusBoolean getNegInvalid() { return (BHonStatusBoolean)get(negInvalid); }

  /**
   * Set the {@code negInvalid} property.
   * @see #negInvalid
   */
  public void setNegInvalid(BHonStatusBoolean v) { set(negInvalid, v, null); }

  //endregion Property "negInvalid"

  //region Property "tailOperation"

  /**
   * Slot for the {@code tailOperation} property.
   * @see #getTailOperation
   * @see #setTailOperation
   */
  public static final Property tailOperation = newProperty(0, BTailOperationEnum.NoChange, null);

  /**
   * Get the {@code tailOperation} property.
   * @see #tailOperation
   */
  public BTailOperationEnum getTailOperation() { return (BTailOperationEnum)get(tailOperation); }

  /**
   * Set the {@code tailOperation} property.
   * @see #tailOperation
   */
  public void setTailOperation(BTailOperationEnum v) { set(tailOperation, v, null); }

  //endregion Property "tailOperation"

  //region Property "Z"

  /**
   * Slot for the {@code Z} property.
   * @see #getZ
   * @see #setZ
   */
  public static final Property Z = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Z} property.
   * @see #Z
   */
  public BHonStatusNumeric getZ() { return (BHonStatusNumeric)get(Z); }

  /**
   * Set the {@code Z} property.
   * @see #Z
   */
  public void setZ(BHonStatusNumeric v) { set(Z, v, null); }

  //endregion Property "Z"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BExponential.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		double yValue = 0;
		if(isConfigured(y)) {
			yValue = getY().getValue();
		}
		
		if(!isConfigured(x)) {
			if(Double.compare(yValue, 0.0) != 0) {
				getZ().setValue(0);
			} else {
				getZ().setValue(doTailOperation(1, getTailOperation()));
			}
			
			return;
		}
		
		//If xValue (or) yValue is -inf (or) +inf (or) NaN, set the output as +inf
		double xValue = getX().getValue();
		if(!Double.isFinite(xValue) || !Double.isFinite(yValue)) {
			getZ().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		if((xValue < 0.0) && Double.compare(Math.floor(yValue), yValue)!=0) {
			if(isNegInvalidSet()) {
				getZ().setValue(Double.POSITIVE_INFINITY);
				return;
			}

			xValue = -xValue;
		}
		
		getZ().setValue(doTailOperation(Math.pow(xValue, yValue), getTailOperation()));
	}

	private boolean isNegInvalidSet() {
		if(isConfigured(negInvalid))
			return getNegInvalid().getBoolean();
		return true;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = new ArrayList<>();
		properties.add(x);
		properties.add(y);
		properties.add(negInvalid);
		properties.add(tailOperation);
		return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(Z);
		return propertyArrayList;		
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(tailOperation);
		return propertyArrayList;		
	}
	

}
