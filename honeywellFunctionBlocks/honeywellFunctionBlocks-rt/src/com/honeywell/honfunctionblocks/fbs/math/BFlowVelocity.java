/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Flow Velocity block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405 
 * <AUTHOR> - Lavanya B.
 * @since Feb 22, 2018
 */

@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"math_func_flow_velocity.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@NiagaraProperty(name = "press", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.INCHES_OF_WATER)"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "kFactor", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "autoSetOffset", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false,BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "clearOffset", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "area", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SQUARE_FOOT)"),
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "FLOW", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.CFM)"),
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

@NiagaraProperty(name = "OFFSET", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"),
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")  })

@NiagaraProperty(name = "VEL", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.FEET_PER_MIN)"),
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

@NiagaraProperty(name="lastSetOffset", type="boolean", defaultValue="false", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BFlowVelocity extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BFlowVelocity(725132727)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_flow_velocity.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "press"

  /**
   * Slot for the {@code press} property.
   * @see #getPress
   * @see #setPress
   */
  public static final Property press = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.INCHES_OF_WATER))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code press} property.
   * @see #press
   */
  public BHonStatusNumeric getPress() { return (BHonStatusNumeric)get(press); }

  /**
   * Set the {@code press} property.
   * @see #press
   */
  public void setPress(BHonStatusNumeric v) { set(press, v, null); }

  //endregion Property "press"

  //region Property "kFactor"

  /**
   * Slot for the {@code kFactor} property.
   * @see #getKFactor
   * @see #setKFactor
   */
  public static final Property kFactor = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code kFactor} property.
   * @see #kFactor
   */
  public BHonStatusNumeric getKFactor() { return (BHonStatusNumeric)get(kFactor); }

  /**
   * Set the {@code kFactor} property.
   * @see #kFactor
   */
  public void setKFactor(BHonStatusNumeric v) { set(kFactor, v, null); }

  //endregion Property "kFactor"

  //region Property "autoSetOffset"

  /**
   * Slot for the {@code autoSetOffset} property.
   * @see #getAutoSetOffset
   * @see #setAutoSetOffset
   */
  public static final Property autoSetOffset = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false,BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code autoSetOffset} property.
   * @see #autoSetOffset
   */
  public BFiniteStatusBoolean getAutoSetOffset() { return (BFiniteStatusBoolean)get(autoSetOffset); }

  /**
   * Set the {@code autoSetOffset} property.
   * @see #autoSetOffset
   */
  public void setAutoSetOffset(BFiniteStatusBoolean v) { set(autoSetOffset, v, null); }

  //endregion Property "autoSetOffset"

  //region Property "clearOffset"

  /**
   * Slot for the {@code clearOffset} property.
   * @see #getClearOffset
   * @see #setClearOffset
   */
  public static final Property clearOffset = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code clearOffset} property.
   * @see #clearOffset
   */
  public BFiniteStatusBoolean getClearOffset() { return (BFiniteStatusBoolean)get(clearOffset); }

  /**
   * Set the {@code clearOffset} property.
   * @see #clearOffset
   */
  public void setClearOffset(BFiniteStatusBoolean v) { set(clearOffset, v, null); }

  //endregion Property "clearOffset"

  //region Property "area"

  /**
   * Slot for the {@code area} property.
   * @see #getArea
   * @see #setArea
   */
  public static final Property area = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SQUARE_FOOT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code area} property.
   * @see #area
   */
  public BHonStatusNumeric getArea() { return (BHonStatusNumeric)get(area); }

  /**
   * Set the {@code area} property.
   * @see #area
   */
  public void setArea(BHonStatusNumeric v) { set(area, v, null); }

  //endregion Property "area"

  //region Property "FLOW"

  /**
   * Slot for the {@code FLOW} property.
   * @see #getFLOW
   * @see #setFLOW
   */
  public static final Property FLOW = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.CFM))), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code FLOW} property.
   * @see #FLOW
   */
  public BHonStatusNumeric getFLOW() { return (BHonStatusNumeric)get(FLOW); }

  /**
   * Set the {@code FLOW} property.
   * @see #FLOW
   */
  public void setFLOW(BHonStatusNumeric v) { set(FLOW, v, null); }

  //endregion Property "FLOW"

  //region Property "OFFSET"

  /**
   * Slot for the {@code OFFSET} property.
   * @see #getOFFSET
   * @see #setOFFSET
   */
  public static final Property OFFSET = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE)));

  /**
   * Get the {@code OFFSET} property.
   * @see #OFFSET
   */
  public BHonStatusNumeric getOFFSET() { return (BHonStatusNumeric)get(OFFSET); }

  /**
   * Set the {@code OFFSET} property.
   * @see #OFFSET
   */
  public void setOFFSET(BHonStatusNumeric v) { set(OFFSET, v, null); }

  //endregion Property "OFFSET"

  //region Property "VEL"

  /**
   * Slot for the {@code VEL} property.
   * @see #getVEL
   * @see #setVEL
   */
  public static final Property VEL = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.FEET_PER_MIN))), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code VEL} property.
   * @see #VEL
   */
  public BHonStatusNumeric getVEL() { return (BHonStatusNumeric)get(VEL); }

  /**
   * Set the {@code VEL} property.
   * @see #VEL
   */
  public void setVEL(BHonStatusNumeric v) { set(VEL, v, null); }

  //endregion Property "VEL"

  //region Property "lastSetOffset"

  /**
   * Slot for the {@code lastSetOffset} property.
   * @see #getLastSetOffset
   * @see #setLastSetOffset
   */
  public static final Property lastSetOffset = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code lastSetOffset} property.
   * @see #lastSetOffset
   */
  public boolean getLastSetOffset() { return getBoolean(lastSetOffset); }

  /**
   * Set the {@code lastSetOffset} property.
   * @see #lastSetOffset
   */
  public void setLastSetOffset(boolean v) { setBoolean(lastSetOffset, v, null); }

  //endregion Property "lastSetOffset"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowVelocity.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeFlowVelocity();
	}
	
	private void executeFlowVelocity() {
		double areaValue = Double.POSITIVE_INFINITY;
		double flowValue = Double.POSITIVE_INFINITY;
		
		double offsetValue;
		boolean autoOffsetValue = getOffsetFlagValue(autoSetOffset);
		
		if(isSlotValueValid(press)) {
			double pressureValue = getPress().getValue();
			
			if(autoOffsetValue && !getLastSetOffset()) {
				getOFFSET().setValue(pressureValue);			
			}		
			
			if(isSlotValueValid(kFactor)) {
				double kFactorValue = getKFactorValue();
				offsetValue = getOFFSET().getValue();
				flowValue = computeFlow(pressureValue,offsetValue,kFactorValue);
				areaValue = computeVelocity(flowValue);
			}
		}
		
		setLastSetOffset(autoOffsetValue);
		boolean isClearOffset = getOffsetFlagValue(clearOffset);
		if(isClearOffset) {
			getOFFSET().setValue(0.0);	
		}
		getFLOW().setValue(flowValue);
		getVEL().setValue(areaValue);
	}
	
	private double computeFlow(double pressureValue,double offsetValue,double kFactorValue) {
		double flowValue;
		double pressureDelta = pressureValue-offsetValue;
		if(pressureDelta < PLUS_MIN_PRESSURE_DELTA && 
				pressureDelta > MINUS_MIN_PRESSURE_DELTA)
				return 0.0;
		
		if(pressureDelta < 0) {
			flowValue = -(Math.sqrt(offsetValue-pressureValue) * kFactorValue);
		}else {
			flowValue = Math.sqrt(pressureDelta) * kFactorValue;
		}
		
		if(Double.isNaN(flowValue))
			flowValue = Double.POSITIVE_INFINITY;
		
		return flowValue;
	}
	
	private double getKFactorValue() {
		double kFactorValue = getKFactor().getValue();
		if (kFactorValue <=0)
			kFactorValue=K_FACTOR_DEF_VALUE;
		
		return kFactorValue;
	}
	
	private double computeVelocity(double flowValue) {
		double areaValue = getArea().getValue();
		if(!isSlotValueValid(area) || areaValue<=0.0)
			return Double.POSITIVE_INFINITY;
		
		return flowValue/areaValue;
	}
	
	private boolean getOffsetFlagValue(Property property) {
		boolean val = false;
		if(isConfigured(property)) {
			BFiniteStatusBoolean bval = (BFiniteStatusBoolean) get(property);
			val = bval.getValue();
		}
		
		return val;
	}

	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		setLastSetOffset(true);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = super.getInputPropertiesList();
		properties.add(press);
		properties.add(kFactor);
		properties.add(autoSetOffset);
		properties.add(clearOffset);
		properties.add(area);
		return properties;
	}
	
	@Override
		public List<Property> getOutputPropertiesList() {
			List<Property> properties = super.getOutputPropertiesList();
			properties.add(OFFSET);
			properties.add(FLOW);
			properties.add(VEL);
			return properties;
		}

	private static final double PLUS_MIN_PRESSURE_DELTA = 0.002425;
	private static final double MINUS_MIN_PRESSURE_DELTA = -0.002425;
	private static final double K_FACTOR_DEF_VALUE = 1015.0;

}
