/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Enum created as part of Ratio block implementation. 
 * This is used for config slot 'operation' 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 9, 2018
 */
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

@NiagaraType
@NiagaraEnum(range = {
		@Range("Unlimited"),
		@Range("Vav_Flow_Bal"),
		@Range("Endpt_Limited")
		}, 
defaultValue = "Endpt_Limited")


public final class BRatioOperationEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.enums.BRatioOperationEnum(3875883316)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Unlimited. */
  public static final int UNLIMITED = 0;
  /** Ordinal value for Vav_Flow_Bal. */
  public static final int VAV_FLOW_BAL = 1;
  /** Ordinal value for Endpt_Limited. */
  public static final int ENDPT_LIMITED = 2;

  /** BRatioOperationEnum constant for Unlimited. */
  public static final BRatioOperationEnum Unlimited = new BRatioOperationEnum(UNLIMITED);
  /** BRatioOperationEnum constant for Vav_Flow_Bal. */
  public static final BRatioOperationEnum Vav_Flow_Bal = new BRatioOperationEnum(VAV_FLOW_BAL);
  /** BRatioOperationEnum constant for Endpt_Limited. */
  public static final BRatioOperationEnum Endpt_Limited = new BRatioOperationEnum(ENDPT_LIMITED);

  /** Factory method with ordinal. */
  public static BRatioOperationEnum make(int ordinal)
  {
    return (BRatioOperationEnum)Unlimited.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BRatioOperationEnum make(String tag)
  {
    return (BRatioOperationEnum)Unlimited.getRange().get(tag);
  }

  /** Private constructor. */
  private BRatioOperationEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BRatioOperationEnum DEFAULT = Endpt_Limited;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRatioOperationEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
