/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;

/**
 * Implementation of Add block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 31, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_add.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "x1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x5", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x6", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x7", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "x8", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "ignoreInvalidInput", type="boolean", defaultValue="true")

@NiagaraProperty(name = "tailOperation", type="honeywellFunctionBlocks:TailOperationEnum", defaultValue="BTailOperationEnum.NoChange")

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BAdd extends BArithmetic {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BAdd(297130111)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_add.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x1"

  /**
   * Slot for the {@code x1} property.
   * @see #getX1
   * @see #setX1
   */
  public static final Property x1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x1} property.
   * @see #x1
   */
  public BHonStatusNumeric getX1() { return (BHonStatusNumeric)get(x1); }

  /**
   * Set the {@code x1} property.
   * @see #x1
   */
  public void setX1(BHonStatusNumeric v) { set(x1, v, null); }

  //endregion Property "x1"

  //region Property "x2"

  /**
   * Slot for the {@code x2} property.
   * @see #getX2
   * @see #setX2
   */
  public static final Property x2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x2} property.
   * @see #x2
   */
  public BHonStatusNumeric getX2() { return (BHonStatusNumeric)get(x2); }

  /**
   * Set the {@code x2} property.
   * @see #x2
   */
  public void setX2(BHonStatusNumeric v) { set(x2, v, null); }

  //endregion Property "x2"

  //region Property "x3"

  /**
   * Slot for the {@code x3} property.
   * @see #getX3
   * @see #setX3
   */
  public static final Property x3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x3} property.
   * @see #x3
   */
  public BHonStatusNumeric getX3() { return (BHonStatusNumeric)get(x3); }

  /**
   * Set the {@code x3} property.
   * @see #x3
   */
  public void setX3(BHonStatusNumeric v) { set(x3, v, null); }

  //endregion Property "x3"

  //region Property "x4"

  /**
   * Slot for the {@code x4} property.
   * @see #getX4
   * @see #setX4
   */
  public static final Property x4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x4} property.
   * @see #x4
   */
  public BHonStatusNumeric getX4() { return (BHonStatusNumeric)get(x4); }

  /**
   * Set the {@code x4} property.
   * @see #x4
   */
  public void setX4(BHonStatusNumeric v) { set(x4, v, null); }

  //endregion Property "x4"

  //region Property "x5"

  /**
   * Slot for the {@code x5} property.
   * @see #getX5
   * @see #setX5
   */
  public static final Property x5 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x5} property.
   * @see #x5
   */
  public BHonStatusNumeric getX5() { return (BHonStatusNumeric)get(x5); }

  /**
   * Set the {@code x5} property.
   * @see #x5
   */
  public void setX5(BHonStatusNumeric v) { set(x5, v, null); }

  //endregion Property "x5"

  //region Property "x6"

  /**
   * Slot for the {@code x6} property.
   * @see #getX6
   * @see #setX6
   */
  public static final Property x6 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x6} property.
   * @see #x6
   */
  public BHonStatusNumeric getX6() { return (BHonStatusNumeric)get(x6); }

  /**
   * Set the {@code x6} property.
   * @see #x6
   */
  public void setX6(BHonStatusNumeric v) { set(x6, v, null); }

  //endregion Property "x6"

  //region Property "x7"

  /**
   * Slot for the {@code x7} property.
   * @see #getX7
   * @see #setX7
   */
  public static final Property x7 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x7} property.
   * @see #x7
   */
  public BHonStatusNumeric getX7() { return (BHonStatusNumeric)get(x7); }

  /**
   * Set the {@code x7} property.
   * @see #x7
   */
  public void setX7(BHonStatusNumeric v) { set(x7, v, null); }

  //endregion Property "x7"

  //region Property "x8"

  /**
   * Slot for the {@code x8} property.
   * @see #getX8
   * @see #setX8
   */
  public static final Property x8 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x8} property.
   * @see #x8
   */
  public BHonStatusNumeric getX8() { return (BHonStatusNumeric)get(x8); }

  /**
   * Set the {@code x8} property.
   * @see #x8
   */
  public void setX8(BHonStatusNumeric v) { set(x8, v, null); }

  //endregion Property "x8"

  //region Property "ignoreInvalidInput"

  /**
   * Slot for the {@code ignoreInvalidInput} property.
   * @see #getIgnoreInvalidInput
   * @see #setIgnoreInvalidInput
   */
  public static final Property ignoreInvalidInput = newProperty(0, true, null);

  /**
   * Get the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public boolean getIgnoreInvalidInput() { return getBoolean(ignoreInvalidInput); }

  /**
   * Set the {@code ignoreInvalidInput} property.
   * @see #ignoreInvalidInput
   */
  public void setIgnoreInvalidInput(boolean v) { setBoolean(ignoreInvalidInput, v, null); }

  //endregion Property "ignoreInvalidInput"

  //region Property "tailOperation"

  /**
   * Slot for the {@code tailOperation} property.
   * @see #getTailOperation
   * @see #setTailOperation
   */
  public static final Property tailOperation = newProperty(0, BTailOperationEnum.NoChange, null);

  /**
   * Get the {@code tailOperation} property.
   * @see #tailOperation
   */
  public BTailOperationEnum getTailOperation() { return (BTailOperationEnum)get(tailOperation); }

  /**
   * Set the {@code tailOperation} property.
   * @see #tailOperation
   */
  public void setTailOperation(BTailOperationEnum v) { set(tailOperation, v, null); }

  //endregion Property "tailOperation"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAdd.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		if(condition1() || condition2()) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		double result = calculateOutput();
		getY().setValue(doTailOperation(result, getTailOperation()));
	}
	
	private boolean condition1() {
		return !getIgnoreInvalidInput() && hasAnyInvalidInput();
	}
	
	private boolean condition2() {
		return  getIgnoreInvalidInput() && isAllInputsInvalid();
	}

	private double calculateOutput() {
		double result = 0.0;
		//  Use only valid values
		if (isSlotValueValid(x1)) 
			result += getX1().getValue();
		if (isSlotValueValid(x2)) 
			result += getX2().getValue();
		if (isSlotValueValid(x3)) 
			result += getX3().getValue();
		if (isSlotValueValid(x4)) 
			result += getX4().getValue();
		if (isSlotValueValid(x5)) 
			result += getX5().getValue();
		if (isSlotValueValid(x6)) 
			result += getX6().getValue();
		if (isSlotValueValid(x7)) 
			result += getX7().getValue();
		if (isSlotValueValid(x8)) 
			result += getX8().getValue();
		
		return result;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x1);
		propertyArrayList.add(x2);
		propertyArrayList.add(x3);
		propertyArrayList.add(x4);
		propertyArrayList.add(x5);
		propertyArrayList.add(x6);
		propertyArrayList.add(x7);
		propertyArrayList.add(x8);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(Y);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(ignoreInvalidInput);
		propertyArrayList.add(tailOperation);
		return propertyArrayList;
	}

}
