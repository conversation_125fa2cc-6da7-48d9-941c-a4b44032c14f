/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;

/**
 * Implementation of Limit block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya B.
 * @since Feb 19, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"math_func_limit.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "x", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "hiLimit", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "loLimit", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BLimit extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BLimit(1734205618)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "math_func_limit.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "hiLimit"

  /**
   * Slot for the {@code hiLimit} property.
   * @see #getHiLimit
   * @see #setHiLimit
   */
  public static final Property hiLimit = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code hiLimit} property.
   * @see #hiLimit
   */
  public BHonStatusNumeric getHiLimit() { return (BHonStatusNumeric)get(hiLimit); }

  /**
   * Set the {@code hiLimit} property.
   * @see #hiLimit
   */
  public void setHiLimit(BHonStatusNumeric v) { set(hiLimit, v, null); }

  //endregion Property "hiLimit"

  //region Property "loLimit"

  /**
   * Slot for the {@code loLimit} property.
   * @see #getLoLimit
   * @see #setLoLimit
   */
  public static final Property loLimit = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code loLimit} property.
   * @see #loLimit
   */
  public BHonStatusNumeric getLoLimit() { return (BHonStatusNumeric)get(loLimit); }

  /**
   * Set the {@code loLimit} property.
   * @see #loLimit
   */
  public void setLoLimit(BHonStatusNumeric v) { set(loLimit, v, null); }

  //endregion Property "loLimit"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLimit.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		if(!isSlotValueValid(x)) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;			
		}
		
		double xVal = getX().getValue();
		double yVal = xVal;
		double loLimitVal = getLimitInputValue(loLimit);
		if(!LimitCheckUtil.isInvalidValue(loLimitVal) && (xVal < loLimitVal)) {
				yVal = loLimitVal;
		}
		
		double hiLimitVal = getLimitInputValue(hiLimit);
		if(!LimitCheckUtil.isInvalidValue(hiLimitVal) && (xVal > hiLimitVal)) {
				yVal = hiLimitVal;
		}		
		
		if(!LimitCheckUtil.isInvalidValue(loLimitVal) && 
			!LimitCheckUtil.isInvalidValue(hiLimitVal) &&
			(loLimitVal >  hiLimitVal)){
			yVal = xVal;
		}
		
		getY().setValue(yVal);
	}
	
	private double getLimitInputValue(Property property) {
	  double val = Double.POSITIVE_INFINITY;
	  if(isSlotValueValid(property)) {
		  BHonStatusNumeric bval = (BHonStatusNumeric) get(property);
		  val = bval.getValue();
	  }  
	  return val;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x);
		propertyArrayList.add(loLimit);
		propertyArrayList.add(hiLimit);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(Y);
		return properties;
	}
	
}
