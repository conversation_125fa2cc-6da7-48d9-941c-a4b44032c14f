/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;

/**
 * Implementation of Digital Filter block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya B.
 * @since Feb 23, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"digital_filter.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "x", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "tau", type = "double", defaultValue = "1.0", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "1"),
		@Facet(name = "BFacets.MAX", value = "65535"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "zeroInit", type = "int", defaultValue = "0", 
facets = { @Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "1"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.INT_UX_FE")	})

@NiagaraProperty(name = "Y", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.TRANSIENT|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

//LoopStaticVariables
@NiagaraProperty(name="tauMult", type="double", defaultValue="Double.NaN", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BDigitalFilter extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.BDigitalFilter(2354875011)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "digital_filter.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "tau"

  /**
   * Slot for the {@code tau} property.
   * @see #getTau
   * @see #setTau
   */
  public static final Property tau = newProperty(Flags.SUMMARY, 1.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, 65535)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code tau} property.
   * @see #tau
   */
  public double getTau() { return getDouble(tau); }

  /**
   * Set the {@code tau} property.
   * @see #tau
   */
  public void setTau(double v) { setDouble(tau, v, null); }

  //endregion Property "tau"

  //region Property "zeroInit"

  /**
   * Slot for the {@code zeroInit} property.
   * @see #getZeroInit
   * @see #setZeroInit
   */
  public static final Property zeroInit = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 1)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.INT_UX_FE)));

  /**
   * Get the {@code zeroInit} property.
   * @see #zeroInit
   */
  public int getZeroInit() { return getInt(zeroInit); }

  /**
   * Set the {@code zeroInit} property.
   * @see #zeroInit
   */
  public void setZeroInit(int v) { setInt(zeroInit, v, null); }

  //endregion Property "zeroInit"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.TRANSIENT | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * @see #Y
   */
  public BHonStatusNumeric getY() { return (BHonStatusNumeric)get(Y); }

  /**
   * Set the {@code Y} property.
   * @see #Y
   */
  public void setY(BHonStatusNumeric v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Property "tauMult"

  /**
   * Slot for the {@code tauMult} property.
   * LoopStaticVariables
   * @see #getTauMult
   * @see #setTauMult
   */
  public static final Property tauMult = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, Double.NaN, null);

  /**
   * Get the {@code tauMult} property.
   * LoopStaticVariables
   * @see #tauMult
   */
  public double getTauMult() { return getDouble(tauMult); }

  /**
   * Set the {@code tauMult} property.
   * LoopStaticVariables
   * @see #tauMult
   */
  public void setTauMult(double v) { setDouble(tauMult, v, null); }

  //endregion Property "tauMult"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDigitalFilter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeDigitalFiler(executionParams);
	}
	
	private void executeDigitalFiler(BExecutionParams executionParams) {
		if(!isSlotValueValid(x)) {
			getY().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		if(Double.isNaN(getTauMult())) {
			calculateTauMultiplier(executionParams.getIterationInterval());
		}
		
		double lastOut = getY().getValue();
		double xVal = getX().getValue();
		double out;
		
		if(LimitCheckUtil.isInvalidValue(lastOut)) {
			if(getZeroInit() > 0) {
				lastOut = 0.0;
			}else {
				lastOut = xVal;
			}
		}
		
		out = lastOut + getTauMult() * (xVal - lastOut);
		
		if(Double.isNaN(out)) {
			getY().setValue(Double.POSITIVE_INFINITY);
		}else {
			getY().setValue(out);
		}
	}

	private void calculateTauMultiplier(double iterartionInterval) {
		double tauValue = getTau();
		double tauTemp = limitInput(tauValue,TAU_LOW_LIMIT,TAU_HIGH_LIMIT,TAU_INVALID);
		double iterationIntervalInSec = iterartionInterval/UnitConstants.THOUSAND_MILLI_SECOND;
		tauTemp = -1.0 * (iterationIntervalInSec/tauTemp);
		setTauMult(1.0-Math.exp(tauTemp));
	}
	
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x);
		propertyArrayList.add(tau);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(Y);
		return propertyArrayList;
	}

	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(zeroInit);
		return propertyArrayList;
	}	
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		int iterationInterval = executionParams.getIterationInterval();
		calculateTauMultiplier(iterationInterval);
	}
	
	@Override
	public void changed(Property property, Context context) {
		if (!Sys.atSteadyState())
			return;
		if(property.getName().equals(tau.getName())) { 
			setTauMult(Double.NaN);
		}
		super.changed(property, context);
	}

	private static final double TAU_LOW_LIMIT = 1.0d;
	private static final double TAU_HIGH_LIMIT = 65535.0d;
	private static final double TAU_INVALID = 0.05d;
}
