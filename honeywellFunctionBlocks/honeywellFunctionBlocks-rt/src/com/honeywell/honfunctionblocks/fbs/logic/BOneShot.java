/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of OneShot block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 30, 2017
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"one_shot.png\")", flags=Flags.HIDDEN|Flags.READONLY)
//Input slots
@NiagaraProperty(name="x", type="BNegatableHonStatusNumeric", defaultValue="new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false)", flags=Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="onTime", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(65535)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0")
})

//Output slots
@NiagaraProperty(name="Y", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.TRANSIENT|Flags.DEFAULT_ON_CLONE,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})

//LoopStaticVariables
@NiagaraProperty(name="holdTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="lastInput", type="boolean", defaultValue="false", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103"})

public class BOneShot extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.logic.BOneShot(642778688)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "one_shot.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * Input slots
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BNegatableHonStatusNumeric(0.0F, BStatus.nullStatus, false), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * Input slots
   * @see #x
   */
  public BNegatableHonStatusNumeric getX() { return (BNegatableHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * Input slots
   * @see #x
   */
  public void setX(BNegatableHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "onTime"

  /**
   * Slot for the {@code onTime} property.
   * @see #getOnTime
   * @see #setOnTime
   */
  public static final Property onTime = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65535))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code onTime} property.
   * @see #onTime
   */
  public BHonStatusNumeric getOnTime() { return (BHonStatusNumeric)get(onTime); }

  /**
   * Set the {@code onTime} property.
   * @see #onTime
   */
  public void setOnTime(BHonStatusNumeric v) { set(onTime, v, null); }

  //endregion Property "onTime"

  //region Property "Y"

  /**
   * Slot for the {@code Y} property.
   * Output slots
   * @see #getY
   * @see #setY
   */
  public static final Property Y = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.TRANSIENT | Flags.DEFAULT_ON_CLONE, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y} property.
   * Output slots
   * @see #Y
   */
  public BNegatableStatusBoolean getY() { return (BNegatableStatusBoolean)get(Y); }

  /**
   * Set the {@code Y} property.
   * Output slots
   * @see #Y
   */
  public void setY(BNegatableStatusBoolean v) { set(Y, v, null); }

  //endregion Property "Y"

  //region Property "holdTimer"

  /**
   * Slot for the {@code holdTimer} property.
   * LoopStaticVariables
   * @see #getHoldTimer
   * @see #setHoldTimer
   */
  public static final Property holdTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code holdTimer} property.
   * LoopStaticVariables
   * @see #holdTimer
   */
  public double getHoldTimer() { return getDouble(holdTimer); }

  /**
   * Set the {@code holdTimer} property.
   * LoopStaticVariables
   * @see #holdTimer
   */
  public void setHoldTimer(double v) { setDouble(holdTimer, v, null); }

  //endregion Property "holdTimer"

  //region Property "lastInput"

  /**
   * Slot for the {@code lastInput} property.
   * @see #getLastInput
   * @see #setLastInput
   */
  public static final Property lastInput = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code lastInput} property.
   * @see #lastInput
   */
  public boolean getLastInput() { return getBoolean(lastInput); }

  /**
   * Set the {@code lastInput} property.
   * @see #lastInput
   */
  public void setLastInput(boolean v) { setBoolean(lastInput, v, null); }

  //endregion Property "lastInput"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOneShot.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		setHoldTimer(0.0);
		setLastInput(false);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		double iterationIntervalInSec = (double)executionParams.getIterationInterval()/UnitConstants.THOUSAND_MILLI_SECOND;
		computeOneShotValue(iterationIntervalInSec);
		
		boolean outVal = Double.compare(getHoldTimer(), 0.0) !=0;
		if(getY().getNegate())
			outVal = !outVal;
		getY().setValue(outVal);
	}
	
	private void computeOneShotValue(double iterationInterval) {
		if(!isSlotValueValid(x)) {
			setHoldTimer(0);
			setLastInput(false);
			return;
		}
		
		double onTimeVal = getOnTimeAfterValidate();
		double inVal = getX().getValue();
		boolean bInVal = Double.compare(inVal, 0.0)!=0;
		if(!getX().getNegate())
			bInVal = !bInVal;
		
		boolean edge = getLastInput() && !bInVal;
		setLastInput(bInVal);
		
		if(!edge && (Double.compare(getHoldTimer(),0.0)==0)) 
			return;
		
		if(edge)
			setHoldTimer(0.0);
		
		if(Double.compare(getHoldTimer(),onTimeVal) < 0) {
			double d = getHoldTimer() + iterationInterval;
			setHoldTimer(d);
			return;
		}
		
		setHoldTimer(0);
	}

	private double getOnTimeAfterValidate() {
		if(!isSlotValueValid(onTime)) {
			return 0;
		}
		
		double onTimeVal = getOnTime().getValue();
		onTimeVal = limitInput(onTimeVal,0.0,ON_TIME_LIMIT,0.0);
		return onTimeVal;
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x);
		propertyArrayList.add(onTime);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(Y);
		return properties;
	}
	
	private static final double ON_TIME_LIMIT = 65535.0;
	
}
