/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Or & Xor block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 28, 2017
 */
@NiagaraType
//Input slots
@NiagaraProperty(name="in1", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="in2", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="in3", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="in4", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="in5", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="in6", type="BNegatableFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="trueDelay", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(32767)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0")
})
@NiagaraProperty(name="falseDelay", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(32767)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0")
})

//Output slots
@NiagaraProperty(name="OUTPUT", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.TRANSIENT|Flags.DEFAULT_ON_CLONE,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})

//LoopStaticVariables
@NiagaraProperty(name="delayServed", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="lastOutput", type="boolean", defaultValue="false", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public abstract class BLogicBlock extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.logic.BLogicBlock(1206517314)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "in1"

  /**
   * Slot for the {@code in1} property.
   * Input slots
   * @see #getIn1
   * @see #setIn1
   */
  public static final Property in1 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in1} property.
   * Input slots
   * @see #in1
   */
  public BNegatableFiniteStatusBoolean getIn1() { return (BNegatableFiniteStatusBoolean)get(in1); }

  /**
   * Set the {@code in1} property.
   * Input slots
   * @see #in1
   */
  public void setIn1(BNegatableFiniteStatusBoolean v) { set(in1, v, null); }

  //endregion Property "in1"

  //region Property "in2"

  /**
   * Slot for the {@code in2} property.
   * @see #getIn2
   * @see #setIn2
   */
  public static final Property in2 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in2} property.
   * @see #in2
   */
  public BNegatableFiniteStatusBoolean getIn2() { return (BNegatableFiniteStatusBoolean)get(in2); }

  /**
   * Set the {@code in2} property.
   * @see #in2
   */
  public void setIn2(BNegatableFiniteStatusBoolean v) { set(in2, v, null); }

  //endregion Property "in2"

  //region Property "in3"

  /**
   * Slot for the {@code in3} property.
   * @see #getIn3
   * @see #setIn3
   */
  public static final Property in3 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in3} property.
   * @see #in3
   */
  public BNegatableFiniteStatusBoolean getIn3() { return (BNegatableFiniteStatusBoolean)get(in3); }

  /**
   * Set the {@code in3} property.
   * @see #in3
   */
  public void setIn3(BNegatableFiniteStatusBoolean v) { set(in3, v, null); }

  //endregion Property "in3"

  //region Property "in4"

  /**
   * Slot for the {@code in4} property.
   * @see #getIn4
   * @see #setIn4
   */
  public static final Property in4 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in4} property.
   * @see #in4
   */
  public BNegatableFiniteStatusBoolean getIn4() { return (BNegatableFiniteStatusBoolean)get(in4); }

  /**
   * Set the {@code in4} property.
   * @see #in4
   */
  public void setIn4(BNegatableFiniteStatusBoolean v) { set(in4, v, null); }

  //endregion Property "in4"

  //region Property "in5"

  /**
   * Slot for the {@code in5} property.
   * @see #getIn5
   * @see #setIn5
   */
  public static final Property in5 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in5} property.
   * @see #in5
   */
  public BNegatableFiniteStatusBoolean getIn5() { return (BNegatableFiniteStatusBoolean)get(in5); }

  /**
   * Set the {@code in5} property.
   * @see #in5
   */
  public void setIn5(BNegatableFiniteStatusBoolean v) { set(in5, v, null); }

  //endregion Property "in5"

  //region Property "in6"

  /**
   * Slot for the {@code in6} property.
   * @see #getIn6
   * @see #setIn6
   */
  public static final Property in6 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code in6} property.
   * @see #in6
   */
  public BNegatableFiniteStatusBoolean getIn6() { return (BNegatableFiniteStatusBoolean)get(in6); }

  /**
   * Set the {@code in6} property.
   * @see #in6
   */
  public void setIn6(BNegatableFiniteStatusBoolean v) { set(in6, v, null); }

  //endregion Property "in6"

  //region Property "trueDelay"

  /**
   * Slot for the {@code trueDelay} property.
   * @see #getTrueDelay
   * @see #setTrueDelay
   */
  public static final Property trueDelay = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(32767))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code trueDelay} property.
   * @see #trueDelay
   */
  public BHonStatusNumeric getTrueDelay() { return (BHonStatusNumeric)get(trueDelay); }

  /**
   * Set the {@code trueDelay} property.
   * @see #trueDelay
   */
  public void setTrueDelay(BHonStatusNumeric v) { set(trueDelay, v, null); }

  //endregion Property "trueDelay"

  //region Property "falseDelay"

  /**
   * Slot for the {@code falseDelay} property.
   * @see #getFalseDelay
   * @see #setFalseDelay
   */
  public static final Property falseDelay = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(32767))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code falseDelay} property.
   * @see #falseDelay
   */
  public BHonStatusNumeric getFalseDelay() { return (BHonStatusNumeric)get(falseDelay); }

  /**
   * Set the {@code falseDelay} property.
   * @see #falseDelay
   */
  public void setFalseDelay(BHonStatusNumeric v) { set(falseDelay, v, null); }

  //endregion Property "falseDelay"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * Output slots
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.TRANSIENT | Flags.DEFAULT_ON_CLONE, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * Output slots
   * @see #OUTPUT
   */
  public BNegatableStatusBoolean getOUTPUT() { return (BNegatableStatusBoolean)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * Output slots
   * @see #OUTPUT
   */
  public void setOUTPUT(BNegatableStatusBoolean v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "delayServed"

  /**
   * Slot for the {@code delayServed} property.
   * LoopStaticVariables
   * @see #getDelayServed
   * @see #setDelayServed
   */
  public static final Property delayServed = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code delayServed} property.
   * LoopStaticVariables
   * @see #delayServed
   */
  public double getDelayServed() { return getDouble(delayServed); }

  /**
   * Set the {@code delayServed} property.
   * LoopStaticVariables
   * @see #delayServed
   */
  public void setDelayServed(double v) { setDouble(delayServed, v, null); }

  //endregion Property "delayServed"

  //region Property "lastOutput"

  /**
   * Slot for the {@code lastOutput} property.
   * @see #getLastOutput
   * @see #setLastOutput
   */
  public static final Property lastOutput = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code lastOutput} property.
   * @see #lastOutput
   */
  public boolean getLastOutput() { return getBoolean(lastOutput); }

  /**
   * Set the {@code lastOutput} property.
   * @see #lastOutput
   */
  public void setLastOutput(boolean v) { setBoolean(lastOutput, v, null); }

  //endregion Property "lastOutput"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLogicBlock.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default constructor
	 */
	public BLogicBlock() {
		//EMPTY IMPLEMENTATION
	}
	
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		setDelayServed(0);
  		setLastOutput(false);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		double iterationInterval = executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND;

		boolean computedOutput = computeOutput();		
		
		if(getOUTPUT().getNegate())
			computedOutput = !computedOutput;
		
		boolean actualOutput = computeOutputWithDelay(computedOutput,iterationInterval);
		getOUTPUT().setValue(actualOutput);
	}
	
	protected abstract boolean computeOutput();

	/**
	 * Get the value of given 'in' slot after validation like configured?,applied Negate 
	 * @param input
	 * @return
	 */
	boolean getValueAfterValidationAndNegateApplied(BNegatableFiniteStatusBoolean input) {
		boolean inputVal = false;
		
		if(isConfigured(getProperty(input.getName()))) {
			inputVal = input.getValue();		
			if(!input.getIsInvalidValue() && input.getNegate())
				inputVal = !inputVal;
		}
		
		return inputVal;
	}
	
	/**
	 * Compute the output with delay applied
	 * @param computedOutput
	 * @param iterationInterval
	 * @return
	 */
	private boolean computeOutputWithDelay(final boolean computedOutput,final double iterationInterval) {
		double fTrueDelay = getTrueDelay().getValue();
		double fFalseDelay = getFalseDelay().getValue();
		
		fTrueDelay = limitInput(fTrueDelay,0.0,DELAY_HIGH_LIMIT, 0.0);
		fFalseDelay = limitInput(fFalseDelay,0.0,DELAY_HIGH_LIMIT, 0.0);

		if(Double.compare(fTrueDelay,0.0)==0 && Double.compare(fFalseDelay,0.0)==0) {
			return computedOutput;
		}

		boolean actualOut = getOUTPUT().getValue();

		if(computedOutput != getLastOutput())
			setDelayServed(0.0);

		if(computedOutput != actualOut) {
			double chosenDelay = computedOutput ? fTrueDelay : fFalseDelay;
			if(getDelayServed() < chosenDelay) {
				setDelayServed(getDelayServed()+iterationInterval);
			}else {
				actualOut = computedOutput;
			}			
		}

		// Save the computed output and delay served for next time.
		setLastOutput(computedOutput);
		
		return actualOut;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(in1);
		propertyArrayList.add(in2);
		propertyArrayList.add(in3);
		propertyArrayList.add(in4);
		propertyArrayList.add(in5);
		propertyArrayList.add(in6);
		propertyArrayList.add(trueDelay);
		propertyArrayList.add(falseDelay);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	private static final double DELAY_HIGH_LIMIT = 32767.0;

}
