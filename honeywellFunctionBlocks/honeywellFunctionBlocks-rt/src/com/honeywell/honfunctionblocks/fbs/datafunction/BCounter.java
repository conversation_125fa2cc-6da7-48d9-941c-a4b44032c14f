/*
 * Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 * This file contains trade secrets of Honeywell International, Inc. No part may be reproduced or
 * transmitted in any form by any means or for any purpose without the express written permission of
 * Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.datafunction;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Counter block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 2, 2018
 */
@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"counter.png\")", flags = Flags.HIDDEN | Flags.READONLY)

//Input slots
@NiagaraProperty(name = "Input", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "Enable", type = "BHonStatusBoolean", defaultValue = "new BNegatableStatusBoolean(true, BStatus.nullStatus,false)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "Preset", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "PresetValue", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "CountValue", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "StopAtZero", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.ok)", flags = Flags.SUMMARY)

//Output slots
@NiagaraProperty(name = "COUNT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")  })

//LoopStaticVariables
@NiagaraProperty(name="previousInput", type="boolean", defaultValue="false", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public class BCounter extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.datafunction.BCounter(2329604998)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "counter.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "Input"

  /**
   * Slot for the {@code Input} property.
   * Input slots
   * @see #getInput
   * @see #setInput
   */
  public static final Property Input = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code Input} property.
   * Input slots
   * @see #Input
   */
  public BFiniteStatusBoolean getInput() { return (BFiniteStatusBoolean)get(Input); }

  /**
   * Set the {@code Input} property.
   * Input slots
   * @see #Input
   */
  public void setInput(BFiniteStatusBoolean v) { set(Input, v, null); }

  //endregion Property "Input"

  //region Property "Enable"

  /**
   * Slot for the {@code Enable} property.
   * @see #getEnable
   * @see #setEnable
   */
  public static final Property Enable = newProperty(Flags.SUMMARY, new BNegatableStatusBoolean(true, BStatus.nullStatus,false), null);

  /**
   * Get the {@code Enable} property.
   * @see #Enable
   */
  public BHonStatusBoolean getEnable() { return (BHonStatusBoolean)get(Enable); }

  /**
   * Set the {@code Enable} property.
   * @see #Enable
   */
  public void setEnable(BHonStatusBoolean v) { set(Enable, v, null); }

  //endregion Property "Enable"

  //region Property "Preset"

  /**
   * Slot for the {@code Preset} property.
   * @see #getPreset
   * @see #setPreset
   */
  public static final Property Preset = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code Preset} property.
   * @see #Preset
   */
  public BFiniteStatusBoolean getPreset() { return (BFiniteStatusBoolean)get(Preset); }

  /**
   * Set the {@code Preset} property.
   * @see #Preset
   */
  public void setPreset(BFiniteStatusBoolean v) { set(Preset, v, null); }

  //endregion Property "Preset"

  //region Property "PresetValue"

  /**
   * Slot for the {@code PresetValue} property.
   * @see #getPresetValue
   * @see #setPresetValue
   */
  public static final Property PresetValue = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code PresetValue} property.
   * @see #PresetValue
   */
  public BHonStatusNumeric getPresetValue() { return (BHonStatusNumeric)get(PresetValue); }

  /**
   * Set the {@code PresetValue} property.
   * @see #PresetValue
   */
  public void setPresetValue(BHonStatusNumeric v) { set(PresetValue, v, null); }

  //endregion Property "PresetValue"

  //region Property "CountValue"

  /**
   * Slot for the {@code CountValue} property.
   * @see #getCountValue
   * @see #setCountValue
   */
  public static final Property CountValue = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code CountValue} property.
   * @see #CountValue
   */
  public BHonStatusNumeric getCountValue() { return (BHonStatusNumeric)get(CountValue); }

  /**
   * Set the {@code CountValue} property.
   * @see #CountValue
   */
  public void setCountValue(BHonStatusNumeric v) { set(CountValue, v, null); }

  //endregion Property "CountValue"

  //region Property "StopAtZero"

  /**
   * Slot for the {@code StopAtZero} property.
   * @see #getStopAtZero
   * @see #setStopAtZero
   */
  public static final Property StopAtZero = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.ok), null);

  /**
   * Get the {@code StopAtZero} property.
   * @see #StopAtZero
   */
  public BFiniteStatusBoolean getStopAtZero() { return (BFiniteStatusBoolean)get(StopAtZero); }

  /**
   * Set the {@code StopAtZero} property.
   * @see #StopAtZero
   */
  public void setStopAtZero(BFiniteStatusBoolean v) { set(StopAtZero, v, null); }

  //endregion Property "StopAtZero"

  //region Property "COUNT"

  /**
   * Slot for the {@code COUNT} property.
   * Output slots
   * @see #getCOUNT
   * @see #setCOUNT
   */
  public static final Property COUNT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE)));

  /**
   * Get the {@code COUNT} property.
   * Output slots
   * @see #COUNT
   */
  public BHonStatusNumeric getCOUNT() { return (BHonStatusNumeric)get(COUNT); }

  /**
   * Set the {@code COUNT} property.
   * Output slots
   * @see #COUNT
   */
  public void setCOUNT(BHonStatusNumeric v) { set(COUNT, v, null); }

  //endregion Property "COUNT"

  //region Property "previousInput"

  /**
   * Slot for the {@code previousInput} property.
   * LoopStaticVariables
   * @see #getPreviousInput
   * @see #setPreviousInput
   */
  public static final Property previousInput = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code previousInput} property.
   * LoopStaticVariables
   * @see #previousInput
   */
  public boolean getPreviousInput() { return getBoolean(previousInput); }

  /**
   * Set the {@code previousInput} property.
   * LoopStaticVariables
   * @see #previousInput
   */
  public void setPreviousInput(boolean v) { setBoolean(previousInput, v, null); }

  //endregion Property "previousInput"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCounter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  	@Override
  	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
  		super.initHoneywellComponent(executionParams);
  		setPreviousInput(false);
  	}
  	
	/*
	 * (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeCounter();
	}
	
	@Override
		public void started() throws Exception {
			super.started();
	        BHonStatusBoolean enable2 = getEnable();
	        if(!(enable2 instanceof BNegatableStatusBoolean)) {
	            BNegatableStatusBoolean negatableStatusBoolean = new BNegatableStatusBoolean();
	            negatableStatusBoolean.setStatus(enable2.getStatus());
	            negatableStatusBoolean.setValue(enable2.getValue());
	            setEnable(negatableStatusBoolean);
	        }
		}
	
	private void executeCounter() {
		if(presetCounter()) {
			getCOUNT().setValue(getPresetValueAfterValidation());
		} else if(isEnabled() && getInputValue() && !getPreviousInput()) {
			double count = getCOUNT().getValue();
			double previousCount = count;
			count = count + getCountValueAfterValidation();
			
			if(isStopAtZero() && (Double.compare(previousCount, 0.0)==0 || isCountCrossedZero(count, previousCount))){
				count = 0.0;
			}
			getCOUNT().setValue(count);
		}
		
		updatePreviousInput();
	}

	private boolean isCountCrossedZero(final double count, final double previousCount) {
		return (previousCount<0.0 && count>0.0) || (previousCount>0.0 && count<0.0);
	}
	
	private boolean isStopAtZero() {
		return isConfigured(StopAtZero) && getStopAtZero().getBoolean();
	}

	private double getCountValueAfterValidation() {
		if(isSlotValueValid(CountValue)) {
			return getCountValue().getValue();
		}
		
		return 1.0d;
	}

	private boolean isEnabled() {
		boolean enabled = true;
		if(isConfigured(Enable)) {
			enabled = getEnable().getBoolean();
			
			if(((BNegatableStatusBoolean)getEnable()).getNegate())
				enabled = !enabled;
		}
			
		return enabled;
	}

	private void updatePreviousInput() {
		setPreviousInput(getInputValue());
	}

	private boolean getInputValue() {
		if(isConfigured(Input))
			return getInput().getBoolean();
		return false;
	}

	private double getPresetValueAfterValidation() {
		if(isSlotValueValid(PresetValue)) {
			return getPresetValue().getValue();
		}
		
		return 0.0d;
	}

	private boolean presetCounter() {
		return isConfigured(Preset) && getPreset().getBoolean();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(Input);
		propertyArrayList.add(Enable);
		propertyArrayList.add(Preset);
		propertyArrayList.add(PresetValue);
		propertyArrayList.add(CountValue);
		propertyArrayList.add(StopAtZero);
		return propertyArrayList;
	}

	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(COUNT);
		return properties;
	}
	
}
