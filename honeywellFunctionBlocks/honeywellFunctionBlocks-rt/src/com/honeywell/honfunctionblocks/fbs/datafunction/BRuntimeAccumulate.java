/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.datafunction;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Runtime Accumulate as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 2, 2018
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"run_time_accumulate.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name="Input", type="BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="Enable", type="BHonStatusBoolean", defaultValue = "new BNegatableStatusBoolean(true, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="Preset", type="BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="PresetValue", type="BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
	@Facet(name = "BFacets.PRECISION", value = "6")
})
@NiagaraProperty(name = "RUNTIME_MIN", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE")
})
@NiagaraProperty(name = "RUNTIME_SEC", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "RUNTIME_HRS", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "RUNTIME_DAYS", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="accumulatedSeconds", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})

public class BRuntimeAccumulate extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.datafunction.BRuntimeAccumulate(826671023)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "run_time_accumulate.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "Input"

  /**
   * Slot for the {@code Input} property.
   * @see #getInput
   * @see #setInput
   */
  public static final Property Input = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code Input} property.
   * @see #Input
   */
  public BFiniteStatusBoolean getInput() { return (BFiniteStatusBoolean)get(Input); }

  /**
   * Set the {@code Input} property.
   * @see #Input
   */
  public void setInput(BFiniteStatusBoolean v) { set(Input, v, null); }

  //endregion Property "Input"

  //region Property "Enable"

  /**
   * Slot for the {@code Enable} property.
   * @see #getEnable
   * @see #setEnable
   */
  public static final Property Enable = newProperty(Flags.SUMMARY, new BNegatableStatusBoolean(true, BStatus.nullStatus, false), null);

  /**
   * Get the {@code Enable} property.
   * @see #Enable
   */
  public BHonStatusBoolean getEnable() { return (BHonStatusBoolean)get(Enable); }

  /**
   * Set the {@code Enable} property.
   * @see #Enable
   */
  public void setEnable(BHonStatusBoolean v) { set(Enable, v, null); }

  //endregion Property "Enable"

  //region Property "Preset"

  /**
   * Slot for the {@code Preset} property.
   * @see #getPreset
   * @see #setPreset
   */
  public static final Property Preset = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code Preset} property.
   * @see #Preset
   */
  public BFiniteStatusBoolean getPreset() { return (BFiniteStatusBoolean)get(Preset); }

  /**
   * Set the {@code Preset} property.
   * @see #Preset
   */
  public void setPreset(BFiniteStatusBoolean v) { set(Preset, v, null); }

  //endregion Property "Preset"

  //region Property "PresetValue"

  /**
   * Slot for the {@code PresetValue} property.
   * @see #getPresetValue
   * @see #setPresetValue
   */
  public static final Property PresetValue = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code PresetValue} property.
   * @see #PresetValue
   */
  public BHonStatusNumeric getPresetValue() { return (BHonStatusNumeric)get(PresetValue); }

  /**
   * Set the {@code PresetValue} property.
   * @see #PresetValue
   */
  public void setPresetValue(BHonStatusNumeric v) { set(PresetValue, v, null); }

  //endregion Property "PresetValue"

  //region Property "RUNTIME_MIN"

  /**
   * Slot for the {@code RUNTIME_MIN} property.
   * @see #getRUNTIME_MIN
   * @see #setRUNTIME_MIN
   */
  public static final Property RUNTIME_MIN = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSAVE_ONLY_UX_FE)));

  /**
   * Get the {@code RUNTIME_MIN} property.
   * @see #RUNTIME_MIN
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getRUNTIME_MIN() { return (BHonStatusNumeric)get(RUNTIME_MIN); }

  /**
   * Set the {@code RUNTIME_MIN} property.
   * @see #RUNTIME_MIN
   */
  @SuppressWarnings("squid:S00100")
  public void setRUNTIME_MIN(BHonStatusNumeric v) { set(RUNTIME_MIN, v, null); }

  //endregion Property "RUNTIME_MIN"

  //region Property "RUNTIME_SEC"

  /**
   * Slot for the {@code RUNTIME_SEC} property.
   * @see #getRUNTIME_SEC
   * @see #setRUNTIME_SEC
   */
  public static final Property RUNTIME_SEC = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code RUNTIME_SEC} property.
   * @see #RUNTIME_SEC
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getRUNTIME_SEC() { return (BHonStatusNumeric)get(RUNTIME_SEC); }

  /**
   * Set the {@code RUNTIME_SEC} property.
   * @see #RUNTIME_SEC
   */
  @SuppressWarnings("squid:S00100")
  public void setRUNTIME_SEC(BHonStatusNumeric v) { set(RUNTIME_SEC, v, null); }

  //endregion Property "RUNTIME_SEC"

  //region Property "RUNTIME_HRS"

  /**
   * Slot for the {@code RUNTIME_HRS} property.
   * @see #getRUNTIME_HRS
   * @see #setRUNTIME_HRS
   */
  @SuppressWarnings("squid:S00100")
  public static final Property RUNTIME_HRS = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code RUNTIME_HRS} property.
   * @see #RUNTIME_HRS
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getRUNTIME_HRS() { return (BHonStatusNumeric)get(RUNTIME_HRS); }

  /**
   * Set the {@code RUNTIME_HRS} property.
   * @see #RUNTIME_HRS
   */
  @SuppressWarnings("squid:S00100")
  public void setRUNTIME_HRS(BHonStatusNumeric v) { set(RUNTIME_HRS, v, null); }

  //endregion Property "RUNTIME_HRS"

  //region Property "RUNTIME_DAYS"

  /**
   * Slot for the {@code RUNTIME_DAYS} property.
   * @see #getRUNTIME_DAYS
   * @see #setRUNTIME_DAYS
   */
  public static final Property RUNTIME_DAYS = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code RUNTIME_DAYS} property.
   * @see #RUNTIME_DAYS
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getRUNTIME_DAYS() { return (BHonStatusNumeric)get(RUNTIME_DAYS); }

  /**
   * Set the {@code RUNTIME_DAYS} property.
   * @see #RUNTIME_DAYS
   */
  @SuppressWarnings("squid:S00100")
  public void setRUNTIME_DAYS(BHonStatusNumeric v) { set(RUNTIME_DAYS, v, null); }

  //endregion Property "RUNTIME_DAYS"

  //region Property "accumulatedSeconds"

  /**
   * Slot for the {@code accumulatedSeconds} property.
   * @see #getAccumulatedSeconds
   * @see #setAccumulatedSeconds
   */
  public static final Property accumulatedSeconds = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code accumulatedSeconds} property.
   * @see #accumulatedSeconds
   */
  public double getAccumulatedSeconds() { return getDouble(accumulatedSeconds); }

  /**
   * Set the {@code accumulatedSeconds} property.
   * @see #accumulatedSeconds
   */
  public void setAccumulatedSeconds(double v) { setDouble(accumulatedSeconds, v, null); }

  //endregion Property "accumulatedSeconds"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRuntimeAccumulate.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  	@Override
  	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
  		super.initHoneywellComponent(executionParams);
  		setRunTimeValues(getRUNTIME_MIN().getValue());
  	}
  	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeRuntimeAccumulate(executionParams);
	}
	
	private void executeRuntimeAccumulate(BExecutionParams executionParams) {
		if(presetRunTimeAccumulator()) {
			setAccumulatedSeconds(0.0D);
			setRunTimeValues(getPresetValueAfterValidation());
			return;
		}
		
		if(!isEnabled() || isInputFalse()) {
			return;
		}
		
		double runTimeValue = calculateRunTime(getRUNTIME_MIN().getValue(), executionParams.getIterationInterval());
		setRunTimeValues(runTimeValue);
	}
	
	@Override
	public void started() throws Exception {
		super.started();
		updateInputSlotAfterNegate();
		updateEnableSlotAfterNegate();
		updatePresetSlotAfterNegate();
	}
	
	private void updateInputSlotAfterNegate() {
		BFiniteStatusBoolean input1 = getInput();
		if(!(input1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(input1.getStatus());
			negatableFiniteStatusBoolean.setValue(input1.getValue());
			setInput(negatableFiniteStatusBoolean);
		}
	}
	
	private void updateEnableSlotAfterNegate() {
		BHonStatusBoolean enable1 = getEnable();
		if(!(enable1 instanceof BNegatableStatusBoolean)) {
			BNegatableStatusBoolean negatableStatusBoolean = new BNegatableStatusBoolean();
			negatableStatusBoolean.setStatus(enable1.getStatus());
			negatableStatusBoolean.setValue(enable1.getValue());
			setEnable(negatableStatusBoolean);
		}
	}
	
	private void updatePresetSlotAfterNegate() {
		BFiniteStatusBoolean preset1 = getPreset();
		if(!(preset1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(preset1.getStatus());
			negatableFiniteStatusBoolean.setValue(preset1.getValue());
			setPreset(negatableFiniteStatusBoolean);
		}
	}

	private double calculateRunTime(final double runTime, final int iterationInterval) {
		setAccumulatedSeconds(getAccumulatedSeconds() + ((double)iterationInterval/UnitConstants.THOUSAND_MILLI_SECOND));

		double minute = Math.floor(getAccumulatedSeconds()/UnitConstants.SIXTY_SECOND);
		setAccumulatedSeconds(getAccumulatedSeconds()%UnitConstants.SIXTY_SECOND);
		return runTime + minute;
	}

	private void setRunTimeValues(final double runTimeValue) {
		getRUNTIME_MIN().setValue(runTimeValue);
		getRUNTIME_SEC().setValue(runTimeValue * UnitConstants.SIXTY_SECOND + getAccumulatedSeconds());
		getRUNTIME_HRS().setValue(runTimeValue / UnitConstants.SIXTY_SECOND);
		getRUNTIME_DAYS().setValue(runTimeValue / UnitConstants.ONEDAY_IN_MINUTE);
	}

	private double getPresetValueAfterValidation() {
		if(isSlotValueValid(PresetValue))
			return getPresetValue().getValue();
		else 
			return 0d;
	}

	private boolean presetRunTimeAccumulator() {
		if (isConfigured(Preset)) 
			return ((BNegatableFiniteStatusBoolean) getPreset()).getNegate() ? !getPreset().getBoolean() : getPreset().getBoolean();
			
		return false;
	} 

	private boolean isEnabled() {
		if(isConfigured(Enable))
			return ((BNegatableStatusBoolean) getEnable()).getNegate() ? !getEnable().getBoolean() : getEnable().getBoolean();
		
		return true;
	}   

	private boolean isInputFalse() {
		if(isConfigured(Input)) {
			boolean temp = ((BNegatableFiniteStatusBoolean)getInput()).getNegate() ? !getInput().getBoolean() : getInput().getBoolean();			
			return !temp;
		}
		return true;
	} 

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(Input);
		propertyArrayList.add(Enable);
		propertyArrayList.add(Preset);
		propertyArrayList.add(PresetValue);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(RUNTIME_MIN);
		propertyArrayList.add(RUNTIME_SEC);
		propertyArrayList.add(RUNTIME_HRS);
		propertyArrayList.add(RUNTIME_DAYS);
		return propertyArrayList;
	}

}
