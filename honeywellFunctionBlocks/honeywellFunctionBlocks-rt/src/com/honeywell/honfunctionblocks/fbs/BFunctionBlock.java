/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;

import javax.baja.license.FeatureNotLicensedException;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.platform.RuntimeProfile;
import javax.baja.rpc.NiagaraRpc;
import javax.baja.rpc.Transport;
import javax.baja.rpc.TransportType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.status.BStatusValue;
import javax.baja.sys.Action;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BLink;
import javax.baja.sys.BRelTime;
import javax.baja.sys.BValue;
import javax.baja.sys.Clock;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.LocalizableRuntimeException;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BNameMap;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLogger;
import com.honeywell.honfunctionblocks.utils.LicenseHandler;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;
import com.honeywell.versionmanager.BHonVersion;




/**
 *
 * <AUTHOR> - Ravi Bharathi .K
 * @since Oct 31, 2017
 */

@NiagaraType
@NiagaraProperty(name="ExecutionOrder", type="int", defaultValue="0", flags=Flags.SUMMARY|Flags.READONLY|Flags.HIDDEN)
@NiagaraProperty(name="toolVersion", type="BHonVersion", defaultValue="BHonVersion.NULL", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name="OverrideExpiration", type="BAbsTime", defaultValue="BAbsTime.DEFAULT", flags=Flags.READONLY)
@NiagaraAction(name="executeBlock", parameterType="honeywellFunctionBlocks:ExecutionParams", defaultValue="new BExecutionParams()",flags = Flags.HIDDEN)
@NiagaraAction(name="Auto")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S00100","squid:S1845","squid:S00103"})

public abstract class BFunctionBlock extends BComponent implements IHoneywellExecutionBlock,IHoneywellComponent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.BFunctionBlock(2245668228)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "ExecutionOrder"

  /**
   * Slot for the {@code ExecutionOrder} property.
   * @see #getExecutionOrder
   * @see #setExecutionOrder
   */
  public static final Property ExecutionOrder = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.HIDDEN, 0, null);

  /**
   * Get the {@code ExecutionOrder} property.
   * @see #ExecutionOrder
   */
  public int getExecutionOrder() { return getInt(ExecutionOrder); }

  /**
   * Set the {@code ExecutionOrder} property.
   * @see #ExecutionOrder
   */
  public void setExecutionOrder(int v) { setInt(ExecutionOrder, v, null); }

  //endregion Property "ExecutionOrder"

  //region Property "toolVersion"

  /**
   * Slot for the {@code toolVersion} property.
   * @see #getToolVersion
   * @see #setToolVersion
   */
  public static final Property toolVersion = newProperty(Flags.HIDDEN | Flags.READONLY, BHonVersion.NULL, null);

  /**
   * Get the {@code toolVersion} property.
   * @see #toolVersion
   */
  public BHonVersion getToolVersion() { return (BHonVersion)get(toolVersion); }

  /**
   * Set the {@code toolVersion} property.
   * @see #toolVersion
   */
  public void setToolVersion(BHonVersion v) { set(toolVersion, v, null); }

  //endregion Property "toolVersion"

  //region Property "OverrideExpiration"

  /**
   * Slot for the {@code OverrideExpiration} property.
   * @see #getOverrideExpiration
   * @see #setOverrideExpiration
   */
  public static final Property OverrideExpiration = newProperty(Flags.READONLY, BAbsTime.DEFAULT, null);

  /**
   * Get the {@code OverrideExpiration} property.
   * @see #OverrideExpiration
   */
  public BAbsTime getOverrideExpiration() { return (BAbsTime)get(OverrideExpiration); }

  /**
   * Set the {@code OverrideExpiration} property.
   * @see #OverrideExpiration
   */
  public void setOverrideExpiration(BAbsTime v) { set(OverrideExpiration, v, null); }

  //endregion Property "OverrideExpiration"

  //region Action "executeBlock"

  /**
   * Slot for the {@code executeBlock} action.
   * @see #executeBlock(BExecutionParams parameter)
   */
  public static final Action executeBlock = newAction(Flags.HIDDEN, new BExecutionParams(), null);

  /**
   * Invoke the {@code executeBlock} action.
   * @see #executeBlock
   */
  public void executeBlock(BExecutionParams parameter) { invoke(executeBlock, parameter, null); }

  //endregion Action "executeBlock"

  //region Action "Auto"

  /**
   * Slot for the {@code Auto} action.
   * @see #Auto()
   */
  public static final Action Auto = newAction(0, null);

  /**
   * Invoke the {@code Auto} action.
   * @see #Auto
   */
  public void Auto() { invoke(Auto, null, null); }

  //endregion Action "Auto"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFunctionBlock.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#atSteadyState()
	 */
	@Override
	public void atSteadyState() throws Exception {
		super.atSteadyState();
		
		//this logic is to set the Non-critical flag for the applications which are migrated
		//from Spyder and added to F1, since migration does not set non-critical flag, this is needed
		BLink [] links = getChildren(BLink.class);
		for (int i = 0; i < links.length; i++) {
			BLink link = links[i];
			setNonCriticalFlagToLinkedSlot(link);
		}
	}

	private void setNonCriticalFlagToLinkedSlot(BLink link) {
		this.setFlags(link.getTargetSlot(), getFlags(link.getTargetSlot()) | Flags.NON_CRITICAL);
		
		Slot linkSlot = this.getSlot(link.getName());
		this.setFlags(linkSlot, this.getFlags(linkSlot) | Flags.NON_CRITICAL);
	}
  
	@Override
	public void started() throws Exception {
		super.started();
		initOverrideAction();
	}
	
	@Override
	public void descendantsStarted() throws Exception {
		super.descendantsStarted();
		BHonVersion moduleVersion = BHonVersion.getModuleVersion(BFunctionBlock.TYPE, RuntimeProfile.rt);
		try {
			if(BHonVersion.isNull(getToolVersion())) {
				//Version number is NULL. DO NOT MIGRATE COMPONENT
				return;
			}
		
			if(this.getToolVersion().compareToolVersion(moduleVersion) < 0) {
				//HANDLE MIGRATION
			}
		} finally {
			this.setToolVersion(moduleVersion);
		}
	}
	
	@Override
	public void checkRemove(Property property, Context context) {

		if (property.getName().equals(FBSlotConstants.OVERRIDE)) {
			throw new LocalizableRuntimeException(TYPE.getModule().getModuleName(),
					FunctionBlocksLexicon.getLexicon().getText("cannot.remove.Override.slot"));
		}
		super.checkRemove(property, context);
	}
	
	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#added(javax.baja.sys.Property, javax.baja.sys.Context)
	 */
	@Override
	public void added(Property property, Context context) {
		if(!Sys.isStation() || !isRunning()) {
			return;
		}
		
		super.added(property, context);

		//updating linked slot to be non-critical, as we don't want data recovery service to record
		//the changes to this slot. In F1, this slot will get updated every second with computed value
		//and we don't want to track the changes to this slot
		if(property.getType() != null && property.getType().is(BLink.TYPE)) {
			BLink link = (BLink) get(property);
			
			//starting a thread to update the flag, instead of doing in the added call back
			//this is required to ensure call back is completed independently, else we will
			//have sync.op commit error when framework sync the change to workbench proxy
			new Thread(() -> setNonCriticalFlagToLinkedSlot(link)).start();
		}
	}

	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context)
	 */
	@Override
	public void removed(Property property, BValue oldValue, Context context) {
		if(!Sys.isStation() || !isRunning()) {
			return;
		}
		
		super.removed(property, oldValue, context);
		
		if(property.getType() != null && property.getType().is(BLink.TYPE)) {
			BLink link = (BLink) get(property);
			Slot targetSlot = getSlot(link.getTargetSlotName());
			this.setFlags(targetSlot, getFlags(targetSlot) & ~Flags.NON_CRITICAL) ;
		}
	}

	private void initOverrideAction() {
		if (null == this.get(FBSlotConstants.OVERRIDE)) {
			BFBOverrideAction overrideAction = new BFBOverrideAction();
			this.add(FBSlotConstants.OVERRIDE, overrideAction);
		} else {
			// On station restart clears all function block overrides
			doAuto();
		}

	}
	
	public void doExecuteBlock(BExecutionParams executionParams) {
		try {
			 if(isOutputPropertiesOverridden()) {
				 return;
			}
			executeHoneywellComponent(executionParams);
		} catch (BlockExecutionException e) {
			FunctionBlocksLogger.getLogger().log(Level.SEVERE, e.getMessage(), e);
		}
	}
	
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		//license check, if proper license not present, the function block will not execute
		if(!LicenseHandler.isHoneywellFbLicensed()) {
			setOutputPropertyStatusToFault();
            throw new FeatureNotLicensedException(FunctionBlocksLexicon.getLexicon().getText("license.exception"));
		}
	}
	
	private void setOutputPropertyStatusToFault() {
		List<Property> properties = this.getOutputPropertiesList();
		for (int i = 0; i < properties.size(); i++) {
			Property property = this.getProperty(properties.get(i).getName());
			if (null != property) {
				BStatusValue y = (BStatusValue) this.get(property);
				y.setStatus(BStatus.fault);
			}
		}
	}
	
	public boolean isOutputPropertiesOverridden() {
		// If any of the output property is in overridden state then do not execute the
		// block
		List<Property> properties = this.getOutputPropertiesList();
		for (int i = 0; i < properties.size(); i++) {
			Property property = this.getProperty(properties.get(i).getName());
			if (null != property) {
				BStatusValue y = (BStatusValue) this.get(property);
				if (y.getStatus().isOverridden()) {
					return true;
				}
			}
		}
		return false;
	}
  	
	public void doOverride(BFBOverrideProperties args) {
		executeOverrideFunction(args);

		if (overrideFBTimer != null) {
			overrideFBTimer.cancel();
		}
		BRelTime duration = args.getOverrideDuration();
		if (null != duration && duration.getMillis() > 0L) {
			setOverrideExpiration(BAbsTime.make(Clock.millis() + duration.getMillis()));
			overrideFBTimer = Clock.schedule(this, duration, Auto, null);
		} else {
			setOverrideExpiration(BAbsTime.NULL);
			setOutputPropertyStatusToOk();
		}

	}

	@SuppressWarnings("squid:MethodCyclomaticComplexity")
	private void executeOverrideFunction(BFBOverrideProperties args) {
		Property[] fbOverridepropertyArr = args.getPropertiesArray();
		for (int i = 0; i < fbOverridepropertyArr.length; i++) {
			Property fbProperty = this.getProperty(fbOverridepropertyArr[i].getName());
			if (null != fbProperty) {

				BValue overrideOutputSlotVal = args.get(fbOverridepropertyArr[i]);
				BValue outputSlotval = this.get(fbProperty);
				if (overrideOutputSlotVal instanceof BEnum && outputSlotval instanceof BStatusEnum) {
					((BStatusEnum) outputSlotval).setValue(((BEnum) overrideOutputSlotVal).getEnum());

				} else if (overrideOutputSlotVal instanceof BBoolean && outputSlotval instanceof BStatusBoolean) {
					((BStatusBoolean) outputSlotval).setValue(((BBoolean) overrideOutputSlotVal).getBoolean());

				} else if (overrideOutputSlotVal instanceof BDouble && outputSlotval instanceof BStatusNumeric) {
					((BStatusNumeric) outputSlotval).setValue(((BDouble) overrideOutputSlotVal).getDouble());

				}
				// change the status to override
				((BStatusValue) this.get(fbProperty)).setStatusOverridden(true);

			}
		}
	}	
	
	public void doAuto() {
		setOutputPropertyStatusToOk();
		if (overrideFBTimer != null)
			overrideFBTimer.cancel();
		overrideFBTimer = null;
		setOverrideExpiration(BAbsTime.NULL);
	}

	private void setOutputPropertyStatusToOk() {
		List<Property> properties = this.getOutputPropertiesList();
		for (int i = 0; i < properties.size(); i++) {
			Property property = this.getProperty(properties.get(i).getName());
			if (null != property) {
				BStatusValue y = (BStatusValue) this.get(property);
				y.setStatus(BStatus.ok);
			}
		}
	}
	
  	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent(com.honeywell* honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		//place holder to initialize the block properties to default when required
		//not all block need to reset the values to default before starting the DDC
		//engine, the sub-types which needs to initialize only will override this 
		//method and initialize the required properties to default
	}
	
	/**
	 * Check whether the given value is valid or not
	 * @param property
	 * @return
	 */
	public boolean isInvalidValue(final Property property) {
		return LimitCheckUtil.isInvalidValue(this, property);
	}
	
	public boolean isInvalidValueWithIgnoreLimits(final Property property) {
		boolean invalid=false;		
		BValue val = this.get(property);
		if(val.getType().is(BStatusNumeric.TYPE)) {
			double value = ((BStatusNumeric)val).getValue();
			invalid = Double.isNaN(value) || (Double.isInfinite(value) && value > 0 );	
		}
		return invalid;		
	}
	
	/**
	 * Check whether the given Property is configured or not 
	 * @param property
	 * @return
	 */
	public boolean isConfigured(final Property property) {
		return ((BStatusValue) this.get(property)).getStatus().isValid();
	}
	
	
	public boolean isSlotValueValid(final Property property) {
		return isConfigured(property) && !isInvalidValue(property);		
	}
	
	
	public boolean isSlotValueValidWithIgnoreLimits(final Property property) {
		return isConfigured(property) && !isInvalidValueWithIgnoreLimits(property);		
	}
	
	public boolean isAnySlotUnconfiguredOrInvalidWithIgnoreLimits(List<Property> propertiesList) {
		boolean invalid = false; 
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(!isConfigured(property) || isInvalidValueWithIgnoreLimits(property)) {
				invalid = true;
				break;
			}
		}
		return invalid;
	}
	
	/**
	 * Check whether this block has any Invalid Input
	 * @return
	 */
	public boolean hasAnyInvalidInput() {
		boolean invalid = false; 
		List<Property> propertiesList = getInputPropertiesList();
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(isConfigured(property) && isInvalidValue(property)) {
				invalid = true;
				break;
			}
		}
		return invalid;
	}	
	
	public boolean isAnyInputConfiguredAndInvalid(List<Property> propertiesList) {
		boolean invalid = false; 
		for(java.util.Iterator<Property> it=propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
			if(isConfigured(property) && isInvalidValue(property)) {
				invalid = true;
				break;
			}
		}
		return invalid;
	}	
	
	public boolean isAnyInputUnconfigured(List<Property> propertiesList) {
		boolean invalid = false;
		for (Iterator<Property> iterator = propertiesList.iterator(); iterator.hasNext();) {
			Property property = iterator.next();
			if(!isConfigured(property)) {
				invalid = true;
				break;
			}
		}
		return invalid;
	}
	
	public boolean isAllInputsInvalid() {
		boolean invalid = false;
		List<Property> propertiesList = getInputPropertiesList();
		for (java.util.Iterator<Property> it = propertiesList.iterator(); it.hasNext();) {
			Property property = it.next();
				if (isConfigured(property) && isInvalidValue(property)) {
					invalid = true;
				} else {
					invalid = false;
					break;
				}
		}
		return invalid;
	}	
	
	/**
	 * This method returns list of properties that can receive input values through links.
	 * These slots cannot act like source to a link. 
	 * This method never returns null if no such properties exist.
	 * @return
	 */
	public List<Property> getInputPropertiesList() {
		return new ArrayList<>();
	}
	
	/**
	 * This method returns list of properties that can hold configuration/constant values for that function block.
	 * These slots cannot act like source or target to a link. 
	 * This method never returns null if no such properties exist.
	 * @return
	 */
	public List<Property> getConfigPropertiesList() {
		return new ArrayList<>();
	}
	
	/**
	 * This method returns list of properties that can propogate values through links.
	 * These slots cannot act like target to a link. 
	 * This method never returns null if no such properties exist.
	 * @return
	 */
	public List<Property> getOutputPropertiesList() {
		return new ArrayList<>();
	}
	
	/**
	 * Apply limits to a floating point input.  The caller specifies an upper limit, lower limit, and value to be returned if the input value is invalid.  Returns the limited value.
	 * @return
	 */
	protected double limitInput(final double inValue, final double lowLimit, final double highLimit, final double invalidValue) {
		double limValue;
		limValue = inValue;
		if ( Double.isNaN(inValue) || (inValue>0 && Double.isInfinite(inValue))  )
	    {
	        limValue = invalidValue;
	    }
	    else if ( limValue > highLimit )
	    {
	        limValue = highLimit;
	    }
	    else if ( limValue < lowLimit )
	    {
	        limValue = lowLimit;
	    }
	    return limValue;
	}
	

	@Override
	public void updateBlockExecutionOrder(int executionOrder) {
		if(executionOrder > 0 && Flags.isHidden(this, getSlot(ExecutionOrder.getName()))) {
			//Unhide ExecutionOrder property
			this.setFlags(ExecutionOrder, getFlags(ExecutionOrder) & ~Flags.HIDDEN);
		}
		
		setExecutionOrder(executionOrder);
	}
	
	/**
	 * Overriding the getDisplayName so that slot names and their respective display names are same.
	 * Else, Niagara default implementation is invoked.
	 */
	@Override
	public String getDisplayName(Slot slot, Context cx) {
		// By default Niagara will display frozen slot name in Camel Case.
		// To avoid that we need to override that behavior. And overridden code should
		// use the display name if present instead of the name.
		String name = SlotPath.unescape(slot.getName());
		BValue val = get("displayNames");// this slot will be present if an explicit display name is given
		
		if (val instanceof BNameMap && (((BNameMap) val).get(name)!=null)) {
			return super.getDisplayName(slot, cx);
		}

		return name;
	}

	@NiagaraRpc(permissions = "RWI", transports = { @Transport(type = TransportType.web), @Transport(type = TransportType.box) })
	public boolean getNegateValueToRPCCall(String slotName,Context ctx) {
		BValue negateSlot = this.get(slotName);
		if (negateSlot instanceof INegatableStatusValue) {
			return ((INegatableStatusValue) negateSlot).getNegate();
		}
		return false;
	}

	@NiagaraRpc(permissions = "RWI", transports = { @Transport(type = TransportType.web), @Transport(type = TransportType.box) })
	public boolean setNegateValueFromRPCCall(String slotName,boolean isNegate, Context ctx) {
		BValue negateSlot = this.get(slotName);
		if (negateSlot instanceof INegatableStatusValue) {
			((INegatableStatusValue) negateSlot).setNegate(isNegate);
		}
		return isNegate;
	}
	@NiagaraRpc(permissions = "RWI", transports = { @Transport(type = TransportType.web), @Transport(type = TransportType.box) })
	public BFacets getFacetsDataToRPCCall(String slotName,Context ctx) {
		return this.getSlotFacets(this.getSlot(slotName));
	}
	
	public static BOrd getFunctionBlockOverrideViewOrd() {
		return BOrd.make(FUNCTIONBLOCK_OVERRIDE_VIEW_ORD);
	}
	
	public static String getFunctionBlockOverrideQueryOrd() {
		return OVERRIDE_FB_QUERY_ORD;
	}
	
	private Clock.Ticket overrideFBTimer =  null;
	private static final String FUNCTIONBLOCK_OVERRIDE_VIEW_ORD = "module://" + TYPE.getModule().getModuleName() + "/px/FBOverrideView.px";
	private static final String OVERRIDE_FB_QUERY_ORD = "|bql:select name from honeywellFunctionBlocks:FunctionBlock where OverrideExpiration!=BAbsTime.DEFAULT";

}
