/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

/**
 * This class extends BAction and can be invoked on Function Block
 * This gives all the output property of function block to override
 * <AUTHOR> -<PERSON><PERSON><PERSON><PERSON>
 * @since Sep 24, 2018
 */
package com.honeywell.honfunctionblocks.fbs;

import java.util.List;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAction;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845","squid:S00103"})
public class BFBOverrideAction  extends BAction {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.BFBOverrideAction(2979906276)1.0$ @*/
/* Generated Mon Sep 24 18:22:07 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFBOverrideAction.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public BValue invoke(BComponent target, BValue arg) throws Exception {

		if (!(target instanceof BFunctionBlock))
			return null;
		BFunctionBlock fb = (BFunctionBlock) target;
		fb.doOverride((BFBOverrideProperties) arg);

		return arg;
	}

	private BFBOverrideProperties getFBOverrideProperties() {
		BComponent parent = getParentComponent();
		if (!(parent instanceof BFunctionBlock)) {
			return null;
		}
		List<Property> properties = ((BFunctionBlock) parent).getOutputPropertiesList();
		BFBOverrideProperties outPutPropertiesObject = new BFBOverrideProperties();
		for (int i = 0; i < properties.size(); i++) {

			//For stageDriver even if only one stage configured;but it gives all the hidden out slot stages
			//So we should show the output property value field that is not hidden
			Slot slot = parent.getSlot(properties.get(i).getName());
			if (Flags.isHidden(parent, slot)) {
				continue;
			}

			BValue outPutSlotValue = parent.get(properties.get(i).getName());
			if (outPutSlotValue instanceof BStatusEnum) {
				BEnum f1SlotEnum = ((BStatusEnum) outPutSlotValue).getEnum();
				outPutPropertiesObject.add(properties.get(i).getName(), f1SlotEnum.newCopy());

			} else if (outPutSlotValue instanceof BStatusBoolean) {
				boolean isTrueValueSet = ((BStatusBoolean) outPutSlotValue).getValue();
				outPutPropertiesObject.add(properties.get(i).getName(), BBoolean.make(isTrueValueSet));

			} else if (outPutSlotValue instanceof BStatusNumeric) {
				outPutPropertiesObject.add(properties.get(i).getName(),
						BDouble.make(((BStatusNumeric) outPutSlotValue).getValue()));

			} 
		}

		return outPutPropertiesObject;
	}

	@Override
	public BValue getParameterDefault() {
		BFBOverrideProperties outPutPropertiesObject = getFBOverrideProperties();
		if (outPutPropertiesObject != null)
		{
			return outPutPropertiesObject;
		}
		return null;
	}

	@Override
	public Type getParameterType() {
		BFBOverrideProperties outPutPropertiesObject = getFBOverrideProperties();
		if (outPutPropertiesObject != null)
			return BFBOverrideProperties.TYPE;
		return null;
	}

	@Override
	public Type getReturnType() {
		return null;
	}

}
