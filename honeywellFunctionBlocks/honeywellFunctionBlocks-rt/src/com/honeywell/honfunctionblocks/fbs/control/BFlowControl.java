/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of FlowControl block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Jan 5, 2018
 */

@NiagaraType
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S00103" })
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"flow_control.png\")", flags=Flags.HIDDEN|Flags.READONLY)
// Analog Inputs

@NiagaraProperty(name = "cmdFlowPercent", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "sensedFlowVol", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "minFlowSetPt", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "maxFlowSetPt", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "manFlowOverride", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "manFlowValue", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "ductArea", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1F, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0.1f)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "units", type = "BFlowControlUnitEnum", defaultValue = "BFlowControlUnitEnum.DEFAULT")

@NiagaraProperty(name = "motorSpeed", type = "int", defaultValue = "90",
facets = { 
		@Facet(name = "BFacets.MIN", value = "1"),
		@Facet(name = "BFacets.MAX", value = "255"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

// Output
@NiagaraProperty(name = "EFF_FLOW_SETPT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })

@NiagaraProperty(name = "DAMPER_POS", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE,
facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })

@NiagaraProperty(name="oldError", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

public class BFlowControl extends BFunctionBlock  implements INonLinearBlock {

	
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BFlowControl(4159482649)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "flow_control.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "cmdFlowPercent"

  /**
   * Slot for the {@code cmdFlowPercent} property.
   * @see #getCmdFlowPercent
   * @see #setCmdFlowPercent
   */
  public static final Property cmdFlowPercent = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code cmdFlowPercent} property.
   * @see #cmdFlowPercent
   */
  public BHonStatusNumeric getCmdFlowPercent() { return (BHonStatusNumeric)get(cmdFlowPercent); }

  /**
   * Set the {@code cmdFlowPercent} property.
   * @see #cmdFlowPercent
   */
  public void setCmdFlowPercent(BHonStatusNumeric v) { set(cmdFlowPercent, v, null); }

  //endregion Property "cmdFlowPercent"

  //region Property "sensedFlowVol"

  /**
   * Slot for the {@code sensedFlowVol} property.
   * @see #getSensedFlowVol
   * @see #setSensedFlowVol
   */
  public static final Property sensedFlowVol = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code sensedFlowVol} property.
   * @see #sensedFlowVol
   */
  public BHonStatusNumeric getSensedFlowVol() { return (BHonStatusNumeric)get(sensedFlowVol); }

  /**
   * Set the {@code sensedFlowVol} property.
   * @see #sensedFlowVol
   */
  public void setSensedFlowVol(BHonStatusNumeric v) { set(sensedFlowVol, v, null); }

  //endregion Property "sensedFlowVol"

  //region Property "minFlowSetPt"

  /**
   * Slot for the {@code minFlowSetPt} property.
   * @see #getMinFlowSetPt
   * @see #setMinFlowSetPt
   */
  public static final Property minFlowSetPt = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code minFlowSetPt} property.
   * @see #minFlowSetPt
   */
  public BHonStatusNumeric getMinFlowSetPt() { return (BHonStatusNumeric)get(minFlowSetPt); }

  /**
   * Set the {@code minFlowSetPt} property.
   * @see #minFlowSetPt
   */
  public void setMinFlowSetPt(BHonStatusNumeric v) { set(minFlowSetPt, v, null); }

  //endregion Property "minFlowSetPt"

  //region Property "maxFlowSetPt"

  /**
   * Slot for the {@code maxFlowSetPt} property.
   * @see #getMaxFlowSetPt
   * @see #setMaxFlowSetPt
   */
  public static final Property maxFlowSetPt = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code maxFlowSetPt} property.
   * @see #maxFlowSetPt
   */
  public BHonStatusNumeric getMaxFlowSetPt() { return (BHonStatusNumeric)get(maxFlowSetPt); }

  /**
   * Set the {@code maxFlowSetPt} property.
   * @see #maxFlowSetPt
   */
  public void setMaxFlowSetPt(BHonStatusNumeric v) { set(maxFlowSetPt, v, null); }

  //endregion Property "maxFlowSetPt"

  //region Property "manFlowOverride"

  /**
   * Slot for the {@code manFlowOverride} property.
   * @see #getManFlowOverride
   * @see #setManFlowOverride
   */
  public static final Property manFlowOverride = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code manFlowOverride} property.
   * @see #manFlowOverride
   */
  public BHonStatusNumeric getManFlowOverride() { return (BHonStatusNumeric)get(manFlowOverride); }

  /**
   * Set the {@code manFlowOverride} property.
   * @see #manFlowOverride
   */
  public void setManFlowOverride(BHonStatusNumeric v) { set(manFlowOverride, v, null); }

  //endregion Property "manFlowOverride"

  //region Property "manFlowValue"

  /**
   * Slot for the {@code manFlowValue} property.
   * @see #getManFlowValue
   * @see #setManFlowValue
   */
  public static final Property manFlowValue = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code manFlowValue} property.
   * @see #manFlowValue
   */
  public BHonStatusNumeric getManFlowValue() { return (BHonStatusNumeric)get(manFlowValue); }

  /**
   * Set the {@code manFlowValue} property.
   * @see #manFlowValue
   */
  public void setManFlowValue(BHonStatusNumeric v) { set(manFlowValue, v, null); }

  //endregion Property "manFlowValue"

  //region Property "ductArea"

  /**
   * Slot for the {@code ductArea} property.
   * @see #getDuctArea
   * @see #setDuctArea
   */
  public static final Property ductArea = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0.1f)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code ductArea} property.
   * @see #ductArea
   */
  public BHonStatusNumeric getDuctArea() { return (BHonStatusNumeric)get(ductArea); }

  /**
   * Set the {@code ductArea} property.
   * @see #ductArea
   */
  public void setDuctArea(BHonStatusNumeric v) { set(ductArea, v, null); }

  //endregion Property "ductArea"

  //region Property "units"

  /**
   * Slot for the {@code units} property.
   * @see #getUnits
   * @see #setUnits
   */
  public static final Property units = newProperty(0, BFlowControlUnitEnum.DEFAULT, null);

  /**
   * Get the {@code units} property.
   * @see #units
   */
  public BFlowControlUnitEnum getUnits() { return (BFlowControlUnitEnum)get(units); }

  /**
   * Set the {@code units} property.
   * @see #units
   */
  public void setUnits(BFlowControlUnitEnum v) { set(units, v, null); }

  //endregion Property "units"

  //region Property "motorSpeed"

  /**
   * Slot for the {@code motorSpeed} property.
   * @see #getMotorSpeed
   * @see #setMotorSpeed
   */
  public static final Property motorSpeed = newProperty(0, 90, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code motorSpeed} property.
   * @see #motorSpeed
   */
  public int getMotorSpeed() { return getInt(motorSpeed); }

  /**
   * Set the {@code motorSpeed} property.
   * @see #motorSpeed
   */
  public void setMotorSpeed(int v) { setInt(motorSpeed, v, null); }

  //endregion Property "motorSpeed"

  //region Property "EFF_FLOW_SETPT"

  /**
   * Slot for the {@code EFF_FLOW_SETPT} property.
   *  Output
   * @see #getEFF_FLOW_SETPT
   * @see #setEFF_FLOW_SETPT
   */
  public static final Property EFF_FLOW_SETPT = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.UNITS, BUnit.NULL), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code EFF_FLOW_SETPT} property.
   *  Output
   * @see #EFF_FLOW_SETPT
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getEFF_FLOW_SETPT() { return (BHonStatusNumeric)get(EFF_FLOW_SETPT); }

  /**
   * Set the {@code EFF_FLOW_SETPT} property.
   *  Output
   * @see #EFF_FLOW_SETPT
   */
  @SuppressWarnings("squid:S00100")
  public void setEFF_FLOW_SETPT(BHonStatusNumeric v) { set(EFF_FLOW_SETPT, v, null); }

  //endregion Property "EFF_FLOW_SETPT"

  //region Property "DAMPER_POS"

  /**
   * Slot for the {@code DAMPER_POS} property.
   * @see #getDAMPER_POS
   * @see #setDAMPER_POS
   */
  public static final Property DAMPER_POS = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.UNITS, BUnit.NULL), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code DAMPER_POS} property.
   * @see #DAMPER_POS
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getDAMPER_POS() { return (BHonStatusNumeric)get(DAMPER_POS); }

  /**
   * Set the {@code DAMPER_POS} property.
   * @see #DAMPER_POS
   */
  @SuppressWarnings("squid:S00100")
  public void setDAMPER_POS(BHonStatusNumeric v) { set(DAMPER_POS, v, null); }

  //endregion Property "DAMPER_POS"

  //region Property "oldError"

  /**
   * Slot for the {@code oldError} property.
   * @see #getOldError
   * @see #setOldError
   */
  public static final Property oldError = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code oldError} property.
   * @see #oldError
   */
  @Override
  public double getOldError() { return getDouble(oldError); }

  /**
   * Set the {@code oldError} property.
   * @see #oldError
   */
  @Override
  public void setOldError(double v) { setDouble(oldError, v, null); }

  //endregion Property "oldError"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowControl.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/


	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent()
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		setOldError(0.0);
		damperPosCmd = 0.0;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeFlowControl();
	}
	
	private void executeFlowControl() {
		double setPt = Double.POSITIVE_INFINITY;		
		area = getDuctArea().getValue();
		double sensor = getSensedFlowVol().getValue(); // sensor converts volumetric flow(CFM or L/S) to

		cmdFlow = getCmdFlowPercent().getValue();				
		if (!isSlotValueValid(cmdFlowPercent))
			cmdFlow = 0.0;
		
		minFlowSetPt1 = getMinFlowSetPt().getValue();// velocity set point
		maxFlowSetPt1 = getMaxFlowSetPt().getValue();// velocity set point		

		if (areAnyInputsInvalid() || areFlowSetpointsInvalid()) {
			if (areFlowSetpointsInvalid()) {
				minFlowSetPt1 = TWENTY_PERCENT;
			} else {
				minFlowSetPt1 = minFlowSetPt1 / maxFlowSetPt1 * HUNDRED_PERCENT;
			}
			damperPosCmd = XYLineCalculator.xyline(cmdFlow, 0.0, HUNDRED_PERCENT, minFlowSetPt1, HUNDRED_PERCENT, true);// this will limit output to max
			// flow pt if
			// cmd flow is > 100!
		} else {
			double motorSpeed1 = getMotorSpeed();
			if (motorSpeed1 < 1.0)
				motorSpeed1 = DEFAULT_MOTOR_SPEED;
			motorSpeed1 = limitInput(motorSpeed1,1,TWOFIFITYFIVE,DEFAULT_MOTOR_SPEED);
			
			manVal = getManFlowValue().getValue();
			units1 = getUnits().getOrdinal();
			
			manFlowMode = getManFlowOverride().getValue();			
			if(!isSlotValueValid(manFlowOverride))
				manFlowMode = 0.0;
			
			double maxAOchng = HUNDRED_PERCENT / motorSpeed1;
			getFlowAndAreaBasedOnUnits();
			setPt = getSetPtBasedOnManFlowMode();
			
			double propErr = ((setPt - sensor) * mulFlow) / (area * mulArea);// prop_err is in normalized velocity (fpm)
			damperPosCmd = getDAMPER_POS().getValue();
			damperPosCmd += calculateGains(propErr,maxAOchng,0.0);
		}

		damperPosCmd = limitInput(damperPosCmd, 0., HUNDRED_PERCENT, 0.);

		getEFF_FLOW_SETPT().setValue(setPt);
		getDAMPER_POS().setValue(damperPosCmd);
	}


	private void getFlowAndAreaBasedOnUnits() {
		switch (units1) {		
			case BFlowControlUnitEnum.LPS_MTR:// Lps,m**2
				mulFlow = DEFAULT_FLOW_LPS;
				mulArea = DEFAULT_AREA_M2;
				break;
			case BFlowControlUnitEnum.CMH_MTR:// cmh,m**2
				mulFlow = DEFAULT_FLOW_CMH;
				mulArea = DEFAULT_AREA_M2;
				break;
			case BFlowControlUnitEnum.CFM_FT:// flow(cfm),area(ft**2)
			///CLOVER:OFF
			default:
			///CLOVER:ON 				
				mulFlow = 1.;
				mulArea = 1.;
				break;

		}
	}

	private double getSetPtBasedOnManFlowMode() {
		double setPt;
		switch ((int) manFlowMode) {
			case BHVACOverrideEnum.HVO_FLOW_VALUE:
				setPt = manVal;
				break;
			case BHVACOverrideEnum.HVO_MINIMUM:
				setPt = minFlowSetPt1;
				break;
			case BHVACOverrideEnum.HVO_MAXIMUM:
				setPt = maxFlowSetPt1;
				break;
			case BHVACOverrideEnum.HVO_OFF:
			default:
				setPt = XYLineCalculator.xyline(cmdFlow, 0., HUNDRED_PERCENT, minFlowSetPt1, maxFlowSetPt1, true); // this will limit output to max // flow // pt if cmd flow is >  100!
				break;
		}
		return setPt;
	}

	private boolean areAnyInputsInvalid() {
		return !isSlotValueValid(sensedFlowVol) || !isSlotValueValid(ductArea) || area <= 0.0;
	}
	private boolean areFlowSetpointsInvalid() {
		return !isSlotValueValid(minFlowSetPt) || !isSlotValueValid(maxFlowSetPt);
	}

	private double calculateGains(double error,double maxAOchng,double derivGain) {
		NonLinearData aia = new NonLinearData();

		aia.setError(error);
		aia.setTrValue(DEFAULT_TR_VALUE);
		aia.setMaxAOChangeValue(maxAOchng);
		aia.setDeadbandValue(TWENTY_PERCENT);
		aia.setDervGainValue(derivGain);
		aia.setMinAOChangeValue(DEFAULT_MIN_AO_CHANGE_VALUE);

		return NonLinearCalculator.calculateGains(aia, this);
	}


	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> properties = super.getInputPropertiesList();
		properties.add(cmdFlowPercent);
		properties.add(sensedFlowVol);
		properties.add(minFlowSetPt);
		properties.add(maxFlowSetPt);
		properties.add(manFlowOverride);
		properties.add(manFlowValue);
		properties.add(ductArea);
		return properties;

	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(DAMPER_POS);
		properties.add(EFF_FLOW_SETPT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(motorSpeed);
		properties.add(units);
		return properties;
	}

	private double damperPosCmd; 
	private double mulFlow;
	private double mulArea;

	private double area;
	private double minFlowSetPt1;
	private double maxFlowSetPt1;
	// velocity
	private double cmdFlow;
	private double manVal;
	private double manFlowMode;
	private int units1;

	private static final int HUNDRED_PERCENT = 100;
	private static final int TWENTY_PERCENT = 20;
	private static final int DEFAULT_TR_VALUE = 120;
	private static final int DEFAULT_MOTOR_SPEED = 90;
	private static final int TWOFIFITYFIVE = 255;
	private static final double DEFAULT_FLOW_LPS = 2.11888;
	private static final double DEFAULT_AREA_M2 = 10.76391;
	private static final double DEFAULT_FLOW_CMH = 0.58858;
	private static final double DEFAULT_MIN_AO_CHANGE_VALUE = 0.11;
	


}
