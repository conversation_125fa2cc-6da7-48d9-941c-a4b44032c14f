/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

/**
 * XYLine calculator: used to calculate y for a given x;given the line reference points x1, x2, y1, y2.
 * <AUTHOR> - <PERSON><PERSON><PERSON> 
 * @since Jan 17, 2018
 */

public final class XYLineCalculator {
	///CLOVER:OFF
	private XYLineCalculator() {}
	///CLOVER:ON
	
	
	/**
	 * To calculate XYLine
	 */
	public static double xyline( double x, double x1, double x2, double y1, double y2, boolean endPtLimited)
	{
		if(Double.compare(x1, x2)==0)
			return y1;      //slope is infinite so return y1 by default

		//If end points are limits, check for x outside range
		if( endPtLimited )
		{
			if (x1 < x2)                 //check the relationship between x1 & x2
			{
				if( x <= x1 ) 
					return y1;  //check if we're outside of the ref points
				else if( x >= x2 ) 
					return y2;
			}
			else    //x1 > x2
			{
				if( x <= x2 ) 
					return y2;  //check if we're outside of the ref points
				else if( x >= x1 ) 
					return y1;
			}
		}
		//Unlimited so return calculated y
		return  y1 + (((x - x1) * (y2 - y1)) / (x2 - x1));
	}
}
