/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.enums.BPidOutputRangeEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of PID block as per FB SDD rev26 Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 *
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"pid.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "sensor", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY, facets = { @Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
				@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
				@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), @Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "setPt", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY, facets = { @Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
				@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
				@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), @Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "disable", type = "BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags = Flags.SUMMARY
		)

@NiagaraProperty(name = "tr", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1F, BStatus.ok)", flags = Flags.SUMMARY, facets = { @Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
				@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
				@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), @Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="intgTime", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name="dervTime", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name = "deadBand", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = { @Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
				@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
				@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), @Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="dbDelay", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(65565)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name = "revAct", type = "BDirectReverseSignOfTREnum", defaultValue = "BDirectReverseSignOfTREnum.DEFAULT")

@NiagaraProperty(name = "bias", type = "int", defaultValue = "0", facets = { @Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "100"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name = "outputRange", type = "BPidOutputRangeEnum", defaultValue = "BPidOutputRangeEnum.DEFAULT", flags=Flags.SUMMARY)

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY
		| Flags.TRANSIENT | Flags.READONLY|Flags.DEFAULT_ON_CLONE, facets = { @Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
				@Facet(name = "BFacets.PRECISION", value = "6"),
				@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
				@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})

@NiagaraProperty(name="dBTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="oldErr", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="intglerr", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S00103" })

public class BPid extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BPid(1501171998)1.0$ @*/
/* Generated Mon Aug 25 22:37:59 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "pid.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "sensor"

  /**
   * Slot for the {@code sensor} property.
   * @see #getSensor
   * @see #setSensor
   */
  public static final Property sensor = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code sensor} property.
   * @see #sensor
   */
  public BHonStatusNumeric getSensor() { return (BHonStatusNumeric)get(sensor); }

  /**
   * Set the {@code sensor} property.
   * @see #sensor
   */
  public void setSensor(BHonStatusNumeric v) { set(sensor, v, null); }

  //endregion Property "sensor"

  //region Property "setPt"

  /**
   * Slot for the {@code setPt} property.
   * @see #getSetPt
   * @see #setSetPt
   */
  public static final Property setPt = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code setPt} property.
   * @see #setPt
   */
  public BHonStatusNumeric getSetPt() { return (BHonStatusNumeric)get(setPt); }

  /**
   * Set the {@code setPt} property.
   * @see #setPt
   */
  public void setSetPt(BHonStatusNumeric v) { set(setPt, v, null); }

  //endregion Property "setPt"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "tr"

  /**
   * Slot for the {@code tr} property.
   * @see #getTr
   * @see #setTr
   */
  public static final Property tr = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code tr} property.
   * @see #tr
   */
  public BHonStatusNumeric getTr() { return (BHonStatusNumeric)get(tr); }

  /**
   * Set the {@code tr} property.
   * @see #tr
   */
  public void setTr(BHonStatusNumeric v) { set(tr, v, null); }

  //endregion Property "tr"

  //region Property "intgTime"

  /**
   * Slot for the {@code intgTime} property.
   * @see #getIntgTime
   * @see #setIntgTime
   */
  public static final Property intgTime = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code intgTime} property.
   * @see #intgTime
   */
  public BHonStatusNumeric getIntgTime() { return (BHonStatusNumeric)get(intgTime); }

  /**
   * Set the {@code intgTime} property.
   * @see #intgTime
   */
  public void setIntgTime(BHonStatusNumeric v) { set(intgTime, v, null); }

  //endregion Property "intgTime"

  //region Property "dervTime"

  /**
   * Slot for the {@code dervTime} property.
   * @see #getDervTime
   * @see #setDervTime
   */
  public static final Property dervTime = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code dervTime} property.
   * @see #dervTime
   */
  public BHonStatusNumeric getDervTime() { return (BHonStatusNumeric)get(dervTime); }

  /**
   * Set the {@code dervTime} property.
   * @see #dervTime
   */
  public void setDervTime(BHonStatusNumeric v) { set(dervTime, v, null); }

  //endregion Property "dervTime"

  //region Property "deadBand"

  /**
   * Slot for the {@code deadBand} property.
   * @see #getDeadBand
   * @see #setDeadBand
   */
  public static final Property deadBand = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code deadBand} property.
   * @see #deadBand
   */
  public BHonStatusNumeric getDeadBand() { return (BHonStatusNumeric)get(deadBand); }

  /**
   * Set the {@code deadBand} property.
   * @see #deadBand
   */
  public void setDeadBand(BHonStatusNumeric v) { set(deadBand, v, null); }

  //endregion Property "deadBand"

  //region Property "dbDelay"

  /**
   * Slot for the {@code dbDelay} property.
   * @see #getDbDelay
   * @see #setDbDelay
   */
  public static final Property dbDelay = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65565))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code dbDelay} property.
   * @see #dbDelay
   */
  public BHonStatusNumeric getDbDelay() { return (BHonStatusNumeric)get(dbDelay); }

  /**
   * Set the {@code dbDelay} property.
   * @see #dbDelay
   */
  public void setDbDelay(BHonStatusNumeric v) { set(dbDelay, v, null); }

  //endregion Property "dbDelay"

  //region Property "revAct"

  /**
   * Slot for the {@code revAct} property.
   * @see #getRevAct
   * @see #setRevAct
   */
  public static final Property revAct = newProperty(0, BDirectReverseSignOfTREnum.DEFAULT, null);

  /**
   * Get the {@code revAct} property.
   * @see #revAct
   */
  public BDirectReverseSignOfTREnum getRevAct() { return (BDirectReverseSignOfTREnum)get(revAct); }

  /**
   * Set the {@code revAct} property.
   * @see #revAct
   */
  public void setRevAct(BDirectReverseSignOfTREnum v) { set(revAct, v, null); }

  //endregion Property "revAct"

  //region Property "bias"

  /**
   * Slot for the {@code bias} property.
   * @see #getBias
   * @see #setBias
   */
  public static final Property bias = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 100)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code bias} property.
   * @see #bias
   */
  public int getBias() { return getInt(bias); }

  /**
   * Set the {@code bias} property.
   * @see #bias
   */
  public void setBias(int v) { setInt(bias, v, null); }

  //endregion Property "bias"

  //region Property "outputRange"

  /**
   * Slot for the {@code outputRange} property.
   * @see #getOutputRange
   * @see #setOutputRange
   */
  public static final Property outputRange = newProperty(Flags.SUMMARY, BPidOutputRangeEnum.DEFAULT, null);

  /**
   * Get the {@code outputRange} property.
   * @see #outputRange
   */
  public BPidOutputRangeEnum getOutputRange() { return (BPidOutputRangeEnum)get(outputRange); }

  /**
   * Set the {@code outputRange} property.
   * @see #outputRange
   */
  public void setOutputRange(BPidOutputRangeEnum v) { set(outputRange, v, null); }

  //endregion Property "outputRange"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "dBTimer"

  /**
   * Slot for the {@code dBTimer} property.
   * @see #getDBTimer
   * @see #setDBTimer
   */
  public static final Property dBTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code dBTimer} property.
   * @see #dBTimer
   */
  public double getDBTimer() { return getDouble(dBTimer); }

  /**
   * Set the {@code dBTimer} property.
   * @see #dBTimer
   */
  public void setDBTimer(double v) { setDouble(dBTimer, v, null); }

  //endregion Property "dBTimer"

  //region Property "oldErr"

  /**
   * Slot for the {@code oldErr} property.
   * @see #getOldErr
   * @see #setOldErr
   */
  public static final Property oldErr = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code oldErr} property.
   * @see #oldErr
   */
  public double getOldErr() { return getDouble(oldErr); }

  /**
   * Set the {@code oldErr} property.
   * @see #oldErr
   */
  public void setOldErr(double v) { setDouble(oldErr, v, null); }

  //endregion Property "oldErr"

  //region Property "intglerr"

  /**
   * Slot for the {@code intglerr} property.
   * @see #getIntglerr
   * @see #setIntglerr
   */
  public static final Property intglerr = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code intglerr} property.
   * @see #intglerr
   */
  public double getIntglerr() { return getDouble(intglerr); }

  /**
   * Set the {@code intglerr} property.
   * @see #intglerr
   */
  public void setIntglerr(double v) { setDouble(intglerr, v, null); }

  //endregion Property "intglerr"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPid.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/


	/**
	 * Default Constructor
	 */
	public BPid() {
		// EMPTY IMPLEMENTATION
	}
	@Override
	public void started() throws Exception {
		super.started();
		updateDisableSlotAfterNegate();
			}
	
	private void updateDisableSlotAfterNegate() {
		BFiniteStatusBoolean disable1 = getDisable();
		if(!(disable1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(disable1.getStatus());
			negatableFiniteStatusBoolean.setValue(disable1.getValue());
			setDisable(negatableFiniteStatusBoolean);
		}
	}
	
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		getOUTPUT().setValue(0);
		setIntglerr(0);
		setOldErr(0);
	}

	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executePID(executionParams);
	}
	
	private void executePID(BExecutionParams executionParams) {
		// Determine whether direct or reverse acting and establish a positive
		// value for throttling range. The revAct configuration parameter
		// indicates the desired behavior. If the direct/reverse is to be
		// driven by the sign of the tr input, and the input tr is negative,
		// select reverse action and negate the tr to make it positive
		// hereafter.
		boolean revActing = false;
		double trVal = getTr().getValue();
		double iterationIntervalInSec = (double)executionParams.getIterationInterval()/UnitConstants.THOUSAND_MILLI_SECOND;
		
		if(getRevAct().getOrdinal() == BDirectReverseSignOfTREnum.REVERSE_ACTING) {
			revActing = true;
		} else if ((getRevAct().getOrdinal() == BDirectReverseSignOfTREnum.SIGN_OF_TR) && (trVal < 0.0)) {
			revActing = true;
			trVal = -trVal;
		}
		

		if (disablePid(trVal)) {
			getOUTPUT().setValue(0);
			setIntglerr(0);
			setOldErr(0);
		} else {
			// ************************************************
			// ******** gain calculations ********
			Gains gains = new Gains();
			gains.calculateGains(trVal, iterationIntervalInSec);

			int biasVal = getBiasVal();

			double sensorVal = getSensor().getValue();
			double setptVal = getSetPt().getValue();

			double propErr = sensorVal - setptVal;

			if (revActing)
				propErr = -propErr;

			double delta = propErr - this.getOldErr();
			this.setOldErr(propErr);
			double derivErr = delta * gains.getGainDeriv();
			derivErr = limitInput(derivErr, MINUS_HUNDRED, HUNDRED, 0.0);
			double totErr = biasVal + propErr * gains.getGainPro() + this.getIntglerr() + derivErr;

			totErr = getTotalErrorAfterDeadbandOperation(propErr, trVal, totErr, gains, iterationIntervalInSec);

			getOUTPUT().setValue(totErr);
		}
	}

	private int getBiasVal() {
		int biasVal = getBias();
		if (biasVal < 0 || biasVal > HUNDRED)
			biasVal = 0;

		return biasVal;
	}

	private double getTotalErrorAfterDeadbandOperation(double propErr, double trVal, double totErr, Gains gains,
			double iterationInterval) {
		// ***** deadband operation
		double ipErr = propErr;
		double tempTotError = totErr;
		double deadBandVal = 0;
		double dbDelayVal = 0;
		int biasVal = getBiasVal();

		if (isConfigured(BPid.deadBand))
			deadBandVal = getDeadBand().getValue();
		if (isConfigured(BPid.dbDelay))
			dbDelayVal = getDbDelay().getValue();
		double db = limitInput(deadBandVal, 0, trVal, 0);
		double dlay = limitInput(dbDelayVal, 0, DB_DELAY_LIMIT, 0);

		if (propErr > -db && propErr < db) {
			if (this.getDBTimer() < dlay) {
				this.setDBTimer(this.getDBTimer() + iterationInterval);
			} else {
				ipErr = 0;
				tempTotError = getOUTPUT().getValue();
			}
		} else {
			this.setDBTimer(0);
		}

		if ((propErr > HUNDRED / gains.getGainPro()) || (propErr < MINUS_HUNDRED / gains.getGainPro())) {
			ipErr = 0;
		}

		setIntglerr(this.getIntglerr() + ipErr * gains.getGainIntgl());
		setIntglerr(limitInput(getIntglerr(), 0.0 - biasVal, HUNDRED - biasVal, 0));
		BPidOutputRangeEnum currentOutoutRange = getOutputRange();
		tempTotError = currentOutoutRange.getEnum().equals(BPidOutputRangeEnum.pidOutputRange0to100) ? limitInput(tempTotError, ZERO,  HUNDRED, 0) : 
				limitInput(tempTotError, MINUS_TWO_HUNDRED, TWO_HUNDRED, 0);
		return tempTotError;
	}

	private boolean disablePid(double trVal) {
		// Disable PID if sensor (or) setPt (or) tr is unconnected/NullStatus
		if (!isSlotValueValid(sensor) || !isSlotValueValid(setPt) || !isSlotValueValid(tr)) {
			return true;
		} else if (!isConfigured(disable)) {
			return false;
		}

		boolean disablePidBlock = false;
		boolean isDisable = getDisable().getBoolean();
		if (((BNegatableFiniteStatusBoolean) getDisable()).getNegate()) {
			isDisable = !isDisable;
		}
		if (isDisable || trVal <= 0.0)
			disablePidBlock = true;
		
		
		return disablePidBlock;
	}

	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(sensor);
		propertyArrayList.add(setPt);
		propertyArrayList.add(tr);
		propertyArrayList.add(intgTime);
		propertyArrayList.add(dervTime);
		propertyArrayList.add(deadBand);
		propertyArrayList.add(dbDelay);
		propertyArrayList.add(disable);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(revAct);
		properties.add(bias);
		return properties;
	}

	private class Gains {
		private double gainPro;
		private double gainIntgl;
		private double gainDeriv;

		public double getGainPro() {
			return gainPro;
		}

		public double getGainIntgl() {
			return gainIntgl;
		}

		public double getGainDeriv() {
			return gainDeriv;
		}

		public void calculateGains(double trVal, double iterationInterval) {
			// ************************************************
			// ******** gain calculations ********
			double dt;
			gainPro = HUNDRED / trVal;

			// Set the integral gain and the derivative gain to 0 when the loop
			// is executed on demand, so we generate output but do not change
			// any time related factors.
			if (Double.compare(iterationInterval, 0.0) == 0) {
				gainIntgl = 0;
				gainDeriv = 0;
			} else {
				gainIntgl = getIntegralGain(gainPro, iterationInterval);
				if (!isConfigured(BPid.dervTime)) {
					dt = 0;
				} else {
					dt = limitInput(getDervTime().getValue(), 0, Float.MAX_VALUE, 0);
				}
				gainDeriv = gainPro * dt / iterationInterval;
			}
		}

		private double getIntegralGain(double gainPro, double iterationInterval) {
			double intgGain;
			double intgTimeVal = getIntgTime().getValue();
			if (!isConfigured(BPid.intgTime) || isInvalidValue(BPid.intgTime) || intgTimeVal <= 0.0) {
				intgGain = 0;
			} else {
				// When an integral time has been specified, set the
				// integral gain to the time between iterations, in seconds,
				// times the ratio of proportional gain to integral time.

				intgGain = iterationInterval * gainPro / intgTimeVal;
			}
			return intgGain;
		}

	}
	

	private static final double HUNDRED = 100.0;
	private static final double MINUS_HUNDRED = -100.0;
	private static final double TWO_HUNDRED = 200.0;
	private static final double MINUS_TWO_HUNDRED = -200.0;
	private static final double DB_DELAY_LIMIT = 65565.0;
	private static final double ZERO = 0;

}
