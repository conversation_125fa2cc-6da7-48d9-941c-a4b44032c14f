/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Stager block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - Suresh Khatri
 * @since Dec 27, 2017
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S00103" })

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"stager_thermostat_cycler.png\")", flags=Flags.HIDDEN|Flags.READONLY)
// Analog Inputs
@NiagaraProperty(name = "in", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "-200"), @Facet(name = "BFacets.MAX", value = "200"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"),
	@Facet(name = "BFacets.PRECISION", value = "6"), })
@NiagaraProperty(name = "maxStgs", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(1.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "1"), @Facet(name = "BFacets.MAX", value = "255"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), @Facet(name = "BFacets.PRECISION", value = "0"), })
@NiagaraProperty(name = "minOn", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "0"), @Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0"), })
@NiagaraProperty(name = "minOff", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "0"), @Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0"), })
@NiagaraProperty(name = "intstgOn", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "0"), @Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0"), })
@NiagaraProperty(name = "intstgOff", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.SUMMARY, facets = {
	@Facet(name = "BFacets.MIN", value = "0"), @Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0"), })

//Logical Inputs
@NiagaraProperty(name = "overrideOff", type = "BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags = Flags.SUMMARY)
@NiagaraProperty(name = "disable", type = "BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags = Flags.SUMMARY)

//Configuration
@NiagaraProperty(name = "hyst", type = "int", defaultValue = "0", facets = { @Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "100"), @Facet(name = "BFacets.UNITS", value = "BUnit.NULL"),
	@Facet(name = "BFacets.PRECISION", value = "0"), })

// Output
@NiagaraProperty(name = "STAGES_ACTIVE", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0F, BStatus.ok)", flags = Flags.READONLY|Flags.DEFAULT_ON_CLONE
	| Flags.TRANSIENT | Flags.SUMMARY, facets = { @Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "255"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"),
		@Facet(name = "BFacets.PRECISION", value = "0"),
    @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
    @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")  })

//LoopStaticVariables
@NiagaraProperty(name="onTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="offTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

public class BStager extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BStager(2001727240)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "stager_thermostat_cycler.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "in"

  /**
   * Slot for the {@code in} property.
   *  Analog Inputs
   * @see #getIn
   * @see #setIn
   */
  public static final Property in = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, -200), BFacets.make(BFacets.MAX, 200)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in} property.
   *  Analog Inputs
   * @see #in
   */
  public BHonStatusNumeric getIn() { return (BHonStatusNumeric)get(in); }

  /**
   * Set the {@code in} property.
   *  Analog Inputs
   * @see #in
   */
  public void setIn(BHonStatusNumeric v) { set(in, v, null); }

  //endregion Property "in"

  //region Property "maxStgs"

  /**
   * Slot for the {@code maxStgs} property.
   * @see #getMaxStgs
   * @see #setMaxStgs
   */
  public static final Property maxStgs = newProperty(Flags.SUMMARY, new BHonStatusNumeric(1.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code maxStgs} property.
   * @see #maxStgs
   */
  public BHonStatusNumeric getMaxStgs() { return (BHonStatusNumeric)get(maxStgs); }

  /**
   * Set the {@code maxStgs} property.
   * @see #maxStgs
   */
  public void setMaxStgs(BHonStatusNumeric v) { set(maxStgs, v, null); }

  //endregion Property "maxStgs"

  //region Property "minOn"

  /**
   * Slot for the {@code minOn} property.
   * @see #getMinOn
   * @see #setMinOn
   */
  public static final Property minOn = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code minOn} property.
   * @see #minOn
   */
  public BHonStatusNumeric getMinOn() { return (BHonStatusNumeric)get(minOn); }

  /**
   * Set the {@code minOn} property.
   * @see #minOn
   */
  public void setMinOn(BHonStatusNumeric v) { set(minOn, v, null); }

  //endregion Property "minOn"

  //region Property "minOff"

  /**
   * Slot for the {@code minOff} property.
   * @see #getMinOff
   * @see #setMinOff
   */
  public static final Property minOff = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code minOff} property.
   * @see #minOff
   */
  public BHonStatusNumeric getMinOff() { return (BHonStatusNumeric)get(minOff); }

  /**
   * Set the {@code minOff} property.
   * @see #minOff
   */
  public void setMinOff(BHonStatusNumeric v) { set(minOff, v, null); }

  //endregion Property "minOff"

  //region Property "intstgOn"

  /**
   * Slot for the {@code intstgOn} property.
   * @see #getIntstgOn
   * @see #setIntstgOn
   */
  public static final Property intstgOn = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code intstgOn} property.
   * @see #intstgOn
   */
  public BHonStatusNumeric getIntstgOn() { return (BHonStatusNumeric)get(intstgOn); }

  /**
   * Set the {@code intstgOn} property.
   * @see #intstgOn
   */
  public void setIntstgOn(BHonStatusNumeric v) { set(intstgOn, v, null); }

  //endregion Property "intstgOn"

  //region Property "intstgOff"

  /**
   * Slot for the {@code intstgOff} property.
   * @see #getIntstgOff
   * @see #setIntstgOff
   */
  public static final Property intstgOff = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code intstgOff} property.
   * @see #intstgOff
   */
  public BHonStatusNumeric getIntstgOff() { return (BHonStatusNumeric)get(intstgOff); }

  /**
   * Set the {@code intstgOff} property.
   * @see #intstgOff
   */
  public void setIntstgOff(BHonStatusNumeric v) { set(intstgOff, v, null); }

  //endregion Property "intstgOff"

  //region Property "overrideOff"

  /**
   * Slot for the {@code overrideOff} property.
   * Logical Inputs
   * @see #getOverrideOff
   * @see #setOverrideOff
   */
  public static final Property overrideOff = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code overrideOff} property.
   * Logical Inputs
   * @see #overrideOff
   */
  public BFiniteStatusBoolean getOverrideOff() { return (BFiniteStatusBoolean)get(overrideOff); }

  /**
   * Set the {@code overrideOff} property.
   * Logical Inputs
   * @see #overrideOff
   */
  public void setOverrideOff(BFiniteStatusBoolean v) { set(overrideOff, v, null); }

  //endregion Property "overrideOff"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "hyst"

  /**
   * Slot for the {@code hyst} property.
   * Configuration
   * @see #getHyst
   * @see #setHyst
   */
  public static final Property hyst = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 100)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code hyst} property.
   * Configuration
   * @see #hyst
   */
  public int getHyst() { return getInt(hyst); }

  /**
   * Set the {@code hyst} property.
   * Configuration
   * @see #hyst
   */
  public void setHyst(int v) { setInt(hyst, v, null); }

  //endregion Property "hyst"

  //region Property "STAGES_ACTIVE"

  /**
   * Slot for the {@code STAGES_ACTIVE} property.
   *  Output
   * @see #getSTAGES_ACTIVE
   * @see #setSTAGES_ACTIVE
   */
  public static final Property STAGES_ACTIVE = newProperty(Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT | Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGES_ACTIVE} property.
   *  Output
   * @see #STAGES_ACTIVE
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getSTAGES_ACTIVE() { return (BHonStatusNumeric)get(STAGES_ACTIVE); }

  /**
   * Set the {@code STAGES_ACTIVE} property.
   *  Output
   * @see #STAGES_ACTIVE
   */
  @SuppressWarnings("squid:S00100")
  public void setSTAGES_ACTIVE(BHonStatusNumeric v) { set(STAGES_ACTIVE, v, null); }

  //endregion Property "STAGES_ACTIVE"

  //region Property "onTimer"

  /**
   * Slot for the {@code onTimer} property.
   * LoopStaticVariables
   * @see #getOnTimer
   * @see #setOnTimer
   */
  public static final Property onTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code onTimer} property.
   * LoopStaticVariables
   * @see #onTimer
   */
  public double getOnTimer() { return getDouble(onTimer); }

  /**
   * Set the {@code onTimer} property.
   * LoopStaticVariables
   * @see #onTimer
   */
  public void setOnTimer(double v) { setDouble(onTimer, v, null); }

  //endregion Property "onTimer"

  //region Property "offTimer"

  /**
   * Slot for the {@code offTimer} property.
   * @see #getOffTimer
   * @see #setOffTimer
   */
  public static final Property offTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code offTimer} property.
   * @see #offTimer
   */
  public double getOffTimer() { return getDouble(offTimer); }

  /**
   * Set the {@code offTimer} property.
   * @see #offTimer
   */
  public void setOffTimer(double v) { setDouble(offTimer, v, null); }

  //endregion Property "offTimer"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStager.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
    @Override
    public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
    	super.initHoneywellComponent(executionParams);
    	stgsAct = 0;
    	setOnTimer(getIntstgOffPropertyValue());
    	setOffTimer(0);
    }

    @Override
    public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
    	super.executeHoneywellComponent(executionParams);
    	
    	executeStager(executionParams);
    }
    
    @Override
	public void started() throws Exception {
		super.started();
		addNegateSupportOnDisableSlot();
		addNegateSupportOnOverrideOffSlot();
	}
	
	private void addNegateSupportOnDisableSlot() {
		BFiniteStatusBoolean disable1 = getDisable();
		if(!(disable1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(disable1.getStatus());
			negatableFiniteStatusBoolean.setValue(disable1.getValue());
			setDisable(negatableFiniteStatusBoolean);
		}
	}
	
	private void addNegateSupportOnOverrideOffSlot() {
		BFiniteStatusBoolean overrideoff1 = getOverrideOff();
		if(!(overrideoff1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(overrideoff1.getStatus());
			negatableFiniteStatusBoolean.setValue(overrideoff1.getValue());
			setOverrideOff(negatableFiniteStatusBoolean);
		}
	}
	
    private void executeStager(BExecutionParams executionParams) {
    	boolean disable1 = getDisablePropertyValue();
    	maxStgs1 = (int) getMaxStgsPropertyValue();
    	if (maxStgs1 == 0) {
    		maxStgs1 = 1;
    		disable1 = true;
    	}

    	if (disable1 || !isConfigured(in)) {
    		getSTAGES_ACTIVE().setValue(0);
    		setOnTimer(intStgOnSec);
    		setOffTimer(0);
    		return;
    	}

    	initializeProperites(executionParams);

    	updateActiveStages();

    	if (stgsAct > maxStgs1) {
    		stgsAct = maxStgs1;
    	}
    	getSTAGES_ACTIVE().setValue(stgsAct);
    }

    private void updateActiveStages() {
    	if (condition1() && condition2()) {
    		stgsAct++;
    		setOnTimer(0);
    	}

    	if (condition3() && condition4()) {
    		stgsAct--;
    		setOffTimer(0);
    	}
    }

    private boolean condition1() {
    	return (stgsAct < maxStgs1) && (getOffTimer() > minOffSec1) && (getOnTimer() > intStgOnSec);
    }

    private boolean condition2() {
    	return stagerErr > stgsAct * delta && !overrideOff1;
    }

    private boolean condition3() {
    	return (stgsAct > 0) && (getOnTimer() > minOnSec1) && (getOffTimer() > intStgOffSec);
    }

    private boolean condition4() {
    	return stagerErr < ((stgsAct - 1) * delta - hyst1) || overrideOff1;
    }

    private void initializeProperites(BExecutionParams executionParams) {
    	overrideOff1 = getOverrideOffPropertyValue();
    	intStgOffSec = (int) getIntstgOffPropertyValue();
    	delta = HUNDRED / (double) maxStgs1;
    	hyst1 = getHyst();

    	if (hyst1 <= 0.0 || hyst1 >= HUNDRED / maxStgs1)
    		hyst1 = NINTYNINE / (double) maxStgs1;
    	stagerErr = getInPropertyValue();
    	minOnSec1 = (int) getMinOnPropertyValue();
    	minOffSec1 = (int) getMinOffPropertyValue();
    	intStgOnSec = (int) getIntstgOnPropertyValue();
    	stgsAct = (int) limitInput(getSTAGES_ACTIVE().getValue(), 0, ONEBYTE, 0);
    	setOnTimer(getOnTimer() + (executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND));
    	if (getOnTimer() >= MAXONTIME)
    		setOnTimer(MAXONTIME);// 18 hrs max
    	setOffTimer(getOffTimer() + (executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND));
    	if (getOffTimer() >= MAXOFFTIME)
    		setOffTimer(MAXOFFTIME);// 18 hrs max

    }

    private boolean getDisablePropertyValue() {
    	if (isSlotValueValid(disable)) {
    		return ((BNegatableFiniteStatusBoolean) getDisable()).getNegate() ? !getDisable().getBoolean() : getDisable().getBoolean();
    	}
    	return false;
    }

    private boolean getOverrideOffPropertyValue() {
    	if (isSlotValueValid(overrideOff)) {
    		return ((BNegatableFiniteStatusBoolean) getOverrideOff()).getNegate() ? !getOverrideOff().getBoolean():getOverrideOff().getBoolean();
    	}
    	return false;
    }

    private double getInPropertyValue() {
    	if(isConfigured(in) && !Double.isNaN(getIn().getValue())) {
    		return getIn().getValue();
    	}
    	return 0;
    }

    private double getMaxStgsPropertyValue() {
    	if (!isSlotValueValid(maxStgs)) {
    		return 0;
    	}
    	return limitInput(getMaxStgs().getValue(), 0.0, MAXSTAGES, 0.0);
    }

    private double getMinOnPropertyValue() {
    	if (isSlotValueValid(minOn)) {
    		return limitInput(getMinOn().getValue(), 0.0, MINONSEC, 0.0);
    	}
    	return 0;
    }

    private double getMinOffPropertyValue() {
    	if (isSlotValueValid(minOff)) {
    		return limitInput(getMinOff().getValue(), 0.0, MINOFFSEC, 0.0);
    	}
    	return 0;
    }

    private double getIntstgOffPropertyValue() {
    	if (isSlotValueValid(intstgOff)) {
    		return limitInput(getIntstgOff().getValue(), 0.0, INTERSTAGEOFFSEC, 0.0);
    	}
    	return 0;
    }

    private double getIntstgOnPropertyValue() {
    	if (isSlotValueValid(intstgOn)) {
    		return limitInput(getIntstgOn().getValue(), 0.0, INTERSTAGEONSEC, 0.0);
    	}
    	return 0;
    }

    @Override
    public List<Property> getInputPropertiesList() {
    	List<Property> properties = super.getInputPropertiesList();
    	properties.add(disable);
    	properties.add(overrideOff);
    	properties.add(in);
    	properties.add(maxStgs);
    	properties.add(minOn);
    	properties.add(minOff);
    	properties.add(intstgOn);
    	properties.add(intstgOff);
    	return properties;
    }

	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
    	properties.add(STAGES_ACTIVE);
    	return properties;
	}

	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(hyst);
		return propertyArrayList;
	}
    
    private int maxStgs1;
    private boolean overrideOff1;
    private double hyst1;
    private double delta;
    private double stagerErr;
    private int stgsAct;
    private int minOnSec1;
    private int minOffSec1;
    private int intStgOnSec;
    private int intStgOffSec;
    private static final int MAXSTAGES = 255;
    private static final int INTERSTAGEONSEC = 64799;
    private static final int INTERSTAGEOFFSEC = 64799;
    private static final int MINONSEC = 64799;
    private static final int MINOFFSEC = 64799;
    private static final int MAXONTIME = 64800;
    private static final int MAXOFFTIME = 64800;
    private static final int HUNDRED = 100;
    private static final int ONEBYTE = 255;
    private static final float NINTYNINE = 99.9F;
}
