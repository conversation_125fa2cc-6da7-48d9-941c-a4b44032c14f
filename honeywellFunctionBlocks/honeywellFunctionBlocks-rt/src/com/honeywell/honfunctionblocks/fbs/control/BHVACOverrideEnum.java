/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of FlowControl block implementation
 * This ENUM is used Manual Flow Override values used in Flow control block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Jan 5, 2018
 */

@NiagaraType
@NiagaraEnum(range = {
	@Range("Hvo_OFF"),
	@Range("Hvo_POSITION"),
	@Range("Hvo_FLOW_VALUE"),
	@Range("Hvo_FLOW_PERCENT"),
	@Range("Hvo_OPEN"),
	@Range("Hvo_CLOSE"),
	@Range("Hvo_MINIMUM"),
	@Range("Hvo_MAXIMUM"),
	@Range("Hvo_NUL"),
})

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
public final class BHVACOverrideEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BHVACOverrideEnum(4063011263)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Hvo_OFF. */
  public static final int HVO_OFF = 0;
  /** Ordinal value for Hvo_POSITION. */
  public static final int HVO_POSITION = 1;
  /** Ordinal value for Hvo_FLOW_VALUE. */
  public static final int HVO_FLOW_VALUE = 2;
  /** Ordinal value for Hvo_FLOW_PERCENT. */
  public static final int HVO_FLOW_PERCENT = 3;
  /** Ordinal value for Hvo_OPEN. */
  public static final int HVO_OPEN = 4;
  /** Ordinal value for Hvo_CLOSE. */
  public static final int HVO_CLOSE = 5;
  /** Ordinal value for Hvo_MINIMUM. */
  public static final int HVO_MINIMUM = 6;
  /** Ordinal value for Hvo_MAXIMUM. */
  public static final int HVO_MAXIMUM = 7;
  /** Ordinal value for Hvo_NUL. */
  public static final int HVO_NUL = 8;

  /** BHVACOverrideEnum constant for Hvo_OFF. */
  public static final BHVACOverrideEnum Hvo_OFF = new BHVACOverrideEnum(HVO_OFF);
  /** BHVACOverrideEnum constant for Hvo_POSITION. */
  public static final BHVACOverrideEnum Hvo_POSITION = new BHVACOverrideEnum(HVO_POSITION);
  /** BHVACOverrideEnum constant for Hvo_FLOW_VALUE. */
  public static final BHVACOverrideEnum Hvo_FLOW_VALUE = new BHVACOverrideEnum(HVO_FLOW_VALUE);
  /** BHVACOverrideEnum constant for Hvo_FLOW_PERCENT. */
  public static final BHVACOverrideEnum Hvo_FLOW_PERCENT = new BHVACOverrideEnum(HVO_FLOW_PERCENT);
  /** BHVACOverrideEnum constant for Hvo_OPEN. */
  public static final BHVACOverrideEnum Hvo_OPEN = new BHVACOverrideEnum(HVO_OPEN);
  /** BHVACOverrideEnum constant for Hvo_CLOSE. */
  public static final BHVACOverrideEnum Hvo_CLOSE = new BHVACOverrideEnum(HVO_CLOSE);
  /** BHVACOverrideEnum constant for Hvo_MINIMUM. */
  public static final BHVACOverrideEnum Hvo_MINIMUM = new BHVACOverrideEnum(HVO_MINIMUM);
  /** BHVACOverrideEnum constant for Hvo_MAXIMUM. */
  public static final BHVACOverrideEnum Hvo_MAXIMUM = new BHVACOverrideEnum(HVO_MAXIMUM);
  /** BHVACOverrideEnum constant for Hvo_NUL. */
  public static final BHVACOverrideEnum Hvo_NUL = new BHVACOverrideEnum(HVO_NUL);

  /** Factory method with ordinal. */
  public static BHVACOverrideEnum make(int ordinal)
  {
    return (BHVACOverrideEnum)Hvo_OFF.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BHVACOverrideEnum make(String tag)
  {
    return (BHVACOverrideEnum)Hvo_OFF.getRange().get(tag);
  }

  /** Private constructor. */
  private BHVACOverrideEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BHVACOverrideEnum DEFAULT = Hvo_OFF;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHVACOverrideEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
