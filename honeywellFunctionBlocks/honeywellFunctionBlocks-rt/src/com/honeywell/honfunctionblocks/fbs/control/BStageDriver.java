/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.rpc.NiagaraRpc;
import javax.baja.rpc.Transport;
import javax.baja.rpc.TransportType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.BInteger;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.enums.BLeadLagStrategyEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Stage Driver as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-254
 * <AUTHOR> - Lavanya
 * @since Dec 22, 2017
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"stage_driver.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "nStagesActive", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = {
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name = "runtimeReset", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = {
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(1)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name="leadLag", type="BLeadLagStrategyEnum", defaultValue="BLeadLagStrategyEnum.DEFAULT")
@NiagaraProperty(name="maxStgs", type="int", defaultValue="1",
facets = {
		@Facet(name = "BFacets.MIN", value = "1"),
		@Facet(name = "BFacets.MAX", value = "BInteger.make(20)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.INT_UX_FE")
})

@NiagaraProperty(name="stageStatusParams", type="BStageStaus", defaultValue="new BStageStaus()",flags=Flags.HIDDEN|Flags.TRANSIENT|Flags.READONLY|Flags.DEFAULT_ON_CLONE)
@NiagaraProperty(name="llFOFOStrategyParams", type="BFOFOStrategy", defaultValue="new BFOFOStrategy()",flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="llRuntimeStrategyParams", type="BRuntimeStrategy", defaultValue="new BRuntimeStrategy()",flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)



@NiagaraProperty(name = "STAGE1", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.SUMMARY |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE2", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE3", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE4", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE5", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE6", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE7", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE8", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE9", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE10", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE11", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE12", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE13", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE14", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE15", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE16", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE17", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE18", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE19", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name = "STAGE20", type = "BHonStatusBoolean", defaultValue = "new BHonStatusBoolean(false, BStatus.ok)", flags = Flags.HIDDEN |Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})



@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public class BStageDriver extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BStageDriver(900496062)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "stage_driver.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "nStagesActive"

  /**
   * Slot for the {@code nStagesActive} property.
   * @see #getNStagesActive
   * @see #setNStagesActive
   */
  public static final Property nStagesActive = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code nStagesActive} property.
   * @see #nStagesActive
   */
  public BHonStatusNumeric getNStagesActive() { return (BHonStatusNumeric)get(nStagesActive); }

  /**
   * Set the {@code nStagesActive} property.
   * @see #nStagesActive
   */
  public void setNStagesActive(BHonStatusNumeric v) { set(nStagesActive, v, null); }

  //endregion Property "nStagesActive"

  //region Property "runtimeReset"

  /**
   * Slot for the {@code runtimeReset} property.
   * @see #getRuntimeReset
   * @see #setRuntimeReset
   */
  public static final Property runtimeReset = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(1))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code runtimeReset} property.
   * @see #runtimeReset
   */
  public BHonStatusNumeric getRuntimeReset() { return (BHonStatusNumeric)get(runtimeReset); }

  /**
   * Set the {@code runtimeReset} property.
   * @see #runtimeReset
   */
  public void setRuntimeReset(BHonStatusNumeric v) { set(runtimeReset, v, null); }

  //endregion Property "runtimeReset"

  //region Property "leadLag"

  /**
   * Slot for the {@code leadLag} property.
   * @see #getLeadLag
   * @see #setLeadLag
   */
  public static final Property leadLag = newProperty(0, BLeadLagStrategyEnum.DEFAULT, null);

  /**
   * Get the {@code leadLag} property.
   * @see #leadLag
   */
  public BLeadLagStrategyEnum getLeadLag() { return (BLeadLagStrategyEnum)get(leadLag); }

  /**
   * Set the {@code leadLag} property.
   * @see #leadLag
   */
  public void setLeadLag(BLeadLagStrategyEnum v) { set(leadLag, v, null); }

  //endregion Property "leadLag"

  //region Property "maxStgs"

  /**
   * Slot for the {@code maxStgs} property.
   * @see #getMaxStgs
   * @see #setMaxStgs
   */
  public static final Property maxStgs = newProperty(0, 1, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, BInteger.make(20))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.INT_UX_FE)));

  /**
   * Get the {@code maxStgs} property.
   * @see #maxStgs
   */
  public int getMaxStgs() { return getInt(maxStgs); }

  /**
   * Set the {@code maxStgs} property.
   * @see #maxStgs
   */
  public void setMaxStgs(int v) { setInt(maxStgs, v, null); }

  //endregion Property "maxStgs"

  //region Property "stageStatusParams"

  /**
   * Slot for the {@code stageStatusParams} property.
   * @see #getStageStatusParams
   * @see #setStageStatusParams
   */
  public static final Property stageStatusParams = newProperty(Flags.HIDDEN | Flags.TRANSIENT | Flags.READONLY | Flags.DEFAULT_ON_CLONE, new BStageStaus(), null);

  /**
   * Get the {@code stageStatusParams} property.
   * @see #stageStatusParams
   */
  public BStageStaus getStageStatusParams() { return (BStageStaus)get(stageStatusParams); }

  /**
   * Set the {@code stageStatusParams} property.
   * @see #stageStatusParams
   */
  public void setStageStatusParams(BStageStaus v) { set(stageStatusParams, v, null); }

  //endregion Property "stageStatusParams"

  //region Property "llFOFOStrategyParams"

  /**
   * Slot for the {@code llFOFOStrategyParams} property.
   * @see #getLlFOFOStrategyParams
   * @see #setLlFOFOStrategyParams
   */
  public static final Property llFOFOStrategyParams = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BFOFOStrategy(), null);

  /**
   * Get the {@code llFOFOStrategyParams} property.
   * @see #llFOFOStrategyParams
   */
  public BFOFOStrategy getLlFOFOStrategyParams() { return (BFOFOStrategy)get(llFOFOStrategyParams); }

  /**
   * Set the {@code llFOFOStrategyParams} property.
   * @see #llFOFOStrategyParams
   */
  public void setLlFOFOStrategyParams(BFOFOStrategy v) { set(llFOFOStrategyParams, v, null); }

  //endregion Property "llFOFOStrategyParams"

  //region Property "llRuntimeStrategyParams"

  /**
   * Slot for the {@code llRuntimeStrategyParams} property.
   * @see #getLlRuntimeStrategyParams
   * @see #setLlRuntimeStrategyParams
   */
  public static final Property llRuntimeStrategyParams = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BRuntimeStrategy(), null);

  /**
   * Get the {@code llRuntimeStrategyParams} property.
   * @see #llRuntimeStrategyParams
   */
  public BRuntimeStrategy getLlRuntimeStrategyParams() { return (BRuntimeStrategy)get(llRuntimeStrategyParams); }

  /**
   * Set the {@code llRuntimeStrategyParams} property.
   * @see #llRuntimeStrategyParams
   */
  public void setLlRuntimeStrategyParams(BRuntimeStrategy v) { set(llRuntimeStrategyParams, v, null); }

  //endregion Property "llRuntimeStrategyParams"

  //region Property "STAGE1"

  /**
   * Slot for the {@code STAGE1} property.
   * @see #getSTAGE1
   * @see #setSTAGE1
   */
  public static final Property STAGE1 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE1} property.
   * @see #STAGE1
   */
  public BHonStatusBoolean getSTAGE1() { return (BHonStatusBoolean)get(STAGE1); }

  /**
   * Set the {@code STAGE1} property.
   * @see #STAGE1
   */
  public void setSTAGE1(BHonStatusBoolean v) { set(STAGE1, v, null); }

  //endregion Property "STAGE1"

  //region Property "STAGE2"

  /**
   * Slot for the {@code STAGE2} property.
   * @see #getSTAGE2
   * @see #setSTAGE2
   */
  public static final Property STAGE2 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE2} property.
   * @see #STAGE2
   */
  public BHonStatusBoolean getSTAGE2() { return (BHonStatusBoolean)get(STAGE2); }

  /**
   * Set the {@code STAGE2} property.
   * @see #STAGE2
   */
  public void setSTAGE2(BHonStatusBoolean v) { set(STAGE2, v, null); }

  //endregion Property "STAGE2"

  //region Property "STAGE3"

  /**
   * Slot for the {@code STAGE3} property.
   * @see #getSTAGE3
   * @see #setSTAGE3
   */
  public static final Property STAGE3 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE3} property.
   * @see #STAGE3
   */
  public BHonStatusBoolean getSTAGE3() { return (BHonStatusBoolean)get(STAGE3); }

  /**
   * Set the {@code STAGE3} property.
   * @see #STAGE3
   */
  public void setSTAGE3(BHonStatusBoolean v) { set(STAGE3, v, null); }

  //endregion Property "STAGE3"

  //region Property "STAGE4"

  /**
   * Slot for the {@code STAGE4} property.
   * @see #getSTAGE4
   * @see #setSTAGE4
   */
  public static final Property STAGE4 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE4} property.
   * @see #STAGE4
   */
  public BHonStatusBoolean getSTAGE4() { return (BHonStatusBoolean)get(STAGE4); }

  /**
   * Set the {@code STAGE4} property.
   * @see #STAGE4
   */
  public void setSTAGE4(BHonStatusBoolean v) { set(STAGE4, v, null); }

  //endregion Property "STAGE4"

  //region Property "STAGE5"

  /**
   * Slot for the {@code STAGE5} property.
   * @see #getSTAGE5
   * @see #setSTAGE5
   */
  public static final Property STAGE5 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE5} property.
   * @see #STAGE5
   */
  public BHonStatusBoolean getSTAGE5() { return (BHonStatusBoolean)get(STAGE5); }

  /**
   * Set the {@code STAGE5} property.
   * @see #STAGE5
   */
  public void setSTAGE5(BHonStatusBoolean v) { set(STAGE5, v, null); }

  //endregion Property "STAGE5"

  //region Property "STAGE6"

  /**
   * Slot for the {@code STAGE6} property.
   * @see #getSTAGE6
   * @see #setSTAGE6
   */
  public static final Property STAGE6 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE6} property.
   * @see #STAGE6
   */
  public BHonStatusBoolean getSTAGE6() { return (BHonStatusBoolean)get(STAGE6); }

  /**
   * Set the {@code STAGE6} property.
   * @see #STAGE6
   */
  public void setSTAGE6(BHonStatusBoolean v) { set(STAGE6, v, null); }

  //endregion Property "STAGE6"

  //region Property "STAGE7"

  /**
   * Slot for the {@code STAGE7} property.
   * @see #getSTAGE7
   * @see #setSTAGE7
   */
  public static final Property STAGE7 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE7} property.
   * @see #STAGE7
   */
  public BHonStatusBoolean getSTAGE7() { return (BHonStatusBoolean)get(STAGE7); }

  /**
   * Set the {@code STAGE7} property.
   * @see #STAGE7
   */
  public void setSTAGE7(BHonStatusBoolean v) { set(STAGE7, v, null); }

  //endregion Property "STAGE7"

  //region Property "STAGE8"

  /**
   * Slot for the {@code STAGE8} property.
   * @see #getSTAGE8
   * @see #setSTAGE8
   */
  public static final Property STAGE8 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE8} property.
   * @see #STAGE8
   */
  public BHonStatusBoolean getSTAGE8() { return (BHonStatusBoolean)get(STAGE8); }

  /**
   * Set the {@code STAGE8} property.
   * @see #STAGE8
   */
  public void setSTAGE8(BHonStatusBoolean v) { set(STAGE8, v, null); }

  //endregion Property "STAGE8"

  //region Property "STAGE9"

  /**
   * Slot for the {@code STAGE9} property.
   * @see #getSTAGE9
   * @see #setSTAGE9
   */
  public static final Property STAGE9 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE9} property.
   * @see #STAGE9
   */
  public BHonStatusBoolean getSTAGE9() { return (BHonStatusBoolean)get(STAGE9); }

  /**
   * Set the {@code STAGE9} property.
   * @see #STAGE9
   */
  public void setSTAGE9(BHonStatusBoolean v) { set(STAGE9, v, null); }

  //endregion Property "STAGE9"

  //region Property "STAGE10"

  /**
   * Slot for the {@code STAGE10} property.
   * @see #getSTAGE10
   * @see #setSTAGE10
   */
  public static final Property STAGE10 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE10} property.
   * @see #STAGE10
   */
  public BHonStatusBoolean getSTAGE10() { return (BHonStatusBoolean)get(STAGE10); }

  /**
   * Set the {@code STAGE10} property.
   * @see #STAGE10
   */
  public void setSTAGE10(BHonStatusBoolean v) { set(STAGE10, v, null); }

  //endregion Property "STAGE10"

  //region Property "STAGE11"

  /**
   * Slot for the {@code STAGE11} property.
   * @see #getSTAGE11
   * @see #setSTAGE11
   */
  public static final Property STAGE11 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE11} property.
   * @see #STAGE11
   */
  public BHonStatusBoolean getSTAGE11() { return (BHonStatusBoolean)get(STAGE11); }

  /**
   * Set the {@code STAGE11} property.
   * @see #STAGE11
   */
  public void setSTAGE11(BHonStatusBoolean v) { set(STAGE11, v, null); }

  //endregion Property "STAGE11"

  //region Property "STAGE12"

  /**
   * Slot for the {@code STAGE12} property.
   * @see #getSTAGE12
   * @see #setSTAGE12
   */
  public static final Property STAGE12 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE12} property.
   * @see #STAGE12
   */
  public BHonStatusBoolean getSTAGE12() { return (BHonStatusBoolean)get(STAGE12); }

  /**
   * Set the {@code STAGE12} property.
   * @see #STAGE12
   */
  public void setSTAGE12(BHonStatusBoolean v) { set(STAGE12, v, null); }

  //endregion Property "STAGE12"

  //region Property "STAGE13"

  /**
   * Slot for the {@code STAGE13} property.
   * @see #getSTAGE13
   * @see #setSTAGE13
   */
  public static final Property STAGE13 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE13} property.
   * @see #STAGE13
   */
  public BHonStatusBoolean getSTAGE13() { return (BHonStatusBoolean)get(STAGE13); }

  /**
   * Set the {@code STAGE13} property.
   * @see #STAGE13
   */
  public void setSTAGE13(BHonStatusBoolean v) { set(STAGE13, v, null); }

  //endregion Property "STAGE13"

  //region Property "STAGE14"

  /**
   * Slot for the {@code STAGE14} property.
   * @see #getSTAGE14
   * @see #setSTAGE14
   */
  public static final Property STAGE14 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE14} property.
   * @see #STAGE14
   */
  public BHonStatusBoolean getSTAGE14() { return (BHonStatusBoolean)get(STAGE14); }

  /**
   * Set the {@code STAGE14} property.
   * @see #STAGE14
   */
  public void setSTAGE14(BHonStatusBoolean v) { set(STAGE14, v, null); }

  //endregion Property "STAGE14"

  //region Property "STAGE15"

  /**
   * Slot for the {@code STAGE15} property.
   * @see #getSTAGE15
   * @see #setSTAGE15
   */
  public static final Property STAGE15 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE15} property.
   * @see #STAGE15
   */
  public BHonStatusBoolean getSTAGE15() { return (BHonStatusBoolean)get(STAGE15); }

  /**
   * Set the {@code STAGE15} property.
   * @see #STAGE15
   */
  public void setSTAGE15(BHonStatusBoolean v) { set(STAGE15, v, null); }

  //endregion Property "STAGE15"

  //region Property "STAGE16"

  /**
   * Slot for the {@code STAGE16} property.
   * @see #getSTAGE16
   * @see #setSTAGE16
   */
  public static final Property STAGE16 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE16} property.
   * @see #STAGE16
   */
  public BHonStatusBoolean getSTAGE16() { return (BHonStatusBoolean)get(STAGE16); }

  /**
   * Set the {@code STAGE16} property.
   * @see #STAGE16
   */
  public void setSTAGE16(BHonStatusBoolean v) { set(STAGE16, v, null); }

  //endregion Property "STAGE16"

  //region Property "STAGE17"

  /**
   * Slot for the {@code STAGE17} property.
   * @see #getSTAGE17
   * @see #setSTAGE17
   */
  public static final Property STAGE17 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE17} property.
   * @see #STAGE17
   */
  public BHonStatusBoolean getSTAGE17() { return (BHonStatusBoolean)get(STAGE17); }

  /**
   * Set the {@code STAGE17} property.
   * @see #STAGE17
   */
  public void setSTAGE17(BHonStatusBoolean v) { set(STAGE17, v, null); }

  //endregion Property "STAGE17"

  //region Property "STAGE18"

  /**
   * Slot for the {@code STAGE18} property.
   * @see #getSTAGE18
   * @see #setSTAGE18
   */
  public static final Property STAGE18 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE18} property.
   * @see #STAGE18
   */
  public BHonStatusBoolean getSTAGE18() { return (BHonStatusBoolean)get(STAGE18); }

  /**
   * Set the {@code STAGE18} property.
   * @see #STAGE18
   */
  public void setSTAGE18(BHonStatusBoolean v) { set(STAGE18, v, null); }

  //endregion Property "STAGE18"

  //region Property "STAGE19"

  /**
   * Slot for the {@code STAGE19} property.
   * @see #getSTAGE19
   * @see #setSTAGE19
   */
  public static final Property STAGE19 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE19} property.
   * @see #STAGE19
   */
  public BHonStatusBoolean getSTAGE19() { return (BHonStatusBoolean)get(STAGE19); }

  /**
   * Set the {@code STAGE19} property.
   * @see #STAGE19
   */
  public void setSTAGE19(BHonStatusBoolean v) { set(STAGE19, v, null); }

  //endregion Property "STAGE19"

  //region Property "STAGE20"

  /**
   * Slot for the {@code STAGE20} property.
   * @see #getSTAGE20
   * @see #setSTAGE20
   */
  public static final Property STAGE20 = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGE20} property.
   * @see #STAGE20
   */
  public BHonStatusBoolean getSTAGE20() { return (BHonStatusBoolean)get(STAGE20); }

  /**
   * Set the {@code STAGE20} property.
   * @see #STAGE20
   */
  public void setSTAGE20(BHonStatusBoolean v) { set(STAGE20, v, null); }

  //endregion Property "STAGE20"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStageDriver.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent(com.honeywell* honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		showHideOutslots();
		super.initHoneywellComponent(executionParams);
	}
  
  
/* (non-Javadoc)
 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
 */
@Override
public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	super.executeHoneywellComponent(executionParams);
	
	executeStageDriver(executionParams);
	
}

private void executeStageDriver(BExecutionParams executionParams) {
	double iterationInterval = executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND;
	BLeadLagStrategyEnum llEnum = getLeadLag();
	
	switch (llEnum.getOrdinal()) {
	case BLeadLagStrategyEnum.LL_FOLO:
		executeWithFOLOStrategy();
		break;

	case BLeadLagStrategyEnum.LL_FOFO:
		executeWithFOFOStrategy();
		break;

	default:
		executeWithRuntimeStrategy(iterationInterval);
		break;
	}
	
	for(int stage=0; stage<getMaxStagesVal();stage++) {
		int i=stage+1;
		BStatusBoolean val = (BStatusBoolean) this.get(STAGE+i);
		val.setValue(isStageOn(i));
		val.setStatus(BStatus.ok);
	}
}

private void executeWithFOLOStrategy() {
	int maxStages = getMaxStagesVal();

	int stagesActiveVal = getInputStagesVal();
	
	boolean cmd;
	for(int stage=0; stage<maxStages;stage++) {
		cmd = false;
		if(stage<stagesActiveVal) {
			cmd = true;
		}
		setStageStatus(stage+1, cmd);
	}	
}

private void executeWithFOFOStrategy() {
	int firstONstg;
	int lastONstg;
	boolean cmd;
	int oldNumStages;
	
	int maxStages = getMaxStagesVal();	
	
	BFOFOStrategy lastFOFOParams = getLlFOFOStrategyParams();
	
	firstONstg = lastFOFOParams.getSeqStartPtr();
	oldNumStages = lastFOFOParams.getOldNumStages();
	
	//The code below computes the first stage that needs to be 
	//on and the last stage till which the stages need be ON
	
	// Set the start of the ON stages to be the same as the last
    // iteration until proven otherwise.  If the desired number of
    // ON stages is less than the previous cycle, remove stages by
    // advancing the first ON stage number, modulo the number of
    // stages.	
	
	int inputStages = getInputStagesVal();
	if (inputStages < oldNumStages) {
		firstONstg += oldNumStages - inputStages;		
        firstONstg %= maxStages;
	}
	
	// Calculate the last stage to be on as the start stage, plus
    // the number of ON stages, modulo the number of stages.
	lastONstg = firstONstg;
    if (inputStages > 0)
    {
        lastONstg += inputStages - 1;
        lastONstg %= maxStages;
    }
    
    // Set the stage status to ON, unless there are no stages to be
    // turned on.  Walk the stages starting from the first ON stage
    // in a circular fashion, until you return to the first ON
    // stage.  When you have processed the last ON stage, change the
    // stage status to OFF for the remaining stages.
    cmd = true;
    if ( inputStages == 0 ) {
        cmd = false;
    }
    int stg = firstONstg;
    do
    {
    	setStageStatus(stg+1, cmd);
        if ( stg == lastONstg ) {
            cmd = false;
        }
        stg++;
        if ( stg >= maxStages ) {
            stg = 0;
        }
    }
    while ( stg != firstONstg );
    
    
    getLlFOFOStrategyParams().setOldNumStages(inputStages);
    getLlFOFOStrategyParams().setSeqStartPtr(firstONstg);	
}

private void executeWithRuntimeStrategy(double iterationInterval) {
	double runTimeCopy;
	int maxStages = getMaxStagesVal();
	
	int inputStages = getInputStagesVal();	

	updateRunTimersForEachStage(iterationInterval);	
	
    // Reset the runtime for a stage if the runtimeReset input
    // is set to a stage number.  0 is no action, and 1-255 is
    // the stage number to be reset
	resetRunTimeForStages();
	
	// Set the lowest runtime to a very large value and the
    // highest runtime to a very large negative value, so any
    // value will qualify as the new low/high.
	int highestRunTimeOnStage=0;
    double highestOnRunTime = Double.NEGATIVE_INFINITY;
    int lowestRunTimeOffStage=0;
    double lowestOffRunTime = Double.MAX_VALUE;
    
    // Zero a counter of the number of stages that are on.  Get
    // the run time for each stage.  If it is not available skip
    // it.
    int numberStagesOn = 0;
    for(int i=0;i<maxStages;i++) {
    	runTimeCopy = getStageRunTimer(i+1);
    	if(isStageOn(i+1)) {
    		numberStagesOn++;
    		// If the stage has the highest runtime of any ON
            // stage so far, update the highest ON runtime stage
            // and the new highest run time for a stage that is
            // ON.
    		if(runTimeCopy >= highestOnRunTime)
            {
                highestOnRunTime = runTimeCopy;
                highestRunTimeOnStage=i;
            }
    	}else {
		
            // The stage is OFF.  If the stage has the lowest
            // runtime of any OFF stage so far, update the
            // lowest OFF runtime stage and the new low run time
            // among OFF stages.

            if(runTimeCopy < lowestOffRunTime)
            {
                lowestOffRunTime=runTimeCopy;
                lowestRunTimeOffStage = i;
            }
        }    	
    }
    
    
    // If we found an OFF stage with a run timer, indicated by a
    // lowest run time that is no longer a very large number,
    // and the requested number of stages exceeds the number
    // that are already on, turn on the OFF stage with the
    // lowest run time.  If the requested number of stages is
    // less than the number that are already on, turn off the
    // stage with the highest run time.  NOTE that we only turn
    // on or off one stage per iteration.  If the number of
    // stages desired changes by a large amount, it will take a
    // number of iterations to implement the change.
    if((inputStages>numberStagesOn) && (Double.compare(lowestOffRunTime,Double.MAX_VALUE)!=0) ) {
    	setStageStatus(lowestRunTimeOffStage+1,true);
    }
    
    if(inputStages<numberStagesOn) {
    	setStageStatus(highestRunTimeOnStage+1,false);
    }
    
}



private void updateRunTimersForEachStage(double iterationInterval) {
	double runTimeCopy;
	int maxStages = getMaxStagesVal();
	
	BRuntimeStrategy stageRunTime = getLlRuntimeStrategyParams();
	double totalRunTime = stageRunTime.getTotalRuntime() + iterationInterval; 
	stageRunTime.setTotalRuntime(totalRunTime);
	if(totalRunTime >= SIXTY_SECONDS) {
		double mins = Math.floor(totalRunTime/SIXTY_SECONDS);
		totalRunTime = totalRunTime%SIXTY_SECONDS;
		stageRunTime.setTotalRuntime(totalRunTime);
		
		for(int i=0;i<maxStages;i++) {
			// If the stage is on, add any accumulated minutes
            // from the runtime seconds counter and save it back
            // to the run timer.  If there is no run timer
            // available, skip it.
			if(isStageOn(i+1)) {
				runTimeCopy = getStageRunTimer(i+1);
				runTimeCopy = runTimeCopy+mins;
				setStageRunTimer(i+1,runTimeCopy);
			}			
		}	
	}
}

private void resetRunTimeForStages() {
	// Reset the runtime for a stage if the runtimeReset input
    // is set to a stage number.  0 is no action, and 1-255 is
    // the stage number to be reset
	int maxStages = getMaxStagesVal();	
	double runtimeResetVal = getRuntimeReset().getValue();
	if(!isConfigured(runtimeReset))
		runtimeResetVal = 0;
	else
		runtimeResetVal = limitInput(runtimeResetVal,0.0,maxStages,0.0);
	if(Double.compare(runtimeResetVal, 0.0)!=0) {
		setStageRunTimer((int)runtimeResetVal,0.0);
	}
}


private boolean isStageOn(int stageNum) {
	BStageStaus stageStatusStruct = getStageStatusParams();	
	return stageStatusStruct.getBoolean(stageStatusStruct.getProperty("stageStatus"+stageNum));	
}

private void setStageStatus(int stageNum,boolean cmd) {	
	BStageStaus stageStatusStruct = getStageStatusParams();
	stageStatusStruct.set("stageStatus"+stageNum, BBoolean.make(cmd));
}

private double getStageRunTimer(int stageNum) {
	BRuntimeStrategy stageRunTime = getLlRuntimeStrategyParams();
	return stageRunTime.getDouble(stageRunTime.getProperty("RunTime"+stageNum));	
}

private void setStageRunTimer(int stageNum,double runTime) {
	BRuntimeStrategy stageRunTime = getLlRuntimeStrategyParams();
	stageRunTime.setDouble(stageRunTime.getProperty("RunTime"+stageNum), runTime);	
}

/* (non-Javadoc)
 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
 */
@Override
public List<Property> getInputPropertiesList() {
	List<Property> propertyArrayList = super.getInputPropertiesList();
  	propertyArrayList.add(nStagesActive);
  	propertyArrayList.add(runtimeReset);
  	return propertyArrayList;
}

@Override
public List<Property> getOutputPropertiesList() {
	List<Property> propertyArrayList = super.getOutputPropertiesList();
  	propertyArrayList.add(STAGE1);
  	propertyArrayList.add(STAGE2);
  	propertyArrayList.add(STAGE3);
  	propertyArrayList.add(STAGE4);
  	propertyArrayList.add(STAGE5);
  	propertyArrayList.add(STAGE6);
  	propertyArrayList.add(STAGE7);
  	propertyArrayList.add(STAGE8);
  	propertyArrayList.add(STAGE9);
  	propertyArrayList.add(STAGE10);
  	propertyArrayList.add(STAGE11);
  	propertyArrayList.add(STAGE12);
  	propertyArrayList.add(STAGE13);
  	propertyArrayList.add(STAGE14);
  	propertyArrayList.add(STAGE15);
  	propertyArrayList.add(STAGE16);
  	propertyArrayList.add(STAGE17);
  	propertyArrayList.add(STAGE18);
  	propertyArrayList.add(STAGE19);
  	propertyArrayList.add(STAGE20);
  	return propertyArrayList;
}

@Override
public List<Property> getConfigPropertiesList() {
	List<Property> propertyArrayList = super.getConfigPropertiesList();
	propertyArrayList.add(leadLag);
	propertyArrayList.add(maxStgs);
	return propertyArrayList;		
}

@Override
public BFacets getSlotFacets(Slot slot){
	BFacets facets = super.getSlotFacets(slot);
	if(slot.getName().equals(maxStgs.getName())) {
		int maxStageCount = MAX_STAGES_TOTAL;		
		if(getLeadLag().getEnum().equals(BLeadLagStrategyEnum.llRUNEQ))
			maxStageCount = MAX_STAGES_LLRUNTIME;			
		facets = BFacets.make(facets, BFacets.MAX, BInteger.make(maxStageCount));
	}else if(slot.getName().equals(runtimeReset.getName())) {
		facets = BFacets.make(facets, BFacets.MAX, BInteger.make(getMaxStagesVal()));
	}
	return facets;
}

@Override
public void changed(Property property, Context context) {	
	if (!Sys.atSteadyState())
		return;
	if(property.getName().equals(maxStgs.getName())) {
		showHideOutslots();
	}else if(property.getName().equals(leadLag.getName())) {
		showHideOutslots();
		//limit the number of stages to 10 when user changes from LOFO/FOFO to RunEQ. This is to address the issue: 
		//set stages to 16 say for FOFO/LOFO and change to RunEQ. The max stages limit changes to 10, but the value remains at 16  
		limitMaxStagesVal();
		int maxStages = MAX_STAGES_TOTAL;
		for(int i=0;i<maxStages;i++) {
			setStageStatus(i+1,false);			
		}
		
		for(int i=0;i<MAX_STAGES_LLRUNTIME;i++) {
			BRuntimeStrategy stageRuntime = getLlRuntimeStrategyParams();
			stageRuntime.setTotalRuntime(0);
			setStageRunTimer(i+1, 0.0);
		}
		
		BFOFOStrategy fofoStrategy = getLlFOFOStrategyParams();
		fofoStrategy.setSeqStartPtr(0);
		fofoStrategy.setOldNumStages(0);
	}
	
	super.changed(property, context);
}

private void limitMaxStagesVal() {
	int maxStagesVal = getMaxStgs();	
	if(getLeadLag().equals(BLeadLagStrategyEnum.llRUNEQ) && maxStagesVal>MAX_STAGES_LLRUNTIME) {
		setMaxStgs(MAX_STAGES_LLRUNTIME);
	}	
}

private void showHideOutslots() {
	int j;
	int maxStages = getMaxStagesVal();
	for(int i=0;i<maxStages;i++) {
		j=i+1;
		Slot slot = this.getSlot(STAGE+j);
		int flags = this.getFlags(slot);		
		flags = flags & ~Flags.HIDDEN;
		flags = flags | Flags.SUMMARY;
		this.setFlags(slot, flags);
	}
	
	for(int i=maxStages;i<MAX_STAGES_TOTAL;i++) {
		j=i+1;
		Slot slot = this.getSlot(STAGE+j);
		int flags = this.getFlags(slot);
		flags = flags & ~Flags.SUMMARY;
		flags = flags | Flags.HIDDEN;	
		this.setFlags(slot, flags);
	}	
}

private int getMaxStagesVal() {
	int maxStages = getMaxStgs();
	BLeadLagStrategyEnum llEnum = getLeadLag();
	
	if(BLeadLagStrategyEnum.llRUNEQ.equals(llEnum)){	
		if(maxStages > MAX_STAGES_LLRUNTIME)
			maxStages = MAX_STAGES_LLRUNTIME;
	}else {
		if(maxStages > MAX_STAGES_TOTAL)
			maxStages = MAX_STAGES_TOTAL;
	}
	
	return maxStages;	
}

private int getInputStagesVal() {
	int maxStages = getMaxStagesVal();
	if(!isConfigured(nStagesActive))
		return 0;
	else
		return (int)limitInput(getNStagesActive().getValue(),0.0,maxStages,0.0);	
}

@Override
@NiagaraRpc(permissions = "RWI", transports = { @Transport(type = TransportType.web), @Transport(type = TransportType.box) })
public BFacets getFacetsDataToRPCCall(String slotName,Context ctx) {
	BFacets facets =  this.getSlotFacets(this.getSlot(slotName));
	facets = BFacets.make(facets, "isStageDriver", BBoolean.make(true));
	return facets;
}

private static final int MAX_STAGES_LLRUNTIME = 10;
private static final int MAX_STAGES_TOTAL = 20;
private static final int SIXTY_SECONDS = 60;
private static final String STAGE = "STAGE"; 


}
