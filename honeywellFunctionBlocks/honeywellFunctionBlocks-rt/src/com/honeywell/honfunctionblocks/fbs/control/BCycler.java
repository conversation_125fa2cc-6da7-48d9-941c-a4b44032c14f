/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Cycler block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 9, 2018
 */


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"stager_thermostat_cycler.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "in", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "-200"),
	@Facet(name = "BFacets.MAX", value = "200"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "maxStgs", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(1.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "1"),
	@Facet(name = "BFacets.MAX", value = "255"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "minOn", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "minOff", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "intstgOn", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "intstgOff", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "64799"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="overrideOff", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="disable", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)

@NiagaraProperty(name = "anticipatorAuthority", type = "int", defaultValue = "0", 
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "200"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "cph", type = "int", defaultValue = "1", 
facets = { 
	@Facet(name = "BFacets.MIN", value = "1"),
	@Facet(name = "BFacets.MAX", value = "60"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "hyst", type = "double", defaultValue = "0.0", 
facets = { 
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "100"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "2") })


@NiagaraProperty(name = "STAGES_ACTIVE", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "0"),
		@Facet(name = "BFacets.MAX", value = "255"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })

@NiagaraProperty(name="onTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="offTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)
@NiagaraProperty(name="antic", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

public class BCycler extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BCycler(3398670568)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "stager_thermostat_cycler.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "in"

  /**
   * Slot for the {@code in} property.
   * @see #getIn
   * @see #setIn
   */
  public static final Property in = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, -200), BFacets.make(BFacets.MAX, 200)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in} property.
   * @see #in
   */
  public BHonStatusNumeric getIn() { return (BHonStatusNumeric)get(in); }

  /**
   * Set the {@code in} property.
   * @see #in
   */
  public void setIn(BHonStatusNumeric v) { set(in, v, null); }

  //endregion Property "in"

  //region Property "maxStgs"

  /**
   * Slot for the {@code maxStgs} property.
   * @see #getMaxStgs
   * @see #setMaxStgs
   */
  public static final Property maxStgs = newProperty(Flags.SUMMARY, new BHonStatusNumeric(1.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code maxStgs} property.
   * @see #maxStgs
   */
  public BHonStatusNumeric getMaxStgs() { return (BHonStatusNumeric)get(maxStgs); }

  /**
   * Set the {@code maxStgs} property.
   * @see #maxStgs
   */
  public void setMaxStgs(BHonStatusNumeric v) { set(maxStgs, v, null); }

  //endregion Property "maxStgs"

  //region Property "minOn"

  /**
   * Slot for the {@code minOn} property.
   * @see #getMinOn
   * @see #setMinOn
   */
  public static final Property minOn = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code minOn} property.
   * @see #minOn
   */
  public BHonStatusNumeric getMinOn() { return (BHonStatusNumeric)get(minOn); }

  /**
   * Set the {@code minOn} property.
   * @see #minOn
   */
  public void setMinOn(BHonStatusNumeric v) { set(minOn, v, null); }

  //endregion Property "minOn"

  //region Property "minOff"

  /**
   * Slot for the {@code minOff} property.
   * @see #getMinOff
   * @see #setMinOff
   */
  public static final Property minOff = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code minOff} property.
   * @see #minOff
   */
  public BHonStatusNumeric getMinOff() { return (BHonStatusNumeric)get(minOff); }

  /**
   * Set the {@code minOff} property.
   * @see #minOff
   */
  public void setMinOff(BHonStatusNumeric v) { set(minOff, v, null); }

  //endregion Property "minOff"

  //region Property "intstgOn"

  /**
   * Slot for the {@code intstgOn} property.
   * @see #getIntstgOn
   * @see #setIntstgOn
   */
  public static final Property intstgOn = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code intstgOn} property.
   * @see #intstgOn
   */
  public BHonStatusNumeric getIntstgOn() { return (BHonStatusNumeric)get(intstgOn); }

  /**
   * Set the {@code intstgOn} property.
   * @see #intstgOn
   */
  public void setIntstgOn(BHonStatusNumeric v) { set(intstgOn, v, null); }

  //endregion Property "intstgOn"

  //region Property "intstgOff"

  /**
   * Slot for the {@code intstgOff} property.
   * @see #getIntstgOff
   * @see #setIntstgOff
   */
  public static final Property intstgOff = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 64799)), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code intstgOff} property.
   * @see #intstgOff
   */
  public BHonStatusNumeric getIntstgOff() { return (BHonStatusNumeric)get(intstgOff); }

  /**
   * Set the {@code intstgOff} property.
   * @see #intstgOff
   */
  public void setIntstgOff(BHonStatusNumeric v) { set(intstgOff, v, null); }

  //endregion Property "intstgOff"

  //region Property "overrideOff"

  /**
   * Slot for the {@code overrideOff} property.
   * @see #getOverrideOff
   * @see #setOverrideOff
   */
  public static final Property overrideOff = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code overrideOff} property.
   * @see #overrideOff
   */
  public BFiniteStatusBoolean getOverrideOff() { return (BFiniteStatusBoolean)get(overrideOff); }

  /**
   * Set the {@code overrideOff} property.
   * @see #overrideOff
   */
  public void setOverrideOff(BFiniteStatusBoolean v) { set(overrideOff, v, null); }

  //endregion Property "overrideOff"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "anticipatorAuthority"

  /**
   * Slot for the {@code anticipatorAuthority} property.
   * @see #getAnticipatorAuthority
   * @see #setAnticipatorAuthority
   */
  public static final Property anticipatorAuthority = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 200)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code anticipatorAuthority} property.
   * @see #anticipatorAuthority
   */
  public int getAnticipatorAuthority() { return getInt(anticipatorAuthority); }

  /**
   * Set the {@code anticipatorAuthority} property.
   * @see #anticipatorAuthority
   */
  public void setAnticipatorAuthority(int v) { setInt(anticipatorAuthority, v, null); }

  //endregion Property "anticipatorAuthority"

  //region Property "cph"

  /**
   * Slot for the {@code cph} property.
   * @see #getCph
   * @see #setCph
   */
  public static final Property cph = newProperty(0, 1, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 1), BFacets.make(BFacets.MAX, 60)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code cph} property.
   * @see #cph
   */
  public int getCph() { return getInt(cph); }

  /**
   * Set the {@code cph} property.
   * @see #cph
   */
  public void setCph(int v) { setInt(cph, v, null); }

  //endregion Property "cph"

  //region Property "hyst"

  /**
   * Slot for the {@code hyst} property.
   * @see #getHyst
   * @see #setHyst
   */
  public static final Property hyst = newProperty(0, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 100)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 2)));

  /**
   * Get the {@code hyst} property.
   * @see #hyst
   */
  public double getHyst() { return getDouble(hyst); }

  /**
   * Set the {@code hyst} property.
   * @see #hyst
   */
  public void setHyst(double v) { setDouble(hyst, v, null); }

  //endregion Property "hyst"

  //region Property "STAGES_ACTIVE"

  /**
   * Slot for the {@code STAGES_ACTIVE} property.
   * @see #getSTAGES_ACTIVE
   * @see #setSTAGES_ACTIVE
   */
  public static final Property STAGES_ACTIVE = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code STAGES_ACTIVE} property.
   * @see #STAGES_ACTIVE
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusNumeric getSTAGES_ACTIVE() { return (BHonStatusNumeric)get(STAGES_ACTIVE); }

  /**
   * Set the {@code STAGES_ACTIVE} property.
   * @see #STAGES_ACTIVE
   */
  @SuppressWarnings("squid:S00100")
  public void setSTAGES_ACTIVE(BHonStatusNumeric v) { set(STAGES_ACTIVE, v, null); }

  //endregion Property "STAGES_ACTIVE"

  //region Property "onTimer"

  /**
   * Slot for the {@code onTimer} property.
   * @see #getOnTimer
   * @see #setOnTimer
   */
  public static final Property onTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code onTimer} property.
   * @see #onTimer
   */
  public double getOnTimer() { return getDouble(onTimer); }

  /**
   * Set the {@code onTimer} property.
   * @see #onTimer
   */
  public void setOnTimer(double v) { setDouble(onTimer, v, null); }

  //endregion Property "onTimer"

  //region Property "offTimer"

  /**
   * Slot for the {@code offTimer} property.
   * @see #getOffTimer
   * @see #setOffTimer
   */
  public static final Property offTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code offTimer} property.
   * @see #offTimer
   */
  public double getOffTimer() { return getDouble(offTimer); }

  /**
   * Set the {@code offTimer} property.
   * @see #offTimer
   */
  public void setOffTimer(double v) { setDouble(offTimer, v, null); }

  //endregion Property "offTimer"

  //region Property "antic"

  /**
   * Slot for the {@code antic} property.
   * @see #getAntic
   * @see #setAntic
   */
  public static final Property antic = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code antic} property.
   * @see #antic
   */
  public double getAntic() { return getDouble(antic); }

  /**
   * Set the {@code antic} property.
   * @see #antic
   */
  public void setAntic(double v) { setDouble(antic, v, null); }

  //endregion Property "antic"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCycler.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		resetValues();
	}

	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeCyclerBlock(executionParams);
	}
	
	  @Override
		public void started() throws Exception {
			super.started();
			addNegateSupportOnDisableSlot();
			addNegateSupportOnOverrideOffSlot();
		}
		
		private void addNegateSupportOnDisableSlot() {
			BFiniteStatusBoolean disable1 = getDisable();
			if(!(disable1 instanceof BNegatableFiniteStatusBoolean)) {
				BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
				negatableFiniteStatusBoolean.setStatus(disable1.getStatus());
				negatableFiniteStatusBoolean.setValue(disable1.getValue());
				setDisable(negatableFiniteStatusBoolean);
			}
		}
		
		private void addNegateSupportOnOverrideOffSlot() {
			BFiniteStatusBoolean overrideoff1 = getOverrideOff();
			if(!(overrideoff1 instanceof BNegatableFiniteStatusBoolean)) {
				BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
				negatableFiniteStatusBoolean.setStatus(overrideoff1.getStatus());
				negatableFiniteStatusBoolean.setValue(overrideoff1.getValue());
				setOverrideOff(negatableFiniteStatusBoolean);
			}
		}
		
	
	private void executeCyclerBlock(BExecutionParams executionParams) {
		boolean disable1;
		double maxStgs1;
		double antAuth;
		double hyst1;
		double cph1;
		double intStgOffSec;
		double delta = 0;
		double totErr;
		
		// If the maximum number of stages is out of range, clamp it to the
		// limits.  If it is INVALID, set it to 1, so we don't attempt divisions
		// by zero, and disable the block.
		disable1 = getDisablePropertyValue();
		if(isInMaxStagesUnConfigured(disable1)) {
			resetValues();
			return;
		}

		double tempMaxStgs = getMaxStgs().getValue();
		if(isMaxStagesValid(tempMaxStgs)) {
			resetValues();
			return;
		}
		
		totErr = getIn().getValue();
		if (Double.isNaN(totErr) || (totErr > 0 && Double.isInfinite(totErr))) {
			resetValues();
			return;
		}

		maxStgs1 = limitInput(tempMaxStgs, 0.0, MAXSTAGES, 0.0);
		if (Double.compare(maxStgs1, 0) == 0) {
			resetValues();
			return;
		}

		// If the cph specified via the configuration parameter is a special
	    // value, force antAuth to a default value of 100.0 (unchangeable).  Use
	    // the antAuth configuration parameter value for intstgOff.  Use the
	    // intgstgOff input value for cph.  This special case allows a live
	    // input to be used to specify CPH.
		cph1 = getCph();
		if (Double.compare(cph1, CPHMAX) == 0) {
			antAuth = HUNDRED;
			intStgOffSec = getAnticipatorAuthority();
			
			// If the live CPH input is out of range (including FL_INVALID and
	        // unconnected), disable the loop.  This seems the safest thing to
	        // do on bad input, so someone might notice the error, rather than
	        // keep running using a default CPH.
			cph1 = getIntstgOffPropertyValue();
			disable1 = updateDisable(disable1, cph1);
			
		} else {
			// With no special case in force, read the anticipator authority,
	        // interstage off time and CPH from their normal configuration
	        // parameters and inputs.  If the anticipator authority is out of
	        // range (including possibly 0.0), default to 100%.
			antAuth = computeAntAuth();
			intStgOffSec = getIntstgOffPropertyValue();
			cph1 = getCph();
		}

		if (disable1) {
			resetValues();
			return;
		}
		
		hyst1 = computeHyst(maxStgs1, antAuth);
		cph1 = updateCph(cph1);
		computeCphMult(executionParams, maxStgs1, antAuth, hyst1, cph1); 
		computeOutput(executionParams, maxStgs1, antAuth, hyst1, intStgOffSec, delta, totErr);
		getSTAGES_ACTIVE().setValue(stgsAct);
	}

	private void resetValues() {
		// We initialize the ON timer to the interstage ON time so there is no
	    // delay in activating the first stage.  However, the interstage ON
	    // timer is forced to 0 when invalid, so if it transitions from invalid
	    // to valid, the interstage ON time must be served before a stage will
	    // activate.
		getSTAGES_ACTIVE().setValue(0);
		setAntic(0);
		setOnTimer(intStgOnSec);
		setOffTimer(0);
	}

	private boolean updateDisable(boolean disable1, double cph1) {
		if ((cph1 < 1) || (cph1 > FIFTYNINE)) {
			return true;
		}
		return disable1;
	}

	private double updateCph(double cph1) {
		// If the cph specified is out of range, default to 3cph
		if (cph1 < 1 || cph1 > CPHMAX) {
			return THREE;
		}
		return cph1;
	}

	private double computeAntAuth() {
		double antAuth;
		antAuth = getAnticipatorAuthority();
		if (antAuth <= 0 || antAuth > TWOHUNDRED) {
			antAuth = HUNDRED;
		}
		return antAuth;
	}

	private boolean isInMaxStagesUnConfigured(boolean disable1) {
		boolean isUnconfigred = false;
		if (disable1 || !isConfigured(in) || !isConfigured(BCycler.maxStgs)) {
			isUnconfigred = true;
		}
		return isUnconfigred;
	}

	private boolean isMaxStagesValid(double tempMaxStgs) {
		boolean isValid = false;
		if (Double.isNaN(tempMaxStgs) || (tempMaxStgs > 0 && Double.isInfinite(tempMaxStgs))) {
			isValid = true;
		}
		return isValid;
	}

	private double computeHyst(double maxStgs1, double antAuth) {
		double hyst1;
		hyst1 = getHyst();
		if (hyst1 <= 0 || hyst1 >= antAuth / maxStgs1)
			hyst1 = antAuth / maxStgs1 / ONEQUAT;
		return hyst1;
	}

	private void computeCphMult(BExecutionParams executionParams, double maxStgs1, double antAuth, double hyst1, double cph1) {
		// Calculate the cph multiplier using the iteration interval.  For
        // on-demand (iteration interval == 0), the cph multiplier comes out
        // to be 0, which is what is desired.
		double x;
		x = Math.log((antAuth / maxStgs1 + hyst1) / (antAuth / maxStgs1 - hyst1));
		double temp = executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND;
		double tempValue = Math.exp(-1.0F *  temp / (THIRTYSIXHUN * (1 / (TWO * cph1 * x)))); 
		cphMult = 1.F - tempValue;
	}

	private void computeOutput(BExecutionParams executionParams, double maxStgs1, double antAuth, double hyst1, double intStgOffSec, double delta, double totErr) {
		intStgOnSec = getIntstgOnPropertyValue();
		stgsAct = limitInput(getSTAGES_ACTIVE().getValue(), 0, ONEBYTE, 0);

		updateStages(executionParams, maxStgs1, hyst1, intStgOffSec, delta, totErr);

		setAntic(getAntic() + cphMult * (stgsAct * antAuth / maxStgs1 - getAntic()));

		// Never let the number of stages active exceed the maximum.  This can
	    // happen when a configuration change is made to reduce maxStgs when all
	    // stages are on (BCSPROG-7827).  This is not considered a case where we
	    // must be graceful and apply interstage off time, but a mistake in
	    // configuration being corrected, so just force the number of active
	    // stages down to the max.  Write the number of active stages to its output.
		if (stgsAct > maxStgs1) {
			stgsAct = maxStgs1;
		}
	}

	private void updateStages(BExecutionParams executionParams, double maxStgs1, double hyst1, double intStgOffSec, double delta, double totErr) {
		boolean overrideOff1;
		double cyclerErr;
		double minOnSec;
		double minOffSec;
		minOnSec = getMinOnPropertyValue();
		minOffSec = getMinOffPropertyValue();

		updateTimers(executionParams);
		overrideOff1 = getOverrideOffPropertyValue();
		cyclerErr = totErr - getAntic();
		if (condition1(maxStgs1, minOffSec) && condition2(cyclerErr, delta, overrideOff1)) {
			stgsAct++;
			setOnTimer(0);
		}
		
		if (condition3(minOnSec, intStgOffSec) && condition4(cyclerErr, delta, hyst1, overrideOff1)) {
			stgsAct--;
			setOffTimer(0);
		}
	}

	private void updateTimers(BExecutionParams executionParams) {
		setOnTimer(getOnTimer() + (executionParams.getIterationInterval()/(double) UnitConstants.THOUSAND_MILLI_SECOND));
		if (getOnTimer() >= MAXONTIME)
			setOnTimer(MAXONTIME);
		setOffTimer(getOffTimer() + (executionParams.getIterationInterval()/(double)UnitConstants.THOUSAND_MILLI_SECOND));
		if (getOffTimer() >= MAXOFFTIME)
			setOffTimer(MAXOFFTIME);
	}
	
	private boolean condition1(double maxStgs1, double minOffSec) {
		return (stgsAct < maxStgs1) && (getOffTimer() > minOffSec);
	}

	private boolean condition2(double cyclerErr, double delta, boolean overrideOff1) {
		return (getOnTimer() > intStgOnSec) && cyclerErr > stgsAct*delta && !overrideOff1;
	}

	private boolean condition3(double minOnSec, double intStgOffSec) {
		return (stgsAct > 0) && (getOnTimer() > minOnSec) && (getOffTimer() > intStgOffSec);
	}

	private boolean condition4(double cyclerErr, double delta, double hyst1, boolean overrideOff1) {
		return cyclerErr < ((stgsAct - 1)*delta - hyst1) || overrideOff1;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
    	List<Property> properties = super.getInputPropertiesList();
    	properties.add(disable);
    	properties.add(overrideOff);
    	properties.add(in);
    	properties.add(maxStgs);
    	properties.add(minOn);
    	properties.add(minOff);
    	properties.add(intstgOn);
    	properties.add(intstgOff);
    	return properties;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(STAGES_ACTIVE);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(anticipatorAuthority);
		properties.add(cph);
		properties.add(hyst);
		return properties;
	}

	private boolean getDisablePropertyValue() {
    	if (isSlotValueValid(disable)) {
    		return ((BNegatableFiniteStatusBoolean) getDisable()).getNegate() ? !getDisable().getBoolean() : getDisable().getBoolean();
    	}
    	return false;
    }

    private boolean getOverrideOffPropertyValue() {
    	if (isSlotValueValid(overrideOff)) {
    		return ((BNegatableFiniteStatusBoolean) getOverrideOff()).getNegate() ? !getOverrideOff().getBoolean():getOverrideOff().getBoolean();
    	}
    	return false;
    }

    private double getIntstgOffPropertyValue() {
    	if (isConfigured(intstgOff)) {
    		return limitInput(getIntstgOff().getValue(), 0.0, INTERSTAGEOFFSEC, 0.0);
    	}
    	return 0;
    }

    private double getMinOnPropertyValue() {
    	if (isConfigured(minOn)) {
    		return limitInput(getMinOn().getValue(), 0.0, MINONSEC, 0.0);
    	}
    	return 0;
    }

    private double getMinOffPropertyValue() {
    	if (isConfigured(minOff)) {
    		return limitInput(getMinOff().getValue(), 0.0, MINOFFSEC, 0.0);
    	}
    	return 0;
    }

    private double getIntstgOnPropertyValue() {
    	if (isConfigured(intstgOn)) {
    		return limitInput(getIntstgOn().getValue(), 0.0, INTERSTAGEONSEC, 0.0);
    	}
    	return 0;
    }

    private double stgsAct;
    private double intStgOnSec;
    private double cphMult;   

    
	private static final int MAXSTAGES = 255;
	private static final int INTERSTAGEONSEC = 64799;
	private static final int INTERSTAGEOFFSEC = 64799;
	private static final int MINONSEC = 64799;
	private static final int MINOFFSEC = 64799;
	private static final int MAXONTIME = 64800;
	private static final int MAXOFFTIME = 64800;
	private static final int HUNDRED = 100;
	private static final int TWOHUNDRED = 200;
	private static final int ONEBYTE = 255;
	private static final int CPHMAX = 60;
	private static final float ONEQUAT = 1.25F;
	private static final int TWO = 2;
	private static final int THREE = 3;
	private static final int FIFTYNINE = 59;
	private static final int THIRTYSIXHUN = 3600;

}
