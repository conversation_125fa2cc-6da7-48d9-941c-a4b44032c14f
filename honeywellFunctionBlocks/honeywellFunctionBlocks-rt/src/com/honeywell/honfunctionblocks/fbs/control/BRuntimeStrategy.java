/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BDouble;
import javax.baja.sys.BFacets;
import javax.baja.sys.BStruct;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.UnitConstants;

/**
 * Created this as part of StageDriver block implementation
 * This class holds the runtime data of each stage and the total runtime required for RunTime lead lag strategy. 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 * @since Dec 22, 2017
 */

@NiagaraType

@NiagaraProperty(name="totalRuntime", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })


@NiagaraProperty(name="RunTime1", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime2", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime3", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime4", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime5", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime6", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime7", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime8", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime9", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name="RunTime10", type="double", defaultValue="0.0",flags=Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BDouble.make(0.0)"),
		@Facet(name = "BFacets.MAX", value = "BDouble.make(Double.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MINUTE)"),
		@Facet(name = "BFacets.PRECISION", value = "0") })



@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BRuntimeStrategy extends BStruct {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BRuntimeStrategy(1174919621)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "totalRuntime"

  /**
   * Slot for the {@code totalRuntime} property.
   * @see #getTotalRuntime
   * @see #setTotalRuntime
   */
  public static final Property totalRuntime = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code totalRuntime} property.
   * @see #totalRuntime
   */
  public double getTotalRuntime() { return getDouble(totalRuntime); }

  /**
   * Set the {@code totalRuntime} property.
   * @see #totalRuntime
   */
  public void setTotalRuntime(double v) { setDouble(totalRuntime, v, null); }

  //endregion Property "totalRuntime"

  //region Property "RunTime1"

  /**
   * Slot for the {@code RunTime1} property.
   * @see #getRunTime1
   * @see #setRunTime1
   */
  public static final Property RunTime1 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime1} property.
   * @see #RunTime1
   */
  public double getRunTime1() { return getDouble(RunTime1); }

  /**
   * Set the {@code RunTime1} property.
   * @see #RunTime1
   */
  public void setRunTime1(double v) { setDouble(RunTime1, v, null); }

  //endregion Property "RunTime1"

  //region Property "RunTime2"

  /**
   * Slot for the {@code RunTime2} property.
   * @see #getRunTime2
   * @see #setRunTime2
   */
  public static final Property RunTime2 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime2} property.
   * @see #RunTime2
   */
  public double getRunTime2() { return getDouble(RunTime2); }

  /**
   * Set the {@code RunTime2} property.
   * @see #RunTime2
   */
  public void setRunTime2(double v) { setDouble(RunTime2, v, null); }

  //endregion Property "RunTime2"

  //region Property "RunTime3"

  /**
   * Slot for the {@code RunTime3} property.
   * @see #getRunTime3
   * @see #setRunTime3
   */
  public static final Property RunTime3 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime3} property.
   * @see #RunTime3
   */
  public double getRunTime3() { return getDouble(RunTime3); }

  /**
   * Set the {@code RunTime3} property.
   * @see #RunTime3
   */
  public void setRunTime3(double v) { setDouble(RunTime3, v, null); }

  //endregion Property "RunTime3"

  //region Property "RunTime4"

  /**
   * Slot for the {@code RunTime4} property.
   * @see #getRunTime4
   * @see #setRunTime4
   */
  public static final Property RunTime4 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime4} property.
   * @see #RunTime4
   */
  public double getRunTime4() { return getDouble(RunTime4); }

  /**
   * Set the {@code RunTime4} property.
   * @see #RunTime4
   */
  public void setRunTime4(double v) { setDouble(RunTime4, v, null); }

  //endregion Property "RunTime4"

  //region Property "RunTime5"

  /**
   * Slot for the {@code RunTime5} property.
   * @see #getRunTime5
   * @see #setRunTime5
   */
  public static final Property RunTime5 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime5} property.
   * @see #RunTime5
   */
  public double getRunTime5() { return getDouble(RunTime5); }

  /**
   * Set the {@code RunTime5} property.
   * @see #RunTime5
   */
  public void setRunTime5(double v) { setDouble(RunTime5, v, null); }

  //endregion Property "RunTime5"

  //region Property "RunTime6"

  /**
   * Slot for the {@code RunTime6} property.
   * @see #getRunTime6
   * @see #setRunTime6
   */
  public static final Property RunTime6 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime6} property.
   * @see #RunTime6
   */
  public double getRunTime6() { return getDouble(RunTime6); }

  /**
   * Set the {@code RunTime6} property.
   * @see #RunTime6
   */
  public void setRunTime6(double v) { setDouble(RunTime6, v, null); }

  //endregion Property "RunTime6"

  //region Property "RunTime7"

  /**
   * Slot for the {@code RunTime7} property.
   * @see #getRunTime7
   * @see #setRunTime7
   */
  public static final Property RunTime7 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime7} property.
   * @see #RunTime7
   */
  public double getRunTime7() { return getDouble(RunTime7); }

  /**
   * Set the {@code RunTime7} property.
   * @see #RunTime7
   */
  public void setRunTime7(double v) { setDouble(RunTime7, v, null); }

  //endregion Property "RunTime7"

  //region Property "RunTime8"

  /**
   * Slot for the {@code RunTime8} property.
   * @see #getRunTime8
   * @see #setRunTime8
   */
  public static final Property RunTime8 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime8} property.
   * @see #RunTime8
   */
  public double getRunTime8() { return getDouble(RunTime8); }

  /**
   * Set the {@code RunTime8} property.
   * @see #RunTime8
   */
  public void setRunTime8(double v) { setDouble(RunTime8, v, null); }

  //endregion Property "RunTime8"

  //region Property "RunTime9"

  /**
   * Slot for the {@code RunTime9} property.
   * @see #getRunTime9
   * @see #setRunTime9
   */
  public static final Property RunTime9 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime9} property.
   * @see #RunTime9
   */
  public double getRunTime9() { return getDouble(RunTime9); }

  /**
   * Set the {@code RunTime9} property.
   * @see #RunTime9
   */
  public void setRunTime9(double v) { setDouble(RunTime9, v, null); }

  //endregion Property "RunTime9"

  //region Property "RunTime10"

  /**
   * Slot for the {@code RunTime10} property.
   * @see #getRunTime10
   * @see #setRunTime10
   */
  public static final Property RunTime10 = newProperty(Flags.SUMMARY, 0.0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BDouble.make(0.0)), BFacets.make(BFacets.MAX, BDouble.make(Double.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MINUTE))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code RunTime10} property.
   * @see #RunTime10
   */
  public double getRunTime10() { return getDouble(RunTime10); }

  /**
   * Set the {@code RunTime10} property.
   * @see #RunTime10
   */
  public void setRunTime10(double v) { setDouble(RunTime10, v, null); }

  //endregion Property "RunTime10"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRuntimeStrategy.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
}
