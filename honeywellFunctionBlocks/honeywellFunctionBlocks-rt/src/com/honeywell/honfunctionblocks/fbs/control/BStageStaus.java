/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BStruct;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of StageDriver block implementation
 * This class holds the stage outputs in  the last iteration. 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 * @since Dec 22, 2017
 */

@NiagaraType
@NiagaraProperty(name = "stageStatus1", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus2", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus3", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus4", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus5", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus6", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus7", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus8", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus9", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus10", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus11", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus12", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus13", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus14", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus15", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus16", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus17", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus18", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus19", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "stageStatus20", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStageStaus extends BStruct {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BStageStaus(4248604313)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "stageStatus1"

  /**
   * Slot for the {@code stageStatus1} property.
   * @see #getStageStatus1
   * @see #setStageStatus1
   */
  public static final Property stageStatus1 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus1} property.
   * @see #stageStatus1
   */
  public boolean getStageStatus1() { return getBoolean(stageStatus1); }

  /**
   * Set the {@code stageStatus1} property.
   * @see #stageStatus1
   */
  public void setStageStatus1(boolean v) { setBoolean(stageStatus1, v, null); }

  //endregion Property "stageStatus1"

  //region Property "stageStatus2"

  /**
   * Slot for the {@code stageStatus2} property.
   * @see #getStageStatus2
   * @see #setStageStatus2
   */
  public static final Property stageStatus2 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus2} property.
   * @see #stageStatus2
   */
  public boolean getStageStatus2() { return getBoolean(stageStatus2); }

  /**
   * Set the {@code stageStatus2} property.
   * @see #stageStatus2
   */
  public void setStageStatus2(boolean v) { setBoolean(stageStatus2, v, null); }

  //endregion Property "stageStatus2"

  //region Property "stageStatus3"

  /**
   * Slot for the {@code stageStatus3} property.
   * @see #getStageStatus3
   * @see #setStageStatus3
   */
  public static final Property stageStatus3 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus3} property.
   * @see #stageStatus3
   */
  public boolean getStageStatus3() { return getBoolean(stageStatus3); }

  /**
   * Set the {@code stageStatus3} property.
   * @see #stageStatus3
   */
  public void setStageStatus3(boolean v) { setBoolean(stageStatus3, v, null); }

  //endregion Property "stageStatus3"

  //region Property "stageStatus4"

  /**
   * Slot for the {@code stageStatus4} property.
   * @see #getStageStatus4
   * @see #setStageStatus4
   */
  public static final Property stageStatus4 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus4} property.
   * @see #stageStatus4
   */
  public boolean getStageStatus4() { return getBoolean(stageStatus4); }

  /**
   * Set the {@code stageStatus4} property.
   * @see #stageStatus4
   */
  public void setStageStatus4(boolean v) { setBoolean(stageStatus4, v, null); }

  //endregion Property "stageStatus4"

  //region Property "stageStatus5"

  /**
   * Slot for the {@code stageStatus5} property.
   * @see #getStageStatus5
   * @see #setStageStatus5
   */
  public static final Property stageStatus5 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus5} property.
   * @see #stageStatus5
   */
  public boolean getStageStatus5() { return getBoolean(stageStatus5); }

  /**
   * Set the {@code stageStatus5} property.
   * @see #stageStatus5
   */
  public void setStageStatus5(boolean v) { setBoolean(stageStatus5, v, null); }

  //endregion Property "stageStatus5"

  //region Property "stageStatus6"

  /**
   * Slot for the {@code stageStatus6} property.
   * @see #getStageStatus6
   * @see #setStageStatus6
   */
  public static final Property stageStatus6 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus6} property.
   * @see #stageStatus6
   */
  public boolean getStageStatus6() { return getBoolean(stageStatus6); }

  /**
   * Set the {@code stageStatus6} property.
   * @see #stageStatus6
   */
  public void setStageStatus6(boolean v) { setBoolean(stageStatus6, v, null); }

  //endregion Property "stageStatus6"

  //region Property "stageStatus7"

  /**
   * Slot for the {@code stageStatus7} property.
   * @see #getStageStatus7
   * @see #setStageStatus7
   */
  public static final Property stageStatus7 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus7} property.
   * @see #stageStatus7
   */
  public boolean getStageStatus7() { return getBoolean(stageStatus7); }

  /**
   * Set the {@code stageStatus7} property.
   * @see #stageStatus7
   */
  public void setStageStatus7(boolean v) { setBoolean(stageStatus7, v, null); }

  //endregion Property "stageStatus7"

  //region Property "stageStatus8"

  /**
   * Slot for the {@code stageStatus8} property.
   * @see #getStageStatus8
   * @see #setStageStatus8
   */
  public static final Property stageStatus8 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus8} property.
   * @see #stageStatus8
   */
  public boolean getStageStatus8() { return getBoolean(stageStatus8); }

  /**
   * Set the {@code stageStatus8} property.
   * @see #stageStatus8
   */
  public void setStageStatus8(boolean v) { setBoolean(stageStatus8, v, null); }

  //endregion Property "stageStatus8"

  //region Property "stageStatus9"

  /**
   * Slot for the {@code stageStatus9} property.
   * @see #getStageStatus9
   * @see #setStageStatus9
   */
  public static final Property stageStatus9 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus9} property.
   * @see #stageStatus9
   */
  public boolean getStageStatus9() { return getBoolean(stageStatus9); }

  /**
   * Set the {@code stageStatus9} property.
   * @see #stageStatus9
   */
  public void setStageStatus9(boolean v) { setBoolean(stageStatus9, v, null); }

  //endregion Property "stageStatus9"

  //region Property "stageStatus10"

  /**
   * Slot for the {@code stageStatus10} property.
   * @see #getStageStatus10
   * @see #setStageStatus10
   */
  public static final Property stageStatus10 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus10} property.
   * @see #stageStatus10
   */
  public boolean getStageStatus10() { return getBoolean(stageStatus10); }

  /**
   * Set the {@code stageStatus10} property.
   * @see #stageStatus10
   */
  public void setStageStatus10(boolean v) { setBoolean(stageStatus10, v, null); }

  //endregion Property "stageStatus10"

  //region Property "stageStatus11"

  /**
   * Slot for the {@code stageStatus11} property.
   * @see #getStageStatus11
   * @see #setStageStatus11
   */
  public static final Property stageStatus11 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus11} property.
   * @see #stageStatus11
   */
  public boolean getStageStatus11() { return getBoolean(stageStatus11); }

  /**
   * Set the {@code stageStatus11} property.
   * @see #stageStatus11
   */
  public void setStageStatus11(boolean v) { setBoolean(stageStatus11, v, null); }

  //endregion Property "stageStatus11"

  //region Property "stageStatus12"

  /**
   * Slot for the {@code stageStatus12} property.
   * @see #getStageStatus12
   * @see #setStageStatus12
   */
  public static final Property stageStatus12 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus12} property.
   * @see #stageStatus12
   */
  public boolean getStageStatus12() { return getBoolean(stageStatus12); }

  /**
   * Set the {@code stageStatus12} property.
   * @see #stageStatus12
   */
  public void setStageStatus12(boolean v) { setBoolean(stageStatus12, v, null); }

  //endregion Property "stageStatus12"

  //region Property "stageStatus13"

  /**
   * Slot for the {@code stageStatus13} property.
   * @see #getStageStatus13
   * @see #setStageStatus13
   */
  public static final Property stageStatus13 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus13} property.
   * @see #stageStatus13
   */
  public boolean getStageStatus13() { return getBoolean(stageStatus13); }

  /**
   * Set the {@code stageStatus13} property.
   * @see #stageStatus13
   */
  public void setStageStatus13(boolean v) { setBoolean(stageStatus13, v, null); }

  //endregion Property "stageStatus13"

  //region Property "stageStatus14"

  /**
   * Slot for the {@code stageStatus14} property.
   * @see #getStageStatus14
   * @see #setStageStatus14
   */
  public static final Property stageStatus14 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus14} property.
   * @see #stageStatus14
   */
  public boolean getStageStatus14() { return getBoolean(stageStatus14); }

  /**
   * Set the {@code stageStatus14} property.
   * @see #stageStatus14
   */
  public void setStageStatus14(boolean v) { setBoolean(stageStatus14, v, null); }

  //endregion Property "stageStatus14"

  //region Property "stageStatus15"

  /**
   * Slot for the {@code stageStatus15} property.
   * @see #getStageStatus15
   * @see #setStageStatus15
   */
  public static final Property stageStatus15 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus15} property.
   * @see #stageStatus15
   */
  public boolean getStageStatus15() { return getBoolean(stageStatus15); }

  /**
   * Set the {@code stageStatus15} property.
   * @see #stageStatus15
   */
  public void setStageStatus15(boolean v) { setBoolean(stageStatus15, v, null); }

  //endregion Property "stageStatus15"

  //region Property "stageStatus16"

  /**
   * Slot for the {@code stageStatus16} property.
   * @see #getStageStatus16
   * @see #setStageStatus16
   */
  public static final Property stageStatus16 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus16} property.
   * @see #stageStatus16
   */
  public boolean getStageStatus16() { return getBoolean(stageStatus16); }

  /**
   * Set the {@code stageStatus16} property.
   * @see #stageStatus16
   */
  public void setStageStatus16(boolean v) { setBoolean(stageStatus16, v, null); }

  //endregion Property "stageStatus16"

  //region Property "stageStatus17"

  /**
   * Slot for the {@code stageStatus17} property.
   * @see #getStageStatus17
   * @see #setStageStatus17
   */
  public static final Property stageStatus17 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus17} property.
   * @see #stageStatus17
   */
  public boolean getStageStatus17() { return getBoolean(stageStatus17); }

  /**
   * Set the {@code stageStatus17} property.
   * @see #stageStatus17
   */
  public void setStageStatus17(boolean v) { setBoolean(stageStatus17, v, null); }

  //endregion Property "stageStatus17"

  //region Property "stageStatus18"

  /**
   * Slot for the {@code stageStatus18} property.
   * @see #getStageStatus18
   * @see #setStageStatus18
   */
  public static final Property stageStatus18 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus18} property.
   * @see #stageStatus18
   */
  public boolean getStageStatus18() { return getBoolean(stageStatus18); }

  /**
   * Set the {@code stageStatus18} property.
   * @see #stageStatus18
   */
  public void setStageStatus18(boolean v) { setBoolean(stageStatus18, v, null); }

  //endregion Property "stageStatus18"

  //region Property "stageStatus19"

  /**
   * Slot for the {@code stageStatus19} property.
   * @see #getStageStatus19
   * @see #setStageStatus19
   */
  public static final Property stageStatus19 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus19} property.
   * @see #stageStatus19
   */
  public boolean getStageStatus19() { return getBoolean(stageStatus19); }

  /**
   * Set the {@code stageStatus19} property.
   * @see #stageStatus19
   */
  public void setStageStatus19(boolean v) { setBoolean(stageStatus19, v, null); }

  //endregion Property "stageStatus19"

  //region Property "stageStatus20"

  /**
   * Slot for the {@code stageStatus20} property.
   * @see #getStageStatus20
   * @see #setStageStatus20
   */
  public static final Property stageStatus20 = newProperty(Flags.SUMMARY | Flags.TRANSIENT, false, null);

  /**
   * Get the {@code stageStatus20} property.
   * @see #stageStatus20
   */
  public boolean getStageStatus20() { return getBoolean(stageStatus20); }

  /**
   * Set the {@code stageStatus20} property.
   * @see #stageStatus20
   */
  public void setStageStatus20(boolean v) { setBoolean(stageStatus20, v, null); }

  //endregion Property "stageStatus20"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStageStaus.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
}
