/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

/**
 * Requirement ID: F1PLT-ADR-405 - Data holder to be used for Non-linear calculation
 * for AIA and FlowControl
 * 
 * <AUTHOR> - <PERSON> .<PERSON>
 * @since Jan 7, 2018
 */
class NonLinearData {
	private double error;
	private double trValue;
	private double maxAOChangeValue;
	private double deadbandValue;
	private double dervGainValue = 0;
	private double minAOChangeValue = 0;
	
	/**
	 * @return the error
	 */
	public double getError() {
		return error;
	}
	/**
	 * @param error the error to set
	 */
	public void setError(double error) {
		this.error = error;
	}
	/**
	 * @return the trValue
	 */
	public double getTrValue() {
		return trValue;
	}
	/**
	 * @param trValue the trValue to set
	 */
	public void setTrValue(double trValue) {
		this.trValue = trValue;
	}
	/**
	 * @return the maxAOChangeValue
	 */
	public double getMaxAOChangeValue() {
		return maxAOChangeValue;
	}
	/**
	 * @param maxAOChangeValue the maxAOChangeValue to set
	 */
	public void setMaxAOChangeValue(double maxAOChangeValue) {
		this.maxAOChangeValue = maxAOChangeValue;
	}
	/**
	 * @return the deadbandValue
	 */
	public double getDeadbandValue() {
		return deadbandValue;
	}
	/**
	 * @param deadbandValue the deadbandValue to set
	 */
	public void setDeadbandValue(double deadbandValue) {
		this.deadbandValue = deadbandValue;
	}
	/**
	 * @return the dervGainValue
	 */
	public double getDervGainValue() {
		return dervGainValue;
	}
	/**
	 * @param dervGainValue the dervGainValue to set
	 */
	public void setDervGainValue(double dervGainValue) {
		this.dervGainValue = dervGainValue;
	}
	/**
	 * @return the minAOChangeValue
	 */
	public double getMinAOChangeValue() {
		return minAOChangeValue;
	}
	/**
	 * @param minAOChangeValue the minAOChangeValue to set
	 */
	public void setMinAOChangeValue(double minAOChangeValue) {
		this.minAOChangeValue = minAOChangeValue;
	}
}
