/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

/**
 * Nonlinear calculation usable by either AIA or flow control
 * 
 * <AUTHOR> - <PERSON> .K
 * @since Jan 15, 2018
 */

public final class NonLinearCalculator {
	private static final int POWER_FACTOR = 3;
	
	///CLOVER:OFF
	private NonLinearCalculator()  {}
	
	///CLOVER:ON
	/**
	 * To calculate the gain or error to correct from the previous output
	 */
	public static double calculateGains(NonLinearData nonLinearBlockData, INonLinearBlock block) {
		double error = nonLinearBlockData.getError();
		double trValue = nonLinearBlockData.getTrValue();
		double maxAOChangeValue = nonLinearBlockData.getMaxAOChangeValue();
		double deadbandValue = nonLinearBlockData.getDeadbandValue();
		double dervGainValue = nonLinearBlockData.getDervGainValue();
		double minAOChangeValue = nonLinearBlockData.getMinAOChangeValue();
		
		double gains = 0;
		
		//using derivative gain only if configured
		if(dervGainValue > 0) {
			double deltaError = error - block.getOldError();
			block.setOldError(error);
			
			if(deltaError > 1)
				deltaError = 1;
			else if(deltaError < -1)
				deltaError = -1;
			
			error = error + dervGainValue * deltaError;
		}
		
		int errorDirection = 1;
		if(error < 0) {
			errorDirection = -1;
			error = -error;
		}
		
		double deltaDb = trValue - deadbandValue;
		if(error > deadbandValue && (Double.compare(deltaDb, 0) != 0)) {
			if(error >= trValue) {
				gains = maxAOChangeValue * errorDirection;
			} else {
				double aoDelta = maxAOChangeValue - minAOChangeValue;
				double v = (error - deadbandValue) / deltaDb;
				gains = errorDirection * (aoDelta * Math.pow(v, POWER_FACTOR) + minAOChangeValue);
			}
		}
		return gains;
	}
}
