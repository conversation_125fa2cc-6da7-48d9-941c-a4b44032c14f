/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BStruct;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

/**
 * Created this as part of StageDriver block implementation
 * This class holds the data from last iteration required for FOFO lead lag strategy. 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 * @since Dec 22, 2017
 */

@NiagaraType
@NiagaraProperty(name = "seqStartPtr", type = "int", defaultValue = "0", flags = Flags.SUMMARY,
facets = {
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(20)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name = "oldNumStages", type = "int", defaultValue = "0", flags = Flags.SUMMARY,
facets = {
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(20)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
})

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BFOFOStrategy extends BStruct {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BFOFOStrategy(3477319181)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "seqStartPtr"

  /**
   * Slot for the {@code seqStartPtr} property.
   * @see #getSeqStartPtr
   * @see #setSeqStartPtr
   */
  public static final Property seqStartPtr = newProperty(Flags.SUMMARY, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(20))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code seqStartPtr} property.
   * @see #seqStartPtr
   */
  public int getSeqStartPtr() { return getInt(seqStartPtr); }

  /**
   * Set the {@code seqStartPtr} property.
   * @see #seqStartPtr
   */
  public void setSeqStartPtr(int v) { setInt(seqStartPtr, v, null); }

  //endregion Property "seqStartPtr"

  //region Property "oldNumStages"

  /**
   * Slot for the {@code oldNumStages} property.
   * @see #getOldNumStages
   * @see #setOldNumStages
   */
  public static final Property oldNumStages = newProperty(Flags.SUMMARY, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(20))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code oldNumStages} property.
   * @see #oldNumStages
   */
  public int getOldNumStages() { return getInt(oldNumStages); }

  /**
   * Set the {@code oldNumStages} property.
   * @see #oldNumStages
   */
  public void setOldNumStages(int v) { setInt(oldNumStages, v, null); }

  //endregion Property "oldNumStages"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFOFOStrategy.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/


}
