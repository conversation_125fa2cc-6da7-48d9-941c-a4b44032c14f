/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of FlowControl block implementation
 * This ENUM is used for Flow Control Units used in Flow Control block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON>
 * @since Jan 3, 2018
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range("CfmFt"), 
		@Range("LpsMtr"),
		@Range("CmhMtr")
		}, defaultValue = "CfmFt")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BFlowControlUnitEnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BFlowControlUnitEnum(2702906721)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for CfmFt. */
  public static final int CFM_FT = 0;
  /** Ordinal value for LpsMtr. */
  public static final int LPS_MTR = 1;
  /** Ordinal value for CmhMtr. */
  public static final int CMH_MTR = 2;

  /** BFlowControlUnitEnum constant for CfmFt. */
  public static final BFlowControlUnitEnum CfmFt = new BFlowControlUnitEnum(CFM_FT);
  /** BFlowControlUnitEnum constant for LpsMtr. */
  public static final BFlowControlUnitEnum LpsMtr = new BFlowControlUnitEnum(LPS_MTR);
  /** BFlowControlUnitEnum constant for CmhMtr. */
  public static final BFlowControlUnitEnum CmhMtr = new BFlowControlUnitEnum(CMH_MTR);

  /** Factory method with ordinal. */
  public static BFlowControlUnitEnum make(int ordinal)
  {
    return (BFlowControlUnitEnum)CfmFt.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BFlowControlUnitEnum make(String tag)
  {
    return (BFlowControlUnitEnum)CfmFt.getRange().get(tag);
  }

  /** Private constructor. */
  private BFlowControlUnitEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BFlowControlUnitEnum DEFAULT = CfmFt;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowControlUnitEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
