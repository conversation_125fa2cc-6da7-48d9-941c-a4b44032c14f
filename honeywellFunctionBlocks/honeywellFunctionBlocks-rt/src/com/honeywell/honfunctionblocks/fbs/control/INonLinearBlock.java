/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

/**
 * Interface to bring non-linear block under one group, will be used by AIA and FlowControl
 * 
 * <AUTHOR> - <PERSON>K
 * @since Jan 15, 2018
 */
public interface INonLinearBlock {
	/**
	 * @return the oldError
	 */
	double getOldError();

	/**
	 * @param oldError the oldError to set
	 */
	void setOldError(double oldError);
}
