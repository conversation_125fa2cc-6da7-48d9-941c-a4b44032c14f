/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BDouble;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.enums.BAiaDirectionEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Ravi Bharathi .K
 * @since Jan 4, 2018
 */

@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"aia.png\")", flags = Flags.HIDDEN | Flags.READONLY)

@NiagaraProperty(name = "sensor", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "setPt", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="disable", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)

@NiagaraProperty(name = "tr", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

/**
 * deadband range = 0 < deadband < tr (throttling range value)
 */
@NiagaraProperty(name = "deadband", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "maxAOChange", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(100)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT_PER_SECOND)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "dervGain", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "minAOChange", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "revAct", type = "BAiaDirectionEnum", defaultValue = "BAiaDirectionEnum.directActing")

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.PERCENT)"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })

@NiagaraProperty(name="oldError", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103"})
public class BAia extends BFunctionBlock implements INonLinearBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BAia(1451665540)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "aia.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "sensor"

  /**
   * Slot for the {@code sensor} property.
   * @see #getSensor
   * @see #setSensor
   */
  public static final Property sensor = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code sensor} property.
   * @see #sensor
   */
  public BHonStatusNumeric getSensor() { return (BHonStatusNumeric)get(sensor); }

  /**
   * Set the {@code sensor} property.
   * @see #sensor
   */
  public void setSensor(BHonStatusNumeric v) { set(sensor, v, null); }

  //endregion Property "sensor"

  //region Property "setPt"

  /**
   * Slot for the {@code setPt} property.
   * @see #getSetPt
   * @see #setSetPt
   */
  public static final Property setPt = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code setPt} property.
   * @see #setPt
   */
  public BHonStatusNumeric getSetPt() { return (BHonStatusNumeric)get(setPt); }

  /**
   * Set the {@code setPt} property.
   * @see #setPt
   */
  public void setSetPt(BHonStatusNumeric v) { set(setPt, v, null); }

  //endregion Property "setPt"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "tr"

  /**
   * Slot for the {@code tr} property.
   * @see #getTr
   * @see #setTr
   */
  public static final Property tr = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code tr} property.
   * @see #tr
   */
  public BHonStatusNumeric getTr() { return (BHonStatusNumeric)get(tr); }

  /**
   * Set the {@code tr} property.
   * @see #tr
   */
  public void setTr(BHonStatusNumeric v) { set(tr, v, null); }

  //endregion Property "tr"

  //region Property "deadband"

  /**
   * Slot for the {@code deadband} property.
   * deadband range = 0 < deadband < tr (throttling range value)
   * @see #getDeadband
   * @see #setDeadband
   */
  public static final Property deadband = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code deadband} property.
   * deadband range = 0 < deadband < tr (throttling range value)
   * @see #deadband
   */
  public BHonStatusNumeric getDeadband() { return (BHonStatusNumeric)get(deadband); }

  /**
   * Set the {@code deadband} property.
   * deadband range = 0 < deadband < tr (throttling range value)
   * @see #deadband
   */
  public void setDeadband(BHonStatusNumeric v) { set(deadband, v, null); }

  //endregion Property "deadband"

  //region Property "maxAOChange"

  /**
   * Slot for the {@code maxAOChange} property.
   * @see #getMaxAOChange
   * @see #setMaxAOChange
   */
  public static final Property maxAOChange = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(100))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT_PER_SECOND))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code maxAOChange} property.
   * @see #maxAOChange
   */
  public BHonStatusNumeric getMaxAOChange() { return (BHonStatusNumeric)get(maxAOChange); }

  /**
   * Set the {@code maxAOChange} property.
   * @see #maxAOChange
   */
  public void setMaxAOChange(BHonStatusNumeric v) { set(maxAOChange, v, null); }

  //endregion Property "maxAOChange"

  //region Property "dervGain"

  /**
   * Slot for the {@code dervGain} property.
   * @see #getDervGain
   * @see #setDervGain
   */
  public static final Property dervGain = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code dervGain} property.
   * @see #dervGain
   */
  public BHonStatusNumeric getDervGain() { return (BHonStatusNumeric)get(dervGain); }

  /**
   * Set the {@code dervGain} property.
   * @see #dervGain
   */
  public void setDervGain(BHonStatusNumeric v) { set(dervGain, v, null); }

  //endregion Property "dervGain"

  //region Property "minAOChange"

  /**
   * Slot for the {@code minAOChange} property.
   * @see #getMinAOChange
   * @see #setMinAOChange
   */
  public static final Property minAOChange = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code minAOChange} property.
   * @see #minAOChange
   */
  public BHonStatusNumeric getMinAOChange() { return (BHonStatusNumeric)get(minAOChange); }

  /**
   * Set the {@code minAOChange} property.
   * @see #minAOChange
   */
  public void setMinAOChange(BHonStatusNumeric v) { set(minAOChange, v, null); }

  //endregion Property "minAOChange"

  //region Property "revAct"

  /**
   * Slot for the {@code revAct} property.
   * @see #getRevAct
   * @see #setRevAct
   */
  public static final Property revAct = newProperty(0, BAiaDirectionEnum.directActing, null);

  /**
   * Get the {@code revAct} property.
   * @see #revAct
   */
  public BAiaDirectionEnum getRevAct() { return (BAiaDirectionEnum)get(revAct); }

  /**
   * Set the {@code revAct} property.
   * @see #revAct
   */
  public void setRevAct(BAiaDirectionEnum v) { set(revAct, v, null); }

  //endregion Property "revAct"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.PERCENT))), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "oldError"

  /**
   * Slot for the {@code oldError} property.
   * @see #getOldError
   * @see #setOldError
   */
  public static final Property oldError = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code oldError} property.
   * @see #oldError
   */
  @Override
  public double getOldError() { return getDouble(oldError); }

  /**
   * Set the {@code oldError} property.
   * @see #oldError
   */
  @Override
  public void setOldError(double v) { setDouble(oldError, v, null); }

  //endregion Property "oldError"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAia.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent()
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		setOldError(0.0);
		getOUTPUT().setValue(0.0);
	}
	
	@Override
	public void started() throws Exception {
		super.started();
		updateDisableSlotAfterNegate();
	}
			
	private void updateDisableSlotAfterNegate() {
		BFiniteStatusBoolean disable1 = getDisable();
		if(!(disable1 instanceof BNegatableFiniteStatusBoolean)) {
			BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			negatableFiniteStatusBoolean.setStatus(disable1.getStatus());
			negatableFiniteStatusBoolean.setValue(disable1.getValue());
			setDisable(negatableFiniteStatusBoolean);
		}
	}

	
	/*
	 * (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		double outValue = getOUTPUT().getValue();
		
		boolean isDisabled = false;
		if(isConfigured(disable))
		{
			isDisabled = getDisable().getBoolean();
		
			if (((BNegatableFiniteStatusBoolean) getDisable()).getNegate()) {
				isDisabled = !isDisabled;
				}
		}
		
		//AIA functional will be disabled and output will be set to "0" for the following condition
		//disable = TRUE; sensor = not-configured or invalid; setPt = not-configured or invalid; tr = not-configured or invalid
		if(isDisabled || !isSlotValueValid(sensor) || !isSlotValueValid(setPt) || !isSlotValueValid(tr)) {
			setOldError(0.0);
			outValue = 0.0;
			
		} else {
			NonLinearData aia = calculateLogicValue(executionParams);
			double gains = NonLinearCalculator.calculateGains(aia, this);

			outValue += gains;
			
			//limiting the actual out to be between 0 and 100
			outValue = limitInput(outValue, 0, HUNDRED, 0);
		}
		
		getOUTPUT().setValue(outValue);
	}

	/**
	 * This method calculate the actual value to be used in the logic for all the inputs 
	 */
	private NonLinearData calculateLogicValue(BExecutionParams executionParams) {
		NonLinearData aia = new NonLinearData();
		
		double iterationInterval = executionParams.getIterationInterval();

		double sensorValue = getSensor().getValue();
		double setPtValue = getSetPt().getValue();
		double trValue = getTr().getValue();
		
		double maxAOChangeValue = MAX_AO_CHANGE_VALUE_DEFAULT;
		if(isSlotValueValid(maxAOChange)) {
			maxAOChangeValue = getMaxAOChange().getValue();
		}
		maxAOChangeValue *= (iterationInterval / UnitConstants.THOUSAND_MILLI_SECOND);
		
		double deadbandValue = 0;
		if(isSlotValueValid(deadband)) {
			deadbandValue = getDeadband().getValue();
		}
		
		double dervGainValue = 0;
		if(isSlotValueValid(dervGain)) {
			dervGainValue = getDervGain().getValue();
		}
		
		double minAOChangeValue = 0;
		if(isSlotValueValid(minAOChange)) {
			minAOChangeValue = getMinAOChange().getValue();
		}
		
		boolean revActValue = getRevAct().getOrdinal() == BAiaDirectionEnum.DIRECT_ACTING ? false : true;
		double error = sensorValue - setPtValue;
		if(revActValue)
			error = -error;
		
		aia.setError(error);
		aia.setTrValue(trValue);
		aia.setMaxAOChangeValue(maxAOChangeValue);
		aia.setDeadbandValue(deadbandValue);
		aia.setDervGainValue(dervGainValue);
		aia.setMinAOChangeValue(minAOChangeValue);
		return aia;
	}

	/* (non-Javadoc)
	 * @see javax.baja.sys.BComplex#getSlotFacets(javax.baja.sys.Slot)
	 */
	@Override
	public BFacets getSlotFacets(Slot slot) {
		BFacets f = super.getSlotFacets(slot);
		if(slot.equals(deadband)) { //Deadband range = 0 < deadband < tr
			double trValue = getTr().getValue();
			f = BFacets.make(f, BFacets.MAX, BDouble.make(trValue-1));
			
		} else if (slot.equals(minAOChange)) { //minAOChange range = 0 < minAOChange <= maxAOChange
			double maxAOValue = getMaxAOChange().getValue();
			double minAOValue = Float.MAX_VALUE;
			if(maxAOValue < Float.MAX_VALUE)
				minAOValue = maxAOValue;
			f = BFacets.make(f, BFacets.MAX, BDouble.make(minAOValue));
		}
		return f;
	}

	/*
	 * (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(sensor);
		propertyArrayList.add(setPt);
		propertyArrayList.add(disable);
		propertyArrayList.add(tr);
		propertyArrayList.add(maxAOChange);
		propertyArrayList.add(deadband);
		propertyArrayList.add(dervGain);
		propertyArrayList.add(minAOChange);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(OUTPUT);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(revAct);
		propertyArrayList.add(tr);
		return propertyArrayList;
	}
	
	//class variables
	private static final double MAX_AO_CHANGE_VALUE_DEFAULT = 1.1;
	private static final int HUNDRED = 100;
}
