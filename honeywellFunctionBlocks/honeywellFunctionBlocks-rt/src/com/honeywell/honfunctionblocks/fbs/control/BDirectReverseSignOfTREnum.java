/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of PID block implementation
 * This ENUM is used for revAct configuration used in PID block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 *
 */
@NiagaraType
@NiagaraEnum(range = {
		@Range("DirectActing"), 
		@Range("ReverseActing"),
		@Range("SignOfTR")
		}, defaultValue = "DirectActing")

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845"})

public final class BDirectReverseSignOfTREnum extends BFrozenEnum{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BDirectReverseSignOfTREnum(789448359)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for DirectActing. */
  public static final int DIRECT_ACTING = 0;
  /** Ordinal value for ReverseActing. */
  public static final int REVERSE_ACTING = 1;
  /** Ordinal value for SignOfTR. */
  public static final int SIGN_OF_TR = 2;

  /** BDirectReverseSignOfTREnum constant for DirectActing. */
  public static final BDirectReverseSignOfTREnum DirectActing = new BDirectReverseSignOfTREnum(DIRECT_ACTING);
  /** BDirectReverseSignOfTREnum constant for ReverseActing. */
  public static final BDirectReverseSignOfTREnum ReverseActing = new BDirectReverseSignOfTREnum(REVERSE_ACTING);
  /** BDirectReverseSignOfTREnum constant for SignOfTR. */
  public static final BDirectReverseSignOfTREnum SignOfTR = new BDirectReverseSignOfTREnum(SIGN_OF_TR);

  /** Factory method with ordinal. */
  public static BDirectReverseSignOfTREnum make(int ordinal)
  {
    return (BDirectReverseSignOfTREnum)DirectActing.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BDirectReverseSignOfTREnum make(String tag)
  {
    return (BDirectReverseSignOfTREnum)DirectActing.getRange().get(tag);
  }

  /** Private constructor. */
  private BDirectReverseSignOfTREnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BDirectReverseSignOfTREnum DEFAULT = DirectActing;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDirectReverseSignOfTREnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
