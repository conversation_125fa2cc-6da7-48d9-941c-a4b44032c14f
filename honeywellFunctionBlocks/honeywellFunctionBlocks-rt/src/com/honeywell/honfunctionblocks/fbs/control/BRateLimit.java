/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BDimension;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of RateLimit block as per Functional Block SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since Dec 28, 2017
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"rateLimit.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "in", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="disable", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false)", flags=Flags.SUMMARY)

@NiagaraProperty(name = "startVal", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "upRate", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.make(UnitConstants.CHG_PER_SECOND, BDimension.DEFAULT)"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "downRate", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.1, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.make(UnitConstants.CHG_PER_SECOND, BDimension.DEFAULT)"),
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="startInterval", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(65535)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
		@Facet(name = "BFacets.PRECISION", value = "0")
})

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY
| Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })

@NiagaraProperty(name="startTimer", type="int", defaultValue="0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103"})

public class BRateLimit extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.BRateLimit(4062344335)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "rateLimit.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "in"

  /**
   * Slot for the {@code in} property.
   * @see #getIn
   * @see #setIn
   */
  public static final Property in = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in} property.
   * @see #in
   */
  public BHonStatusNumeric getIn() { return (BHonStatusNumeric)get(in); }

  /**
   * Set the {@code in} property.
   * @see #in
   */
  public void setIn(BHonStatusNumeric v) { set(in, v, null); }

  //endregion Property "in"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "startVal"

  /**
   * Slot for the {@code startVal} property.
   * @see #getStartVal
   * @see #setStartVal
   */
  public static final Property startVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code startVal} property.
   * @see #startVal
   */
  public BHonStatusNumeric getStartVal() { return (BHonStatusNumeric)get(startVal); }

  /**
   * Set the {@code startVal} property.
   * @see #startVal
   */
  public void setStartVal(BHonStatusNumeric v) { set(startVal, v, null); }

  //endregion Property "startVal"

  //region Property "upRate"

  /**
   * Slot for the {@code upRate} property.
   * @see #getUpRate
   * @see #setUpRate
   */
  public static final Property upRate = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.make(UnitConstants.CHG_PER_SECOND, BDimension.DEFAULT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code upRate} property.
   * @see #upRate
   */
  public BHonStatusNumeric getUpRate() { return (BHonStatusNumeric)get(upRate); }

  /**
   * Set the {@code upRate} property.
   * @see #upRate
   */
  public void setUpRate(BHonStatusNumeric v) { set(upRate, v, null); }

  //endregion Property "upRate"

  //region Property "downRate"

  /**
   * Slot for the {@code downRate} property.
   * @see #getDownRate
   * @see #setDownRate
   */
  public static final Property downRate = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.1, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.make(UnitConstants.CHG_PER_SECOND, BDimension.DEFAULT))), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code downRate} property.
   * @see #downRate
   */
  public BHonStatusNumeric getDownRate() { return (BHonStatusNumeric)get(downRate); }

  /**
   * Set the {@code downRate} property.
   * @see #downRate
   */
  public void setDownRate(BHonStatusNumeric v) { set(downRate, v, null); }

  //endregion Property "downRate"

  //region Property "startInterval"

  /**
   * Slot for the {@code startInterval} property.
   * @see #getStartInterval
   * @see #setStartInterval
   */
  public static final Property startInterval = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65535))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code startInterval} property.
   * @see #startInterval
   */
  public BHonStatusNumeric getStartInterval() { return (BHonStatusNumeric)get(startInterval); }

  /**
   * Set the {@code startInterval} property.
   * @see #startInterval
   */
  public void setStartInterval(BHonStatusNumeric v) { set(startInterval, v, null); }

  //endregion Property "startInterval"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "startTimer"

  /**
   * Slot for the {@code startTimer} property.
   * @see #getStartTimer
   * @see #setStartTimer
   */
  public static final Property startTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0, null);

  /**
   * Get the {@code startTimer} property.
   * @see #startTimer
   */
  public int getStartTimer() { return getInt(startTimer); }

  /**
   * Set the {@code startTimer} property.
   * @see #startTimer
   */
  public void setStartTimer(int v) { setInt(startTimer, v, null); }

  //endregion Property "startTimer"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRateLimit.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  	public BRateLimit() {
  		//EMPTY CONSTRUCTOR FOR FRAMEWORK USE
  	}
  	
  	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent()
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		
		int startIntervalValue = 0;
		if(isSlotValueValid(startInterval)) {
			startIntervalValue = (int) limitInput(getStartInterval().getValue(), 0, START_INTERVAL_LIMIT, 0);
		}
		setStartTimer(startIntervalValue * UnitConstants.THOUSAND_MILLI_SECOND);
		
		if(isSlotValueValid(startVal)) {
			getOUTPUT().setValue(getStartVal().getValue());
		}
	}
  
  /* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		int iterationInterval = executionParams.getIterationInterval();
		
		//initializing property "in"
		double inValue = 0.0;
		if(isConfigured(in)) {
			inValue = getIn().getValue();
			if(Double.isNaN(inValue))
				inValue = Double.POSITIVE_INFINITY;
		}
		
		//initializing property "disable"
		boolean disableValue = false;
		if(isConfigured(disable))  {
			disableValue = getDisable().getBoolean();
			if(((BNegatableFiniteStatusBoolean)getDisable()).getNegate()) {
				disableValue = !disableValue;
			}
		}
			
		
		//initializing property startInterval
		int startIntervalValue = 0;
		if(isSlotValueValid(startInterval)) {
			startIntervalValue = (int) limitInput(getStartInterval().getValue(), 0, START_INTERVAL_LIMIT, 0);
		}
		
		//RateLimit Algorithm starts here
		double outValue;
		if(disableValue) {
			//initializing property startVal
			double startValValue = getStartVal().getValue();
			
			outValue = handleRateLimitDisabled(inValue, startValValue, startIntervalValue);
		} else {
			
			//initializing property upRate
			double upRateValue = 0;
			if(isConfigured(upRate))
				upRateValue = limitInput(getUpRate().getValue(), 0, Float.MAX_VALUE, 0);
			
			//initializing property downRate
			double downRateValue = 0;
			if(isConfigured(downRate))
				downRateValue = limitInput(getDownRate().getValue(), 0, Float.MAX_VALUE, 0);
			
			outValue = handleRateLimitEnabled(iterationInterval, inValue, upRateValue, downRateValue, startIntervalValue);
		}
		
		getOUTPUT().setValue(outValue);
	}

	/**
	 * When RateLimit is enabled, honor the upRate or downRate as applicable and hold the output value till the 
	 * startInterval seconds is expired
	 */
	private double handleRateLimitEnabled(int iterationInterval, double inValue, double upRateValue, double downRateValue, int startIntervalValue) {
		//updating last out value
		double outValue = getOUTPUT().getValue();
		if(getStartTimer() != 0 || startIntervalValue == 0) {
			if(getStartTimer() > iterationInterval) {
				setStartTimer(getStartTimer() - iterationInterval);
			} else {
				setStartTimer(0);
			}
			
			double deltaOut = inValue - outValue;
			if(deltaOut > upRateValue) {
				outValue = outValue + upRateValue * ((double)iterationInterval  / UnitConstants.THOUSAND_MILLI_SECOND);
			} else if (deltaOut < -downRateValue) {
				outValue = outValue - downRateValue * ((double)iterationInterval  / UnitConstants.THOUSAND_MILLI_SECOND);
			} else {
				outValue = inValue;
			}
		} else {
			outValue = inValue;
		}
		return outValue;
	}

	/**
	 * When Rate limit is disabled then set the startVal as output only when startVal has valid value
	 */
	private double handleRateLimitDisabled(double inValue, double startValValue, int startIntervalValue) {
		double outValue;
		setStartTimer(startIntervalValue * UnitConstants.THOUSAND_MILLI_SECOND);
		if(isSlotValueValid(startVal)) {
			outValue = startValValue;
		} else {
			outValue = inValue;
		}
		return outValue;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(in);
		propertyArrayList.add(disable);
		propertyArrayList.add(startVal);
		propertyArrayList.add(upRate);
		propertyArrayList.add(downRate);
		propertyArrayList.add(startInterval);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	@Override
	public void started() throws Exception {
		super.started();
		BFiniteStatusBoolean disable2 = getDisable();
        if(!(disable2 instanceof BNegatableFiniteStatusBoolean)) {
        	BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
        	negatableFiniteStatusBoolean.setStatus(disable2.getStatus());
        	negatableFiniteStatusBoolean.setValue(disable2.getValue());
            setDisable(negatableFiniteStatusBoolean);
        }
	}
	
	//class variables
	private static final int START_INTERVAL_LIMIT = 65535;
			
}
