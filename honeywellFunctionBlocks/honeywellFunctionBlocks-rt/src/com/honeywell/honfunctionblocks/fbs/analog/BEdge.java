/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Edge as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 2, 2018
 */
@NiagaraType
//Inputs
@NiagaraProperty(name="x", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="offset", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})

//Outputs
@NiagaraProperty(name="THIS", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="PREVIOUS", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="RISE", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="FALL", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})

@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"edge.png\")", flags = Flags.HIDDEN | Flags.READONLY)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public class BEdge extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BEdge(3053222435)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * Inputs
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * Inputs
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * Inputs
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "offset"

  /**
   * Slot for the {@code offset} property.
   * @see #getOffset
   * @see #setOffset
   */
  public static final Property offset = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code offset} property.
   * @see #offset
   */
  public BHonStatusNumeric getOffset() { return (BHonStatusNumeric)get(offset); }

  /**
   * Set the {@code offset} property.
   * @see #offset
   */
  public void setOffset(BHonStatusNumeric v) { set(offset, v, null); }

  //endregion Property "offset"

  //region Property "THIS"

  /**
   * Slot for the {@code THIS} property.
   * Outputs
   * @see #getTHIS
   * @see #setTHIS
   */
  public static final Property THIS = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.UNITS, BUnit.NULL), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code THIS} property.
   * Outputs
   * @see #THIS
   */
  public BHonStatusNumeric getTHIS() { return (BHonStatusNumeric)get(THIS); }

  /**
   * Set the {@code THIS} property.
   * Outputs
   * @see #THIS
   */
  public void setTHIS(BHonStatusNumeric v) { set(THIS, v, null); }

  //endregion Property "THIS"

  //region Property "PREVIOUS"

  /**
   * Slot for the {@code PREVIOUS} property.
   * @see #getPREVIOUS
   * @see #setPREVIOUS
   */
  public static final Property PREVIOUS = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Float.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.UNITS, BUnit.NULL), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code PREVIOUS} property.
   * @see #PREVIOUS
   */
  public BHonStatusNumeric getPREVIOUS() { return (BHonStatusNumeric)get(PREVIOUS); }

  /**
   * Set the {@code PREVIOUS} property.
   * @see #PREVIOUS
   */
  public void setPREVIOUS(BHonStatusNumeric v) { set(PREVIOUS, v, null); }

  //endregion Property "PREVIOUS"

  //region Property "RISE"

  /**
   * Slot for the {@code RISE} property.
   * @see #getRISE
   * @see #setRISE
   */
  public static final Property RISE = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code RISE} property.
   * @see #RISE
   */
  public BHonStatusBoolean getRISE() { return (BHonStatusBoolean)get(RISE); }

  /**
   * Set the {@code RISE} property.
   * @see #RISE
   */
  public void setRISE(BHonStatusBoolean v) { set(RISE, v, null); }

  //endregion Property "RISE"

  //region Property "FALL"

  /**
   * Slot for the {@code FALL} property.
   * @see #getFALL
   * @see #setFALL
   */
  public static final Property FALL = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code FALL} property.
   * @see #FALL
   */
  public BHonStatusBoolean getFALL() { return (BHonStatusBoolean)get(FALL); }

  /**
   * Set the {@code FALL} property.
   * @see #FALL
   */
  public void setFALL(BHonStatusBoolean v) { set(FALL, v, null); }

  //endregion Property "FALL"

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "edge.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEdge.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeEdgeBlock();
	}
	
	private void executeEdgeBlock() {
		double offsetVal = getOffsetVal();
		double previousX = getTHIS().getValue();
		double thisX = getXVal();
		
		boolean isRise = false;
		boolean isFall = false;
		
		do {
			if(isValuePositiveInfinity(thisX) || isValuePositiveInfinity(previousX))
				break;
		
			if ( thisX > (previousX + offsetVal) ) {
	            isRise = true;
	        }
	        if ( thisX < (previousX - offsetVal) ) {
	            isFall = true;
	        }
		}while(false);
			
        getTHIS().setValue(thisX);
        getPREVIOUS().setValue(previousX);
        getRISE().setValue(isRise);
        getFALL().setValue(isFall);
	}
	
	
	
	private double getXVal() {
		double xVal = getX().getValue();
		if(!isSlotValueValid(x))
			xVal = Double.POSITIVE_INFINITY;
		return xVal;
	}
	
	private boolean isValuePositiveInfinity(double inValue) {
		return inValue>0 && Double.isInfinite(inValue);
	}
	
	private double getOffsetVal() {
		double offsetVal = getOffset().getValue();
		if(!isSlotValueValid(offset) || Double.isInfinite(offsetVal))
			offsetVal = 0;
		return offsetVal;			
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(x);
		propertyArrayList.add(offset);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(FALL);
		properties.add(PREVIOUS);
		properties.add(RISE);
		properties.add(THIS);
		return properties;
	}
	
}
