/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of Compare block implementation
 * This ENUM is used for 'operation' configuration used in Compare block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 *
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range("Equals"),
		@Range("LessThan"),
		@Range("GreaterThan")		
		}, defaultValue = "Equals")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class BCompareOperationEnum extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BCompareOperationEnum(2746105032)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Equals. */
  public static final int EQUALS = 0;
  /** Ordinal value for LessThan. */
  public static final int LESS_THAN = 1;
  /** Ordinal value for GreaterThan. */
  public static final int GREATER_THAN = 2;

  /** BCompareOperationEnum constant for Equals. */
  public static final BCompareOperationEnum Equals = new BCompareOperationEnum(EQUALS);
  /** BCompareOperationEnum constant for LessThan. */
  public static final BCompareOperationEnum LessThan = new BCompareOperationEnum(LESS_THAN);
  /** BCompareOperationEnum constant for GreaterThan. */
  public static final BCompareOperationEnum GreaterThan = new BCompareOperationEnum(GREATER_THAN);

  /** Factory method with ordinal. */
  public static BCompareOperationEnum make(int ordinal)
  {
    return (BCompareOperationEnum)Equals.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BCompareOperationEnum make(String tag)
  {
    return (BCompareOperationEnum)Equals.getRange().get(tag);
  }

  /** Private constructor. */
  private BCompareOperationEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BCompareOperationEnum DEFAULT = Equals;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareOperationEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
