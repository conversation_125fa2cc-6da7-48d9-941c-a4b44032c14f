/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;


import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;

/**
 * Implementation of BMaximum block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON><PERSON>
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"maximum.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BMaximum extends BMinMaxAverageBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BMaximum(1195732321)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "maximum.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMaximum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default Constructor
	 */
	public BMaximum() {
		//EMPTY IMPLEMENTATION
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		getOUTPUT().setValue(Double.POSITIVE_INFINITY);
		
		if(!getIgnoreInvalidInput() && hasAnyInvalidInput()){
			return;
		}
		
		double result = calculateMaximum();
		
		if(Double.isFinite(result)) {
			getOUTPUT().setValue(result);
		}
	}

	/**
	 * Get maximum value among the given inputs
	 * @return
	 */
	private double calculateMaximum() {
		double result = Double.NEGATIVE_INFINITY;
		
		//  Use only valid values
		if (isSlotValueValid(in1))
			result = Math.max(result, getIn1().getValue());
		if (isSlotValueValid(in2))
			result = Math.max(result, getIn2().getValue());
		if (isSlotValueValid(in3))
			result = Math.max(result, getIn3().getValue());
		if (isSlotValueValid(in4))
			result = Math.max(result, getIn4().getValue());
		if (isSlotValueValid(in5))
			result = Math.max(result, getIn5().getValue());
		if (isSlotValueValid(in6))
			result = Math.max(result, getIn6().getValue());
		if (isSlotValueValid(in7))
			result = Math.max(result, getIn7().getValue());
		if (isSlotValueValid(in8))
			result = Math.max(result, getIn8().getValue());
		return result;
	}
}
