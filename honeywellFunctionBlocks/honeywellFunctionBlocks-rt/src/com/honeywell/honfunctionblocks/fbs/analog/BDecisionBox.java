/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.Arrays;
import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Decision box as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 29, 2017
 */
@NiagaraType
//Inputs
//Not starting with small letter for YorNinput input slot for better readability

@NiagaraProperty(name = "YorNinput", type = "BFiniteStatusBoolean", defaultValue = "new BFiniteStatusBoolean(false, BStatus.nullStatus)", flags=Flags.SUMMARY)

@NiagaraProperty(name="hysteresis", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(65535)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})

@NiagaraProperty(name="value1", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value2", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value3", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value4", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value5", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value6", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value7", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value8", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value9", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value10", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value11", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value12", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value13", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value14", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value15", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value16", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value17", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value18", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value19", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value20", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value21", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value22", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value23", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value24", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value25", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value26", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value27", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value28", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value29", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value30", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="value31", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0f, BStatus.ok)",
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})

//Configuration
@NiagaraProperty(name="operation", type="BDecisionOperationEnum", defaultValue="BDecisionOperationEnum.DEFAULT")
@NiagaraProperty(name="numValues", type="int", defaultValue="2",
facets = { 
	@Facet(name = "BFacets.MIN", value = "2"),
	@Facet(name = "BFacets.MAX", value = "31"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") 
})

//Outputs
@NiagaraProperty(name="Y_OUTPUT", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="N_OUTPUT", type="BHonStatusBoolean", defaultValue="new BHonStatusBoolean(false, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")
})

@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"decision_box.png\")", flags = Flags.HIDDEN | Flags.READONLY)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public class BDecisionBox extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BDecisionBox(2962524174)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "YorNinput"

  /**
   * Slot for the {@code YorNinput} property.
   * @see #getYorNinput
   * @see #setYorNinput
   */
  public static final Property YorNinput = newProperty(Flags.SUMMARY, new BFiniteStatusBoolean(false, BStatus.nullStatus), null);

  /**
   * Get the {@code YorNinput} property.
   * @see #YorNinput
   */
  public BFiniteStatusBoolean getYorNinput() { return (BFiniteStatusBoolean)get(YorNinput); }

  /**
   * Set the {@code YorNinput} property.
   * @see #YorNinput
   */
  public void setYorNinput(BFiniteStatusBoolean v) { set(YorNinput, v, null); }

  //endregion Property "YorNinput"

  //region Property "hysteresis"

  /**
   * Slot for the {@code hysteresis} property.
   * @see #getHysteresis
   * @see #setHysteresis
   */
  public static final Property hysteresis = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65535))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code hysteresis} property.
   * @see #hysteresis
   */
  public BHonStatusNumeric getHysteresis() { return (BHonStatusNumeric)get(hysteresis); }

  /**
   * Set the {@code hysteresis} property.
   * @see #hysteresis
   */
  public void setHysteresis(BHonStatusNumeric v) { set(hysteresis, v, null); }

  //endregion Property "hysteresis"

  //region Property "value1"

  /**
   * Slot for the {@code value1} property.
   * @see #getValue1
   * @see #setValue1
   */
  public static final Property value1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value1} property.
   * @see #value1
   */
  public BHonStatusNumeric getValue1() { return (BHonStatusNumeric)get(value1); }

  /**
   * Set the {@code value1} property.
   * @see #value1
   */
  public void setValue1(BHonStatusNumeric v) { set(value1, v, null); }

  //endregion Property "value1"

  //region Property "value2"

  /**
   * Slot for the {@code value2} property.
   * @see #getValue2
   * @see #setValue2
   */
  public static final Property value2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value2} property.
   * @see #value2
   */
  public BHonStatusNumeric getValue2() { return (BHonStatusNumeric)get(value2); }

  /**
   * Set the {@code value2} property.
   * @see #value2
   */
  public void setValue2(BHonStatusNumeric v) { set(value2, v, null); }

  //endregion Property "value2"

  //region Property "value3"

  /**
   * Slot for the {@code value3} property.
   * @see #getValue3
   * @see #setValue3
   */
  public static final Property value3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value3} property.
   * @see #value3
   */
  public BHonStatusNumeric getValue3() { return (BHonStatusNumeric)get(value3); }

  /**
   * Set the {@code value3} property.
   * @see #value3
   */
  public void setValue3(BHonStatusNumeric v) { set(value3, v, null); }

  //endregion Property "value3"

  //region Property "value4"

  /**
   * Slot for the {@code value4} property.
   * @see #getValue4
   * @see #setValue4
   */
  public static final Property value4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value4} property.
   * @see #value4
   */
  public BHonStatusNumeric getValue4() { return (BHonStatusNumeric)get(value4); }

  /**
   * Set the {@code value4} property.
   * @see #value4
   */
  public void setValue4(BHonStatusNumeric v) { set(value4, v, null); }

  //endregion Property "value4"

  //region Property "value5"

  /**
   * Slot for the {@code value5} property.
   * @see #getValue5
   * @see #setValue5
   */
  public static final Property value5 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value5} property.
   * @see #value5
   */
  public BHonStatusNumeric getValue5() { return (BHonStatusNumeric)get(value5); }

  /**
   * Set the {@code value5} property.
   * @see #value5
   */
  public void setValue5(BHonStatusNumeric v) { set(value5, v, null); }

  //endregion Property "value5"

  //region Property "value6"

  /**
   * Slot for the {@code value6} property.
   * @see #getValue6
   * @see #setValue6
   */
  public static final Property value6 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value6} property.
   * @see #value6
   */
  public BHonStatusNumeric getValue6() { return (BHonStatusNumeric)get(value6); }

  /**
   * Set the {@code value6} property.
   * @see #value6
   */
  public void setValue6(BHonStatusNumeric v) { set(value6, v, null); }

  //endregion Property "value6"

  //region Property "value7"

  /**
   * Slot for the {@code value7} property.
   * @see #getValue7
   * @see #setValue7
   */
  public static final Property value7 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value7} property.
   * @see #value7
   */
  public BHonStatusNumeric getValue7() { return (BHonStatusNumeric)get(value7); }

  /**
   * Set the {@code value7} property.
   * @see #value7
   */
  public void setValue7(BHonStatusNumeric v) { set(value7, v, null); }

  //endregion Property "value7"

  //region Property "value8"

  /**
   * Slot for the {@code value8} property.
   * @see #getValue8
   * @see #setValue8
   */
  public static final Property value8 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value8} property.
   * @see #value8
   */
  public BHonStatusNumeric getValue8() { return (BHonStatusNumeric)get(value8); }

  /**
   * Set the {@code value8} property.
   * @see #value8
   */
  public void setValue8(BHonStatusNumeric v) { set(value8, v, null); }

  //endregion Property "value8"

  //region Property "value9"

  /**
   * Slot for the {@code value9} property.
   * @see #getValue9
   * @see #setValue9
   */
  public static final Property value9 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value9} property.
   * @see #value9
   */
  public BHonStatusNumeric getValue9() { return (BHonStatusNumeric)get(value9); }

  /**
   * Set the {@code value9} property.
   * @see #value9
   */
  public void setValue9(BHonStatusNumeric v) { set(value9, v, null); }

  //endregion Property "value9"

  //region Property "value10"

  /**
   * Slot for the {@code value10} property.
   * @see #getValue10
   * @see #setValue10
   */
  public static final Property value10 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value10} property.
   * @see #value10
   */
  public BHonStatusNumeric getValue10() { return (BHonStatusNumeric)get(value10); }

  /**
   * Set the {@code value10} property.
   * @see #value10
   */
  public void setValue10(BHonStatusNumeric v) { set(value10, v, null); }

  //endregion Property "value10"

  //region Property "value11"

  /**
   * Slot for the {@code value11} property.
   * @see #getValue11
   * @see #setValue11
   */
  public static final Property value11 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value11} property.
   * @see #value11
   */
  public BHonStatusNumeric getValue11() { return (BHonStatusNumeric)get(value11); }

  /**
   * Set the {@code value11} property.
   * @see #value11
   */
  public void setValue11(BHonStatusNumeric v) { set(value11, v, null); }

  //endregion Property "value11"

  //region Property "value12"

  /**
   * Slot for the {@code value12} property.
   * @see #getValue12
   * @see #setValue12
   */
  public static final Property value12 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value12} property.
   * @see #value12
   */
  public BHonStatusNumeric getValue12() { return (BHonStatusNumeric)get(value12); }

  /**
   * Set the {@code value12} property.
   * @see #value12
   */
  public void setValue12(BHonStatusNumeric v) { set(value12, v, null); }

  //endregion Property "value12"

  //region Property "value13"

  /**
   * Slot for the {@code value13} property.
   * @see #getValue13
   * @see #setValue13
   */
  public static final Property value13 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value13} property.
   * @see #value13
   */
  public BHonStatusNumeric getValue13() { return (BHonStatusNumeric)get(value13); }

  /**
   * Set the {@code value13} property.
   * @see #value13
   */
  public void setValue13(BHonStatusNumeric v) { set(value13, v, null); }

  //endregion Property "value13"

  //region Property "value14"

  /**
   * Slot for the {@code value14} property.
   * @see #getValue14
   * @see #setValue14
   */
  public static final Property value14 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value14} property.
   * @see #value14
   */
  public BHonStatusNumeric getValue14() { return (BHonStatusNumeric)get(value14); }

  /**
   * Set the {@code value14} property.
   * @see #value14
   */
  public void setValue14(BHonStatusNumeric v) { set(value14, v, null); }

  //endregion Property "value14"

  //region Property "value15"

  /**
   * Slot for the {@code value15} property.
   * @see #getValue15
   * @see #setValue15
   */
  public static final Property value15 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value15} property.
   * @see #value15
   */
  public BHonStatusNumeric getValue15() { return (BHonStatusNumeric)get(value15); }

  /**
   * Set the {@code value15} property.
   * @see #value15
   */
  public void setValue15(BHonStatusNumeric v) { set(value15, v, null); }

  //endregion Property "value15"

  //region Property "value16"

  /**
   * Slot for the {@code value16} property.
   * @see #getValue16
   * @see #setValue16
   */
  public static final Property value16 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value16} property.
   * @see #value16
   */
  public BHonStatusNumeric getValue16() { return (BHonStatusNumeric)get(value16); }

  /**
   * Set the {@code value16} property.
   * @see #value16
   */
  public void setValue16(BHonStatusNumeric v) { set(value16, v, null); }

  //endregion Property "value16"

  //region Property "value17"

  /**
   * Slot for the {@code value17} property.
   * @see #getValue17
   * @see #setValue17
   */
  public static final Property value17 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value17} property.
   * @see #value17
   */
  public BHonStatusNumeric getValue17() { return (BHonStatusNumeric)get(value17); }

  /**
   * Set the {@code value17} property.
   * @see #value17
   */
  public void setValue17(BHonStatusNumeric v) { set(value17, v, null); }

  //endregion Property "value17"

  //region Property "value18"

  /**
   * Slot for the {@code value18} property.
   * @see #getValue18
   * @see #setValue18
   */
  public static final Property value18 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value18} property.
   * @see #value18
   */
  public BHonStatusNumeric getValue18() { return (BHonStatusNumeric)get(value18); }

  /**
   * Set the {@code value18} property.
   * @see #value18
   */
  public void setValue18(BHonStatusNumeric v) { set(value18, v, null); }

  //endregion Property "value18"

  //region Property "value19"

  /**
   * Slot for the {@code value19} property.
   * @see #getValue19
   * @see #setValue19
   */
  public static final Property value19 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value19} property.
   * @see #value19
   */
  public BHonStatusNumeric getValue19() { return (BHonStatusNumeric)get(value19); }

  /**
   * Set the {@code value19} property.
   * @see #value19
   */
  public void setValue19(BHonStatusNumeric v) { set(value19, v, null); }

  //endregion Property "value19"

  //region Property "value20"

  /**
   * Slot for the {@code value20} property.
   * @see #getValue20
   * @see #setValue20
   */
  public static final Property value20 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value20} property.
   * @see #value20
   */
  public BHonStatusNumeric getValue20() { return (BHonStatusNumeric)get(value20); }

  /**
   * Set the {@code value20} property.
   * @see #value20
   */
  public void setValue20(BHonStatusNumeric v) { set(value20, v, null); }

  //endregion Property "value20"

  //region Property "value21"

  /**
   * Slot for the {@code value21} property.
   * @see #getValue21
   * @see #setValue21
   */
  public static final Property value21 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value21} property.
   * @see #value21
   */
  public BHonStatusNumeric getValue21() { return (BHonStatusNumeric)get(value21); }

  /**
   * Set the {@code value21} property.
   * @see #value21
   */
  public void setValue21(BHonStatusNumeric v) { set(value21, v, null); }

  //endregion Property "value21"

  //region Property "value22"

  /**
   * Slot for the {@code value22} property.
   * @see #getValue22
   * @see #setValue22
   */
  public static final Property value22 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value22} property.
   * @see #value22
   */
  public BHonStatusNumeric getValue22() { return (BHonStatusNumeric)get(value22); }

  /**
   * Set the {@code value22} property.
   * @see #value22
   */
  public void setValue22(BHonStatusNumeric v) { set(value22, v, null); }

  //endregion Property "value22"

  //region Property "value23"

  /**
   * Slot for the {@code value23} property.
   * @see #getValue23
   * @see #setValue23
   */
  public static final Property value23 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value23} property.
   * @see #value23
   */
  public BHonStatusNumeric getValue23() { return (BHonStatusNumeric)get(value23); }

  /**
   * Set the {@code value23} property.
   * @see #value23
   */
  public void setValue23(BHonStatusNumeric v) { set(value23, v, null); }

  //endregion Property "value23"

  //region Property "value24"

  /**
   * Slot for the {@code value24} property.
   * @see #getValue24
   * @see #setValue24
   */
  public static final Property value24 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value24} property.
   * @see #value24
   */
  public BHonStatusNumeric getValue24() { return (BHonStatusNumeric)get(value24); }

  /**
   * Set the {@code value24} property.
   * @see #value24
   */
  public void setValue24(BHonStatusNumeric v) { set(value24, v, null); }

  //endregion Property "value24"

  //region Property "value25"

  /**
   * Slot for the {@code value25} property.
   * @see #getValue25
   * @see #setValue25
   */
  public static final Property value25 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value25} property.
   * @see #value25
   */
  public BHonStatusNumeric getValue25() { return (BHonStatusNumeric)get(value25); }

  /**
   * Set the {@code value25} property.
   * @see #value25
   */
  public void setValue25(BHonStatusNumeric v) { set(value25, v, null); }

  //endregion Property "value25"

  //region Property "value26"

  /**
   * Slot for the {@code value26} property.
   * @see #getValue26
   * @see #setValue26
   */
  public static final Property value26 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value26} property.
   * @see #value26
   */
  public BHonStatusNumeric getValue26() { return (BHonStatusNumeric)get(value26); }

  /**
   * Set the {@code value26} property.
   * @see #value26
   */
  public void setValue26(BHonStatusNumeric v) { set(value26, v, null); }

  //endregion Property "value26"

  //region Property "value27"

  /**
   * Slot for the {@code value27} property.
   * @see #getValue27
   * @see #setValue27
   */
  public static final Property value27 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value27} property.
   * @see #value27
   */
  public BHonStatusNumeric getValue27() { return (BHonStatusNumeric)get(value27); }

  /**
   * Set the {@code value27} property.
   * @see #value27
   */
  public void setValue27(BHonStatusNumeric v) { set(value27, v, null); }

  //endregion Property "value27"

  //region Property "value28"

  /**
   * Slot for the {@code value28} property.
   * @see #getValue28
   * @see #setValue28
   */
  public static final Property value28 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value28} property.
   * @see #value28
   */
  public BHonStatusNumeric getValue28() { return (BHonStatusNumeric)get(value28); }

  /**
   * Set the {@code value28} property.
   * @see #value28
   */
  public void setValue28(BHonStatusNumeric v) { set(value28, v, null); }

  //endregion Property "value28"

  //region Property "value29"

  /**
   * Slot for the {@code value29} property.
   * @see #getValue29
   * @see #setValue29
   */
  public static final Property value29 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value29} property.
   * @see #value29
   */
  public BHonStatusNumeric getValue29() { return (BHonStatusNumeric)get(value29); }

  /**
   * Set the {@code value29} property.
   * @see #value29
   */
  public void setValue29(BHonStatusNumeric v) { set(value29, v, null); }

  //endregion Property "value29"

  //region Property "value30"

  /**
   * Slot for the {@code value30} property.
   * @see #getValue30
   * @see #setValue30
   */
  public static final Property value30 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value30} property.
   * @see #value30
   */
  public BHonStatusNumeric getValue30() { return (BHonStatusNumeric)get(value30); }

  /**
   * Set the {@code value30} property.
   * @see #value30
   */
  public void setValue30(BHonStatusNumeric v) { set(value30, v, null); }

  //endregion Property "value30"

  //region Property "value31"

  /**
   * Slot for the {@code value31} property.
   * @see #getValue31
   * @see #setValue31
   */
  public static final Property value31 = newProperty(0, new BHonStatusNumeric(0f, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code value31} property.
   * @see #value31
   */
  public BHonStatusNumeric getValue31() { return (BHonStatusNumeric)get(value31); }

  /**
   * Set the {@code value31} property.
   * @see #value31
   */
  public void setValue31(BHonStatusNumeric v) { set(value31, v, null); }

  //endregion Property "value31"

  //region Property "operation"

  /**
   * Slot for the {@code operation} property.
   * Configuration
   * @see #getOperation
   * @see #setOperation
   */
  public static final Property operation = newProperty(0, BDecisionOperationEnum.DEFAULT, null);

  /**
   * Get the {@code operation} property.
   * Configuration
   * @see #operation
   */
  public BDecisionOperationEnum getOperation() { return (BDecisionOperationEnum)get(operation); }

  /**
   * Set the {@code operation} property.
   * Configuration
   * @see #operation
   */
  public void setOperation(BDecisionOperationEnum v) { set(operation, v, null); }

  //endregion Property "operation"

  //region Property "numValues"

  /**
   * Slot for the {@code numValues} property.
   * @see #getNumValues
   * @see #setNumValues
   */
  public static final Property numValues = newProperty(0, 2, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 2), BFacets.make(BFacets.MAX, 31)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code numValues} property.
   * @see #numValues
   */
  public int getNumValues() { return getInt(numValues); }

  /**
   * Set the {@code numValues} property.
   * @see #numValues
   */
  public void setNumValues(int v) { setInt(numValues, v, null); }

  //endregion Property "numValues"

  //region Property "Y_OUTPUT"

  /**
   * Slot for the {@code Y_OUTPUT} property.
   * Outputs
   * @see #getY_OUTPUT
   * @see #setY_OUTPUT
   */
  public static final Property Y_OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code Y_OUTPUT} property.
   * Outputs
   * @see #Y_OUTPUT
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusBoolean getY_OUTPUT() { return (BHonStatusBoolean)get(Y_OUTPUT); }

  /**
   * Set the {@code Y_OUTPUT} property.
   * Outputs
   * @see #Y_OUTPUT
   */
  @SuppressWarnings("squid:S00100")
  public void setY_OUTPUT(BHonStatusBoolean v) { set(Y_OUTPUT, v, null); }

  //endregion Property "Y_OUTPUT"

  //region Property "N_OUTPUT"

  /**
   * Slot for the {@code N_OUTPUT} property.
   * @see #getN_OUTPUT
   * @see #setN_OUTPUT
   */
  public static final Property N_OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusBoolean(false, BStatus.ok), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code N_OUTPUT} property.
   * @see #N_OUTPUT
   */
  @SuppressWarnings("squid:S00100")
  public BHonStatusBoolean getN_OUTPUT() { return (BHonStatusBoolean)get(N_OUTPUT); }

  /**
   * Set the {@code N_OUTPUT} property.
   * @see #N_OUTPUT
   */
  @SuppressWarnings("squid:S00100")
  public void setN_OUTPUT(BHonStatusBoolean v) { set(N_OUTPUT, v, null); }

  //endregion Property "N_OUTPUT"

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "decision_box.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDecisionBox.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
  	@Override
  	public void changed(Property property, Context context) {
  		super.changed(property, context);
  		if (!Sys.atSteadyState()) {
			return;
  		}

  		if(property.getName().equalsIgnoreCase(numValues.getName())) {
  			showValueSlotsInWiresheet(getNumValuesAfterValidation());
  		}
  	}

	private void showValueSlotsInWiresheet(final int numValues) {
		BHonStatusNumeric[] child = this.getChildren(BHonStatusNumeric.class);
		for (int i = ALWAYS_VISIBLE_VALUE_SLOT_COUNT+1; i < child.length; i++) {
			int flags = this.getFlags(getSlot(child[i].getName()));
			if(i<=numValues) {
				//Set summary flag
				this.setFlags(getSlot(child[i].getName()), flags | Flags.SUMMARY);
			} else {
				//Unset summary flag
				flags &= ~Flags.SUMMARY; 
				this.setFlags(getSlot(child[i].getName()), flags);
			}
		}
	}
  
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		 if(!isConfigured(YorNinput) || !getYorNinput().getValue()) {
			setOutputsToFalse();
			return; 
		}
		
		calculateAndSetOutput();
	}
	
	
	private void calculateAndSetOutput() {
		double hysteresisValue = 0;
		if(isConfigured(hysteresis)) {
			hysteresisValue = limitInput(getHysteresis().getValue(), HYSTERESIS_MIN, HYSTERESIS_MAX, HYSTERESIS_MIN);
		}
		
		double dValue1 = getValueFromProperty(value1);
		double dValue2 = getValueFromProperty(value2);
		
		switch (getOperation().getOrdinal()) {
		case BDecisionOperationEnum.EQUALS:
			calculateAndSetOutputForEquals(dValue1, dValue2);
			break;
		case BDecisionOperationEnum.GREATER_THAN:
			calculateAndSetOutputForGreaterThan(dValue1, dValue2, hysteresisValue);
			break;
		case BDecisionOperationEnum.GREATER_THAN_OR_EQUAL_TO:
			calculateAndSetOutputForGreaterThanOrEqual(dValue1, dValue2, hysteresisValue);
			break;
		case BDecisionOperationEnum.LESS_THAN:
			calculateAndSetOutputForLessThan(dValue1, dValue2, hysteresisValue);
			break;
		case BDecisionOperationEnum.LESS_THAN_OR_EQUAL_TO:
			calculateAndSetOutputForLessThanOrEqual(dValue1, dValue2, hysteresisValue);
			break;
		case BDecisionOperationEnum.ANY_EQUAL:
			calculateAndSetOutputForAnyEqual(dValue1);
			break;
		case BDecisionOperationEnum.IN_ANY_RANGE:
			calculateAndSetOutputForInAnyRange(dValue1);
			break;
			///CLOVER:OFF
		default:
			setOutputsToFalse();
			break;
			///CLOVER:ON
		}
	}

	private void calculateAndSetOutputForEquals(final double value1, final double value2) {
		if(Double.compare(value1, value2) == 0) {
			setResultToOutputs(true);
		} else {
			setResultToOutputs(false);
		}
	}

	private void calculateAndSetOutputForLessThanOrEqual(final double value1, final double value2, final double hysteresisValue) {
		if(Double.compare(value1, value2) <= 0) {
			setResultToOutputs(true);
		} else if(Double.compare(value1, value2+hysteresisValue) > 0) {
			setResultToOutputs(false);
		}
	}

	private void calculateAndSetOutputForGreaterThanOrEqual(final double value1, final double value2, final double hysteresisValue) {
		if(Double.compare(value1, value2) >= 0) {
			setResultToOutputs(true);
		} else if(Double.compare(value1, value2-hysteresisValue) < 0) {
			setResultToOutputs(false);
		}
	}

	private void calculateAndSetOutputForGreaterThan(final double value1, final double value2, final double hysteresisValue) {
		if(Double.compare(value1, value2) > 0) {
			setResultToOutputs(true);
		} else if(Double.compare(value1, value2-hysteresisValue) <= 0) {
			setResultToOutputs(false);
		}
	}

	private void calculateAndSetOutputForLessThan(final double value1, final double value2, final double hysteresisValue) {
		if(Double.compare(value1, value2) < 0) {
			setResultToOutputs(true);
		} else if(Double.compare(value1, value2+hysteresisValue) >= 0) {
			setResultToOutputs(false);
		}
	}

	private int getNumValuesAfterValidation() {
		BFacets facets = this.getSlotFacets(numValues);
		float min = facets.getf(BFacets.MIN, NUMVALUES_MIN);
		float max = facets.getf(BFacets.MAX, NUMVALUES_MAX);
		return (int) limitInput(getNumValues(), min, max, min);
	}
	
	private void calculateAndSetOutputForInAnyRange(final double value1) {
		boolean decisionResult = false;
		BHonStatusNumeric[] child = this.getChildren(BHonStatusNumeric.class);
		child = Arrays.copyOfRange(child, VALUE2_POSITION, getNumValuesAfterValidation()+1);
		for (int i = 0; (i+1) < child.length; i+=IN_ANY_RANGE_INCREMENTER) {
			double lowRange = getValueFromProperty(getProperty(child[i].getName()));
			double highRange = getValueFromProperty(getProperty(child[i+1].getName()));
			if((value1>=lowRange) && (value1<=highRange)){
				decisionResult = true;
				break;
			}
		}
		
		setResultToOutputs(decisionResult);
	}

	private void calculateAndSetOutputForAnyEqual(final double value1) {
		boolean decisionResult = false;
		BHonStatusNumeric[] child = this.getChildren(BHonStatusNumeric.class);
		child = Arrays.copyOfRange(child, VALUE2_POSITION, getNumValuesAfterValidation()+1);
		for (int i = 0; i < child.length; i++) {
			if(Double.compare(value1, getValueFromProperty(getProperty(child[i].getName()))) == 0){
				decisionResult = true;
				break;
			}
		}
		
		setResultToOutputs(decisionResult);
	}

	private void setResultToOutputs(final boolean isDecisionSuccess) {
		getY_OUTPUT().setValue(isDecisionSuccess);
		getN_OUTPUT().setValue(!isDecisionSuccess);
	}
	
	private void setOutputsToFalse() {
		getY_OUTPUT().setValue(false);
		getN_OUTPUT().setValue(false);
	}
	
	private double getValueFromProperty(final Property property) {
		if(isSlotValueValid(property)) {
			return ((BHonStatusNumeric) get(property)).getValue();
		}
		
		return 0;
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(YorNinput);
		propertyArrayList.add(hysteresis);
		propertyArrayList.add(value1);
		propertyArrayList.add(value2);
		propertyArrayList.add(value3);
		propertyArrayList.add(value4);
		propertyArrayList.add(value5);
		propertyArrayList.add(value6);
		propertyArrayList.add(value7);
		propertyArrayList.add(value8);
		propertyArrayList.add(value9);
		propertyArrayList.add(value10);
		propertyArrayList.add(value11);
		propertyArrayList.add(value12);
		propertyArrayList.add(value13);
		propertyArrayList.add(value14);
		propertyArrayList.add(value15);
		propertyArrayList.add(value16);
		propertyArrayList.add(value17);
		propertyArrayList.add(value18);
		propertyArrayList.add(value19);
		propertyArrayList.add(value20);
		propertyArrayList.add(value21);
		propertyArrayList.add(value22);
		propertyArrayList.add(value23);
		propertyArrayList.add(value24);
		propertyArrayList.add(value25);
		propertyArrayList.add(value26);
		propertyArrayList.add(value27);
		propertyArrayList.add(value28);
		propertyArrayList.add(value29);
		propertyArrayList.add(value30);
		propertyArrayList.add(value31);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(N_OUTPUT);
		properties.add(Y_OUTPUT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(numValues);
		properties.add(operation);
		return properties;
	}

	private static final int NUMVALUES_MIN = 2;
	private static final int NUMVALUES_MAX = 31;
	private static final int HYSTERESIS_MIN = 0;
	private static final int HYSTERESIS_MAX = 65535;
	private static final int ALWAYS_VISIBLE_VALUE_SLOT_COUNT=5;
	private static final int VALUE2_POSITION = 2;
	private static final int IN_ANY_RANGE_INCREMENTER = 2;
}
