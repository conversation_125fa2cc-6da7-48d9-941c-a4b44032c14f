/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Select block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 *
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"select.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "x", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

//Since the expected slot name "default" is a Java keyword, changed the slot name to defaultVal 
@NiagaraProperty(name = "defaultVal", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })


@NiagaraProperty(name = "input0", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input5", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "offset", type = "int", defaultValue = "0",
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE| Flags.TRANSIENT,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "6"),
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })



@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})

public class BSelect extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BSelect(1261888900)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "select.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "x"

  /**
   * Slot for the {@code x} property.
   * @see #getX
   * @see #setX
   */
  public static final Property x = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code x} property.
   * @see #x
   */
  public BHonStatusNumeric getX() { return (BHonStatusNumeric)get(x); }

  /**
   * Set the {@code x} property.
   * @see #x
   */
  public void setX(BHonStatusNumeric v) { set(x, v, null); }

  //endregion Property "x"

  //region Property "defaultVal"

  /**
   * Slot for the {@code defaultVal} property.
   * Since the expected slot name "default" is a Java keyword, changed the slot name to defaultVal
   * @see #getDefaultVal
   * @see #setDefaultVal
   */
  public static final Property defaultVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code defaultVal} property.
   * Since the expected slot name "default" is a Java keyword, changed the slot name to defaultVal
   * @see #defaultVal
   */
  public BHonStatusNumeric getDefaultVal() { return (BHonStatusNumeric)get(defaultVal); }

  /**
   * Set the {@code defaultVal} property.
   * Since the expected slot name "default" is a Java keyword, changed the slot name to defaultVal
   * @see #defaultVal
   */
  public void setDefaultVal(BHonStatusNumeric v) { set(defaultVal, v, null); }

  //endregion Property "defaultVal"

  //region Property "input0"

  /**
   * Slot for the {@code input0} property.
   * @see #getInput0
   * @see #setInput0
   */
  public static final Property input0 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input0} property.
   * @see #input0
   */
  public BHonStatusNumeric getInput0() { return (BHonStatusNumeric)get(input0); }

  /**
   * Set the {@code input0} property.
   * @see #input0
   */
  public void setInput0(BHonStatusNumeric v) { set(input0, v, null); }

  //endregion Property "input0"

  //region Property "input1"

  /**
   * Slot for the {@code input1} property.
   * @see #getInput1
   * @see #setInput1
   */
  public static final Property input1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input1} property.
   * @see #input1
   */
  public BHonStatusNumeric getInput1() { return (BHonStatusNumeric)get(input1); }

  /**
   * Set the {@code input1} property.
   * @see #input1
   */
  public void setInput1(BHonStatusNumeric v) { set(input1, v, null); }

  //endregion Property "input1"

  //region Property "input2"

  /**
   * Slot for the {@code input2} property.
   * @see #getInput2
   * @see #setInput2
   */
  public static final Property input2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input2} property.
   * @see #input2
   */
  public BHonStatusNumeric getInput2() { return (BHonStatusNumeric)get(input2); }

  /**
   * Set the {@code input2} property.
   * @see #input2
   */
  public void setInput2(BHonStatusNumeric v) { set(input2, v, null); }

  //endregion Property "input2"

  //region Property "input3"

  /**
   * Slot for the {@code input3} property.
   * @see #getInput3
   * @see #setInput3
   */
  public static final Property input3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input3} property.
   * @see #input3
   */
  public BHonStatusNumeric getInput3() { return (BHonStatusNumeric)get(input3); }

  /**
   * Set the {@code input3} property.
   * @see #input3
   */
  public void setInput3(BHonStatusNumeric v) { set(input3, v, null); }

  //endregion Property "input3"

  //region Property "input4"

  /**
   * Slot for the {@code input4} property.
   * @see #getInput4
   * @see #setInput4
   */
  public static final Property input4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input4} property.
   * @see #input4
   */
  public BHonStatusNumeric getInput4() { return (BHonStatusNumeric)get(input4); }

  /**
   * Set the {@code input4} property.
   * @see #input4
   */
  public void setInput4(BHonStatusNumeric v) { set(input4, v, null); }

  //endregion Property "input4"

  //region Property "input5"

  /**
   * Slot for the {@code input5} property.
   * @see #getInput5
   * @see #setInput5
   */
  public static final Property input5 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input5} property.
   * @see #input5
   */
  public BHonStatusNumeric getInput5() { return (BHonStatusNumeric)get(input5); }

  /**
   * Set the {@code input5} property.
   * @see #input5
   */
  public void setInput5(BHonStatusNumeric v) { set(input5, v, null); }

  //endregion Property "input5"

  //region Property "offset"

  /**
   * Slot for the {@code offset} property.
   * @see #getOffset
   * @see #setOffset
   */
  public static final Property offset = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code offset} property.
   * @see #offset
   */
  public int getOffset() { return getInt(offset); }

  /**
   * Set the {@code offset} property.
   * @see #offset
   */
  public void setOffset(int v) { setInt(offset, v, null); }

  //endregion Property "offset"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSelect.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

@Override
public List<Property> getInputPropertiesList() {
	List<Property> propertyArrayList = super.getInputPropertiesList();
	propertyArrayList.add(x);
	propertyArrayList.add(defaultVal);
	propertyArrayList.add(input0);
	propertyArrayList.add(input1);
	propertyArrayList.add(input2);
	propertyArrayList.add(input3);
	propertyArrayList.add(input4);
	propertyArrayList.add(input5);
	return propertyArrayList;
}

	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList =  super.getOutputPropertiesList();
		propertyArrayList.add(OUTPUT);
		return propertyArrayList;
	}

	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList =  super.getConfigPropertiesList();
		propertyArrayList.add(offset);
		return propertyArrayList;
	}

/* (non-Javadoc)
 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
 */
@Override
public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	super.executeHoneywellComponent(executionParams);
	
	int selValue = Integer.MAX_VALUE;
	double selectOutVal;
	
	if(isSlotValueValid(x)) {
		selValue = (int)getX().getValue()-getOffset();
	}
	
	switch(selValue) {
		case INPUT_0:
			selectOutVal = getInValue(input0);
			break;
		
		case INPUT_1:
			selectOutVal = getInValue(input1);
			break;
			
		case INPUT_2:
			selectOutVal = getInValue(input2);
			break;
			
		case INPUT_3:
			selectOutVal = getInValue(input3);
			break;
			
		case INPUT_4:
			selectOutVal = getInValue(input4);
			break;
			
		case INPUT_5:
			selectOutVal = getInValue(input5);
			break;
			
		default:
			selectOutVal = getInValue(defaultVal);
			break;
			
	}
	
	getOUTPUT().setValue(selectOutVal);
}


double getInValue(Property property) {
	  double val = Double.POSITIVE_INFINITY;
	  if(isSlotValueValid(property)) {
		  BHonStatusNumeric bval = (BHonStatusNumeric) get(property);
		  val = bval.getValue();
	  }  
	  return val;	  
}

private static final int INPUT_0 = 0;
private static final int INPUT_1 = 1;
private static final int INPUT_2 = 2;
private static final int INPUT_3 = 3;
private static final int INPUT_4 = 4;
private static final int INPUT_5 = 5;

  
}
