/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of BMaximum block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 *
 */

@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"select.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@NiagaraProperty(name = "input", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(255, BStatus.nullStatus)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })


@NiagaraProperty(name = "disable", type = "BFiniteStatusBoolean", defaultValue = "new BNegatableFiniteStatusBoolean(false, BStatus.ok, false)", flags = Flags.SUMMARY)

@NiagaraProperty(name = "in1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in5", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in6", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in7", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in8", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "in9", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out5", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out6", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out7", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out8", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "out9", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0") })

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
	facets = { 
		@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
		@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
		@Facet(name = "BFacets.PRECISION", value = "0"),
    @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
    @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE")})

@NiagaraProperty(name = "FIRE", type = "BHonStatusBoolean", defaultValue = "new BNegatableStatusBoolean(false, BStatus.ok, false)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT,
  facets = {
    @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
    @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE") })

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213","squid:S1845", "squid:S00103", "squid:S1125"})


public class BEncode extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BEncode(1900812865)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "select.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "input"

  /**
   * Slot for the {@code input} property.
   * @see #getInput
   * @see #setInput
   */
  public static final Property input = newProperty(Flags.SUMMARY, new BHonStatusNumeric(255, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code input} property.
   * @see #input
   */
  public BHonStatusNumeric getInput() { return (BHonStatusNumeric)get(input); }

  /**
   * Set the {@code input} property.
   * @see #input
   */
  public void setInput(BHonStatusNumeric v) { set(input, v, null); }

  //endregion Property "input"

  //region Property "disable"

  /**
   * Slot for the {@code disable} property.
   * @see #getDisable
   * @see #setDisable
   */
  public static final Property disable = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.ok, false), null);

  /**
   * Get the {@code disable} property.
   * @see #disable
   */
  public BFiniteStatusBoolean getDisable() { return (BFiniteStatusBoolean)get(disable); }

  /**
   * Set the {@code disable} property.
   * @see #disable
   */
  public void setDisable(BFiniteStatusBoolean v) { set(disable, v, null); }

  //endregion Property "disable"

  //region Property "in1"

  /**
   * Slot for the {@code in1} property.
   * @see #getIn1
   * @see #setIn1
   */
  public static final Property in1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in1} property.
   * @see #in1
   */
  public BHonStatusNumeric getIn1() { return (BHonStatusNumeric)get(in1); }

  /**
   * Set the {@code in1} property.
   * @see #in1
   */
  public void setIn1(BHonStatusNumeric v) { set(in1, v, null); }

  //endregion Property "in1"

  //region Property "in2"

  /**
   * Slot for the {@code in2} property.
   * @see #getIn2
   * @see #setIn2
   */
  public static final Property in2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in2} property.
   * @see #in2
   */
  public BHonStatusNumeric getIn2() { return (BHonStatusNumeric)get(in2); }

  /**
   * Set the {@code in2} property.
   * @see #in2
   */
  public void setIn2(BHonStatusNumeric v) { set(in2, v, null); }

  //endregion Property "in2"

  //region Property "in3"

  /**
   * Slot for the {@code in3} property.
   * @see #getIn3
   * @see #setIn3
   */
  public static final Property in3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in3} property.
   * @see #in3
   */
  public BHonStatusNumeric getIn3() { return (BHonStatusNumeric)get(in3); }

  /**
   * Set the {@code in3} property.
   * @see #in3
   */
  public void setIn3(BHonStatusNumeric v) { set(in3, v, null); }

  //endregion Property "in3"

  //region Property "in4"

  /**
   * Slot for the {@code in4} property.
   * @see #getIn4
   * @see #setIn4
   */
  public static final Property in4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in4} property.
   * @see #in4
   */
  public BHonStatusNumeric getIn4() { return (BHonStatusNumeric)get(in4); }

  /**
   * Set the {@code in4} property.
   * @see #in4
   */
  public void setIn4(BHonStatusNumeric v) { set(in4, v, null); }

  //endregion Property "in4"

  //region Property "in5"

  /**
   * Slot for the {@code in5} property.
   * @see #getIn5
   * @see #setIn5
   */
  public static final Property in5 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in5} property.
   * @see #in5
   */
  public BHonStatusNumeric getIn5() { return (BHonStatusNumeric)get(in5); }

  /**
   * Set the {@code in5} property.
   * @see #in5
   */
  public void setIn5(BHonStatusNumeric v) { set(in5, v, null); }

  //endregion Property "in5"

  //region Property "in6"

  /**
   * Slot for the {@code in6} property.
   * @see #getIn6
   * @see #setIn6
   */
  public static final Property in6 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in6} property.
   * @see #in6
   */
  public BHonStatusNumeric getIn6() { return (BHonStatusNumeric)get(in6); }

  /**
   * Set the {@code in6} property.
   * @see #in6
   */
  public void setIn6(BHonStatusNumeric v) { set(in6, v, null); }

  //endregion Property "in6"

  //region Property "in7"

  /**
   * Slot for the {@code in7} property.
   * @see #getIn7
   * @see #setIn7
   */
  public static final Property in7 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in7} property.
   * @see #in7
   */
  public BHonStatusNumeric getIn7() { return (BHonStatusNumeric)get(in7); }

  /**
   * Set the {@code in7} property.
   * @see #in7
   */
  public void setIn7(BHonStatusNumeric v) { set(in7, v, null); }

  //endregion Property "in7"

  //region Property "in8"

  /**
   * Slot for the {@code in8} property.
   * @see #getIn8
   * @see #setIn8
   */
  public static final Property in8 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in8} property.
   * @see #in8
   */
  public BHonStatusNumeric getIn8() { return (BHonStatusNumeric)get(in8); }

  /**
   * Set the {@code in8} property.
   * @see #in8
   */
  public void setIn8(BHonStatusNumeric v) { set(in8, v, null); }

  //endregion Property "in8"

  //region Property "in9"

  /**
   * Slot for the {@code in9} property.
   * @see #getIn9
   * @see #setIn9
   */
  public static final Property in9 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code in9} property.
   * @see #in9
   */
  public BHonStatusNumeric getIn9() { return (BHonStatusNumeric)get(in9); }

  /**
   * Set the {@code in9} property.
   * @see #in9
   */
  public void setIn9(BHonStatusNumeric v) { set(in9, v, null); }

  //endregion Property "in9"

  //region Property "out1"

  /**
   * Slot for the {@code out1} property.
   * @see #getOut1
   * @see #setOut1
   */
  public static final Property out1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out1} property.
   * @see #out1
   */
  public BHonStatusNumeric getOut1() { return (BHonStatusNumeric)get(out1); }

  /**
   * Set the {@code out1} property.
   * @see #out1
   */
  public void setOut1(BHonStatusNumeric v) { set(out1, v, null); }

  //endregion Property "out1"

  //region Property "out2"

  /**
   * Slot for the {@code out2} property.
   * @see #getOut2
   * @see #setOut2
   */
  public static final Property out2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out2} property.
   * @see #out2
   */
  public BHonStatusNumeric getOut2() { return (BHonStatusNumeric)get(out2); }

  /**
   * Set the {@code out2} property.
   * @see #out2
   */
  public void setOut2(BHonStatusNumeric v) { set(out2, v, null); }

  //endregion Property "out2"

  //region Property "out3"

  /**
   * Slot for the {@code out3} property.
   * @see #getOut3
   * @see #setOut3
   */
  public static final Property out3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out3} property.
   * @see #out3
   */
  public BHonStatusNumeric getOut3() { return (BHonStatusNumeric)get(out3); }

  /**
   * Set the {@code out3} property.
   * @see #out3
   */
  public void setOut3(BHonStatusNumeric v) { set(out3, v, null); }

  //endregion Property "out3"

  //region Property "out4"

  /**
   * Slot for the {@code out4} property.
   * @see #getOut4
   * @see #setOut4
   */
  public static final Property out4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out4} property.
   * @see #out4
   */
  public BHonStatusNumeric getOut4() { return (BHonStatusNumeric)get(out4); }

  /**
   * Set the {@code out4} property.
   * @see #out4
   */
  public void setOut4(BHonStatusNumeric v) { set(out4, v, null); }

  //endregion Property "out4"

  //region Property "out5"

  /**
   * Slot for the {@code out5} property.
   * @see #getOut5
   * @see #setOut5
   */
  public static final Property out5 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out5} property.
   * @see #out5
   */
  public BHonStatusNumeric getOut5() { return (BHonStatusNumeric)get(out5); }

  /**
   * Set the {@code out5} property.
   * @see #out5
   */
  public void setOut5(BHonStatusNumeric v) { set(out5, v, null); }

  //endregion Property "out5"

  //region Property "out6"

  /**
   * Slot for the {@code out6} property.
   * @see #getOut6
   * @see #setOut6
   */
  public static final Property out6 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out6} property.
   * @see #out6
   */
  public BHonStatusNumeric getOut6() { return (BHonStatusNumeric)get(out6); }

  /**
   * Set the {@code out6} property.
   * @see #out6
   */
  public void setOut6(BHonStatusNumeric v) { set(out6, v, null); }

  //endregion Property "out6"

  //region Property "out7"

  /**
   * Slot for the {@code out7} property.
   * @see #getOut7
   * @see #setOut7
   */
  public static final Property out7 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out7} property.
   * @see #out7
   */
  public BHonStatusNumeric getOut7() { return (BHonStatusNumeric)get(out7); }

  /**
   * Set the {@code out7} property.
   * @see #out7
   */
  public void setOut7(BHonStatusNumeric v) { set(out7, v, null); }

  //endregion Property "out7"

  //region Property "out8"

  /**
   * Slot for the {@code out8} property.
   * @see #getOut8
   * @see #setOut8
   */
  public static final Property out8 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out8} property.
   * @see #out8
   */
  public BHonStatusNumeric getOut8() { return (BHonStatusNumeric)get(out8); }

  /**
   * Set the {@code out8} property.
   * @see #out8
   */
  public void setOut8(BHonStatusNumeric v) { set(out8, v, null); }

  //endregion Property "out8"

  //region Property "out9"

  /**
   * Slot for the {@code out9} property.
   * @see #getOut9
   * @see #setOut9
   */
  public static final Property out9 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code out9} property.
   * @see #out9
   */
  public BHonStatusNumeric getOut9() { return (BHonStatusNumeric)get(out9); }

  /**
   * Set the {@code out9} property.
   * @see #out9
   */
  public void setOut9(BHonStatusNumeric v) { set(out9, v, null); }

  //endregion Property "out9"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "FIRE"

  /**
   * Slot for the {@code FIRE} property.
   * @see #getFIRE
   * @see #setFIRE
   */
  public static final Property FIRE = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code FIRE} property.
   * @see #FIRE
   */
  public BHonStatusBoolean getFIRE() { return (BHonStatusBoolean)get(FIRE); }

  /**
   * Set the {@code FIRE} property.
   * @see #FIRE
   */
  public void setFIRE(BHonStatusBoolean v) { set(FIRE, v, null); }

  //endregion Property "FIRE"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEncode.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
  @Override
  public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	  super.executeHoneywellComponent(executionParams);
	  
	  int inputVal = (int)getInputValAfterValidate();
		if (isDisbleEncode()) {
			getOUTPUT().setValue(inputVal);
			getFIRE().setValue(((BNegatableStatusBoolean)getFIRE()).getNegate()?false:true);
			
		} else {
			computeAndSetOutput(inputVal);
		}
  }

  /**
   * @param inputVal
   */
  private void computeAndSetOutput(int inputVal) {
	  int outVal;
	  boolean fireVal;

	  int in1Value = getInSlotsVal(in1);
	  int in2Value = getInSlotsVal(in2);
	  int in3Value = getInSlotsVal(in3);
	  int in4Value = getInSlotsVal(in4);
	  int in5Value = getInSlotsVal(in5);
	  int in6Value = getInSlotsVal(in6);
	  int in7Value = getInSlotsVal(in7);
	  int in8Value = getInSlotsVal(in8);
	  int in9Value = getInSlotsVal(in9);
	  
	  int out1Value = getInSlotsVal(out1);
	  int out2Value = getInSlotsVal(out2);
	  int out3Value = getInSlotsVal(out3); 
	  int out4Value = getInSlotsVal(out4);
	  int out5Value = getInSlotsVal(out5);
	  int out6Value = getInSlotsVal(out6);
	  int out7Value = getInSlotsVal(out7);
	  int out8Value = getInSlotsVal(out8);
	  int out9Value = getInSlotsVal(out9);


	  outVal = inputVal;
	  fireVal=false;

	  if(inputVal==in1Value) {
		  outVal = out1Value;
		  fireVal=true;
	  }else if(inputVal==in2Value) {
		  outVal = out2Value;
		  fireVal=true;
	  }else if(inputVal==in3Value) {
		  outVal = out3Value;
		  fireVal=true;
	  }else if(inputVal==in4Value) {
		  outVal = out4Value;
		  fireVal=true;
	  }else if(inputVal==in5Value) {
		  outVal = out5Value;
		  fireVal=true;
	  }else if(inputVal==in6Value) {
		  outVal = out6Value;
		  fireVal=true;
	  }else if(inputVal==in7Value) {
		  outVal = out7Value;
		  fireVal=true;
	  }else if(inputVal==in8Value) {
		  outVal = out8Value;
		  fireVal=true;
	  }else if(inputVal==in9Value) {
		  outVal = out9Value;
		  fireVal=true;
	  }

	  setOUTPUTandFire(outVal, fireVal);
  }

private void setOUTPUTandFire(int outVal, boolean fireVal) {
	getOUTPUT().setValue(outVal);
		if (((BNegatableStatusBoolean)getFIRE()).getNegate()) {
			getFIRE().setValue(!fireVal);
		} else
			getFIRE().setValue(fireVal);
}
  
  private int getInSlotsVal(Property property){
	  BHonStatusNumeric slot = (BHonStatusNumeric) get(property);
	  return isConfigured(property) ? (int)slot.getValue() : 0;	  
  }
  
  @Override
  public List<Property> getInputPropertiesList() {
	  List<Property> propertyArrayList = super.getInputPropertiesList();
	  propertyArrayList.add(input);
	  propertyArrayList.add(disable);
	  return propertyArrayList;
  }
  
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(FIRE);
		properties.add(OUTPUT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(in1);
		properties.add(in2);
		properties.add(in3);
		properties.add(in4);
		properties.add(in5);
		properties.add(in6);
		properties.add(in7);
		properties.add(in8);
		properties.add(in9);
		properties.add(out1);
		properties.add(out2);
		properties.add(out3);
		properties.add(out4);
		properties.add(out5);
		properties.add(out6);
		properties.add(out7);
		properties.add(out8);
		properties.add(out9);
		return properties;
	}
  
  private boolean isDisbleEncode() {
	  boolean disableEncode = getDisable().getValue();
	  if(!isConfigured(disable)) {
		  disableEncode = false;
	  }else if(((BNegatableFiniteStatusBoolean)getDisable()).getNegate()) {
		  disableEncode = !disableEncode;
	  }
	  return disableEncode;
  }
  
  private double getInputValAfterValidate() {
	  double inputVal = getInput().getValue();
	  //Base class isSlotValueValid() cannot be used in this case , this is because in case the value is a negative value(lets say -5)
	  //the isInvalidValue() method returns true and the inputVal will be assigned to 255
	  //But as per SDS, the value should be set to 0
	  if(!isConfigured(input) || Double.isNaN(inputVal))
		  inputVal = DEF_INPUT_VAL;
	  
	  inputVal = limitInput(inputVal, 0, DEF_INPUT_VAL, DEF_INPUT_VAL);
	  return inputVal;
  }
  
  
  @Override
	public void started() throws Exception {
		super.started();
		updateNegateOnBooleanSlots();
	}
  
  private void updateNegateOnBooleanSlots() {
	  BFiniteStatusBoolean disable2 = getDisable();
	  if(!(disable2 instanceof BNegatableFiniteStatusBoolean)) {
		  BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
		  negatableFiniteStatusBoolean.setStatus(disable2.getStatus());
		  negatableFiniteStatusBoolean.setValue(disable2.getValue());
		  setDisable(negatableFiniteStatusBoolean);
	  }
	  
	  BHonStatusBoolean temp = getFIRE();
	  if(!(temp instanceof BNegatableStatusBoolean)) {
		  BNegatableStatusBoolean negatableStatusBoolean = new BNegatableStatusBoolean();
		  negatableStatusBoolean.setStatus(temp.getStatus());
		  negatableStatusBoolean.setValue(temp.getValue());
		  setFIRE(negatableStatusBoolean);
	  }
  }

  private static final int DEF_INPUT_VAL = 255;
}
