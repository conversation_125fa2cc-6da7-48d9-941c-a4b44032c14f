/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of Decision block implementation
 * This ENUM is used for 'operation' configuration used in Decision block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON>nan
 * @since Dec 29, 2017
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range(value="Equals", ordinal=1),
		@Range(value="GreaterThan", ordinal=2),
		@Range(value="GreaterThanOrEqualTo", ordinal=3),
		@Range(value="LessThan", ordinal=4),
		@Range(value="LessThanOrEqualTo", ordinal=5),
		@Range(value="AnyEqual", ordinal=6),
		@Range(value="InAnyRange", ordinal=7)
		}, defaultValue = "Equals")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class BDecisionOperationEnum extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BDecisionOperationEnum(3768461111)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for Equals. */
  public static final int EQUALS = 1;
  /** Ordinal value for GreaterThan. */
  public static final int GREATER_THAN = 2;
  /** Ordinal value for GreaterThanOrEqualTo. */
  public static final int GREATER_THAN_OR_EQUAL_TO = 3;
  /** Ordinal value for LessThan. */
  public static final int LESS_THAN = 4;
  /** Ordinal value for LessThanOrEqualTo. */
  public static final int LESS_THAN_OR_EQUAL_TO = 5;
  /** Ordinal value for AnyEqual. */
  public static final int ANY_EQUAL = 6;
  /** Ordinal value for InAnyRange. */
  public static final int IN_ANY_RANGE = 7;

  /** BDecisionOperationEnum constant for Equals. */
  public static final BDecisionOperationEnum Equals = new BDecisionOperationEnum(EQUALS);
  /** BDecisionOperationEnum constant for GreaterThan. */
  public static final BDecisionOperationEnum GreaterThan = new BDecisionOperationEnum(GREATER_THAN);
  /** BDecisionOperationEnum constant for GreaterThanOrEqualTo. */
  public static final BDecisionOperationEnum GreaterThanOrEqualTo = new BDecisionOperationEnum(GREATER_THAN_OR_EQUAL_TO);
  /** BDecisionOperationEnum constant for LessThan. */
  public static final BDecisionOperationEnum LessThan = new BDecisionOperationEnum(LESS_THAN);
  /** BDecisionOperationEnum constant for LessThanOrEqualTo. */
  public static final BDecisionOperationEnum LessThanOrEqualTo = new BDecisionOperationEnum(LESS_THAN_OR_EQUAL_TO);
  /** BDecisionOperationEnum constant for AnyEqual. */
  public static final BDecisionOperationEnum AnyEqual = new BDecisionOperationEnum(ANY_EQUAL);
  /** BDecisionOperationEnum constant for InAnyRange. */
  public static final BDecisionOperationEnum InAnyRange = new BDecisionOperationEnum(IN_ANY_RANGE);

  /** Factory method with ordinal. */
  public static BDecisionOperationEnum make(int ordinal)
  {
    return (BDecisionOperationEnum)Equals.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BDecisionOperationEnum make(String tag)
  {
    return (BDecisionOperationEnum)Equals.getRange().get(tag);
  }

  /** Private constructor. */
  private BDecisionOperationEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BDecisionOperationEnum DEFAULT = Equals;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDecisionOperationEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
