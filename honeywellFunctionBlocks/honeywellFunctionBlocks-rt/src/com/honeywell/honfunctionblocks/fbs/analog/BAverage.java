/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;


import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;

/**
 * Implementation of BMaximum block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON><PERSON>
 * @since Dec 28, 2017
 */
@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"average.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BAverage extends BMinMaxAverageBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BAverage(2101530414)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "average.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAverage.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default Constructor
	 */
	public BAverage() {
		//EMPTY IMPLEMENTATION
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		if(!getIgnoreInvalidInput() && hasAnyInvalidInput()){
			getOUTPUT().setValue(Double.POSITIVE_INFINITY);
			return;
		}
		
		double result = calculateAverage();
		
		getOUTPUT().setValue(result);
	}

	/**
	 * Get average value among the given inputs
	 * @return
	 */
	private double calculateAverage() {
		double sum = 0;
		int validInputs = 0;
		
		//Use only valid values
		BStatusNumeric[] inputs = this.getChildren(BStatusNumeric.class);
		for (int i = 0; i < inputs.length; i++) {
			if(inputs[i].getName().startsWith("in") && isSlotValueValid(getProperty(inputs[i].getName()))) {
				sum += inputs[i].getValue();
				validInputs++;
			}
		}
		
		if(validInputs <=0)
			return Double.POSITIVE_INFINITY;
		
		return sum/validInputs;
	}
}
