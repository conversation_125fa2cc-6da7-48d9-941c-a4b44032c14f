/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.enums.BCompareEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BCompareStatusEnum;
import com.honeywell.versionmanager.BHonVersion;

/**
 * Implementation of Compare block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 *
 */
@NiagaraType
@NiagaraProperty(name = "icon", type = "baja:Icon", defaultValue = "BIcon.make(ResourceConstants.ICON_DIR + \"compare.png\")", flags = Flags.HIDDEN | Flags.READONLY)
@NiagaraProperty(name = "input1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "input2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "onHyst", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "offHyst", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0, BStatus.ok)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name="operation", type="BCompareOperationEnum", defaultValue="BCompareOperationEnum.DEFAULT")
@NiagaraProperty(name="negate", type="boolean", defaultValue="false")
@NiagaraProperty(name="outSave", type="boolean", defaultValue="false")

@NiagaraProperty(name="OUTPUT", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
		@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_AX_FE"),
		@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_UX_FE")
})
@NiagaraProperty(name="OUTPUT_ENUM", type="BCompareStatusEnum", defaultValue="new BCompareStatusEnum(BCompareEnum.Null, BStatus.ok)", flags=Flags.SUMMARY|Flags.READONLY|Flags.TRANSIENT|Flags.DEFAULT_ON_CLONE)



@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103", "squid:S1125", "squid:S00100", "squid:S3358" })

public class BCompare extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BCompare(4279554301)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "compare.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "input1"

  /**
   * Slot for the {@code input1} property.
   * @see #getInput1
   * @see #setInput1
   */
  public static final Property input1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input1} property.
   * @see #input1
   */
  public BHonStatusNumeric getInput1() { return (BHonStatusNumeric)get(input1); }

  /**
   * Set the {@code input1} property.
   * @see #input1
   */
  public void setInput1(BHonStatusNumeric v) { set(input1, v, null); }

  //endregion Property "input1"

  //region Property "input2"

  /**
   * Slot for the {@code input2} property.
   * @see #getInput2
   * @see #setInput2
   */
  public static final Property input2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code input2} property.
   * @see #input2
   */
  public BHonStatusNumeric getInput2() { return (BHonStatusNumeric)get(input2); }

  /**
   * Set the {@code input2} property.
   * @see #input2
   */
  public void setInput2(BHonStatusNumeric v) { set(input2, v, null); }

  //endregion Property "input2"

  //region Property "onHyst"

  /**
   * Slot for the {@code onHyst} property.
   * @see #getOnHyst
   * @see #setOnHyst
   */
  public static final Property onHyst = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code onHyst} property.
   * @see #onHyst
   */
  public BHonStatusNumeric getOnHyst() { return (BHonStatusNumeric)get(onHyst); }

  /**
   * Set the {@code onHyst} property.
   * @see #onHyst
   */
  public void setOnHyst(BHonStatusNumeric v) { set(onHyst, v, null); }

  //endregion Property "onHyst"

  //region Property "offHyst"

  /**
   * Slot for the {@code offHyst} property.
   * @see #getOffHyst
   * @see #setOffHyst
   */
  public static final Property offHyst = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code offHyst} property.
   * @see #offHyst
   */
  public BHonStatusNumeric getOffHyst() { return (BHonStatusNumeric)get(offHyst); }

  /**
   * Set the {@code offHyst} property.
   * @see #offHyst
   */
  public void setOffHyst(BHonStatusNumeric v) { set(offHyst, v, null); }

  //endregion Property "offHyst"

  //region Property "operation"

  /**
   * Slot for the {@code operation} property.
   * @see #getOperation
   * @see #setOperation
   */
  public static final Property operation = newProperty(0, BCompareOperationEnum.DEFAULT, null);

  /**
   * Get the {@code operation} property.
   * @see #operation
   */
  public BCompareOperationEnum getOperation() { return (BCompareOperationEnum)get(operation); }

  /**
   * Set the {@code operation} property.
   * @see #operation
   */
  public void setOperation(BCompareOperationEnum v) { set(operation, v, null); }

  //endregion Property "operation"

  //region Property "negate"

  /**
   * Slot for the {@code negate} property.
   * @see #getNegate
   * @see #setNegate
   */
  public static final Property negate = newProperty(0, false, null);

  /**
   * Get the {@code negate} property.
   * @see #negate
   */
  public boolean getNegate() { return getBoolean(negate); }

  /**
   * Set the {@code negate} property.
   * @see #negate
   */
  public void setNegate(boolean v) { setBoolean(negate, v, null); }

  //endregion Property "negate"

  //region Property "outSave"

  /**
   * Slot for the {@code outSave} property.
   * @see #getOutSave
   * @see #setOutSave
   */
  public static final Property outSave = newProperty(0, false, null);

  /**
   * Get the {@code outSave} property.
   * @see #outSave
   */
  public boolean getOutSave() { return getBoolean(outSave); }

  /**
   * Set the {@code outSave} property.
   * @see #outSave
   */
  public void setOutSave(boolean v) { setBoolean(outSave, v, null); }

  //endregion Property "outSave"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BNegatableStatusBoolean getOUTPUT() { return (BNegatableStatusBoolean)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BNegatableStatusBoolean v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "OUTPUT_ENUM"

  /**
   * Slot for the {@code OUTPUT_ENUM} property.
   * @see #getOUTPUT_ENUM
   * @see #setOUTPUT_ENUM
   */
  public static final Property OUTPUT_ENUM = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.TRANSIENT | Flags.DEFAULT_ON_CLONE, new BCompareStatusEnum(BCompareEnum.Null, BStatus.ok), null);

  /**
   * Get the {@code OUTPUT_ENUM} property.
   * @see #OUTPUT_ENUM
   */
  public BCompareStatusEnum getOUTPUT_ENUM() { return (BCompareStatusEnum)get(OUTPUT_ENUM); }

  /**
   * Set the {@code OUTPUT_ENUM} property.
   * @see #OUTPUT_ENUM
   */
  public void setOUTPUT_ENUM(BCompareStatusEnum v) { set(OUTPUT_ENUM, v, null); }

  //endregion Property "OUTPUT_ENUM"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompare.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  /* (non-Javadoc)
   * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
   */
  @Override
  public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	  super.executeHoneywellComponent(executionParams);
	  
  	if(!isSlotValueValid(input1) || !isSlotValueValid(input2)) {
  		getOUTPUT().setValue(false);
  		getOUTPUT_ENUM().setValue(BCompareEnum.False);
  		return;		
  	}
  	
  	Boolean desiredOut = false;
  	int compareOperationOrdinal = getOperation().getOrdinal();
  	
  	switch (compareOperationOrdinal) {
	case BCompareOperationEnum.EQUALS:
		desiredOut = computeEqualsOperation();
		break;
	case BCompareOperationEnum.LESS_THAN:
		desiredOut = computeLessThanOperation();
		break;
	case BCompareOperationEnum.GREATER_THAN:
		desiredOut = computeGreaterThanOperation();
		break;
		///CLOVER:OFF
	default:
		break;
		///CLOVER:ON
	}
  	
  	if(desiredOut != null) {
  		if(getNegate()) {
  			desiredOut = !desiredOut;
  		}
  		getOUTPUT().setValue(desiredOut);
  	}
  	
  	
  	setOutputBasedOnTheResult(desiredOut);
  }
  
  @Override
	public void descendantsStarted() throws Exception {
		try {
			handleMigration();
		} finally {
			super.descendantsStarted();
		}
		
  }

  private void handleMigration() {
	  if (BHonVersion.isNull(getToolVersion())) {
		  // Version number is NULL. DO NOT MIGRATE COMPONENT
		  return;
	  }
	  BHonVersion toolVersionWithNewOutputType = new BHonVersion(toolVersionBeforeAddingOutputEnumSlot);
	  if (this.getToolVersion().compareToolVersion(toolVersionWithNewOutputType) < 0) {
		  setNegate(getOUTPUT().getNegate());
		  getOUTPUT().setNegate(false);
		  setOutSave(false);
		  if(!Flags.isTransient(this, this.getSlot(OUTPUT.getName()))){
			  setOutSave(true);
		  }
	  }
  }
  
	private void setOutputBasedOnTheResult(Boolean desiredOut) {
		if(isFirstExecution) {
			getOUTPUT_ENUM().setValue(BCompareEnum.Null);
			isFirstExecution = false;
			return;
		}
		
		if (desiredOut != null) {
			getOUTPUT_ENUM().setStatus(BStatus.ok);
			if (desiredOut) {
				getOUTPUT_ENUM().setValue(BCompareEnum.True);
			} else {
				getOUTPUT_ENUM().setValue(BCompareEnum.False);
			}
		}
	}

  private Boolean computeEqualsOperation() {
  	double onHystVal = getHysteresisValue(onHyst);
  	double offHystVal = getHysteresisValue(offHyst);
  	
  	double in1Val = getInput1().getValue();
  	double in2Val = getInput2().getValue();
  	
  	return in1Val <= (in2Val + offHystVal) && 
              (in1Val >= (in2Val - onHystVal));
  }

  private Boolean computeLessThanOperation() {
  	double onHystVal = getHysteresisValue(onHyst);
  	double offHystVal = getHysteresisValue(offHyst);
  	
  	double in1Val = getInput1().getValue();
  	double in2Val = getInput2().getValue();
  	
  	Boolean desiredOut;	
  	
  	if  ( in1Val >= (in2Val - onHystVal) ){
          if (in1Val < (in2Val + offHystVal) )
          	desiredOut = getPreviousOutput();
          else
          	desiredOut = false;
      } else {
          desiredOut = true;
      }
  	
  	return desiredOut;
  }

  private Boolean computeGreaterThanOperation() {
  	double onHystVal = getHysteresisValue(onHyst);
  	double offHystVal = getHysteresisValue(offHyst);
  	
  	double in1Val = getInput1().getValue();
  	double in2Val = getInput2().getValue();
  	
  	Boolean desiredOut;
  	
  	if ( in1Val <= (in2Val + onHystVal) ) {
          if ( in1Val > (in2Val - offHystVal)) 
          	desiredOut = getPreviousOutput();
          else
          	desiredOut = false;
      } else {
          desiredOut = true;
      }
  	return desiredOut;
  }

  private Boolean getPreviousOutput() {
	  BCompareEnum previousOutput = BCompareEnum.make(getOUTPUT_ENUM().getValue().getOrdinal()); 
	  Boolean previousValue = null;
	  if(BCompareEnum.True.equals(previousOutput)) {
		  previousValue = true;
	  }else if(BCompareEnum.False.equals(previousOutput)) {
		  previousValue = false;
	  }
	  if(previousValue != null && getNegate()) {
		  if(BCompareEnum.True.equals(previousOutput)) {
			  previousValue = false;
		  } else if(BCompareEnum.False.equals(previousOutput)) {
			  previousValue = true;
		  }
	  }
	  return previousValue;
  }

  private double getHysteresisValue(Property property) {
  	  double val = 0.0;
  	  if(isSlotValueValid(property)) {
  		  BHonStatusNumeric bval = (BHonStatusNumeric) get(property);
  		  val = bval.getValue();
  	  }  
  	  return val;	  
  }

  /* (non-Javadoc)
   * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
   */
  @Override
  public List<Property> getInputPropertiesList() {
  	List<Property> propertyArrayList = super.getInputPropertiesList();
  	propertyArrayList.add(input1);
  	propertyArrayList.add(input2);
  	propertyArrayList.add(onHyst);
  	propertyArrayList.add(offHyst);
  	return propertyArrayList;
  }

  @Override
  public List<Property> getOutputPropertiesList() {
	  List<Property> properties = super.getOutputPropertiesList();
	  properties.add(OUTPUT);
	  properties.add(OUTPUT_ENUM);
	  return properties;
  }

  @Override
  public List<Property> getConfigPropertiesList() {
	  List<Property> properties = super.getConfigPropertiesList();
	  properties.add(operation);
	  properties.add(negate);
	  properties.add(outSave);
	  return properties;
  }
  
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		isFirstExecution = true;
	}
	
	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
  		if (!Sys.atSteadyState()) {
			return;
  		}

		if (property.getName().equals(outSave.getName())) {
			int flags = this.getFlags(OUTPUT);
			int flags2 = this.getFlags(OUTPUT_ENUM);
			if (getOutSave()) {
				flags &= ~Flags.TRANSIENT;
				flags2 &= ~Flags.TRANSIENT;
				this.setFlags(OUTPUT, flags);
				this.setFlags(OUTPUT_ENUM, flags2);
			} else {
				this.setFlags(OUTPUT, flags | Flags.TRANSIENT);
				this.setFlags(OUTPUT_ENUM, flags2 | Flags.TRANSIENT);
			}
		} 
	}
	
	@Override
	public void atSteadyState() throws Exception {
		
		if (!Sys.atSteadyState())
			return;
		try {
			handleMigration();
		} finally {
			super.atSteadyState();
		}
	}
	
	@Override
		public void started() throws Exception {
			if (!Sys.atSteadyState())
				return;
			try {
				handleMigration();
			}finally {
				super.started();
			}
		}
 
	private boolean isFirstExecution = true;
	private static String toolVersionBeforeAddingOutputEnumSlot = "4.7.109.1.0.13";
}
