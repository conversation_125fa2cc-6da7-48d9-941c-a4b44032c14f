/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Switch block as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-194
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 15, 2017
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"switch.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name="input", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(255F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(255)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name="offset", type="int", defaultValue="0",
facets = {
	@Facet(name = "BFacets.MIN", value = "0"),
	@Facet(name = "BFacets.MAX", value = "255"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "0") 
})

@NiagaraProperty(name="OUTPUT0", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT1", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT2", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT3", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT4", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT5", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT6", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})
@NiagaraProperty(name="OUTPUT7", type="BNegatableStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})


@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public class BSwitch extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BSwitch(2393368952)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "switch.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "input"

  /**
   * Slot for the {@code input} property.
   * @see #getInput
   * @see #setInput
   */
  public static final Property input = newProperty(Flags.SUMMARY, new BHonStatusNumeric(255F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(255))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code input} property.
   * @see #input
   */
  public BHonStatusNumeric getInput() { return (BHonStatusNumeric)get(input); }

  /**
   * Set the {@code input} property.
   * @see #input
   */
  public void setInput(BHonStatusNumeric v) { set(input, v, null); }

  //endregion Property "input"

  //region Property "offset"

  /**
   * Slot for the {@code offset} property.
   * @see #getOffset
   * @see #setOffset
   */
  public static final Property offset = newProperty(0, 0, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, 0), BFacets.make(BFacets.MAX, 255)), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code offset} property.
   * @see #offset
   */
  public int getOffset() { return getInt(offset); }

  /**
   * Set the {@code offset} property.
   * @see #offset
   */
  public void setOffset(int v) { setInt(offset, v, null); }

  //endregion Property "offset"

  //region Property "OUTPUT0"

  /**
   * Slot for the {@code OUTPUT0} property.
   * @see #getOUTPUT0
   * @see #setOUTPUT0
   */
  public static final Property OUTPUT0 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT0} property.
   * @see #OUTPUT0
   */
  public BNegatableStatusBoolean getOUTPUT0() { return (BNegatableStatusBoolean)get(OUTPUT0); }

  /**
   * Set the {@code OUTPUT0} property.
   * @see #OUTPUT0
   */
  public void setOUTPUT0(BNegatableStatusBoolean v) { set(OUTPUT0, v, null); }

  //endregion Property "OUTPUT0"

  //region Property "OUTPUT1"

  /**
   * Slot for the {@code OUTPUT1} property.
   * @see #getOUTPUT1
   * @see #setOUTPUT1
   */
  public static final Property OUTPUT1 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT1} property.
   * @see #OUTPUT1
   */
  public BNegatableStatusBoolean getOUTPUT1() { return (BNegatableStatusBoolean)get(OUTPUT1); }

  /**
   * Set the {@code OUTPUT1} property.
   * @see #OUTPUT1
   */
  public void setOUTPUT1(BNegatableStatusBoolean v) { set(OUTPUT1, v, null); }

  //endregion Property "OUTPUT1"

  //region Property "OUTPUT2"

  /**
   * Slot for the {@code OUTPUT2} property.
   * @see #getOUTPUT2
   * @see #setOUTPUT2
   */
  public static final Property OUTPUT2 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT2} property.
   * @see #OUTPUT2
   */
  public BNegatableStatusBoolean getOUTPUT2() { return (BNegatableStatusBoolean)get(OUTPUT2); }

  /**
   * Set the {@code OUTPUT2} property.
   * @see #OUTPUT2
   */
  public void setOUTPUT2(BNegatableStatusBoolean v) { set(OUTPUT2, v, null); }

  //endregion Property "OUTPUT2"

  //region Property "OUTPUT3"

  /**
   * Slot for the {@code OUTPUT3} property.
   * @see #getOUTPUT3
   * @see #setOUTPUT3
   */
  public static final Property OUTPUT3 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT3} property.
   * @see #OUTPUT3
   */
  public BNegatableStatusBoolean getOUTPUT3() { return (BNegatableStatusBoolean)get(OUTPUT3); }

  /**
   * Set the {@code OUTPUT3} property.
   * @see #OUTPUT3
   */
  public void setOUTPUT3(BNegatableStatusBoolean v) { set(OUTPUT3, v, null); }

  //endregion Property "OUTPUT3"

  //region Property "OUTPUT4"

  /**
   * Slot for the {@code OUTPUT4} property.
   * @see #getOUTPUT4
   * @see #setOUTPUT4
   */
  public static final Property OUTPUT4 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT4} property.
   * @see #OUTPUT4
   */
  public BNegatableStatusBoolean getOUTPUT4() { return (BNegatableStatusBoolean)get(OUTPUT4); }

  /**
   * Set the {@code OUTPUT4} property.
   * @see #OUTPUT4
   */
  public void setOUTPUT4(BNegatableStatusBoolean v) { set(OUTPUT4, v, null); }

  //endregion Property "OUTPUT4"

  //region Property "OUTPUT5"

  /**
   * Slot for the {@code OUTPUT5} property.
   * @see #getOUTPUT5
   * @see #setOUTPUT5
   */
  public static final Property OUTPUT5 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT5} property.
   * @see #OUTPUT5
   */
  public BNegatableStatusBoolean getOUTPUT5() { return (BNegatableStatusBoolean)get(OUTPUT5); }

  /**
   * Set the {@code OUTPUT5} property.
   * @see #OUTPUT5
   */
  public void setOUTPUT5(BNegatableStatusBoolean v) { set(OUTPUT5, v, null); }

  //endregion Property "OUTPUT5"

  //region Property "OUTPUT6"

  /**
   * Slot for the {@code OUTPUT6} property.
   * @see #getOUTPUT6
   * @see #setOUTPUT6
   */
  public static final Property OUTPUT6 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT6} property.
   * @see #OUTPUT6
   */
  public BNegatableStatusBoolean getOUTPUT6() { return (BNegatableStatusBoolean)get(OUTPUT6); }

  /**
   * Set the {@code OUTPUT6} property.
   * @see #OUTPUT6
   */
  public void setOUTPUT6(BNegatableStatusBoolean v) { set(OUTPUT6, v, null); }

  //endregion Property "OUTPUT6"

  //region Property "OUTPUT7"

  /**
   * Slot for the {@code OUTPUT7} property.
   * @see #getOUTPUT7
   * @see #setOUTPUT7
   */
  public static final Property OUTPUT7 = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT7} property.
   * @see #OUTPUT7
   */
  public BNegatableStatusBoolean getOUTPUT7() { return (BNegatableStatusBoolean)get(OUTPUT7); }

  /**
   * Set the {@code OUTPUT7} property.
   * @see #OUTPUT7
   */
  public void setOUTPUT7(BNegatableStatusBoolean v) { set(OUTPUT7, v, null); }

  //endregion Property "OUTPUT7"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSwitch.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default Constructor for Niagara
	 */
	public BSwitch() {
		//EMPTY IMPLEMENTATION
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		BNegatableStatusBoolean[] outputs = this.getChildren(BNegatableStatusBoolean.class);
		if(isSlotValueValid(input)) {
			computeAndSetOutputs(outputs);
		} else {
			setAllOutputsOff(outputs);
		}
	}

	private void computeAndSetOutputs(BNegatableStatusBoolean[] outputs) {
		int inputVal = (int) getInput().getValue();
		int offsetVal = getOffset();
		int index = inputVal - offsetVal;

		if(index>=0 && index <outputs.length) {
			for (int i = 0; i < outputs.length; i++) {
				if(index==i) {
					setGivenOutput(outputs[i], true);
				} else {
					setGivenOutput(outputs[i], false);
				}
			}
		} else {
			setAllOutputsOff(outputs);
		}
	}

	private void setAllOutputsOff(BNegatableStatusBoolean[] outputs) {
		for (int i = 0; i < outputs.length; i++) {
			setGivenOutput(outputs[i], false);
		}
	}
	
	private void setGivenOutput(BNegatableStatusBoolean output, boolean desiredOutput) {
		if(output.getNegate()) {
			output.setValue(!desiredOutput);
		} else {
			output.setValue(desiredOutput);
		}
	}
	
	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(input);
		return propertyArrayList;
	}

	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> propertyArrayList = super.getOutputPropertiesList();
		propertyArrayList.add(OUTPUT0);
		propertyArrayList.add(OUTPUT1);
		propertyArrayList.add(OUTPUT2);
		propertyArrayList.add(OUTPUT3);
		propertyArrayList.add(OUTPUT4);
		propertyArrayList.add(OUTPUT5);
		propertyArrayList.add(OUTPUT6);
		propertyArrayList.add(OUTPUT7);
		return propertyArrayList;
	}

	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> propertyArrayList = super.getConfigPropertiesList();
		propertyArrayList.add(offset);
		return propertyArrayList;		
	}
	
}
