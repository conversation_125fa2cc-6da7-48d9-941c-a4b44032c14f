/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Hysteretic Relay as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-193
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 7, 2017
 */
@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"hysteretic_relay.png\")", flags=Flags.HIDDEN|Flags.READONLY)
//Input slots
@NiagaraProperty(name="in", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.nullStatus)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="onVal", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="offVal", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") 
})
@NiagaraProperty(name="minOn", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(65535)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0")
})
@NiagaraProperty(name="minOff", type="BHonStatusNumeric", defaultValue="new BHonStatusNumeric(0.0F, BStatus.ok)", flags=Flags.SUMMARY,
facets = {
	@Facet(name = "BFacets.MIN", value = "BFloat.make(0)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(65535)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"),
	@Facet(name = "BFacets.PRECISION", value = "0")
})

//Output slots
@NiagaraProperty(name="OUTPUT", type="BHonStatusBoolean", defaultValue="new BNegatableStatusBoolean(false, BStatus.ok, false)", flags=Flags.SUMMARY|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT,
facets = {
	@Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE"),
	@Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE")
})

//LoopStaticVariables
@NiagaraProperty(name="minOnOffTimer", type="double", defaultValue="0.0", flags=Flags.HIDDEN|Flags.READONLY|Flags.DEFAULT_ON_CLONE|Flags.TRANSIENT)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213","squid:S2160", "squid:S00103"})

public class BHystereticRelay extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BHystereticRelay(4204445970)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "hysteretic_relay.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "in"

  /**
   * Slot for the {@code in} property.
   * Input slots
   * @see #getIn
   * @see #setIn
   */
  public static final Property in = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in} property.
   * Input slots
   * @see #in
   */
  public BHonStatusNumeric getIn() { return (BHonStatusNumeric)get(in); }

  /**
   * Set the {@code in} property.
   * Input slots
   * @see #in
   */
  public void setIn(BHonStatusNumeric v) { set(in, v, null); }

  //endregion Property "in"

  //region Property "onVal"

  /**
   * Slot for the {@code onVal} property.
   * @see #getOnVal
   * @see #setOnVal
   */
  public static final Property onVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code onVal} property.
   * @see #onVal
   */
  public BHonStatusNumeric getOnVal() { return (BHonStatusNumeric)get(onVal); }

  /**
   * Set the {@code onVal} property.
   * @see #onVal
   */
  public void setOnVal(BHonStatusNumeric v) { set(onVal, v, null); }

  //endregion Property "onVal"

  //region Property "offVal"

  /**
   * Slot for the {@code offVal} property.
   * @see #getOffVal
   * @see #setOffVal
   */
  public static final Property offVal = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code offVal} property.
   * @see #offVal
   */
  public BHonStatusNumeric getOffVal() { return (BHonStatusNumeric)get(offVal); }

  /**
   * Set the {@code offVal} property.
   * @see #offVal
   */
  public void setOffVal(BHonStatusNumeric v) { set(offVal, v, null); }

  //endregion Property "offVal"

  //region Property "minOn"

  /**
   * Slot for the {@code minOn} property.
   * @see #getMinOn
   * @see #setMinOn
   */
  public static final Property minOn = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65535))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code minOn} property.
   * @see #minOn
   */
  public BHonStatusNumeric getMinOn() { return (BHonStatusNumeric)get(minOn); }

  /**
   * Set the {@code minOn} property.
   * @see #minOn
   */
  public void setMinOn(BHonStatusNumeric v) { set(minOn, v, null); }

  //endregion Property "minOn"

  //region Property "minOff"

  /**
   * Slot for the {@code minOff} property.
   * @see #getMinOff
   * @see #setMinOff
   */
  public static final Property minOff = newProperty(Flags.SUMMARY, new BHonStatusNumeric(0.0F, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(0)), BFacets.make(BFacets.MAX, BFloat.make(65535))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code minOff} property.
   * @see #minOff
   */
  public BHonStatusNumeric getMinOff() { return (BHonStatusNumeric)get(minOff); }

  /**
   * Set the {@code minOff} property.
   * @see #minOff
   */
  public void setMinOff(BHonStatusNumeric v) { set(minOff, v, null); }

  //endregion Property "minOff"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * Output slots
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BNegatableStatusBoolean(false, BStatus.ok, false), BFacets.make(BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_AX_FE), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.NEGATABLE_STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * Output slots
   * @see #OUTPUT
   */
  public BHonStatusBoolean getOUTPUT() { return (BHonStatusBoolean)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * Output slots
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusBoolean v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Property "minOnOffTimer"

  /**
   * Slot for the {@code minOnOffTimer} property.
   * LoopStaticVariables
   * @see #getMinOnOffTimer
   * @see #setMinOnOffTimer
   */
  public static final Property minOnOffTimer = newProperty(Flags.HIDDEN | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, 0.0, null);

  /**
   * Get the {@code minOnOffTimer} property.
   * LoopStaticVariables
   * @see #minOnOffTimer
   */
  public double getMinOnOffTimer() { return getDouble(minOnOffTimer); }

  /**
   * Set the {@code minOnOffTimer} property.
   * LoopStaticVariables
   * @see #minOnOffTimer
   */
  public void setMinOnOffTimer(double v) { setDouble(minOnOffTimer, v, null); }

  //endregion Property "minOnOffTimer"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHystereticRelay.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default Constructor for Niagara
	 */
	public BHystereticRelay() {
		//EMPTY IMPLEMENTATION
	}
	
	@Override
	public void started() throws Exception {
		super.started();
		BHonStatusBoolean output2 = getOUTPUT();
		if(!(output2 instanceof BNegatableStatusBoolean)) {
			BNegatableStatusBoolean negatableStatusBoolean = new BNegatableStatusBoolean();
			negatableStatusBoolean.setStatus(output2.getStatus());
			negatableStatusBoolean.setValue(output2.getValue());
			setOUTPUT(negatableStatusBoolean);
		}
	}
	
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		super.initHoneywellComponent(executionParams);
		double minOffTime = limitInput(getMinOff().getValue(), ONOFF_TIME_LOWLIMIT, ONOFF_TIME_HIGHLIMIT, ONOFF_TIME_LOWLIMIT);
		setMinOnOffTimer(minOffTime * UnitConstants.THOUSAND_MILLI_SECOND);
		setOutputValue(false);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		executeHystRelay(executionParams);
	}
	
	private void executeHystRelay(BExecutionParams executionParams) {
		// If the timer is running (nonzero), decrement it toward zero by the iteration interval
		if(getMinOnOffTimer() > 0.0) {
			if(getMinOnOffTimer() > executionParams.getIterationInterval()) {
				setMinOnOffTimer(getMinOnOffTimer() - executionParams.getIterationInterval());
				return;
			}else {
				setMinOnOffTimer(0.0);
			}
		}

		double minOffTime = limitInput(getMinOff().getValue(), ONOFF_TIME_LOWLIMIT, ONOFF_TIME_HIGHLIMIT, ONOFF_TIME_LOWLIMIT);
		minOffTime = minOffTime * UnitConstants.THOUSAND_MILLI_SECOND;
		//if any input values are invalid, turn off output
		if(hasInvalidMandatoryInput()) {
			//Turn off output and start timer at minOff value
			setOutputValue(false);
			setMinOnOffTimer(minOffTime);
			return;
		}
		
		double minOnTime = limitInput(getMinOn().getValue(), ONOFF_TIME_LOWLIMIT, ONOFF_TIME_HIGHLIMIT, ONOFF_TIME_LOWLIMIT);
		minOnTime = minOnTime * UnitConstants.THOUSAND_MILLI_SECOND;
		boolean lastOut = ((BNegatableStatusBoolean)getOUTPUT()).getNegate() ? !getOUTPUT().getValue() : getOUTPUT().getValue();
		
		//If the time is expired then compute and set the output
		computeAndSetOutput(minOnTime, minOffTime, lastOut);
	}

	private void computeAndSetOutput(final double minOnTime, final double minOffTime, final boolean lastOut) {
		if(getOnVal().getValue() >= getOffVal().getValue()) {
			computeForOnValGreaterThanOffVal(minOnTime, minOffTime, lastOut);
		} else {
			calculateOutputForOnValLessThanOffVal(minOnTime, minOffTime, lastOut);
		}
	}

	private void computeForOnValGreaterThanOffVal(final double minOnTime, final double minOffTime, final boolean lastOut) {
		// Check for output off, timer expired and in > onVal
		if(!lastOut && getIn().getValue()>=getOnVal().getValue()) {
			//Turn on output and start timer at minOn value
			setOutputValue(true);
			setMinOnOffTimer(minOnTime);
		} 
		
		// Check for output on, in < offVal and timer expired
		else if(lastOut && getIn().getValue() < getOffVal().getValue()) {
			//Turn off output and start timer at minOff value
			setOutputValue(false);
			setMinOnOffTimer(minOffTime);
		}
	}
	
	private void calculateOutputForOnValLessThanOffVal(final double minOnTime, final double minOffTime, final boolean lastOut) {
		// Check for output off, timer expired and in < onVal
		if(!lastOut && getIn().getValue()<=getOnVal().getValue()) {
			//Turn on output and start timer at minOn value
			setOutputValue(true);
			setMinOnOffTimer(minOnTime);
		}
		
		// Check for output on, in > offVal and timer expired
		else if(lastOut && getIn().getValue()>getOffVal().getValue()) {
			//Turn off output and start timer at minOff value
			setOutputValue(false);
			setMinOnOffTimer(minOffTime);
		}
	}

	private void setOutputValue(boolean value) {
		if(((BNegatableStatusBoolean)getOUTPUT()).getNegate())
			value = !value;
		getOUTPUT().setValue(value);
	}
	
	/**
	 * If in/onVal/offVal is unconnected OUTPUT has to be set FALSE
	 * @return
	 */
	private boolean hasInvalidMandatoryInput() {
		boolean isMandatoryInputUnconnected = !isConfigured(in) || !isConfigured(onVal) || !isConfigured(offVal);
		if(!isMandatoryInputUnconnected) {
			return isInvalidValue(in) || isInvalidValue(onVal) || isInvalidValue(offVal);
		}
		
		return isMandatoryInputUnconnected;
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
	 */
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> propertyArrayList = super.getInputPropertiesList();
		propertyArrayList.add(in);
		propertyArrayList.add(onVal);
		propertyArrayList.add(offVal);
		propertyArrayList.add(minOn);
		propertyArrayList.add(minOff);
		return propertyArrayList;
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	private static final float ONOFF_TIME_LOWLIMIT = 0.0f;		//If value changed, update slotomatic code also
	private static final float ONOFF_TIME_HIGHLIMIT = 65535.0f;	//If value changed, update slotomatic code also
}
