/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog;

import java.util.List;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.BFloat;
import javax.baja.sys.BIcon;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;

/**
 * Implementation of Priority Select block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 *
 */

@NiagaraType
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"priority_select.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name="enable1", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="enable2", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="enable3", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false)", flags=Flags.SUMMARY)
@NiagaraProperty(name="enable4", type="BFiniteStatusBoolean", defaultValue="new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false)", flags=Flags.SUMMARY)
@NiagaraProperty(name = "in1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "in2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "in3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })
@NiagaraProperty(name = "in4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus)", flags = Flags.SUMMARY,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6") })

@NiagaraProperty(name = "In1AsDefault", type="boolean", defaultValue="false")

@NiagaraProperty(name = "OUTPUT", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok)", flags = Flags.SUMMARY | Flags.READONLY|Flags.DEFAULT_ON_CLONE| Flags.TRANSIENT,
facets = { 
	@Facet(name = "BFacets.MIN", value = "BFloat.make(Float.NEGATIVE_INFINITY)"),
	@Facet(name = "BFacets.MAX", value = "BFloat.make(Float.MAX_VALUE)"),
	@Facet(name = "BFacets.UNITS", value = "BUnit.NULL"), 
	@Facet(name = "BFacets.PRECISION", value = "6"),
  @Facet(name = "BFacets.FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE"),
  @Facet(name = "BFacets.UX_FIELD_EDITOR", value="FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE") })


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})

public class BPrioritySelect extends BFunctionBlock{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.BPrioritySelect(3641235073)1.0$ @*/
/* Generated Mon Aug 25 20:14:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "priority_select.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "enable1"

  /**
   * Slot for the {@code enable1} property.
   * @see #getEnable1
   * @see #setEnable1
   */
  public static final Property enable1 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false), null);

  /**
   * Get the {@code enable1} property.
   * @see #enable1
   */
  public BFiniteStatusBoolean getEnable1() { return (BFiniteStatusBoolean)get(enable1); }

  /**
   * Set the {@code enable1} property.
   * @see #enable1
   */
  public void setEnable1(BFiniteStatusBoolean v) { set(enable1, v, null); }

  //endregion Property "enable1"

  //region Property "enable2"

  /**
   * Slot for the {@code enable2} property.
   * @see #getEnable2
   * @see #setEnable2
   */
  public static final Property enable2 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false), null);

  /**
   * Get the {@code enable2} property.
   * @see #enable2
   */
  public BFiniteStatusBoolean getEnable2() { return (BFiniteStatusBoolean)get(enable2); }

  /**
   * Set the {@code enable2} property.
   * @see #enable2
   */
  public void setEnable2(BFiniteStatusBoolean v) { set(enable2, v, null); }

  //endregion Property "enable2"

  //region Property "enable3"

  /**
   * Slot for the {@code enable3} property.
   * @see #getEnable3
   * @see #setEnable3
   */
  public static final Property enable3 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false), null);

  /**
   * Get the {@code enable3} property.
   * @see #enable3
   */
  public BFiniteStatusBoolean getEnable3() { return (BFiniteStatusBoolean)get(enable3); }

  /**
   * Set the {@code enable3} property.
   * @see #enable3
   */
  public void setEnable3(BFiniteStatusBoolean v) { set(enable3, v, null); }

  //endregion Property "enable3"

  //region Property "enable4"

  /**
   * Slot for the {@code enable4} property.
   * @see #getEnable4
   * @see #setEnable4
   */
  public static final Property enable4 = newProperty(Flags.SUMMARY, new BNegatableFiniteStatusBoolean(false, BStatus.nullStatus,false), null);

  /**
   * Get the {@code enable4} property.
   * @see #enable4
   */
  public BFiniteStatusBoolean getEnable4() { return (BFiniteStatusBoolean)get(enable4); }

  /**
   * Set the {@code enable4} property.
   * @see #enable4
   */
  public void setEnable4(BFiniteStatusBoolean v) { set(enable4, v, null); }

  //endregion Property "enable4"

  //region Property "in1"

  /**
   * Slot for the {@code in1} property.
   * @see #getIn1
   * @see #setIn1
   */
  public static final Property in1 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in1} property.
   * @see #in1
   */
  public BHonStatusNumeric getIn1() { return (BHonStatusNumeric)get(in1); }

  /**
   * Set the {@code in1} property.
   * @see #in1
   */
  public void setIn1(BHonStatusNumeric v) { set(in1, v, null); }

  //endregion Property "in1"

  //region Property "in2"

  /**
   * Slot for the {@code in2} property.
   * @see #getIn2
   * @see #setIn2
   */
  public static final Property in2 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in2} property.
   * @see #in2
   */
  public BHonStatusNumeric getIn2() { return (BHonStatusNumeric)get(in2); }

  /**
   * Set the {@code in2} property.
   * @see #in2
   */
  public void setIn2(BHonStatusNumeric v) { set(in2, v, null); }

  //endregion Property "in2"

  //region Property "in3"

  /**
   * Slot for the {@code in3} property.
   * @see #getIn3
   * @see #setIn3
   */
  public static final Property in3 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in3} property.
   * @see #in3
   */
  public BHonStatusNumeric getIn3() { return (BHonStatusNumeric)get(in3); }

  /**
   * Set the {@code in3} property.
   * @see #in3
   */
  public void setIn3(BHonStatusNumeric v) { set(in3, v, null); }

  //endregion Property "in3"

  //region Property "in4"

  /**
   * Slot for the {@code in4} property.
   * @see #getIn4
   * @see #setIn4
   */
  public static final Property in4 = newProperty(Flags.SUMMARY, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.nullStatus), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)));

  /**
   * Get the {@code in4} property.
   * @see #in4
   */
  public BHonStatusNumeric getIn4() { return (BHonStatusNumeric)get(in4); }

  /**
   * Set the {@code in4} property.
   * @see #in4
   */
  public void setIn4(BHonStatusNumeric v) { set(in4, v, null); }

  //endregion Property "in4"

  //region Property "In1AsDefault"

  /**
   * Slot for the {@code In1AsDefault} property.
   * @see #getIn1AsDefault
   * @see #setIn1AsDefault
   */
  public static final Property In1AsDefault = newProperty(0, false, null);

  /**
   * Get the {@code In1AsDefault} property.
   * @see #In1AsDefault
   */
  public boolean getIn1AsDefault() { return getBoolean(In1AsDefault); }

  /**
   * Set the {@code In1AsDefault} property.
   * @see #In1AsDefault
   */
  public void setIn1AsDefault(boolean v) { setBoolean(In1AsDefault, v, null); }

  //endregion Property "In1AsDefault"

  //region Property "OUTPUT"

  /**
   * Slot for the {@code OUTPUT} property.
   * @see #getOUTPUT
   * @see #setOUTPUT
   */
  public static final Property OUTPUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.DEFAULT_ON_CLONE | Flags.TRANSIENT, new BHonStatusNumeric(Double.POSITIVE_INFINITY, BStatus.ok), BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BFloat.make(Float.NEGATIVE_INFINITY)), BFacets.make(BFacets.MAX, BFloat.make(Float.MAX_VALUE))), BFacets.make(BFacets.UNITS, BUnit.NULL)), BFacets.make(BFacets.PRECISION, 6)), BFacets.make(BFacets.FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_AX_FE)), BFacets.make(BFacets.UX_FIELD_EDITOR, FBSlotConstants.STATUS_VALUE_OUTSLOT_UX_FE)));

  /**
   * Get the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public BHonStatusNumeric getOUTPUT() { return (BHonStatusNumeric)get(OUTPUT); }

  /**
   * Set the {@code OUTPUT} property.
   * @see #OUTPUT
   */
  public void setOUTPUT(BHonStatusNumeric v) { set(OUTPUT, v, null); }

  //endregion Property "OUTPUT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPrioritySelect.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@Override
	public void started() throws Exception {
		super.started();
		replaceFiniteStatusBooleanSlotWithNegatableSlot(enable1);
		replaceFiniteStatusBooleanSlotWithNegatableSlot(enable2);
		replaceFiniteStatusBooleanSlotWithNegatableSlot(enable3);
		replaceFiniteStatusBooleanSlotWithNegatableSlot(enable4);
	}

	private void replaceFiniteStatusBooleanSlotWithNegatableSlot(Property property) {
		BValue value = this.get(property);
		if(!(value instanceof BNegatableFiniteStatusBoolean)) {
			BFiniteStatusBoolean enableTemp = (BFiniteStatusBoolean) value;
			BNegatableFiniteStatusBoolean enableNegatableFiniteStatusBoolean = new BNegatableFiniteStatusBoolean();
			enableNegatableFiniteStatusBoolean.setStatus(enableTemp.getStatus());
			enableNegatableFiniteStatusBoolean.setValue(enableTemp.getValue());
			this.set(property,enableNegatableFiniteStatusBoolean);
		}
	}
	
  /* (non-Javadoc)
   * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
   */
  @Override
  public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
	  super.executeHoneywellComponent(executionParams);
	  
	  double outValue;
	  if(getIn1AsDefault())
		  outValue = getInValue(in1);
	  else
		  outValue = Double.POSITIVE_INFINITY;
	  
	  if(getEnableValue(enable1)) {
		  outValue = getInValue(in1);	  
	  }else if(getEnableValue(enable2)) {
		  outValue = getInValue(in2);
	  }else if(getEnableValue(enable3)) {
		  outValue = getInValue(in3);
	  }else if(getEnableValue(enable4)) {
		  outValue = getInValue(in4);
	  }
	  
	  getOUTPUT().setValue(outValue);
  }
  
  
  double getInValue(Property property) {
	  double val = Double.POSITIVE_INFINITY;
	  if(isSlotValueValid(property)) {
		  BHonStatusNumeric bval = (BHonStatusNumeric) get(property);
		  val = bval.getValue();
	  }  
	  
	  return val;	  
  }
  
  
  boolean getEnableValue(Property property) {
	  if(isConfigured(property)) {
		  BNegatableFiniteStatusBoolean val = (BNegatableFiniteStatusBoolean) get(property);
		  boolean value =  val.getValue();
		  if(((BNegatableFiniteStatusBoolean)get(property)).getNegate()) {
			value = !value;
		  }
			
		  return value;
	  }
	  
	  return false;  
  }

  /* (non-Javadoc)
   * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#getInputPropertiesList()
   */
  @Override
  public List<Property> getInputPropertiesList() {
	List<Property> propertyArrayList = super.getInputPropertiesList();
	propertyArrayList.add(enable1);
	propertyArrayList.add(enable2);
	propertyArrayList.add(enable3);
	propertyArrayList.add(enable4);
	propertyArrayList.add(in1);
	propertyArrayList.add(in2);
	propertyArrayList.add(in3);
	propertyArrayList.add(in4);
	return propertyArrayList;
  }
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> properties = super.getOutputPropertiesList();
		properties.add(OUTPUT);
		return properties;
	}
	
	@Override
	public List<Property> getConfigPropertiesList() {
		List<Property> properties = super.getConfigPropertiesList();
		properties.add(In1AsDefault);
		return properties;
	}
  
}
