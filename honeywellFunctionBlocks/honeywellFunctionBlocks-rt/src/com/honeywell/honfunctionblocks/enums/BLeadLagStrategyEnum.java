/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Created this as part of StageDriver block implementation
 * This ENUM is used for Lead Lag strategy configuration used in StageDriver block as per FB SDD rev26 
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya
 * @since Dec 22, 2017
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range("llFOLO"), 
		@Range("llFOFO"),
		@Range("llRUNEQ")
		}, defaultValue = "llFOLO")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class BLeadLagStrategyEnum extends BFrozenEnum {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.enums.BLeadLagStrategyEnum(807923527)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for llFOLO. */
  public static final int LL_FOLO = 0;
  /** Ordinal value for llFOFO. */
  public static final int LL_FOFO = 1;
  /** Ordinal value for llRUNEQ. */
  public static final int LL_RUNEQ = 2;

  /** BLeadLagStrategyEnum constant for llFOLO. */
  public static final BLeadLagStrategyEnum llFOLO = new BLeadLagStrategyEnum(LL_FOLO);
  /** BLeadLagStrategyEnum constant for llFOFO. */
  public static final BLeadLagStrategyEnum llFOFO = new BLeadLagStrategyEnum(LL_FOFO);
  /** BLeadLagStrategyEnum constant for llRUNEQ. */
  public static final BLeadLagStrategyEnum llRUNEQ = new BLeadLagStrategyEnum(LL_RUNEQ);

  /** Factory method with ordinal. */
  public static BLeadLagStrategyEnum make(int ordinal)
  {
    return (BLeadLagStrategyEnum)llFOLO.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BLeadLagStrategyEnum make(String tag)
  {
    return (BLeadLagStrategyEnum)llFOLO.getRange().get(tag);
  }

  /** Private constructor. */
  private BLeadLagStrategyEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BLeadLagStrategyEnum DEFAULT = llFOLO;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLeadLagStrategyEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
