/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON> .K
 * @since Jan 15, 2018
 */

@NiagaraType
@NiagaraEnum(range = {
		@Range("directActing"), 
		@Range("reverseActing")}, 
		defaultValue = "directActing")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
public final class BAiaDirectionEnum extends BFrozenEnum {
//region /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.enums.BAiaDirectionEnum(842893836)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for directActing. */
  public static final int DIRECT_ACTING = 0;
  /** Ordinal value for reverseActing. */
  public static final int REVERSE_ACTING = 1;

  /** BAiaDirectionEnum constant for directActing. */
  public static final BAiaDirectionEnum directActing = new BAiaDirectionEnum(DIRECT_ACTING);
  /** BAiaDirectionEnum constant for reverseActing. */
  public static final BAiaDirectionEnum reverseActing = new BAiaDirectionEnum(REVERSE_ACTING);

  /** Factory method with ordinal. */
  public static BAiaDirectionEnum make(int ordinal)
  {
    return (BAiaDirectionEnum)directActing.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BAiaDirectionEnum make(String tag)
  {
    return (BAiaDirectionEnum)directActing.getRange().get(tag);
  }

  /** Private constructor. */
  private BAiaDirectionEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BAiaDirectionEnum DEFAULT = directActing;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAiaDirectionEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
