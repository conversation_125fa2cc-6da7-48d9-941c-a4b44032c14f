/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
@NiagaraEnum(range = {
		@Range("pidOutputRange0to100"), 
		@Range("pidOutputRangeMinus200to200")}, 
		defaultValue = "pidOutputRangeMinus200to200")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
public final class BPidOutputRangeEnum extends BFrozenEnum {
//region /*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.enums.BPidOutputRangeEnum(2738584351)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  /** Ordinal value for pidOutputRange0to100. */
  public static final int PID_OUTPUT_RANGE_0TO_100 = 0;
  /** Ordinal value for pidOutputRangeMinus200to200. */
  public static final int PID_OUTPUT_RANGE_MINUS_200TO_200 = 1;

  /** BPidOutputRangeEnum constant for pidOutputRange0to100. */
  public static final BPidOutputRangeEnum pidOutputRange0to100 = new BPidOutputRangeEnum(PID_OUTPUT_RANGE_0TO_100);
  /** BPidOutputRangeEnum constant for pidOutputRangeMinus200to200. */
  public static final BPidOutputRangeEnum pidOutputRangeMinus200to200 = new BPidOutputRangeEnum(PID_OUTPUT_RANGE_MINUS_200TO_200);

  /** Factory method with ordinal. */
  public static BPidOutputRangeEnum make(int ordinal)
  {
    return (BPidOutputRangeEnum)pidOutputRange0to100.getRange().get(ordinal, false);
  }

  /** Factory method with tag. */
  public static BPidOutputRangeEnum make(String tag)
  {
    return (BPidOutputRangeEnum)pidOutputRange0to100.getRange().get(tag);
  }

  /** Private constructor. */
  private BPidOutputRangeEnum(int ordinal)
  {
    super(ordinal);
  }

  public static final BPidOutputRangeEnum DEFAULT = pidOutputRangeMinus200to200;

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPidOutputRangeEnum.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
