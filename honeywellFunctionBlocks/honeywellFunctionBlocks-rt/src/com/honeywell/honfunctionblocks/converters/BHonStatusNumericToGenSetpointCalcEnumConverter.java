/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation of General Setpoint Calculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * <AUTHOR> - <PERSON>vanya B.
 * @since Feb 13, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BHonStatusNumericToGenSetpointCalcEnumConverter extends BStatusNumericToGenSetpointCalcEnumConverter {
/*+ ------------ <PERSON>EG<PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.BHonStatusNumericToGenSetpointCalcEnumConverter(2979906276)1.0$ @*/
/* Generated Tue Feb 13 14:32:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumericToGenSetpointCalcEnumConverter.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
