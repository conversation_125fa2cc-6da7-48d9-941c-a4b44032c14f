/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BStatusGenSetpointCalcEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Implementation of General Setpoint Calculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * <AUTHOR> - <PERSON><PERSON>ya B.
 * @since Feb 13, 2018
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusEnum", to="honeywellFunctionBlocks:StatusGenSetpointCalcEnum")
)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStatusEnumToGenSetpointCalcEnumConverter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusEnumToGenSetpointCalcEnumConverter(3158131996)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusEnumToGenSetpointCalcEnumConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
  @Override
  public BObject convert(BObject from, BObject to, Context arg2) {
	  BStatusEnum statusEnum = (BStatusEnum)from;
	  BStatusGenSetpointCalcEnum statusOccEnum = (BStatusGenSetpointCalcEnum)to;

	  int d = statusEnum.getValue().getOrdinal();

	  BFacets facets = statusOccEnum.getParent().getSlotFacets(statusOccEnum.getParent().getSlot(statusOccEnum.getName()));
	  String typeName = facets.gets("range", null);			  

	  if(BOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
		  BOccupancyEnum working;		  
		  int low = BOccupancyEnum.OCCUPIED;
		  int high = BOccupancyEnum.STANDBY;
		  if(d < low || (d > high && d!=BOccupancyEnum.NULL) )
			  working = BOccupancyEnum.Occupied;
		  else
			  working = BOccupancyEnum.make(d);			
		  statusOccEnum.setValue(working);
	  }

	  statusOccEnum.setStatus(statusEnum.getStatus());
	  return statusOccEnum;
  }

}
