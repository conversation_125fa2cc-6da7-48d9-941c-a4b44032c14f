/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BDouble;
import javax.baja.sys.BFloat;
import javax.baja.sys.BNumber;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:Number", to="honeywellFunctionBlocks:FiniteStatusBoolean")		
)
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BNumberToFiniteStatusBoolean extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BNumberToFiniteStatusBoolean(3642622824)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNumberToFiniteStatusBoolean.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BFiniteStatusBoolean statusBoolean = (BFiniteStatusBoolean)to;		
    	if((from instanceof BDouble && checkIfInvalidDouble((BDouble)from)) || (from instanceof BFloat && checkIfInvalidFloat((BFloat)from))) {
			statusBoolean.setValue(false);
	    	statusBoolean.setStatus(BStatus.nullStatus);

	    	if(to instanceof BNegatableFiniteStatusBoolean)
	    		((BNegatableFiniteStatusBoolean) statusBoolean).setIsInvalidValue(true);
        	
	    	return statusBoolean;
		}
		
		BNumber value = (BNumber)from;
		statusBoolean.setValue(Double.compare(value.getDouble(), 0.0)!=0);
		statusBoolean.setStatus(BStatus.ok);
        return statusBoolean;
	}
	
	private boolean checkIfInvalidDouble(BDouble from) {
		double d = from.getDouble();
		return (Double.isInfinite(d) && d > 0.0) || Double.isNaN(d);
	}
	
	private boolean checkIfInvalidFloat(BFloat from) {
		float d = from.getFloat();
		return (Float.isInfinite(d) && d > 0.0) || Float.isNaN(d);
	}

}
