/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;

/**
 * Converter to translate the StatusNumeric value to Status Boolean as need by the F1 DDC engine
 * Value conversion rules
 * IF VAL == 0.0	OR VAL == invalid (nan or +inf) 	-- VALUE = 0
 * IF VAL != 0.0 OR VAL == -inf							-- VALUE = 1
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since Nov 22, 2017
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusNumeric", to="honeywellFunctionBlocks:FiniteStatusBoolean")
		
)
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213"})

public class BStatusNumericToFiniteStatusBoolean extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean(2423538)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToFiniteStatusBoolean.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BStatusNumeric statusNumeric = (BStatusNumeric)from;
        BFiniteStatusBoolean statusBoolean = (BFiniteStatusBoolean)to;
        
        double d = statusNumeric.getValue();
        //							+inf is invalid						NaN is invalid
        boolean isInvalidValue = (Double.isInfinite(d) && d > 0.0) || Double.isNaN(d);

        //  (+inf & NaN check)     (zero check)
        if(isInvalidValue || Double.compare(d, 0.0) == 0)
        	statusBoolean.setValue(false);
        else 
        	statusBoolean.setValue(true);
        
        if(to instanceof BNegatableFiniteStatusBoolean)
        	((BNegatableFiniteStatusBoolean) statusBoolean).setIsInvalidValue(isInvalidValue);
        
        statusBoolean.setStatus(statusNumeric.getStatus());
        return statusBoolean;
	}
}
