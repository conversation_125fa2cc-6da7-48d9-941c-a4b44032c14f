/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * <AUTHOR> - <PERSON><PERSON>
 * @since 06 June,2018
 */


@NiagaraType
  (
    agent = @AgentOn
      (
        types = {"baja:ConversionLink"},
        requiredPermissions="w"
      ),
    adapter = @Adapter(from = "honeywellFunctionBlocks:HonStatusNumeric", to="honeywellFunctionBlocks:StatusSyncEdgeTriggerEnum")
  )

@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})
public class BHonStatusNumericToStatusSyncEdgeTriggerEnumConverter extends BStatusNumericToStatusSyncEdgeTriggerEnumConverter {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BHonStatusNumericToStatusSyncEdgeTriggerEnumConverter(4268382751)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumericToStatusSyncEdgeTriggerEnumConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
