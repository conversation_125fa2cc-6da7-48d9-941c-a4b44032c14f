/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 * Implementation of OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya B.
 * @since Feb 6, 2018
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusNumeric", to="honeywellFunctionBlocks:StatusOccupancyStateEnum")
)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BStatusNumericToStatusOccupancyStateEnumConverter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusOccupancyStateEnumConverter(38091205)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToStatusOccupancyStateEnumConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BStatusNumeric statusNumeric = (BStatusNumeric)from;
		BStatusOccupancyStateEnum statusOccEnum = (BStatusOccupancyStateEnum)to;
      
      double sourceValue = statusNumeric.getValue();
      
      BFacets facets = statusOccEnum.getParent().getSlotFacets(statusOccEnum.getParent().getSlot(statusOccEnum.getName()));
      String typeName = facets.gets("range", null);
      
      
      if(BScheduledStateEnum.TYPE.getTypeSpec().toString().equals(typeName)) {    	  
      	BScheduledStateEnum working = calculateForScheduleStatusEnum(sourceValue);
      	statusOccEnum.setValue(working);
      } else if(BOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
    	  BOccupancyEnum working = calculateForOccupancyEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      } else if(BEffectiveOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)){
    	  BEffectiveOccupancyEnum working = calculateForEffectiveOccupancyEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      } else if(BOccupancySensorStateEnum.TYPE.getTypeSpec().toString().equals(typeName)){
    	  BOccupancySensorStateEnum working = calculateForOccupancySensorStateEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      }
     
      statusOccEnum.setStatus(statusNumeric.getStatus());
      return statusOccEnum;
	}	
	
	private BScheduledStateEnum calculateForScheduleStatusEnum(double d) {
		BScheduledStateEnum working;
		if(isInvalidValue(d))
			working = BScheduledStateEnum.Null;
		else {
			working = OccupancyStateEnumsConverter.calculateForScheduleStatusEnum((int)d);
		}
		return working;	
	}
	
	private BOccupancyEnum calculateForOccupancyEnum(double d) {
		BOccupancyEnum working;
		if(isInvalidValue(d))
			working = BOccupancyEnum.Null;
		else {
			working = OccupancyStateEnumsConverter.calculateForOccupancyEnum((int)d);
		}
		return working;	
		
	}
	
	private BOccupancySensorStateEnum calculateForOccupancySensorStateEnum(double d) {
		BOccupancySensorStateEnum working;
		if(isInvalidValue(d))
			working = BOccupancySensorStateEnum.Null;
		else {
			working = OccupancyStateEnumsConverter.calculateForOccupancySensorStateEnum((int)d);
		}
		return working;	
	}
	
	private BEffectiveOccupancyEnum calculateForEffectiveOccupancyEnum(final double sourceValue) {
		BEffectiveOccupancyEnum working;
		if(isInvalidValue(sourceValue))
			working = BEffectiveOccupancyEnum.Occupied;
		else {
			working = OccupancyStateEnumsConverter.calculateForEffectiveOccupancyEnum((int) sourceValue);
		}
		
		return working;
	}
	
	private boolean isInvalidValue(double sourceValue) {
		return (Double.isInfinite(sourceValue) && sourceValue > 0.0) || Double.isNaN(sourceValue);
	}
}
