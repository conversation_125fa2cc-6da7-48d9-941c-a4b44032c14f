/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum;

/**
 * Implementation of Set Temperature Mode  block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 15, 2018
 */


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class StatusSetTemperatureModeEnumsConverter {
	///CLOVER:OFF
	private StatusSetTemperatureModeEnumsConverter() {}
	///CLOVER:ON	
	
	
	public static BCommandModeEnum calculateForCommandModeEnum(int val) {
		BCommandModeEnum working;
		int low = BCommandModeEnum.CMD_AUTO_MODE;
		int high = BCommandModeEnum.CMD_NUL_MODE;			
		if(val > BCommandModeEnum.CMD_EMERG_HEAT_MODE && val < BCommandModeEnum.CMD_NUL_MODE) {
			working = BCommandModeEnum.Cmd_Eff_Heat_Mode;
		}else if(val < low || val > high) {
			working = BCommandModeEnum.Cmd_Auto_Mode;
		}else {
			working = BCommandModeEnum.make(val);
		}
		return working;	
	}
	
	
	public static BSystemSwitchEnum calculateForSystemSwitchEnum(int val) {
		BSystemSwitchEnum working;
		int low = BSystemSwitchEnum.SS_AUTO;
		int high = BSystemSwitchEnum.SS_OFF;			
		if(val > BSystemSwitchEnum.SS_EMERG_HEAT && val < BSystemSwitchEnum.SS_OFF) {
			working = BSystemSwitchEnum.Ss_Eff_Heat_Mode;
		}else if(val < low || val > high) {
			working = BSystemSwitchEnum.Ss_Others;
		}else {
			working = BSystemSwitchEnum.make(val);
		}
		return working;	
	}
}
