/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BStatusTempSetpointCalcEnum;
import com.honeywell.honfunctionblocks.utils.LimitCheckUtil;

/**
 * This class handles all the conversions from StatusNumeric type to Temperature Setpoint Calc enum types
 * <AUTHOR> - Lavanya
 * @since Jun 26, 2018
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusNumeric", to="honeywellFunctionBlocks:StatusTempSetpointCalcEnum")
)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BStatusNumericToTempSetptCalcEnumConverter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusNumericToTempSetptCalcEnumConverter(2524508246)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToTempSetptCalcEnumConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context context) {
		BStatusNumeric statusNumeric = (BStatusNumeric)from;
		BStatusTempSetpointCalcEnum statusOccEnum = (BStatusTempSetpointCalcEnum)to;
      
      double sourceValue = statusNumeric.getValue();
      
      BFacets facets = statusOccEnum.getParent().getSlotFacets(statusOccEnum.getParent().getSlot(statusOccEnum.getName()));
      String typeName = facets.gets("range", null);
      
      BEnum working;
      
      if(BScheduledStateEnum.TYPE.getTypeSpec().toString().equals(typeName)) {    	  
      	working = calculateForScheduleStatusEnum(sourceValue);
      } else if(BOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
    	  working = calculateForOccupancyEnum(sourceValue);
      } else if(BEffectiveOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)){
    	  working = calculateForEffectiveOccupancyEnum(sourceValue);
      } else {
    	  working = BDynamicEnum.make((int)sourceValue);
      }
      statusOccEnum.setValue(working);
      
      statusOccEnum.setStatus(statusNumeric.getStatus());
      return statusOccEnum;
	}
	
	private BScheduledStateEnum calculateForScheduleStatusEnum(double d) {
		BScheduledStateEnum working;
		int low = BScheduledStateEnum.OCCUPIED;
		int high = BScheduledStateEnum.STANDBY;
		int val = (int) d;
		if(LimitCheckUtil.isInvalidValue(d) || val < low || val > high || val == BYPASS) 
			working = BScheduledStateEnum.Null;
		else		  
			working = BScheduledStateEnum.make(val);			
		return working;
	}
	
	
	private BEffectiveOccupancyEnum calculateForEffectiveOccupancyEnum(final double d) {
		BEffectiveOccupancyEnum working;
		if(LimitCheckUtil.isInvalidValue(d))
			working = BEffectiveOccupancyEnum.Occupied;
		else {
			working = OccupancyStateEnumsConverter.calculateForEffectiveOccupancyEnum((int) d);
		}
		
		return working;
	}
	
	private BOccupancyEnum calculateForOccupancyEnum(double d) {
		BOccupancyEnum working;
		int low = BOccupancyEnum.OCCUPIED;
		int high = BOccupancyEnum.STANDBY;
		int val = (int) d;
		if(LimitCheckUtil.isInvalidValue(d) || val < low || val > high) 
			working = BOccupancyEnum.Null;
		else		  
			working = BOccupancyEnum.make(val);			
		return working;
	}
		 
	private static final int BYPASS = 2;

}
