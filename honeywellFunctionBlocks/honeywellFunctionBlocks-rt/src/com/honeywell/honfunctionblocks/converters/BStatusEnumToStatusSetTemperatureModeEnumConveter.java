/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BStatusSetTemperatureModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum;

/**
 * Implementation Set Temperature Mode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 19, 2018
 */
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusEnum", to="honeywellFunctionBlocks:StatusSetTemperatureModeEnum")
)

public class BStatusEnumToStatusSetTemperatureModeEnumConveter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusSetTemperatureModeEnumConveter(3631928698)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusEnumToStatusSetTemperatureModeEnumConveter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
	@Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BStatusEnum statusEnum = (BStatusEnum)from;
		BStatusSetTemperatureModeEnum setTempModeEnum = (BStatusSetTemperatureModeEnum) to;

		int sourceValue = statusEnum.getValue().getOrdinal();

		BFacets facets = setTempModeEnum.getParent().getSlotFacets(setTempModeEnum.getParent().getSlot(setTempModeEnum.getName()));
		String typeName = facets.gets("range", null);

		if (BCommandModeEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
			BCommandModeEnum working = calculateForCommandModeEnum(sourceValue);
			setTempModeEnum.setValue(working);
		} 
		
		if (BSystemSwitchEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
			BSystemSwitchEnum working = calculateForSystemSwitchEnum(sourceValue);
			setTempModeEnum.setValue(working);
		}

		setTempModeEnum.setStatus(statusEnum.getStatus());
		return setTempModeEnum;
	}

	private BCommandModeEnum calculateForCommandModeEnum(double d) {
		BCommandModeEnum working;
		working = StatusSetTemperatureModeEnumsConverter.calculateForCommandModeEnum((int)d);
		return working;	
	}
	
	private BSystemSwitchEnum calculateForSystemSwitchEnum(double d) {
		BSystemSwitchEnum working;
		working = StatusSetTemperatureModeEnumsConverter.calculateForSystemSwitchEnum((int)d);
		return working;	
		
	}	
}
