/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusBoolean;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;

/**
 * StatusBoolean to HonStatusBoolean converter
 * <AUTHOR>
 *
 */

@NiagaraType(agent = @AgentOn(types = {
		"baja:ConversionLink" }, requiredPermissions = "w"), adapter = @Adapter(from = "baja:StatusBoolean", to = "honeywellFunctionBlocks:HonStatusBoolean"))
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S1213", "squid:S00103" })

public class BStatusBooleanToHonStatusBoolean extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusBooleanToHonStatusBoolean(1871600931)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusBooleanToHonStatusBoolean.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BStatusBoolean statusBoolean = (BStatusBoolean)from;
        BHonStatusBoolean honStatusBoolean = (BHonStatusBoolean)to;       
        
        honStatusBoolean.setValue(statusBoolean.getValue());        
        honStatusBoolean.setStatus(statusBoolean.getStatus());
        
        return statusBoolean;
	}

}
