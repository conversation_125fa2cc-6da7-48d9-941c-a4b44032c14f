/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

/**
 * <AUTHOR>
 * @since Aug 23, 2018
 */

@NiagaraType
(
  agent = @AgentOn
  (
    types = {"baja:ConversionLink"},
    requiredPermissions="w"
  ),
  adapter = @Adapter(from = "honeywellFunctionBlocks:NegatableStatusBoolean", to="honeywellFunctionBlocks:NegatableFiniteStatusBoolean")
)
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public class BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter(3886287655)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @Override
  public BObject convert(BObject from, BObject to, Context context) {
    BNegatableStatusBoolean negatableStatusBoolean = (BNegatableStatusBoolean)from;
    BNegatableFiniteStatusBoolean negatableFiniteStatusBoolean = (BNegatableFiniteStatusBoolean)to;
    negatableFiniteStatusBoolean.setValue(negatableStatusBoolean.getValue());
    negatableFiniteStatusBoolean.setStatus(negatableStatusBoolean.getStatus());
    negatableFiniteStatusBoolean.setIsInvalidValue(negatableStatusBoolean.getIsInvalidValue());
    return negatableFiniteStatusBoolean;
  }
}
