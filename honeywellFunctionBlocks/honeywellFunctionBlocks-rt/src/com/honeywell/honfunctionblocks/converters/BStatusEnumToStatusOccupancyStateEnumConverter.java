/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 * Implementation of OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Lavanya B.
 * @since Feb 8, 2018
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:StatusEnum", to="honeywellFunctionBlocks:StatusOccupancyStateEnum")
)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BStatusEnumToStatusOccupancyStateEnumConverter extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusOccupancyStateEnumConverter(835778020)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusEnumToStatusOccupancyStateEnumConverter.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context arg2) {
		BStatusEnum statusEnum = (BStatusEnum)from;
		BStatusOccupancyStateEnum statusOccEnum = (BStatusOccupancyStateEnum)to;
      
      int sourceValue = statusEnum.getValue().getOrdinal();
      
      BFacets facets = statusOccEnum.getParent().getSlotFacets(statusOccEnum.getParent().getSlot(statusOccEnum.getName()));
      String typeName = facets.gets("range", null);
      
      if(BScheduledStateEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
    	  BScheduledStateEnum working = calculateForScheduleStatusEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      } else if(BOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)) {
    	  BOccupancyEnum working = calculateForOccupancyEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      } else if(BEffectiveOccupancyEnum.TYPE.getTypeSpec().toString().equals(typeName)){
    	  BEffectiveOccupancyEnum working = calculateForEffectiveOccupancyEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      }else if(BOccupancySensorStateEnum.TYPE.getTypeSpec().toString().equals(typeName)){
    	  BOccupancySensorStateEnum working = calculateForOccupancySensorStateEnum(sourceValue);
    	  statusOccEnum.setValue(working);
      }
     
      statusOccEnum.setStatus(statusEnum.getStatus());
      return statusOccEnum;
	}
	
	private BEffectiveOccupancyEnum calculateForEffectiveOccupancyEnum(final double sourceValue) {
		return OccupancyStateEnumsConverter.calculateForEffectiveOccupancyEnum((int) sourceValue);
	}

	private BScheduledStateEnum calculateForScheduleStatusEnum(final double d) {
		return OccupancyStateEnumsConverter.calculateForScheduleStatusEnum((int)d);
	}
	
	private BOccupancyEnum calculateForOccupancyEnum(final double d) {
		return OccupancyStateEnumsConverter.calculateForOccupancyEnum((int)d);
	}
	
	private BOccupancySensorStateEnum calculateForOccupancySensorStateEnum(final double d) {
		return OccupancyStateEnumsConverter.calculateForOccupancySensorStateEnum((int)d);
	}
	
}
