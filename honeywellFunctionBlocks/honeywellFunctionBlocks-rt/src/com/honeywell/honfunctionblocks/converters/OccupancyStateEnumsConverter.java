/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 *
 * <AUTHOR> - <PERSON><PERSON>ya B.
 * @since Feb 9, 2018
 */
public final class OccupancyStateEnumsConverter {
	private static final int BYPASS = 2;
	
	///CLOVER:OFF
	private OccupancyStateEnumsConverter() {}
	///CLOVER:ON	
	
	public static BScheduledStateEnum calculateForScheduleStatusEnum(int val) {
		BScheduledStateEnum working;
			int low = BScheduledStateEnum.OCCUPIED;
			int high = BScheduledStateEnum.STANDBY;			
			if(val < low) {
				working = BScheduledStateEnum.Occupied;
			}else if(val > high || val == BYPASS) {
				working = BScheduledStateEnum.Null;
			}else {
				working = BScheduledStateEnum.make(val);
			}
		return working;	
	}
	
	public static BOccupancyEnum calculateForOccupancyEnum(int val) {
		BOccupancyEnum working;
		int low = BOccupancyEnum.OCCUPIED;
		int high = BOccupancyEnum.STANDBY;			
		if(val < low) {
			working = BOccupancyEnum.Occupied;
		}else if(val > high) {
			working = BOccupancyEnum.Null;
		}else {
			working = BOccupancyEnum.make(val);
		}
		return working;	
	}
	
	public static BOccupancySensorStateEnum calculateForOccupancySensorStateEnum(int val) {
		BOccupancySensorStateEnum working;	
		int low = BOccupancySensorStateEnum.OCCUPIED;
		int high = BOccupancySensorStateEnum.UNOCCUPIED;			
		if(val < low) {
			working = BOccupancySensorStateEnum.Occupied;
		}else if(val > high) {
			working = BOccupancySensorStateEnum.Null;
		}else {
			working = BOccupancySensorStateEnum.make(val);
		}
		return working;	
	}
	
	/**
	 * Used in TemperatureSetpointCalculator functional block
	 * If value < OCCUPIED (or) STANDBY < value; then set as OCCUPIED
	 * @param val
	 * @return
	 */
	public static BEffectiveOccupancyEnum calculateForEffectiveOccupancyEnum(int val) {
		BEffectiveOccupancyEnum working;
		
		if(val < BEffectiveOccupancyEnum.OCCUPIED || BEffectiveOccupancyEnum.STANDBY < val) {
			working = BEffectiveOccupancyEnum.Occupied;
		}else {
			working = BEffectiveOccupancyEnum.make(val);
		}
		
		return working;	
	}

}
