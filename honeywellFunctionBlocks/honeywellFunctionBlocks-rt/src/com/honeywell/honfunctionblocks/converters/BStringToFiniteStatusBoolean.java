/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters;

import java.util.logging.Level;

import javax.baja.agent.BIAgent;
import javax.baja.nre.annotations.Adapter;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BObject;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BConverter;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLogger;


/**
 * <AUTHOR>
 *
 */

@NiagaraType
(
		agent = @AgentOn
		(
				types = {"baja:ConversionLink"},
				requiredPermissions="w"
		),
		adapter = @Adapter(from = "baja:String", to="honeywellFunctionBlocks:FiniteStatusBoolean")
		
)


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStringToFiniteStatusBoolean extends BConverter implements BIAgent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.BStringToFiniteStatusBoolean(3340237766)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStringToFiniteStatusBoolean.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see javax.baja.util.BConverter#convert(javax.baja.sys.BObject, javax.baja.sys.BObject, javax.baja.sys.Context)
	 */
	@Override
	public BObject convert(BObject from, BObject to, Context cx) {
		BString string = (BString)from;
        BFiniteStatusBoolean statusFiniteBoolean = (BFiniteStatusBoolean)to;
        
        String s = string.getString();        
        statusFiniteBoolean = convertToFiniteStatusBoolean(s,statusFiniteBoolean);
        statusFiniteBoolean.setStatus(BStatus.ok);
        return statusFiniteBoolean;
	}
	
	protected BFiniteStatusBoolean convertToFiniteStatusBoolean(String s,BFiniteStatusBoolean statusFiniteBoolean) {
        if("true".equalsIgnoreCase(s) || "-inf".equalsIgnoreCase(s) )
        	statusFiniteBoolean.setValue(true);
      else if("false".equalsIgnoreCase(s) || "+inf".equalsIgnoreCase(s) || "nan".equalsIgnoreCase(s)) {
        	statusFiniteBoolean.setValue(false);
        }else {
        	boolean b = handleNumericValue(s);
        	statusFiniteBoolean.setValue(b);
        } 
       return statusFiniteBoolean;		
	}
	
	
	private boolean handleNumericValue(String s) {
		boolean b = false;
    	try {
    		Double d = Double.valueOf(s);
            //       (+inf check)                            (zero check)            (nan check)
            if((Double.isInfinite(d) && d > 0.0) || Double.compare(d, 0.0) == 0 || Double.isNaN(d))
            	b = false;
            else
            	b = true;        		
    	}catch(Exception e) {
    		FunctionBlocksLogger.getLogger().log(Level.SEVERE,
						FunctionBlocksLexicon.getLexicon().get("invalid.string.to.double.conversion.0", e.getMessage()), e);
    	}
    	
    	return b;		
	}

}
