/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils;

import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BValue;
import javax.baja.sys.Property;

/**
 * This class can be used as util class to hold static methods to check for valid values
 * <AUTHOR> - Lavanya B.
 * @since Feb 15, 2018
 */


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public final class LimitCheckUtil {
	///CLOVER:OFF
	private LimitCheckUtil() {}
	///CLOVER:ON
	
	
	public static boolean isInvalidValue(double sourceValue) {
		return Double.isNaN(sourceValue) || (Double.isInfinite(sourceValue) && sourceValue > 0.0);
	}
	
	/**
	 * Check whether the given value is valid or not
	 * @param component
	 * @param property
	 * @return
	 */
	public static boolean isInvalidValue(final BComponent component, final Property property) {
		boolean invalid = false;
		BValue val = component.get(property);
		if (val.getType().is(BStatusNumeric.TYPE)) {
			BFacets facets = component.getSlotFacets(property);
			float min = facets.getf(BFacets.MIN, Float.MIN_VALUE);
			float max = facets.getf(BFacets.MAX, Float.MAX_VALUE);
			BStatusNumeric value = (BStatusNumeric) val;
			invalid = Double.isNaN(value.getValue()) || (value.getValue() < min) || (value.getValue() > max);
		}
		return invalid;
	}

}
