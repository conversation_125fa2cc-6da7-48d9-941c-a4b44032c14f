/*
 * Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 * This file contains trade secrets of Honeywell International, Inc. No part may be reproduced or
 * transmitted in any form by any means or for any purpose without the express written permission of
 * Honeywell.
 */

package com.honeywell.honfunctionblocks.utils;

import java.util.logging.Level;

import javax.baja.license.FeatureNotLicensedException;
import javax.baja.sys.Sys;

/**
 * A utility class for checking licensing.
 */
public final class LicenseHandler {
	private static final String HONEYWELL = "Honeywell";
	private static final String HONEYWELL_FB = "honeywellFunctionBlock";
	
	private static boolean isHoneywellFb = false;
	
	static {
		//check if Honeywell Function Block Engine feature licensed
		try {
			//TODO: enable the license check once the new feature name is finalized and added to the license
//			Sys.getLicenseManager().checkFeature(HONEYWELL, HONEYWELL_FB);
			isHoneywellFb = true;
		} catch (FeatureNotLicensedException e) {
			FunctionBlocksLogger.getLogger().log(Level.SEVERE, FunctionBlocksLexicon.getLexicon().getText("hfb.feature.not.licensed"), e);
			isHoneywellFb = true;
		}
	}

	private LicenseHandler() {
		// All static methods, need not instantiate
	}
	
	public static boolean isHoneywellFbLicensed() {
		return isHoneywellFb;
	}

}
