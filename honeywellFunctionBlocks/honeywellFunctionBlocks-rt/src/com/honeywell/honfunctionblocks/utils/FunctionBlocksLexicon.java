/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils;

import javax.baja.util.Lexicon;

/**
 * <AUTHOR> - RSH<PERSON>
 * @since Nov 27, 2017
 */
public final class FunctionBlocksLexicon {
	private static final Lexicon LEXICON = Lexicon.make("honeywellFunctionBlocks");

	///CLOVER:OFF
	/**
	 * Default Constructor
	 */
	private FunctionBlocksLexicon() {
		//EMPTY IMPLEMENTATION
	}
	///CLOVER:ON
	
	public static Lexicon getLexicon(){
		return LEXICON;
	}

}
