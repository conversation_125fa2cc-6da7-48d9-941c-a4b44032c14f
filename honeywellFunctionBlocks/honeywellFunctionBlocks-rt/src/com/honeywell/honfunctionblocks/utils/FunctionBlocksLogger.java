/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils;

import java.util.logging.Logger;

/**
 * Logger class
 * <AUTHOR> - RSH.<PERSON>
 *
 */

public final class FunctionBlocksLogger {	
	private static final Logger LOG = Logger.getLogger("honeywellFunctionBlocks");
	
	///CLOVER:OFF
	private FunctionBlocksLogger() {
		
	}
	///CLOVER:ON

	public static Logger getLogger() {
		return LOG;
	}

}
