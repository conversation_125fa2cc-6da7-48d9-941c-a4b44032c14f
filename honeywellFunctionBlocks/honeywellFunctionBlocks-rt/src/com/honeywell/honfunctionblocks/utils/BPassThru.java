/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.utils;

import java.util.List;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.platform.RuntimeProfile;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.versionmanager.BHonVersion;

/**
 * Implementation of PassThru block as per FB SDD rev26
 * Requirement ID: ??
 * <AUTHOR> - Suresh Vemuri
 * @since Feb 23, 2018
 */
@NiagaraType
@NiagaraProperty(name="toolVersion", type="BHonVersion", defaultValue="BHonVersion.NULL", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name="icon", type="baja:Icon", defaultValue="BIcon.make(ResourceConstants.ICON_DIR + \"pass_thru_block.png\")", flags=Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "in", type = "BStatusNumeric", defaultValue = "new BStatusNumeric(0.0, BStatus.nullStatus)", flags = Flags.SUMMARY|Flags.TRANSIENT)
@NiagaraProperty(name = "OUT", type = "BStatusNumeric", defaultValue = "new BStatusNumeric(0.0, BStatus.ok)", flags = Flags.SUMMARY|Flags.READONLY|Flags.TRANSIENT)

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })

public class BPassThru extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.utils.BPassThru(1223748177)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "toolVersion"

  /**
   * Slot for the {@code toolVersion} property.
   * @see #getToolVersion
   * @see #setToolVersion
   */
  public static final Property toolVersion = newProperty(Flags.HIDDEN | Flags.READONLY, BHonVersion.NULL, null);

  /**
   * Get the {@code toolVersion} property.
   * @see #toolVersion
   */
  @Override
  public BHonVersion getToolVersion() { return (BHonVersion)get(toolVersion); }

  /**
   * Set the {@code toolVersion} property.
   * @see #toolVersion
   */
  @Override
  public void setToolVersion(BHonVersion v) { set(toolVersion, v, null); }

  //endregion Property "toolVersion"

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.READONLY, BIcon.make(ResourceConstants.ICON_DIR + "pass_thru_block.png"), null);

  /**
   * Get the {@code icon} property.
   * @see #icon
   */
  @Override
  public BIcon getIcon() { return (BIcon)get(icon); }

  /**
   * Set the {@code icon} property.
   * @see #icon
   */
  public void setIcon(BIcon v) { set(icon, v, null); }

  //endregion Property "icon"

  //region Property "in"

  /**
   * Slot for the {@code in} property.
   * @see #getIn
   * @see #setIn
   */
  public static final Property in = newProperty(Flags.SUMMARY | Flags.TRANSIENT, new BStatusNumeric(0.0, BStatus.nullStatus), null);

  /**
   * Get the {@code in} property.
   * @see #in
   */
  public BStatusNumeric getIn() { return (BStatusNumeric)get(in); }

  /**
   * Set the {@code in} property.
   * @see #in
   */
  public void setIn(BStatusNumeric v) { set(in, v, null); }

  //endregion Property "in"

  //region Property "OUT"

  /**
   * Slot for the {@code OUT} property.
   * @see #getOUT
   * @see #setOUT
   */
  public static final Property OUT = newProperty(Flags.SUMMARY | Flags.READONLY | Flags.TRANSIENT, new BStatusNumeric(0.0, BStatus.ok), null);

  /**
   * Get the {@code OUT} property.
   * @see #OUT
   */
  public BStatusNumeric getOUT() { return (BStatusNumeric)get(OUT); }

  /**
   * Set the {@code OUT} property.
   * @see #OUT
   */
  public void setOUT(BStatusNumeric v) { set(OUT, v, null); }

  //endregion Property "OUT"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPassThru.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/*
	 * (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		//do nothing, to make PassThru also a Honeywell component, we are inheriting from BFunctionBlock so from
		//F1 deterministic folder we can execute all the blocks and reference by IHoneywellComponent
	}

	/*
	 * (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.BFunctionBlock#initHoneywellComponent(com.honeywell* honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void initHoneywellComponent(BExecutionParams executionParams) throws BlockInitializationException {
		//do nothing, to make PassThru also a Honeywell component, we are inheriting from BFunctionBlock so from
		//F1 deterministic folder we can execute all the blocks and reference by IHoneywellComponent
	}

	@Override
	public void started() throws Exception {
		super.started();
		if(null != this.get(FBSlotConstants.OVERRIDE))
			remove(FBSlotConstants.OVERRIDE);
		setFlags(Auto, Flags.HIDDEN);
		setFlags(OverrideExpiration, Flags.HIDDEN);	
	}
	
	@Override
	public void descendantsStarted() throws Exception {
		super.descendantsStarted();
		BHonVersion moduleVersion = BHonVersion.getModuleVersion(TYPE, RuntimeProfile.rt);
		try {
			if(BHonVersion.isNull(getToolVersion())) {
				//Version number is NULL. DO NOT MIGRATE COMPONENT
				return;
			}
		
			if(this.getToolVersion().compareToolVersion(moduleVersion) < 0) {
				//HANDLE MIGRATION
			}
		} finally {
			this.setToolVersion(moduleVersion);
		}
	}
	
	@Override
	public void atSteadyState() throws Exception {
		super.atSteadyState();
		getOUT().setValue(getIn().getValue());
		getOUT().setStatus(getIn().getStatus());	
	}

	@Override
	public void checkRemove(Property property, Context context) {
		//doNothing as we want to remove Override on Passthru
	}

	@Override
	public void doAuto() {
		//empty implementation to avoid problems with user unhiding auto action
	}

	@Override
	public void changed(Property prop, Context ctx) {
		super.changed(prop, ctx);
		if (!Sys.atSteadyState()) {
			return;
		}
		if(prop == in) {
			getOUT().setValue(getIn().getValue());
			getOUT().setStatus(getIn().getStatus());
		}
	}
	
	@Override
	public List<Property> getOutputPropertiesList() {
		List<Property> list=super.getOutputPropertiesList();
		list.add(OUT);
		return list;
	}
	
	@Override
	public List<Property> getInputPropertiesList() {
		List<Property> list=super.getInputPropertiesList();
		list.add(in);
		return list;
	}
}
