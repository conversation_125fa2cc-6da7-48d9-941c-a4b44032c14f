/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Custom BStatusBoolean to have custom field editor 
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.<PERSON><PERSON>ayanan
 * @since Nov 24, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213"})

public class BHonStatusBoolean extends BStatusBoolean{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean(2979906276)1.0$ @*/
/* Generated Thu Feb 15 17:35:22 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusBoolean.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	public BHonStatusBoolean() {
		super();
	}
  
	public BHonStatusBoolean(boolean value, BStatus status) {
  		super(value, status);
  	}
  
}
