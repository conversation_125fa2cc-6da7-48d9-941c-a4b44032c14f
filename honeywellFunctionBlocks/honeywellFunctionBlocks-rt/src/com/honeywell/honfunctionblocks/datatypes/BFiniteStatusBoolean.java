/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Custom type to help convert the source values to the BOOLEAN as needed by F1 DDC engine
 * Different converter will be written to check this TYPE as the target type and convert 
 * the source value to target boolean value as per the rule given below
 * Value conversion rules
 * IF VAL == 0.0	OR VAL == invalid (nan or +inf) 	-- VALUE = 0
 * IF VAL != 0.0 OR VAL == -inf							-- VALUE = 1
 *  
 * <AUTHOR> - Ravi <PERSON> .K
 * @since Nov 22, 2017
 */

@NiagaraType
@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S2160","squid:S1213"})

public class BFiniteStatusBoolean extends BStatusBoolean {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean(2979906276)1.0$ @*/
/* Generated Wed Nov 22 14:13:00 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFiniteStatusBoolean.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/ 
    	
  	
  	public BFiniteStatusBoolean(boolean value, BStatus status) {
  		super(value, status);
  	}

	public BFiniteStatusBoolean() {
		super();
	}

	public BFiniteStatusBoolean(boolean value) {
		super(value);
	}
  
	
}
