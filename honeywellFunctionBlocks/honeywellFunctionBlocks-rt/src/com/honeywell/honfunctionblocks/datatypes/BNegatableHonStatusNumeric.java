/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.sys.BFacets;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * BHonStatusNumeric with Negate option
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>nan
 * @since Nov 24, 2017
 */
@NiagaraType

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213"})

public final class BNegatableHonStatusNumeric extends BHonStatusNumeric implements INegatableStatusValue{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric(2979906276)1.0$ @*/
/* Generated Tue May 29 09:56:33 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableHonStatusNumeric.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  /**
   * Default Constructor
   */
  public BNegatableHonStatusNumeric() {
	  //EMPTY IMPLEMENTATION
  }

  /**
   * Constructor to initialize BStatusNumeric value with negate option
   * @param value double value for BStatusNumeric
   * @param status BStatus
   * @param negate boolean
   */
  public BNegatableHonStatusNumeric(double value, BStatus status, boolean negate) {
	  super(value, status);
	  this.setNegate(negate);
  }

  @Override
  public String toString(Context context) {
	  return (getNegate()?
			  (FunctionBlocksLexicon.getLexicon().getText("negatable.not", new String[] {}) + " "):"") + super.toString(context);
  }
  
  	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue#getNegate()
	 */
	@Override
	public boolean getNegate() {
		return getSlotFacets(BStatusBoolean.value).getb(FBSlotConstants.NEGATE, false);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue#setNegate(boolean)
	 */
	@Override
	public void setNegate(boolean negate) {
		this.setFacets(BStatusBoolean.value, BFacets.make(FBSlotConstants.NEGATE, negate));
	}

}
