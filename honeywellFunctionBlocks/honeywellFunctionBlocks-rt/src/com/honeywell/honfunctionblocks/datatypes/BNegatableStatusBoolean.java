/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BFacets;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * BStatusBoolean with Negate option
 * Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - RSH.<PERSON>
 * @since Nov 24, 2017
 */
@NiagaraType
@NiagaraProperty(name="isInvalidValue", type="boolean", defaultValue="false", flags=Flags.READONLY|Flags.HIDDEN)

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213"})

public final class BNegatableStatusBoolean extends BHonStatusBoolean implements INegatableStatusValue{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean(1661308957)1.0$ @*/
/* Generated Mon Aug 25 20:14:39 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "isInvalidValue"

  /**
   * Slot for the {@code isInvalidValue} property.
   * @see #getIsInvalidValue
   * @see #setIsInvalidValue
   */
  public static final Property isInvalidValue = newProperty(Flags.READONLY | Flags.HIDDEN, false, null);

  /**
   * Get the {@code isInvalidValue} property.
   * @see #isInvalidValue
   */
  public boolean getIsInvalidValue() { return getBoolean(isInvalidValue); }

  /**
   * Set the {@code isInvalidValue} property.
   * @see #isInvalidValue
   */
  public void setIsInvalidValue(boolean v) { setBoolean(isInvalidValue, v, null); }

  //endregion Property "isInvalidValue"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusBoolean.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/**
	 * Default Constructor
	 */
	public BNegatableStatusBoolean() {
		//EMPTY IMPLEMENTATION
	}
	
	/**
	 * Constructor to initialize BStatusBoolean value with negate option
	 * @param value boolean value for BStatusBoolean
	 * @param status BStatus
	 * @param negate boolean
	 */
	public BNegatableStatusBoolean(boolean value, BStatus status, boolean negate) {
		super(value, status);
		this.setNegate(negate);
	}

	@Override
	public String toString(Context context) {
		return (getNegate()?
				(FunctionBlocksLexicon.getLexicon().getText("negatable.not", new String[] {}) + " "):"") + super.toString(context);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue#getNegate()
	 */
	@Override
	public boolean getNegate() {
		return getSlotFacets(value).getb(FBSlotConstants.NEGATE, false);
	}

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue#setNegate(boolean)
	 */
	@Override
	public void setNegate(boolean negate) {
		this.setFacets(value, BFacets.make(FBSlotConstants.NEGATE, negate));
	}
}
