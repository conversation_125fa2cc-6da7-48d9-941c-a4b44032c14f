/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * Implementation HonStatusEnum of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 7, 2018
 */
@NiagaraType
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BHonStatusEnum extends BStatusEnum{
/*+ ------------ <PERSON>EG<PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum(2979906276)1.0$ @*/
/* Generated Wed Feb 07 20:28:24 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BHonStatusEnum() {
	  super();
  }

  public BHonStatusEnum(BEnum value) {
	  super(value);
  }

  public BHonStatusEnum(BEnum value, BStatus status) {
	  super(value, status);
  }
  

}
