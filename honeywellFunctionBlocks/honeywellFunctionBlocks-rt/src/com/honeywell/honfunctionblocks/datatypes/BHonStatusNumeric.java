/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BFacets;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * BStatusNumeric with precision applied on calling getValue()
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>
 * @since Dec 26, 2017
 */

@NiagaraType

@SuppressWarnings({"squid:S2387","squid:MaximumInheritanceDepth","squid:S1213"})

public class BHonStatusNumeric extends BStatusNumeric {
/*+ ------------ <PERSON>EG<PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric(2979906276)1.0$ @*/
/* Generated Thu Dec 28 12:44:06 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumeric.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BHonStatusNumeric() {
	  super();
  }

  public BHonStatusNumeric(double value) {
	  super(value);
  }

  public BHonStatusNumeric(double value, BStatus status) {
	  super(value, status);
  }
  
  @Override
  public double getValue() {
	  try {
		  BigDecimal bigDecimal = BigDecimal.valueOf(super.getValue())
				  .setScale(getStatusValueFacets().geti(BFacets.PRECISION, DEFAULT_PRECISION), RoundingMode.HALF_UP);
		  return bigDecimal.doubleValue();
	  } catch (NumberFormatException nfe) {
		  //Handle when user enters other than Number like String, SpecialChar etc...
		  return super.getValue();
	  }
  }

  public static final int DEFAULT_PRECISION = 6;
}
