/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes;

/**
 * Interface to declare mandatory methods for any StatusValue to be Negated
 * <AUTHOR> - RSH.<PERSON><PERSON>
 * @since Nov 27, 2017
 */
public interface INegatableStatusValue {
	boolean getNegate();
	void setNegate(boolean negate);
}
