/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine;

import java.util.logging.Logger;

import javax.baja.sys.BInterface;
import javax.baja.util.Queue;

/**
 * Represents the root folder of Honeywell Function Blocks that Honeywell function block engine can
 * execute all the function blocks in this folder in a sequential order
 * 
 * <AUTHOR> - <PERSON> .K
 * @since 25-Aug-2025
 */
public interface BIHoneywellFunctionBlockEngine extends BInterface {
	public static final Logger ENGINE_LOGGER = Logger.getLogger("honeywellFunctionBlock.engine");
	
	/**
	 * return engine execution IterationInterval
	 */
	public int getIterationInterval();
	
	
	/**
	 * Queue to be shared by engine thread and metrics thread to calculate the performance
	 * of the every execution and log the details
	 *
	 * @since 25-Aug-2025
	 * @return {@link Queue}
	 */
	public Queue getSharedQueue();

	/**
	 * Returns Function block execution cycle start time in millis
	 *
	 * @since 25-Aug-2025
	 * @return {@link int}
	 */
	public int getCycleStartTime();
	
	/**
	 * To update the cycle start time in the Engine root component
	 *
	 * @since 25-Aug-2025
	 * @param v
	 */
	public void setCycleStartTime(int v);
	
	/**
	 * To get the time take to execute the last cycle
	 *
	 * @since 25-Aug-2025
	 * @return {@link long}
	 */
	public long getLastExecutionTime();

	/**
	 * To update the time take to execute the last cycle
	 *
	 * @since 25-Aug-2025
	 * @param v
	 */
	public void setLastExecutionTime(long v);
	
	/**
	 * To get how much time execution has missed completing one execution cycle
	 * based on cycle time configured in the engine root component
	 *
	 * @since 25-Aug-2025
	 * @return {@link int}
	 */
	public int getPerformanceMissCount();
	
	/**
	 * To update counter how much time execution has missed completing one execution cycle
	 * based on cycle time configured in the engine root component
	 *
	 * @since 25-Aug-2025
	 * @param v
	 * @return {@link void}
	 */
	public void setPerformanceMissCount(int v);
	
	/**
	 * To track from what time cycle time started deviating
	 *
	 * @since 25-Aug-2025
	 * @param v
	 * @return {@link void}
	 */
	public void setLastDeviationInCycleStartTime(int v);

	/**
	 * To update the average time to execute all the cycles till last cycle excution
	 *
	 * @since 25-Aug-2025
	 * @param v
	 * @return {@link void}
	 */
	public void setAverageExecutionTime(double v);
	
	/**
	 * To get if engine is running or not
	 *
	 * @since 25-Aug-2025
	 * @return {@link boolean}
	 */
	public boolean isEngineRunning();

}
