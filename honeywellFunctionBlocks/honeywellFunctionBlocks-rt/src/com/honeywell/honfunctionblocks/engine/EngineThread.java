/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine;

import java.util.logging.Level;

import javax.baja.sys.BComponent;
import javax.baja.sys.Clock;
import javax.baja.util.Queue;

import static com.honeywell.honfunctionblocks.engine.BIHoneywellFunctionBlockEngine.ENGINE_LOGGER;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.IHoneywellComponent;
import com.honeywell.honfunctionblocks.fbs.IHoneywellExecutionBlock;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;


/**
 * Engine Thread that executes the IHoneywellComponent components
 * 
 * <AUTHOR>
 */
@SuppressWarnings({ "squid:S00103" })
public class EngineThread extends Thread {
	public static final int DDC_ENGINE_THREAD_PRIORITY = 7;
	private BIHoneywellFunctionBlockEngine engine = null;
	private boolean stopThread = false;
	private Queue sharedQueue;
	
	public EngineThread(BIHoneywellFunctionBlockEngine engine){
		setDaemon(true);
		setPriority(DDC_ENGINE_THREAD_PRIORITY);
		setName("Honeywell Function Block Engine");
		this.engine = engine;	
		this.sharedQueue = engine.getSharedQueue();
	}
	
	public boolean isStopThread() {
		return stopThread;
	}

	public void setStopThread(boolean stopThread) {
		this.stopThread = stopThread;
	}

	@Override
	public void run() {
		ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("engine.thread.starting"));
		
		int iterationInterval = engine.getIterationInterval();
		
		BExecutionParams executionParams = new BExecutionParams();
		executionParams.setIterationInterval(iterationInterval);
		
		initializeFunctionBlocks(engine,executionParams);
		
		while(true) {
			if(isStopThread()) {
				break;
			}
			
			long t1 = Clock.millis();
			executeFunctionBlocks(engine, executionParams);
			long t2 = Clock.millis();
			
			
			long executionTime = t2-t1;//in ms
			sharedQueue.enqueue(new ExecutionCycleTimeDetail(t1, executionTime));
			
			long sleepTime = iterationInterval-executionTime;
			if(sleepTime > 0) {
				try {
					Thread.sleep(sleepTime);
				} catch (InterruptedException e) {
					ENGINE_LOGGER.log(Level.SEVERE, e.getMessage(),e);
					Thread.currentThread().interrupt();
				}
			}
		}
		
		ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("engine.thread.exiting.gracefully"));
	}
	
	/**
	 * Initialize the function blocks
	 *
	 * @since 25-Aug-2025
	 * @param rootFolder
	 * @param executionParams
	 * @return {@link void}
	 */
	private void initializeFunctionBlocks(BIHoneywellFunctionBlockEngine rootFolder, BExecutionParams executionParams) {
		if(!(rootFolder instanceof BComponent)) {
			
			return;
		}
		
		IHoneywellExecutionBlock[] hwBlocks = ((BComponent) rootFolder).getChildren(IHoneywellExecutionBlock.class);
		for (int i = 0; i < hwBlocks.length; i++) {
			IHoneywellExecutionBlock hwBlock = hwBlocks[i];
			if(hwBlock instanceof IHoneywellComponent) {
				try {
					((IHoneywellComponent)hwBlock).initHoneywellComponent(executionParams);
				} catch (Exception e) {
					ENGINE_LOGGER.log(Level.SEVERE, e.getMessage(),e);
				}
			}else if(hwBlock instanceof BIHoneywellFunctionBlockEngine) {
				initializeFunctionBlocks((BIHoneywellFunctionBlockEngine)hwBlock,executionParams);
			}
		}			
	}
	
	/**
	 * Execute all the Honeywell function block inside the root folder that is instance of BIHoneywellFunctionBlockEngine
	 * 
	 * @since 27-Aug-2025
	 * @param rootFolder
	 * @param executionParams
	 * @return {@link void}
	 */
	private void executeFunctionBlocks(BIHoneywellFunctionBlockEngine rootFolder, BExecutionParams executionParams) {
		if(!(rootFolder instanceof BComponent)) {
			return;
		}
		
		IHoneywellExecutionBlock[] hwBlocks = ((BComponent) rootFolder).getChildren(IHoneywellExecutionBlock.class);
		for (int i = 0; i < hwBlocks.length; i++) {
			IHoneywellExecutionBlock hwBlock = hwBlocks[i];
			if (hwBlock instanceof IHoneywellComponent) {
				try {
					if (!((IHoneywellComponent) hwBlock).isOutputPropertiesOverridden()) 
					{
						((IHoneywellComponent) hwBlock).executeHoneywellComponent(executionParams);
					}
					
				} catch (Exception e) {
					ENGINE_LOGGER.log(Level.SEVERE, e.getMessage(), e);
				}
			} else if (hwBlock instanceof BIHoneywellFunctionBlockEngine) {
				executeFunctionBlocks((BIHoneywellFunctionBlockEngine) hwBlock, executionParams);
			}
		}
	}

	
	

}
