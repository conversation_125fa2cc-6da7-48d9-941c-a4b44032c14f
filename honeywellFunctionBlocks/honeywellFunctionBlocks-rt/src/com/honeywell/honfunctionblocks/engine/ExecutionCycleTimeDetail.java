/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine;

/**
 * Bean class which is used to pass data between EngineThread & MetricsThread 
 * to calculate performance details
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>nan
 * @since Jan 5, 2018
 */
public class ExecutionCycleTimeDetail {
	private long cycleStartTime;
	private long logicExecutionTime;
	
	/**
	 * 
	 */
	public ExecutionCycleTimeDetail(final long cycleStartTime, final long logicExecutionTime) {
		this.cycleStartTime = cycleStartTime;
		this.logicExecutionTime = logicExecutionTime;
	}
	
	public long getCycleStartTime() {
		return cycleStartTime;
	}

	public long getLogicExecutionTime() {
		return logicExecutionTime;
	}
}
