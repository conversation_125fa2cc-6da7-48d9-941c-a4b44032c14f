/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine;

import static com.honeywell.honfunctionblocks.engine.BIHoneywellFunctionBlockEngine.ENGINE_LOGGER;

import java.util.logging.Level;

import javax.baja.util.Queue;

import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;

/**
 * Metrics Thread that updates performance measures of DDC Engine
 * 
 * <AUTHOR> - RSH.<PERSON>nan
 * @since Jan 5, 2018
 */
@SuppressWarnings({ "squid:S00103" })
public class MetricsThread extends Thread {
	private BIHoneywellFunctionBlockEngine engine = null;
	private boolean stopThread = false;
	private Queue sharedQueue;
	private static final int FIVE_HUNDRED_MILLIS = 500;

	public MetricsThread(BIHoneywellFunctionBlockEngine engine) {
		setDaemon(true);
		setPriority(NORM_PRIORITY);
		setName("DDCEngine Metrics Thread");
		this.engine = engine;
		this.sharedQueue = engine.getSharedQueue();
	}

	public boolean isStopThread() {
		return stopThread;
	}

	public void setStopThread(boolean stopThread) {
		this.stopThread = stopThread;
	}

	@Override
	public void run() {
		ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("metrics.thread.starting"));

		int iterationInterval = engine.getIterationInterval();
		double totalExecutionTime = 0;
		double totalExecutionCount = 0;

		while (true) {
			if (isEngineStopped()) {
				break;
			}

			ExecutionCycleTimeDetail take = (ExecutionCycleTimeDetail) sharedQueue.dequeue();

			if (take != null) {
				int lastCycleStartTime = engine.getCycleStartTime();
				engine.setCycleStartTime((int) (take.getCycleStartTime() % iterationInterval));
				engine.setLastExecutionTime(take.getLogicExecutionTime());
				if (take.getLogicExecutionTime() > iterationInterval) {
					engine.setPerformanceMissCount(engine.getPerformanceMissCount() + 1);
				}
				engine.setLastDeviationInCycleStartTime(engine.getCycleStartTime() - lastCycleStartTime);

				totalExecutionTime += engine.getLastExecutionTime();
				totalExecutionCount++;
				engine.setAverageExecutionTime(totalExecutionTime / totalExecutionCount);
			}

			// Test code start
			// Metrics thread not a high priority thread, so putting metrics
			// thread to sleep for 500 ms
			// to give others thread more time for execution.
			try {
				Thread.sleep(FIVE_HUNDRED_MILLIS);
			} catch (InterruptedException e) {
				ENGINE_LOGGER.log(Level.SEVERE, e.getMessage(), e);
				Thread.currentThread().interrupt();
			}
			// test code end
		}

		ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("metrics.thread.exiting.gracefully"));
	}

	private boolean isEngineStopped() {
		if (isStopThread()) {
			ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("engine.stopped.by.user.stop.metrics"));
			return true;
		}

		if (!engine.isEngineRunning()) {
			ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("engine.not.running.stop.metrics"));
			return true;
		}

		return false;
	}

}
