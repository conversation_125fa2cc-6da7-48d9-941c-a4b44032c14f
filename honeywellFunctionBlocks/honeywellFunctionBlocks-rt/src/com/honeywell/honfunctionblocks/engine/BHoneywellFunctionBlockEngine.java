/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine;

import java.lang.Thread.State;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

import javax.baja.agent.AgentInfo;
import javax.baja.agent.AgentList;
import javax.baja.collection.BITable;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BInteger;
import javax.baja.sys.BObject;
import javax.baja.sys.Context;
import javax.baja.sys.Cursor;
import javax.baja.sys.Flags;
import javax.baja.sys.LocalizableRuntimeException;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;
import javax.baja.util.Queue;

import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.utils.FunctionBlocksLexicon;
import com.honeywell.honfunctionblocks.utils.LicenseHandler;

/**
 * Root folder of Honeywell Function Blocks that Honeywell function block engine can
 * execute all the function blocks in this folder in a sequential order 
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since 25-Aug-2025
 */

@NiagaraType
@NiagaraProperty(name = "startEngineAtStartup", type = "boolean", defaultValue = "true", flags = Flags.SUMMARY)
@NiagaraProperty(name = "engineStopped", type = "boolean", defaultValue = "false", flags = Flags.SUMMARY | Flags.READONLY)
@NiagaraProperty(name = "iterationInterval", type = "int", defaultValue = "UnitConstants.THOUSAND_MILLI_SECOND", flags = Flags.SUMMARY, 
	facets = {
		@Facet(name = "BFacets.MIN", value = "BInteger.make(1)"), 
		@Facet(name = "BFacets.MAX", value = "BInteger.make(10)"),
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.SECOND)"), 
		@Facet(name = "BFacets.PRECISION", value = "0") 
	})
@NiagaraProperty(name="lastExecutionTime", type="long", defaultValue="0", flags=Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY,
	facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MILLI_SECOND)")
	})
@NiagaraProperty(name="averageExecutionTime", type="double", defaultValue="0", flags=Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY,
	facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MILLI_SECOND)")
	})
@NiagaraProperty(name="performanceMissCount", type="int", defaultValue="0", flags=Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY)
@NiagaraProperty(name="cycleStartTime", type="int", defaultValue="0", flags=Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY,
	facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MILLI_SECOND)")
	})
@NiagaraProperty(name="lastDeviationInCycleStartTime", type="int", defaultValue="0", flags=Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY,
	facets = { 
		@Facet(name = "BFacets.UNITS", value = "BUnit.getUnit(UnitConstants.MILLI_SECOND)")
	})

@NiagaraAction(name="requestFunctionBlockEngineStop", flags=Flags.ASYNC)
@NiagaraAction(name="startFunctionBlockEngine")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public class BHoneywellFunctionBlockEngine extends BComponent implements BIHoneywellFunctionBlockEngine {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.engine.BHoneywellFunctionBlockEngine(1660463138)1.0$ @*/
/* Generated Wed Sep 03 21:20:07 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "startEngineAtStartup"

  /**
   * Slot for the {@code startEngineAtStartup} property.
   * @see #getStartEngineAtStartup
   * @see #setStartEngineAtStartup
   */
  public static final Property startEngineAtStartup = newProperty(Flags.SUMMARY, true, null);

  /**
   * Get the {@code startEngineAtStartup} property.
   * @see #startEngineAtStartup
   */
  public boolean getStartEngineAtStartup() { return getBoolean(startEngineAtStartup); }

  /**
   * Set the {@code startEngineAtStartup} property.
   * @see #startEngineAtStartup
   */
  public void setStartEngineAtStartup(boolean v) { setBoolean(startEngineAtStartup, v, null); }

  //endregion Property "startEngineAtStartup"

  //region Property "engineStopped"

  /**
   * Slot for the {@code engineStopped} property.
   * @see #getEngineStopped
   * @see #setEngineStopped
   */
  public static final Property engineStopped = newProperty(Flags.SUMMARY | Flags.READONLY, false, null);

  /**
   * Get the {@code engineStopped} property.
   * @see #engineStopped
   */
  public boolean getEngineStopped() { return getBoolean(engineStopped); }

  /**
   * Set the {@code engineStopped} property.
   * @see #engineStopped
   */
  public void setEngineStopped(boolean v) { setBoolean(engineStopped, v, null); }

  //endregion Property "engineStopped"

  //region Property "iterationInterval"

  /**
   * Slot for the {@code iterationInterval} property.
   * @see #getIterationInterval
   * @see #setIterationInterval
   */
  public static final Property iterationInterval = newProperty(Flags.SUMMARY, UnitConstants.THOUSAND_MILLI_SECOND, BFacets.make(BFacets.make(BFacets.make(BFacets.make(BFacets.MIN, BInteger.make(1)), BFacets.make(BFacets.MAX, BInteger.make(10))), BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.SECOND))), BFacets.make(BFacets.PRECISION, 0)));

  /**
   * Get the {@code iterationInterval} property.
   * @see #iterationInterval
   */
  public int getIterationInterval() { return getInt(iterationInterval); }

  /**
   * Set the {@code iterationInterval} property.
   * @see #iterationInterval
   */
  public void setIterationInterval(int v) { setInt(iterationInterval, v, null); }

  //endregion Property "iterationInterval"

  //region Property "lastExecutionTime"

  /**
   * Slot for the {@code lastExecutionTime} property.
   * @see #getLastExecutionTime
   * @see #setLastExecutionTime
   */
  public static final Property lastExecutionTime = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY, 0, BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MILLI_SECOND)));

  /**
   * Get the {@code lastExecutionTime} property.
   * @see #lastExecutionTime
   */
  public long getLastExecutionTime() { return getLong(lastExecutionTime); }

  /**
   * Set the {@code lastExecutionTime} property.
   * @see #lastExecutionTime
   */
  public void setLastExecutionTime(long v) { setLong(lastExecutionTime, v, null); }

  //endregion Property "lastExecutionTime"

  //region Property "averageExecutionTime"

  /**
   * Slot for the {@code averageExecutionTime} property.
   * @see #getAverageExecutionTime
   * @see #setAverageExecutionTime
   */
  public static final Property averageExecutionTime = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY, 0, BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MILLI_SECOND)));

  /**
   * Get the {@code averageExecutionTime} property.
   * @see #averageExecutionTime
   */
  public double getAverageExecutionTime() { return getDouble(averageExecutionTime); }

  /**
   * Set the {@code averageExecutionTime} property.
   * @see #averageExecutionTime
   */
  public void setAverageExecutionTime(double v) { setDouble(averageExecutionTime, v, null); }

  //endregion Property "averageExecutionTime"

  //region Property "performanceMissCount"

  /**
   * Slot for the {@code performanceMissCount} property.
   * @see #getPerformanceMissCount
   * @see #setPerformanceMissCount
   */
  public static final Property performanceMissCount = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY, 0, null);

  /**
   * Get the {@code performanceMissCount} property.
   * @see #performanceMissCount
   */
  public int getPerformanceMissCount() { return getInt(performanceMissCount); }

  /**
   * Set the {@code performanceMissCount} property.
   * @see #performanceMissCount
   */
  public void setPerformanceMissCount(int v) { setInt(performanceMissCount, v, null); }

  //endregion Property "performanceMissCount"

  //region Property "cycleStartTime"

  /**
   * Slot for the {@code cycleStartTime} property.
   * @see #getCycleStartTime
   * @see #setCycleStartTime
   */
  public static final Property cycleStartTime = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY, 0, BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MILLI_SECOND)));

  /**
   * Get the {@code cycleStartTime} property.
   * @see #cycleStartTime
   */
  public int getCycleStartTime() { return getInt(cycleStartTime); }

  /**
   * Set the {@code cycleStartTime} property.
   * @see #cycleStartTime
   */
  public void setCycleStartTime(int v) { setInt(cycleStartTime, v, null); }

  //endregion Property "cycleStartTime"

  //region Property "lastDeviationInCycleStartTime"

  /**
   * Slot for the {@code lastDeviationInCycleStartTime} property.
   * @see #getLastDeviationInCycleStartTime
   * @see #setLastDeviationInCycleStartTime
   */
  public static final Property lastDeviationInCycleStartTime = newProperty(Flags.SUMMARY | Flags.TRANSIENT | Flags.READONLY, 0, BFacets.make(BFacets.UNITS, BUnit.getUnit(UnitConstants.MILLI_SECOND)));

  /**
   * Get the {@code lastDeviationInCycleStartTime} property.
   * @see #lastDeviationInCycleStartTime
   */
  public int getLastDeviationInCycleStartTime() { return getInt(lastDeviationInCycleStartTime); }

  /**
   * Set the {@code lastDeviationInCycleStartTime} property.
   * @see #lastDeviationInCycleStartTime
   */
  public void setLastDeviationInCycleStartTime(int v) { setInt(lastDeviationInCycleStartTime, v, null); }

  //endregion Property "lastDeviationInCycleStartTime"

  //region Action "requestFunctionBlockEngineStop"

  /**
   * Slot for the {@code requestFunctionBlockEngineStop} action.
   * @see #requestFunctionBlockEngineStop()
   */
  public static final Action requestFunctionBlockEngineStop = newAction(Flags.ASYNC, null);

  /**
   * Invoke the {@code requestFunctionBlockEngineStop} action.
   * @see #requestFunctionBlockEngineStop
   */
  public void requestFunctionBlockEngineStop() { invoke(requestFunctionBlockEngineStop, null, null); }

  //endregion Action "requestFunctionBlockEngineStop"

  //region Action "startFunctionBlockEngine"

  /**
   * Slot for the {@code startFunctionBlockEngine} action.
   * @see #startFunctionBlockEngine()
   */
  public static final Action startFunctionBlockEngine = newAction(0, null);

  /**
   * Invoke the {@code startFunctionBlockEngine} action.
   * @see #startFunctionBlockEngine
   */
  public void startFunctionBlockEngine() { invoke(startFunctionBlockEngine, null, null); }

  //endregion Action "startFunctionBlockEngine"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHoneywellFunctionBlockEngine.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  	
	// public variables
	private EngineThread engineThread = null;
	private MetricsThread metricsThread = null;
	private Queue sharedQueue = new Queue();
	
	public BHoneywellFunctionBlockEngine() {
		// DEFAULT CONSTRUCTOR FOR NIAGARA USE
	}
	
	public void doRequestFunctionBlockEngineStop() {
		setEngineStopped(true);
	
		changeIterationIntervalReadOnlyFlagStatus(false);
		if (engineThread != null) {
			engineThread.setStopThread(true);
			metricsThread.setStopThread(true);
		}
	}
	
	public void doStartFunctionBlockEngine() {
		if (!LicenseHandler.isHoneywellFbLicensed()) {
			return;
		}
	
		setEngineStopped(false);
		changeIterationIntervalReadOnlyFlagStatus(true);
		startEngineThread();
	}
	
	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#getAgents(javax.baja.sys.Context)
	 */
	@Override
	public AgentList getAgents(Context cx) {
		AgentList agents =  super.getAgents(cx);
		agents.toTop("wiresheet:WireSheet");

		AgentInfo[] ainfo = agents.list();
		for (int i = 0; i < ainfo.length; i++) {
			if(ainfo[i].getAgentType().toString().equals("hx:HxPxView") || ainfo[i].getAgentType().toString().equals("baja:PxView")) {
				agents.toTop(ainfo[i].getDisplayName(null));
			}
		}
		
		return agents;
	}
	
	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#started()
	 */
	@Override
	public void started() throws Exception {
		//start the engine once the engine folder is added to the station and station 
		//is already running
		if(Sys.atSteadyState() && getStartEngineAtStartup()) {
			startFunctionBlockEngine();
		}
	}
	
	/* (non-Javadoc)
	 * @see javax.baja.sys.BComponent#stationStarted()
	 */
	@Override
	public void stationStarted() throws Exception {
		super.stationStarted();
		
		//start the engine at the end of station startup
		if(getStartEngineAtStartup()) {
			startFunctionBlockEngine();
		}
	}
	
	@Override
	public void stopped() throws Exception {
		super.stopped();
	
		doRequestFunctionBlockEngineStop();
	}
	
	private void changeIterationIntervalReadOnlyFlagStatus(boolean readOnly) {
		// Interval should be changeable when engine is stopped"
		 Property property = this.getProperty(iterationInterval.getName());
			int flags = this.getFlags(property);
			if (readOnly) {
				flags = flags | Flags.READONLY;
			} else {
				flags = flags & ~Flags.READONLY;
			}
			this.setFlags(property, flags);
	}
	
	private void startEngineThread() {
		if (getEngineStopped()) {
			ENGINE_LOGGER.log(Level.FINE, FunctionBlocksLexicon.getLexicon().get("engine.stopped.please.use.action"));
			return;
		}
	
		if (null == engineThread || (State.TERMINATED == engineThread.getState())) {
			engineThread = new EngineThread(this);
			engineThread.start();
	
			metricsThread = new MetricsThread(this);
			metricsThread.start();
		} else
			ENGINE_LOGGER.log(Level.INFO, FunctionBlocksLexicon.getLexicon().get("engine.already.executing"));
	}
	
	@SuppressWarnings("squid:S2629")
	public boolean isEngineRunning() {
		return engineThread.isAlive();
	}
	
	@SuppressWarnings("squid:S2384")
	public Queue getSharedQueue() {
		return sharedQueue;
	}
	
	@Override
	public boolean isParentLegal(BComponent parent) {
		//allowed to add only one instance of FunctionBlock engine
		if (Sys.isStation() && isDuplicateInstancePresentInStation(TYPE.getTypeSpec().toString())) {
			throw new LocalizableRuntimeException(TYPE.getModule().getModuleName(), "duplicate.instance.error", new String[] { TYPE.getTypeName() });
		}
		return true;
	}
	
	/**
	 * Identifies whether any duplicate instance is available for typeName
	 * 
	 * @param typeName
	 * @return
	 */
	private boolean isDuplicateInstancePresentInStation(String typeName) {
		boolean duplicate = false;

		if (Sys.getStation() != null) {
			BOrd query = BOrd.make("slot:/|bql:select * from " + typeName);
			BITable<BObject> result = (BITable<BObject>) query.resolve(Sys.getStation()).get();
			Cursor<BObject> c = result.cursor();
			int instanceCount = 0;
			while (c.next()) {
				instanceCount++;
			}
			if (instanceCount > 0)
				duplicate = true;
		}
		return duplicate;
	}
	
}
