/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;

/**
 * Testing the datatype BNegatableStatusBoolean
 * <AUTHOR> - Sugandhi<PERSON> Parida
 * @since May 16, 2018
 */
@NiagaraType
public class BNegatableFiniteStatusBooleanTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.test.BNegatableFiniteStatusBooleanTest(**********)1.0$ @*/
/* Generated Wed May 16 16:18:26 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableFiniteStatusBooleanTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {{true, BStatus.ok,true,"(NOT) true {ok}"}, {false, BStatus.ok,false,"false {ok}"}, {true, BStatus.nullStatus,true,"(NOT) - {null}"}, {false, BStatus.nullStatus,false,"- {null}"}};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testSettingValueAndStatus(boolean value, BStatus status,boolean nagateVal,String val) {
		BComponent temp = new BComponent();
		BNegatableFiniteStatusBoolean finiteStatusBoolean = new BNegatableFiniteStatusBoolean(value, status,nagateVal);
		temp.add("slot1", finiteStatusBoolean);
		finiteStatusBoolean.setNegate(nagateVal);
		
		Assert.assertEquals(finiteStatusBoolean.getValue(), value);
		Assert.assertEquals(finiteStatusBoolean.getStatus(), status);
		Assert.assertEquals(finiteStatusBoolean.getNegate(), nagateVal);
		Assert.assertEquals(finiteStatusBoolean.toString(), val);
	}

}
