/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;

/**
 * Testing the datatype BHonStatusNumeric
 * <AUTHOR> - RSH.<PERSON>
 * @since Dec 28, 2017
 */
@NiagaraType
public class BHonStatusNumericTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.test.BHonStatusNumericTest(**********)1.0$ @*/
/* Generated Thu Dec 28 17:18:35 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumericTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] { { Double.valueOf("23.999999"), Double.valueOf("23.999999") },
				{ Double.valueOf("-23.999999"), Double.valueOf("-23.999999") },
				{ Double.valueOf("0"), Double.valueOf("0") }, { Double.valueOf("0.0"), Double.valueOf("0.0") },
				{ Double.valueOf("-0.0"), Double.valueOf("0.0") },
				{ Double.valueOf("23.232323"), Double.valueOf("23.232323") },
				{ Double.valueOf("-23.232323"), Double.valueOf("-23.232323") },
				{ Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY },
				{ Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY }, { Double.NaN, Double.NaN },
				{ Double.valueOf("23.9999999"), Double.valueOf("24.000000") },
				{ Double.valueOf("-23.9999999"), Double.valueOf("-24.000000") },
				{ Double.valueOf("100.1234567"), Double.valueOf("100.123457") }};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testPrecisionInGetValue(double value, double expectedValue) {
		BHonStatusNumeric honStatusNumeric = new BHonStatusNumeric(value);
		Assert.assertEquals(honStatusNumeric.getValue(), expectedValue);
	}
}
