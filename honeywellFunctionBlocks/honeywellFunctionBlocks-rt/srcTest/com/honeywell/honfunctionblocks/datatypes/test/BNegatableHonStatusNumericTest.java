/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;

/**
 * Testing the datatype BNegatableHonStatusNumeric
 * <AUTHOR> - Sugandhika Parida
 * @since May 16, 2018
 */
@NiagaraType
public class BNegatableHonStatusNumericTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.test.BNegatableHonStatusNumericTest(**********)1.0$ @*/
/* Generated Wed May 16 16:36:45 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableHonStatusNumericTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {{Double.valueOf("23.999999"), BStatus.ok,true,Double.valueOf("23.999999"),"(NOT) 24.00 {ok}" },
			{Double.valueOf("0"), BStatus.ok,false,Double.valueOf("0"),"0.00 {ok}"}};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testSettingValueAndStatus(double value, BStatus status,boolean nagateVal,double actualVal,String val) {
		BComponent temp = new BComponent();
		BNegatableHonStatusNumeric negatableHonStatusNumeric = new BNegatableHonStatusNumeric(value, status,nagateVal);
		temp.add("slot1", negatableHonStatusNumeric);
		negatableHonStatusNumeric.setNegate(nagateVal);
		
		Assert.assertEquals(negatableHonStatusNumeric.getValue(), actualVal);
		Assert.assertEquals(negatableHonStatusNumeric.getStatus(), status);
		Assert.assertEquals(negatableHonStatusNumeric.getNegate(), nagateVal);
		Assert.assertEquals(negatableHonStatusNumeric.toString(), val);
	}

}
