/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.datatypes.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;

/**
 * Testing the datatype BHonStatusBoolean
 * <AUTHOR> - RSH.<PERSON><PERSON>nan
 * @since Feb 16, 2018
 */
@NiagaraType
public class BHonStatusBooleanTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.datatypes.test.BHonStatusBooleanTest(**********)1.0$ @*/
/* Generated Fri Feb 16 11:01:06 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusBooleanTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {{true, BStatus.ok}, {false, BStatus.ok}, {true, BStatus.nullStatus}, {false, BStatus.nullStatus}};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testSettingValueAndStatus(boolean value, BStatus status) {
		BHonStatusBoolean honStatusBoolean = new BHonStatusBoolean(value, status);
		Assert.assertEquals(honStatusBoolean.getValue(), value);
		Assert.assertEquals(honStatusBoolean.getStatus(), status);
	}
}
