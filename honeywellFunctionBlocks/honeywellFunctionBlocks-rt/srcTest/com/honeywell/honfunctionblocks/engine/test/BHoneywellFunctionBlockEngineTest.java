/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.engine.test;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BStation;
import javax.baja.sys.Flags;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BFolder;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.engine.BHoneywellFunctionBlockEngine;
import com.tridium.kitControl.math.BAdd;

/**
 * BHoneywellFunctionBlockEngine test class
 * <AUTHOR> - <PERSON> .K
 * @since 04-Sep-2025
 */

@NiagaraType
public class BHoneywellFunctionBlockEngineTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.engine.test.BHoneywellFunctionBlockEngineTest(2979906276)1.0$ @*/
/* Generated Thu Sep 04 22:07:04 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHoneywellFunctionBlockEngineTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  	
	private TestStationHandler stationHandler = null;
	private BHoneywellFunctionBlockEngine engineFolder = null;
	
	@BeforeClass(alwaysRun = true)
	public void setUp() throws Exception {
		stationHandler = BTest.createTestStation();
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
	
		while (!station.isRunning()) {
		}
	
		station.add("Folder", new BFolder());
		BFolder folder = (BFolder) station.get("Folder");
		folder.add("engineFolder", new BHoneywellFunctionBlockEngine());
		engineFolder = (BHoneywellFunctionBlockEngine) folder.get("engineFolder");
	}
	
	@AfterClass(alwaysRun = true)
	public void tearDown() {
		engineFolder = null;
		stationHandler.stopStation();
		stationHandler.releaseStation();
		stationHandler = null;
	}
	
	@Test
	public void testHaltEngineState() {
		Assert.assertEquals(false, engineFolder.getEngineStopped());
	}
	
	@Test /*(dependsOnMethods={"testStopEngineAction"})*/
	public void testStartEngineStartAction() {
		engineFolder.startFunctionBlockEngine();
		Assert.assertEquals(false, engineFolder.getEngineStopped(), "Engine is not running");

		boolean threadStarted = false;

		try {
			Thread.sleep(2000);
		} catch (InterruptedException e) {
		}

		Map<Thread, StackTraceElement[]> threads = Thread.getAllStackTraces();
		Set<Thread> threadSet = threads.keySet();

		for (Iterator<Thread> it = threadSet.iterator(); it.hasNext();) {
			Thread t = it.next();
			if (t.getName().equalsIgnoreCase("Honeywell Function Block Engine")) {
				threadStarted = true;
			}
		}

		Assert.assertEquals(threadStarted, true, "EngineThread not started");

		engineFolder.requestFunctionBlockEngineStop();
		try {
			Thread.sleep(2000);
		} catch (InterruptedException e) {
		}
		Assert.assertEquals(true, engineFolder.getEngineStopped(), "Engine thread not stopped");
	}
	
	@Test
	public void testComponentAddition() {
		BFolder folder = new BFolder();
		engineFolder.add("folder1", folder);
		BFolder folder1 = (BFolder) engineFolder.get("folder1");
		Assert.assertNotNull(folder1);

		try {
			BComponent minBlock = (BComponent) Sys.getType("honeywellFunctionBlocks:Minimum").getInstance();
			Assert.assertNull(engineFolder.get("minBlock"));
			try {
				engineFolder.checkAdd("minBlock", minBlock, Flags.SUMMARY, null, null);
				Assert.assertTrue(true, "BMinimum of type IHoneywellComponent is allowed to be added to HoneywellFunctionBlock");
			} catch (Exception e) {
				Assert.assertNull(engineFolder.get("minBlock"));
			}

			// kitControl BAdd block should not be allowed to be added to HoneywellFunctionBlock engine
			BAdd add = new BAdd();
			try {
				engineFolder.add("add", add);
				Assert.assertTrue(true, "kitControl BAdd block allowed to be added to HoneywellFunctionBlock");
			} catch (Exception e) {
				Assert.fail("Kit control Add block should not be allowed to add to HoneywellFunctionBlock");
			}

			BComponent minBlock1 = (BComponent) Sys.getType("honeywellFunctionBlocks:Minimum").getInstance();
			folder1.add("minBlock1", minBlock1);
			Assert.assertNotNull(folder1.get("minBlock1"));
		} catch (Exception e) {
			e.printStackTrace();
			Assert.fail("Not able to test component addition to Determinstic point folder", e);
		}
	}

	@Test
	public void testIfDDCEngineStartsOnStationStart() throws Exception {
		BStation station = stationHandler.getStation();
		station.add("testThread", new BEngineFolderComponent());
		BEngineFolderComponent test = (BEngineFolderComponent) station.get("testThread");
		BBoolean b = test.doCheckDDCEngineRunning();
		Assert.assertTrue(b.getBoolean());
	}

}
