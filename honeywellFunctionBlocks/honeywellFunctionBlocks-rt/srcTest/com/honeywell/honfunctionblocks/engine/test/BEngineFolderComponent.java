/**
 * 
 */
package com.honeywell.honfunctionblocks.engine.test;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Action;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;



@NiagaraType

@NiagaraAction(name="checkDDCEngineRunning",returnType="BBoolean")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BEngineFolderComponent extends BComponent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.engine.test.BEngineFolderComponent(1904770800)1.0$ @*/
/* Generated Fri Sep 05 10:19:22 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Action "checkDDCEngineRunning"

  /**
   * Slot for the {@code checkDDCEngineRunning} action.
   * @see #checkDDCEngineRunning()
   */
  public static final Action checkDDCEngineRunning = newAction(0, null);

  /**
   * Invoke the {@code checkDDCEngineRunning} action.
   * @see #checkDDCEngineRunning
   */
  public BBoolean checkDDCEngineRunning() { return (BBoolean)invoke(checkDDCEngineRunning, null, null); }

  //endregion Action "checkDDCEngineRunning"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEngineFolderComponent.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  public BBoolean doCheckDDCEngineRunning() {
	  boolean threadStarted = false;
	  
	  try {
		Thread.sleep(1000);
	  } catch (InterruptedException e) {
	  }
	  
	  Map<Thread, StackTraceElement[]> threads = Thread.getAllStackTraces() ;
	  Set<Thread> threadSet = threads.keySet();
	  
	  for(Iterator<Thread> it=threadSet.iterator(); it.hasNext();) {
		  Thread t = it.next();
		  if(t.getName().equalsIgnoreCase("Honeywell Function Block Engine")) {
			  threadStarted = true;
		  }
	  }
	  
	  return BBoolean.make(threadStarted);
  }

}
