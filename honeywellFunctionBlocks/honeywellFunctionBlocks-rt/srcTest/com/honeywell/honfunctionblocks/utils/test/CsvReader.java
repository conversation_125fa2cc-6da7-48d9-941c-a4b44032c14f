/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;

/**
 * Helper class to read testdata from CSV file.
 * This will ignore commented line which starts with '#'
 * <AUTHOR> - RSH.<PERSON><PERSON><PERSON>nan
 *
 */
public class CsvReader {
	InputStream ins;
	BufferedReader reader;

	public CsvReader(InputStream ins) {
		this.ins = ins;
		this.reader = new BufferedReader(new InputStreamReader(ins));
	}
	
	@SuppressWarnings("squid:S1168") //Suppress returning of NULL statement
	/**
	 * Method to read a line from given CSV file. This will ignore the commented
	 * line which starts with '#'
	 * 
	 * @return
	 */
	public List<String> read() {
		try {
			String line = reader.readLine();
			while(line.startsWith("#")){
				line = reader.readLine();
			}
			return Arrays.asList(line.split(","));
		} catch (Exception e) {
			return null;
		}
	}

	public void close() throws IOException {
		ins.close();
		reader.close();
	}

}
