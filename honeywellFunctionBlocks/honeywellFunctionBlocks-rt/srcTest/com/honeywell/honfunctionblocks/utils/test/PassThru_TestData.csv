#Iteration Interval,linked,in.type,in.value,in.status,out.value,out.status
1000,no,baja:Integer,10,ok,10,ok
1000,no,baja:Integer,-10,ok,-10,ok
1000,no,baja:Integer,0,ok,0,ok
1000,no,baja:Integer,1,ok,1,ok
1000,no,baja:Float,0.001,ok,0.001,ok
1000,no,baja:Float,-0.001,ok,-0.001,ok
1000,no,baja:Integer,0,null,0,null
1000,no,baja:Integer,5,null,5,null
1000,no,baja:Float,nan,null,nan,null
1000,no,baja:Float,-inf,null,-inf,null
1000,yes,baja:Float,+inf,ok,+inf,ok
1000,yes,baja:Float,-inf,ok,-inf,ok
1000,yes,baja:Float,nan,ok,nan,ok
1000,yes,baja:Integer,5,ok,5,ok
1000,yes,lonworks:LonOccupancyEnum,occupied,ok,0,ok
1000,yes,lonworks:LonOccupancyEnum,unoccupied,ok,1,ok
1000,yes,lonworks:LonOccupancyEnum,bypass,ok,2,ok
1000,yes,lonworks:LonOccupancyEnum,standby,ok,3,ok
1000,yes,lonworks:LonOccupancyEnum,occNull,ok,-1,ok
1000,yes,baja:Integer,5,down,5,down
1000,yes,baja:Integer,5,fault,5,fault
1000,yes,baja:Integer,5,stale,5,stale
1000,yes,baja:Integer,5,disabled,5,disabled
1000,yes,baja:Integer,5,alarm,5,alarm
1000,yes,baja:Integer,5,overridden,5,overridden
1000,yes,baja:Integer,5,unackedAlarm,5,unackedAlarm
