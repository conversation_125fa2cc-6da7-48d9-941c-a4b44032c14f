/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.status.BStatus;

import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;

/**
 * Helper class to convert TestData String to boolean,Float,int datatype
 * <AUTHOR> - RSH.Lakshminarayanan
 */
public class TestDataHelper {
	private TestDataHelper(){}

	public static boolean isConnected(final String inputData){
		return inputData.startsWith("connected=");
	}
	
	public static boolean isUnconnected(final String inputData){
		return inputData.startsWith("unconnected");
	}
	
	private static String getConnectedOrUnconnectedValue(final String inputData) {
		int statusIndex = inputData.indexOf(";status=");
		
		if(isConnected(inputData) || isUnconnected(inputData)) {
			if(-1 == inputData.indexOf('=')) {
				return null;
			}
			
		    if(statusIndex > 0) {
		        return inputData.substring(inputData.indexOf('=')+1, statusIndex);
		    } else {
		        return inputData.substring(inputData.indexOf('=')+1, inputData.length());
		    }
		    
		} else if(statusIndex > 0) {
			return inputData.substring(0, statusIndex);
		}
		
		return inputData;
	}
	
	private static BStatus getStatus(final String inputData) {
	    int statusIndex = inputData.indexOf(";status=");
	    if(statusIndex > 0) {
	    	String status = inputData.substring(inputData.lastIndexOf("=")+1, inputData.length());
	    	switch(status) {
	    		case "disabled":
	    			return BStatus.disabled;
	    			
	    		case "fault":
	    			return BStatus.fault;
	    			
	    		case "down":
	    			return BStatus.down;
	    			
	    		case "stale":
	    			return BStatus.stale;
	    			
	    		case "null":
	    			return BStatus.nullStatus;
	    			
	    		case "overridden":
	    			return BStatus.overridden;
	    			
	    		case "alarm":
	    			return BStatus.alarm;
	    			
	    		case "ok":
	    		default:
	    			return BStatus.ok; 
	    	}
	    } else {
	    	if(isUnconnected(inputData)) {
	    		return BStatus.nullStatus;
	    	} else {
	    		return BStatus.ok;
	    	}
	    }
	}
	
	public static boolean getBoolean(final String inputData){
		String boolText = getConnectedOrUnconnectedValue(inputData);
		if(null==boolText) {
			return false;
		}
		
		try {
			return Float.parseFloat(boolText) != 0;
		} catch (NumberFormatException e) {
			return boolText.equalsIgnoreCase("true");
		}
	}
	
	public static int getInt(final String inputData, final int defaultValue){
		String intText = getConnectedOrUnconnectedValue(inputData);
		if(null==intText) {
			return defaultValue;
		}
		
		return Integer.parseInt(intText);
	}
	
	public static double getDouble(final String inputData, final double defaultValue, final int precision){
		double value = getDouble(inputData, defaultValue);
		
		if(Double.isFinite(value)) {
			return BigDecimal.valueOf(value).setScale(precision, RoundingMode.HALF_UP).doubleValue();
		}
		
		return value;
	}

	public static double getDouble(final String inputData, final double defaultValue){
		String doubleText = getConnectedOrUnconnectedValue(inputData);
		if(null==doubleText) {
			return defaultValue;
		}
		
		if(doubleText.startsWith("+inf"))
			return Double.POSITIVE_INFINITY;
		else if(doubleText.startsWith("-inf"))
			return Double.NEGATIVE_INFINITY;
		else if(doubleText.equalsIgnoreCase("nan"))
			return Double.NaN;					  
		else
			return Double.valueOf(doubleText);
	}
	
	
	public static BHonStatusBoolean getHonStatusBoolean(final String inputData){
		boolean booleanValue = getBoolean(inputData);
		BStatus status = getStatus(inputData);
		return new BHonStatusBoolean(booleanValue, status);
	}
	
	public static BNegatableStatusBoolean getNegatableStatusBoolean(final String inputData, final boolean negate){ 
		boolean booleanValue = getBoolean(inputData);
		BStatus status = getStatus(inputData);
		return new BNegatableStatusBoolean(booleanValue, status, negate);
	}
	
	public static BFiniteStatusBoolean getFiniteStatusBoolean(final String inputData){
		boolean booleanValue = getBoolean(inputData);
		BStatus status = getStatus(inputData);
		return new BFiniteStatusBoolean(booleanValue, status);
	}
	
	public static BNegatableFiniteStatusBoolean getNegatableFiniteStatusBoolean(final String inputData, final boolean negate){
		boolean booleanValue = getBoolean(inputData);
		BStatus status = getStatus(inputData);
		return new BNegatableFiniteStatusBoolean(booleanValue, status, negate);
	}
	
	public static BHonStatusNumeric getHonStatusNumeric(final String inputData, final double defaultValue){
		double doubleValue = getDouble(inputData, defaultValue);
		BStatus status = getStatus(inputData);
		return new BHonStatusNumeric(doubleValue, status);
	}
	
	public static BNegatableHonStatusNumeric getNegatableHonStatusNumeric(final String inputData, final double defaultValue, final boolean negate){
		double doubleValue = getDouble(inputData, defaultValue);
		BStatus status = getStatus(inputData);
		return new BNegatableHonStatusNumeric(doubleValue, status, negate);
	}
	
	/**
	 * Read the given TestData file and covert them into an Object[][] which is TestNG format
	 * @param completeFilePath
	 * @return List<String>
	 */
	public static Object[][] getTestDataInTestNGFormat(String completeFilePath) {
		ArrayList<List<String>> validInputs = readTestDataFromCSVFile(completeFilePath);

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}

	/**
	 *
	 * @since Jan 3, 2018
	 * @param completeFilePath
	 * @return
	 * @return {@link ArrayList<List<String>>}
	 */
	public static ArrayList<List<String>> readTestDataFromCSVFile(String completeFilePath) {
		BIFile file = (BIFile) BOrd.make(completeFilePath).get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		return validInputs;
	}

	public static ArrayList<List<String>> readTestDataFromCSVFile(File file) throws FileNotFoundException {
		FileInputStream fileS = new FileInputStream(file); 
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(fileS);
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		return validInputs;
	}

	/**
	 * Read the given TestData file in sequence and covert them into an Object[][] which is TestNG format
	 * @param completeFilePath
	 * @return List<List<String>>
	 */
	public static Object[][] getSequencedTestDataInTestNGFormat(String completeFilePath) {
		BIFile file = (BIFile) BOrd.make(completeFilePath).get();
		CsvReader readValidInputs;
		int sequenceCount = 0;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				if("start".equalsIgnoreCase(rec.get(0))){
					sequenceCount++;
				}
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[sequenceCount][];
		int objArrayNo=0;
		int seqStartPos=0, seqEndPos = 0;
		for (int i = 0; i < validInputs.size(); i++) {
			if("start".equalsIgnoreCase(validInputs.get(i).get(0))) {
				seqStartPos = i+1;
			} else if("end".equalsIgnoreCase(validInputs.get(i).get(0))) {
				objArray[objArrayNo] = new Object[1];
				seqEndPos = i;
				objArray[objArrayNo][0] = validInputs.subList(seqStartPos, seqEndPos);
				objArrayNo++;
			}
		}

		return objArray;
	}
	
	public static File getTestDataFileReference(Class cls, String testDataFileName) {
		String pn = cls.getPackage().getName();
		
		String pns = pn.replace('.', File.separatorChar);
		String testDataFilePath = "srcTest"+ File.separator + pns + File.separator + testDataFileName;
		
		File currentDir = new File("").getAbsoluteFile();
		File testDataFile = new File(currentDir, testDataFilePath);
		
		return testDataFile;
	}
	
}
