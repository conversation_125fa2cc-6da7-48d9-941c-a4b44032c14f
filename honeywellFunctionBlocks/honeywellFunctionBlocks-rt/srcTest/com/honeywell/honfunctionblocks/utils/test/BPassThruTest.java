/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.baja.control.BBooleanPoint;
import javax.baja.control.BControlPoint;
import javax.baja.control.BEnumPoint;
import javax.baja.control.BNumericPoint;
import javax.baja.control.BNumericWritable;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.registry.TypeInfo;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.status.BStatusValue;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BIcon;
import javax.baja.sys.BNumber;
import javax.baja.sys.BStation;
import javax.baja.sys.Flags;
import javax.baja.sys.Knob;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;
import javax.baja.util.BFolder;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.logic.BAnd;
import com.honeywell.honfunctionblocks.fbs.math.BAdd;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancyArbitrator;
import com.honeywell.honfunctionblocks.utils.BPassThru;

/**
 * Implementation of PassThru block TestCase as per FB SDD rev26
 * Requirement ID: ??
 * <AUTHOR> - Suresh Vemuri
 * @since Jan 31, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BPassThruTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.utils.test.BPassThruTest(**********)1.0$ @*/
/* Generated Fri Feb 23 15:27:14 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPassThruTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name = "provideTestData")
	public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/utils/test/PassThru_TestData.csv");
	}

	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}, {"executeBlock"}, {"icon"}, {"in"}, {"OUT"},{"Auto"},{"OverrideExpiration"}};
	}

	@DataProvider(name = "provideAllLinkCombinations")
	public Object[][] createAllLinkCombinations() {
		return new Object[][] {
			/*Source Comp*/ 	/*Source Slot*/		/*Target Comp*/ 	/*Target Slot*/		/*Is Valid Link*/
			{new BAdd(),		BAdd.Y, 			new BPassThru(),	BPassThru.in,		Boolean.TRUE},
			{new BAdd(),		BAdd.x1, 			new BPassThru(),	BPassThru.in,		Boolean.FALSE},
			{new BAnd(),		BAnd.OUTPUT,		new BPassThru(),	BPassThru.in,		Boolean.TRUE},
			{new BAnd(),		BAnd.in1,			new BPassThru(),	BPassThru.in,		Boolean.FALSE},
			{new BOccupancyArbitrator(),		BOccupancyArbitrator.EFF_OCC_CURRENT_STATE,		new BPassThru(),	BPassThru.in,		Boolean.TRUE},
			{new BOccupancyArbitrator(),		BOccupancyArbitrator.scheduleCurrentState,		new BPassThru(),	BPassThru.in,		Boolean.FALSE},
			{new BOccupancyArbitrator(),		BOccupancyArbitrator.netLastInWins,				new BPassThru(),	BPassThru.in,		Boolean.FALSE},

			{new BPassThru(),	BPassThru.OUT,		new BAdd(),		BAdd.Y, 			Boolean.FALSE},
			{new BPassThru(),	BPassThru.OUT,		new BAdd(),		BAdd.x1, 			Boolean.TRUE},
			{new BPassThru(),	BPassThru.OUT,		new BAnd(),		BAnd.OUTPUT,		Boolean.FALSE},
			{new BPassThru(),	BPassThru.OUT,		new BAnd(),		BAnd.in1,			Boolean.TRUE},
			{new BPassThru(),	BPassThru.OUT,		new BOccupancyArbitrator(),		BOccupancyArbitrator.EFF_OCC_CURRENT_STATE,		Boolean.FALSE},
			{new BPassThru(),	BPassThru.OUT,		new BOccupancyArbitrator(),		BOccupancyArbitrator.scheduleCurrentState,		Boolean.TRUE},
			{new BPassThru(),	BPassThru.OUT,		new BOccupancyArbitrator(),		BOccupancyArbitrator.netLastInWins,				Boolean.FALSE}
		};
	}
	
	private BStatus makeStatusFromTag(String tag) {
		HashMap<String, BStatus> map=new HashMap<String, BStatus>();
		map.put("alarm", BStatus.alarm);
		map.put("disabled", BStatus.disabled);
		map.put("down", BStatus.down);
		map.put("fault", BStatus.fault);
		map.put("null", BStatus.nullStatus);
		map.put("ok", BStatus.ok);
		map.put("overridden", BStatus.overridden);
		map.put("stale", BStatus.stale);
		map.put("unackedAlarm", BStatus.unackedAlarm);
		BStatus s=map.get(tag);
		if(null==s)
			throw new RuntimeException("Invalid BStatus tag '"+tag+"'");
		return s;
	}

	/*@Test(dataProvider = "provideTestData")
	public void testPassThruWithTestData(List<String> inputs) throws BlockExecutionException, FileNotFoundException {
		BPassThru passThruBlock=new BPassThru();
		//setupNumericSlot(passThruBlock, BPassThru.in.getName(), inputs.get(1));
		
		Type type=Sys.getType(inputs.get(2));
		BStatus status=makeStatusFromTag(inputs.get(4));

		if(inputs.get(1).equals("no")) { //set value in "in" slot
			if(type.is(BNumber.TYPE)) {
				passThruBlock.getIn().setValue(BDouble.make(inputs.get(3)).getDouble());
				passThruBlock.getIn().setStatus(status);
			}
		}else { //use link
			BControlPoint srcCmp=null;
			BStatusValue srcVal=null;
			if(type.is(BNumber.TYPE)) { 
				srcCmp=new BNumericPoint();
				srcVal=new BStatusNumeric(BDouble.make(inputs.get(3)).getDouble(), status);
				srcCmp.set(BNumericPoint.out, srcVal);
			}else if(type.is(BBoolean.TYPE)) { 
				srcCmp=new BBooleanPoint();
				srcVal=new BStatusBoolean(BBoolean.make(inputs.get(3)).getBoolean(), status);
				srcCmp.set(BBooleanPoint.out, srcVal);
			}else if(type.is(BEnum.TYPE)) {
				srcCmp=new BEnumPoint();
				BEnum enumObj=(BEnum)type.getInstance();
				srcVal=new BStatusEnum(enumObj.getRange().get(inputs.get(3)), status);
				srcCmp.set(BEnumPoint.out, srcVal);
			}
			
			Type srcType = srcCmp.get("out").getType();
			Type targetType = BPassThru.in.getType();

			TypeInfo adpts[]=Sys.getRegistry().getAdapters(srcType.getTypeInfo(), targetType.getTypeInfo());
			if(null==adpts || adpts.length<=0) {
				throw new RuntimeException("No link converter found for "+srcType+" -> "+targetType);
			}
			BConverter converter = (BConverter) adpts[adpts.length-1].getInstance();
			BConversionLink conversionLink = new BConversionLink(srcCmp, srcCmp.getOutProperty(), BPassThru.in, converter);
            conversionLink.setEnabled(true);
            passThruBlock.add("Link?",conversionLink );                    
            conversionLink.activate();
		}
		
		
		Assert.assertEquals(passThruBlock.getOUT().getValue(), TestDataHelper.getDouble(inputs.get(5), 0.0d), ""+inputs);
		Assert.assertEquals(passThruBlock.getOUT().getStatus().toString(), "{"+inputs.get(6)+"}", ""+inputs);
	}*/

//	private void setupNumericSlot(BPassThru ptBlock, final String slotName, final String inputValue) {
//		if(TestDataHelper.isConnected(inputValue)){
//			BNumericConst nm1 = new BNumericConst();
//			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
//			Type srcType = nm1.getOut().getType();
//			Type targetType = ptBlock.getProperty(slotName).getType();
//
//			TypeInfo adpts[]=Sys.getRegistry().getAdapters(srcType.getTypeInfo(), targetType.getTypeInfo());
//			if(null==adpts || adpts.length<=0) {
//				throw new RuntimeException("No link converter found for "+srcType+" -> "+targetType);
//			}
//			BConverter converter = (BConverter) adpts[0].getInstance();
//            BConversionLink conversionLink = new BConversionLink(nm1,BNumericConst.out,ptBlock.getSlot(slotName),converter);
//            conversionLink.setEnabled(true);
//            ptBlock.add("Link?",conversionLink );                    
//            conversionLink.activate();
//			return;
//		}
//
//		switch (slotName) {
//			case "in":
//				ptBlock.setIn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
//				break;
//		}
//	}
	
	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BPassThru passThruBlock=new BPassThru();
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "pass_thru_block.png");
		BIcon actualFbIcon = passThruBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		// check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		passThruBlock.setIcon(expectedFbIcon);
		actualFbIcon = passThruBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		BPassThru passThruBlock=new BPassThru();
		Assert.assertNotNull(passThruBlock.getSlot(slotName));
	}

	@Test
	public void testInvalidSlots() {
		Object[][] slotNames=createAllSlotNames();
		ArrayList<String> validSlotList=new ArrayList<String>();
		for (int i = 0; i < slotNames.length; i++) {
			validSlotList.add((String) slotNames[i][0]);
		}
		
		BPassThru passThruBlock=new BPassThru();
		Slot[] slots=passThruBlock.getSlotsArray();
		for (int i = 0; i < slots.length; i++) {
			Slot s = slots[i];
			Assert.assertTrue(validSlotList.contains(s.getName()));
		}
	}
	
	@Test
	public void testOutputProperties() {
		BPassThru passThruBlock=new BPassThru();
		List<Property> list=passThruBlock.getOutputPropertiesList();
		Assert.assertTrue(list.contains(BPassThru.OUT));
	}
	
	@Test
	public void testInputProperties() {
		BPassThru passThruBlock=new BPassThru();
		List<Property> list=passThruBlock.getInputPropertiesList();
		Assert.assertTrue(list.contains(BPassThru.in));
	}
	
	@Test
	public void testMisc() {
		BPassThru passThruBlock=new BPassThru();
		try {
			passThruBlock.initHoneywellComponent(new BExecutionParams());
			passThruBlock.executeHoneywellComponent(new BExecutionParams());
			passThruBlock.setIn(new BStatusNumeric());
			passThruBlock.getIn();
		} catch (BlockExecutionException | BlockInitializationException e) {
			e.printStackTrace();
			Assert.fail();
		}
		passThruBlock.setOUT(new BStatusNumeric());
	}

	//TODO @Test(dataProvider = "provideAllLinkCombinations")
//	public void testDoCheckLink(BComponent srcComp, Slot srcSlot, BComponent targetComp, Slot targetSlot, Boolean expectedResult) {
//		Context ctx=new BasicContext(new BUser(), "en");
//		LinkCheck checkLink=targetComp.checkLink(srcComp, srcSlot, targetSlot, ctx);
//		Assert.assertEquals(checkLink.isValid(), expectedResult.booleanValue());
//	}


	@DataProvider(name="interconnections")
	public Object[][] getInterconnections(){
		BPassThru b = new BPassThru();
		BPassThru b1 = new BPassThru();
		BPassThru b2 = new BPassThru();
		BPassThru b3 = new BPassThru();
		BPassThru b4 = new BPassThru();
		BNumericWritable m1 = new BNumericWritable();
		BFolder af = new BFolder();
		BFolder af2 = new BFolder();

    //create composite
		af.add(b3.getName(),b3);
		CompositeCreator.addCompositeSlot(af,b3,b3.getSlot("in"),false);
    CompositeCreator.addCompositeSlot(af,b3,b3.getSlot("OUT"), true);

    //create composite 2
		af2.add(b4.getName(),b4);
		CompositeCreator.addCompositeSlot(af2,b4,b4.getSlot("in"), false);
		CompositeCreator.addCompositeSlot(af2,b4,b4.getSlot("OUT"), true);

		// b -> af -> af2 -> b1 -> b2 -> m1

		m1.linkTo(b2,b2.getSlot("OUT"),m1.getSlot("in10"));
		b2.linkTo(b1,b1.getSlot("OUT"),b2.getSlot("in"));
		b1.linkTo(af2,af2.getSlot("OUT"),b1.getSlot("in"));
		af2.linkTo(af,af.getSlot("OUT"),af2.getSlot("in"));
		af.linkTo(b,b.getSlot("OUT"),af.getSlot("in"));
		return new Object[][]{new Object[]{b, af, m1}};
	}

	@Test(dataProvider = "interconnections")
	public void testActualComponents(Object[] blocks){
		BPassThru b = (BPassThru)blocks[0];
		BFolder af = (BFolder)blocks[1];
		BNumericWritable m1 = (BNumericWritable)blocks[2];

		Assert.assertEquals(getActualTargets(b).get(0),m1);
		Assert.assertEquals(getActualTargets(af,b.getKnobs()[0].getTargetSlot()).get(0),m1);
	}

	@Test
	public void testNoOverridePresent() throws Exception{
		BPassThru b = new BPassThru();
		b.start();
		Assert.assertNull(b.get(FBSlotConstants.OVERRIDE));
		Assert.assertTrue(Flags.isHidden(b,b.getSlot("OverrideExpiration")));
	}

	@Test
	public void testAutoHidden() throws Exception{
		BPassThru b = new BPassThru();
		b.start();
		Assert.assertTrue(Flags.isHidden(b,b.getSlot("Auto")));
		b.getOUT().setStatus(BStatus.disabled);
		b.doAuto();
		Assert.assertEquals(b.getOUT().getStatus(),BStatus.disabled);
	}
	
	public static List<BComponent> getActualTargets(BPassThru passThru){
		ArrayList<BComponent> targets = new ArrayList<>();
		fetchTargets(targets, passThru);
		return targets;
	}

	public static List<BComponent> getActualTargets(BFolder applicationFolder, Slot compositedSlot){
		ArrayList<BComponent> targets = new ArrayList<>();
		fetchTargets(targets, compositedSlot, applicationFolder);
		return targets;
	}

	private static void fetchTargets(ArrayList<BComponent> targets, BPassThru passThru){
		Knob[] knobs = passThru.getKnobs();
		for(Knob x: knobs){
			if(x.getTargetComponent() instanceof  BPassThru){
				fetchTargets(targets, (BPassThru)x.getTargetComponent());
			}
			else if(x.getTargetComponent() instanceof BFolder){
				fetchTargets(targets, x.getTargetSlot(), (BFolder)x.getTargetComponent());
			}
			else{
				targets.add(x.getTargetComponent());
			}
		}
	}

	private static void fetchTargets(ArrayList<BComponent> targets,Slot compositedSlot, BFolder applicationFolder){
		Knob[] knobs = applicationFolder.getKnobs(compositedSlot);
		for(Knob x: knobs){
			if(x.getTargetComponent() instanceof  BPassThru){
				fetchTargets(targets, (BPassThru)x.getTargetComponent());
			}
			else if(x.getTargetComponent() instanceof BFolder){
				fetchTargets(targets, x.getTargetSlot(), (BFolder)x.getTargetComponent());
			}
			else{
				targets.add(x.getTargetComponent());
			}
		}
	}
}
