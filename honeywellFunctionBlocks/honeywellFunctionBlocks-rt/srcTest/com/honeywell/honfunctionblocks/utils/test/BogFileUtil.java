/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import java.io.File;
import java.io.IOException;

import javax.baja.io.ValueDocDecoder;
import javax.baja.io.ValueDocEncoder;
import javax.baja.sys.BComponent;
import javax.baja.sys.BValue;
import javax.baja.sys.Sys;

/**
 * Class to deal with reading & writing of BBogFile 
 * <AUTHOR> - RSH.<PERSON>
 * @since Feb 6, 2018
 */
public class BogFileUtil {
	public static File DIRECTORY = new File(Sys.getNiagaraSharedUserHome(),"f1TestCase");
	
	public File saveComponentToBogFile(String testScenarioName, BComponent componentToSave) throws IOException {
		File file = getBogFile(testScenarioName);
		if(file != null) {
			ValueDocEncoder encode = new ValueDocEncoder(file);
			encode.setZipped(true);
			encode.encodeDocument(componentToSave);
			encode.close();
			
			return file;
		}
		
		return null;
	}
	
	public BComponent getComponentFromBogFile(File file) {
		if (file == null || !file.exists()) {
			return null;
		}
		
		ValueDocDecoder decode = null;
		try {
			decode = new ValueDocDecoder(file);

			BValue value = decode.decodeDocument(true);
			return (BComponent) value;
		} catch (Exception ex) {
			ex.printStackTrace();

		} finally {
			if (decode != null) {
				decode.close();
				decode = null;
			}
		}
		
		return null;
	}
	
	private File getBogFile(String fileName){
        File virtualDir = DIRECTORY;
        if (!virtualDir.exists()) {
            virtualDir.mkdirs();
        }

        if(fileName==null){
            return null;
        }
        
        String bogName = fileName+".bog";
        if ((bogName == null) || bogName.equalsIgnoreCase(".bog")) {
            return null;
        }

        return new File(virtualDir, bogName);
    }

}
