/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import javax.baja.sys.BComponent;
import javax.baja.sys.Context;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Slot;

import org.testng.Assert;

import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Helper class to test link rules between components
 * <AUTHOR> - Lavanya
 */
public class LinkCheckUtil {
	private LinkCheckUtil() {}
	
	public static void checkInvalidLink(BFunctionBlock fb,String slotName) {
		BNumericConst nm = new BNumericConst();
		   LinkCheck checkLink = fb.checkLink(nm, nm.getSlot("out"), fb.getSlot(slotName), Context.forceValidate);	   
		   Assert.assertFalse(checkLink.isValid());
	}
	 

	public static void checkValidLink(BFunctionBlock fb,String slotName) {
		BNumericConst nm = new BNumericConst();
		   LinkCheck checkLink = fb.checkLink(nm, nm.getSlot("out"), fb.getSlot(slotName), Context.forceValidate);
		   Assert.assertTrue(checkLink.isValid());
	}
	
	
	public static void checkInvalidLink(BComponent src,Slot srcSlot,BComponent target,Slot targetSlot) {
		LinkCheck linkCheck = target.checkLink(src,srcSlot,targetSlot,Context.forceValidate);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	
	public static void checkValidLink(BComponent src,Slot srcSlot,BComponent target,Slot targetSlot) {
		LinkCheck linkCheck = target.checkLink(src,srcSlot,targetSlot,Context.forceValidate);
		Assert.assertTrue(linkCheck.isValid());
	}

}
