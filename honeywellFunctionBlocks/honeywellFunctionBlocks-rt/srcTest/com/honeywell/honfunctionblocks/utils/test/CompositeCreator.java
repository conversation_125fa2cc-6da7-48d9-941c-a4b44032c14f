/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.utils.test;

import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BLink;
import javax.baja.sys.BValue;
import javax.baja.sys.Slot;

/**
 * <AUTHOR> - <PERSON><PERSON>
 * @since 14 June,2018
 */
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public class CompositeCreator {

  public static void addCompositeSlot(BComponent composite, BComponent child,Slot slot, boolean isOut){
    BFacets facets = slot.getFacets();
    if (facets == null) {
      facets = BFacets.NULL;
    }
    BValue compSlot = child.get(slot.asProperty()).newCopy();
    int flags = isOut ? 1 : 0;
    flags |= child.getFlags(slot);
    composite.add(slot.getName(),compSlot,flags,facets,null);
    BLink link;
    if (isOut) {
      link = new BLink(child, slot, composite.getSlot(slot.getName()));
      composite.add((String)null, link, 4096, BFacets.NULL);
    } else {
      link = new BLink(composite, composite.getSlot(slot.getName()), slot);
      child.add((String)null, link, 4096, BFacets.NULL);
    }
    link.activate();
  }
}
