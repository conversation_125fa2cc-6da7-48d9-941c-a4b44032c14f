/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;

/**
 * Test FB to test the features implemented in BFunctionBlock class
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since Sep 29, 2018
 */

@NiagaraType
@NiagaraProperty(name = "x1", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.nullStatus)")
@NiagaraProperty(name = "x2", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.disabled)")
@NiagaraProperty(name = "x3", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.fault)")
@NiagaraProperty(name = "x4", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.stale)")
@NiagaraProperty(name = "x5", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.down)")
@NiagaraProperty(name = "x6", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.alarm)")
@NiagaraProperty(name = "x7", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.overridden)")
@NiagaraProperty(name = "x8", type = "BHonStatusNumeric", defaultValue = "new BHonStatusNumeric(0.0, BStatus.ok)")

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103" })
public class BTestFb extends BFunctionBlock {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.BTestFb(2738632828)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "x1"

  /**
   * Slot for the {@code x1} property.
   * @see #getX1
   * @see #setX1
   */
  public static final Property x1 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.nullStatus), null);

  /**
   * Get the {@code x1} property.
   * @see #x1
   */
  public BHonStatusNumeric getX1() { return (BHonStatusNumeric)get(x1); }

  /**
   * Set the {@code x1} property.
   * @see #x1
   */
  public void setX1(BHonStatusNumeric v) { set(x1, v, null); }

  //endregion Property "x1"

  //region Property "x2"

  /**
   * Slot for the {@code x2} property.
   * @see #getX2
   * @see #setX2
   */
  public static final Property x2 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.disabled), null);

  /**
   * Get the {@code x2} property.
   * @see #x2
   */
  public BHonStatusNumeric getX2() { return (BHonStatusNumeric)get(x2); }

  /**
   * Set the {@code x2} property.
   * @see #x2
   */
  public void setX2(BHonStatusNumeric v) { set(x2, v, null); }

  //endregion Property "x2"

  //region Property "x3"

  /**
   * Slot for the {@code x3} property.
   * @see #getX3
   * @see #setX3
   */
  public static final Property x3 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.fault), null);

  /**
   * Get the {@code x3} property.
   * @see #x3
   */
  public BHonStatusNumeric getX3() { return (BHonStatusNumeric)get(x3); }

  /**
   * Set the {@code x3} property.
   * @see #x3
   */
  public void setX3(BHonStatusNumeric v) { set(x3, v, null); }

  //endregion Property "x3"

  //region Property "x4"

  /**
   * Slot for the {@code x4} property.
   * @see #getX4
   * @see #setX4
   */
  public static final Property x4 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.stale), null);

  /**
   * Get the {@code x4} property.
   * @see #x4
   */
  public BHonStatusNumeric getX4() { return (BHonStatusNumeric)get(x4); }

  /**
   * Set the {@code x4} property.
   * @see #x4
   */
  public void setX4(BHonStatusNumeric v) { set(x4, v, null); }

  //endregion Property "x4"

  //region Property "x5"

  /**
   * Slot for the {@code x5} property.
   * @see #getX5
   * @see #setX5
   */
  public static final Property x5 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.down), null);

  /**
   * Get the {@code x5} property.
   * @see #x5
   */
  public BHonStatusNumeric getX5() { return (BHonStatusNumeric)get(x5); }

  /**
   * Set the {@code x5} property.
   * @see #x5
   */
  public void setX5(BHonStatusNumeric v) { set(x5, v, null); }

  //endregion Property "x5"

  //region Property "x6"

  /**
   * Slot for the {@code x6} property.
   * @see #getX6
   * @see #setX6
   */
  public static final Property x6 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.alarm), null);

  /**
   * Get the {@code x6} property.
   * @see #x6
   */
  public BHonStatusNumeric getX6() { return (BHonStatusNumeric)get(x6); }

  /**
   * Set the {@code x6} property.
   * @see #x6
   */
  public void setX6(BHonStatusNumeric v) { set(x6, v, null); }

  //endregion Property "x6"

  //region Property "x7"

  /**
   * Slot for the {@code x7} property.
   * @see #getX7
   * @see #setX7
   */
  public static final Property x7 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.overridden), null);

  /**
   * Get the {@code x7} property.
   * @see #x7
   */
  public BHonStatusNumeric getX7() { return (BHonStatusNumeric)get(x7); }

  /**
   * Set the {@code x7} property.
   * @see #x7
   */
  public void setX7(BHonStatusNumeric v) { set(x7, v, null); }

  //endregion Property "x7"

  //region Property "x8"

  /**
   * Slot for the {@code x8} property.
   * @see #getX8
   * @see #setX8
   */
  public static final Property x8 = newProperty(0, new BHonStatusNumeric(0.0, BStatus.ok), null);

  /**
   * Get the {@code x8} property.
   * @see #x8
   */
  public BHonStatusNumeric getX8() { return (BHonStatusNumeric)get(x8); }

  /**
   * Set the {@code x8} property.
   * @see #x8
   */
  public void setX8(BHonStatusNumeric v) { set(x8, v, null); }

  //endregion Property "x8"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTestFb.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	/* (non-Javadoc)
	 * @see com.honeywell.honfunctionblocks.fbs.IHoneywellComponent#executeHoneywellComponent(com.honeywell.honfunctionblocks.fbs.BExecutionParams)
	 */
	@Override
	public void executeHoneywellComponent(BExecutionParams executionParams) throws BlockExecutionException {
		super.executeHoneywellComponent(executionParams);
		
		//sample method
	}

}
