/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BAdd;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Add block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Lavanya B.
 * @since Jan 31, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BAddTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BAddTest(2979906276)1.0$ @*/
/* Generated Wed Jan 31 10:56:31 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAddTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {
	  addBlock = new BAdd();
	  executionParams = new BExecutionParams();

  }
  
  @AfterClass(alwaysRun=true)
  public void tearDown() {
	  addBlock = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x1"}, {"x2"}, {"x3"}, {"x4"}, {"x5"}, {"x6"}, {"x7"}, {"x8"}, {"ignoreInvalidInput"}, {"tailOperation"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"In"}, {"onValue"}, {"OnVal"}, {"OffVal"}, {"OffValue"}, {"MinOn"}, {"invalidFlag"}, {"TailOperation"}, {"IgnoreInvalidInput"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(addBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(addBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_add.png");
	  BIcon actualFbIcon = addBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  addBlock.setIcon(expectedFbIcon);
	  actualFbIcon = addBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInaddBlock(double snValue) {
	  addBlock.setX1(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX1().getValue(), snValue, 0.1);
	  
	  addBlock.setX2(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX2().getValue(), snValue, 0.1);	  
	  
	  addBlock.setX3(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX3().getValue(), snValue, 0.1);
	  
	  addBlock.setX4(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX4().getValue(), snValue, 0.1);
	  
	  addBlock.setX5(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX5().getValue(), snValue, 0.1);
	  
	  addBlock.setX6(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX6().getValue(), snValue, 0.1);
	  
	  addBlock.setX7(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX7().getValue(), snValue, 0.1);
	  
	  addBlock.setX8(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(addBlock.getX8().getValue(), snValue, 0.1);

	  addBlock.setY(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(addBlock.getY().getValue(), snValue, 0.1);
	  
	  addBlock.setIgnoreInvalidInput(true);
	  Assert.assertEquals(addBlock.getIgnoreInvalidInput(), true);
	  
	  addBlock.setIgnoreInvalidInput(false);
	  Assert.assertEquals(addBlock.getIgnoreInvalidInput(), false);	  
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTestData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Add_TestData.csv");
  }
  
  @DataProvider(name="provideStatusCheckTestData")
  public Object[][] getTestDataForStatusCheck() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/add_status_check.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testAddBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BAdd additionBlock = new BAdd();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(additionBlock, BAdd.x1.getName(), inputs.get(2));
	  setupNumericSlot(additionBlock, BAdd.x2.getName(), inputs.get(3));
	  setupNumericSlot(additionBlock, BAdd.x3.getName(), inputs.get(4));
	  setupNumericSlot(additionBlock, BAdd.x4.getName(), inputs.get(5));
	  setupNumericSlot(additionBlock, BAdd.x5.getName(), inputs.get(6));
	  setupNumericSlot(additionBlock, BAdd.x6.getName(), inputs.get(7));
	  setupNumericSlot(additionBlock, BAdd.x7.getName(), inputs.get(8));
	  setupNumericSlot(additionBlock, BAdd.x8.getName(), inputs.get(9));
	  additionBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
	  additionBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(10), 0)));
	  
	  additionBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(additionBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(11), 0.0d), Arrays.toString(inputs.toArray()));	  
	  additionBlock = null;
  }
  
  @Test(dataProvider="provideStatusCheckTestData", groups={"addStatusTest"})
  public void testAddBlockWithStatusTestData(List<String> inputs) throws BlockExecutionException {
	  BAdd additionBlock = new BAdd();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(additionBlock, BAdd.x1.getName(), inputs.get(1));
	  setupNumericSlot(additionBlock, BAdd.x2.getName(), inputs.get(2));
	  setupNumericSlot(additionBlock, BAdd.x3.getName(), inputs.get(3));
	  setupNumericSlot(additionBlock, BAdd.x4.getName(), inputs.get(4));
	  setupNumericSlot(additionBlock, BAdd.x5.getName(), inputs.get(5));
	  setupNumericSlot(additionBlock, BAdd.x6.getName(), inputs.get(6));
	  setupNumericSlot(additionBlock, BAdd.x7.getName(), inputs.get(7));
	  setupNumericSlot(additionBlock, BAdd.x8.getName(), inputs.get(8));
	  additionBlock.setIgnoreInvalidInput(true);
	  additionBlock.setTailOperation(BTailOperationEnum.NoChange);
	  
	  additionBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(additionBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), Arrays.toString(inputs.toArray()));	  
	  additionBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param addBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BAdd additionBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  additionBlock.linkTo(nm1, nm1.getSlot("out"), additionBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "x1":
		  additionBlock.setX1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "x2":
		  additionBlock.setX2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;	
		  
	  case "x3":
		  additionBlock.setX3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "x4":
		  additionBlock.setX4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "x5":
		  additionBlock.setX5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "x6":
		  additionBlock.setX6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "x7":
		  additionBlock.setX7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "x8":
		  additionBlock.setX8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  //@Test
	public void testLinkRules() {
		BAdd multiplyB = new BAdd();
		checkOutgoingLink(multiplyB, BAdd.x1, false);
		checkOutgoingLink(multiplyB, BAdd.x2, false);
		checkOutgoingLink(multiplyB, BAdd.x3, false);
		checkOutgoingLink(multiplyB, BAdd.x4, false);
		checkOutgoingLink(multiplyB, BAdd.x5, false);
		checkOutgoingLink(multiplyB, BAdd.x6, false);
		checkOutgoingLink(multiplyB, BAdd.x7, false);
		checkOutgoingLink(multiplyB, BAdd.x8, false);
		checkOutgoingLink(multiplyB, BAdd.ignoreInvalidInput, false);
		checkOutgoingLink(multiplyB, BAdd.tailOperation, false);
		
		BComponent testConfigSlots = new BComponent();
		testConfigSlots.add("ignoreInvalidInput", BBoolean.DEFAULT);
		testConfigSlots.add("tailOperation", BTailOperationEnum.DEFAULT);
		
		LinkCheck checkLink = addBlock.checkLink(testConfigSlots,
				testConfigSlots.getSlot(BAdd.ignoreInvalidInput.getName()),
				addBlock.getSlot(BAdd.ignoreInvalidInput.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), false);
		
		checkLink = addBlock.checkLink(testConfigSlots,
				testConfigSlots.getSlot(BAdd.tailOperation.getName()),
				addBlock.getSlot(BAdd.tailOperation.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), false);
	}

	private void checkOutgoingLink(BAdd block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = addBlock.checkLink(addBlock, addBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	@Test
	public void testConfigProperties() {
		List<Property> configList = addBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamNames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamNames);
		String[] expectedConfigParamNames = { BAdd.ignoreInvalidInput.getName(), BAdd.tailOperation.getName() };
		Arrays.sort(expectedConfigParamNames);

		Assert.assertEquals(actualConfigParamNames, expectedConfigParamNames);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = addBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BAdd.x1.getName(), BAdd.x2.getName(),BAdd.x3.getName(),BAdd.x4.getName(),
				BAdd.x5.getName(),BAdd.x6.getName(),BAdd.x7.getName(),BAdd.x8.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = addBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BAdd.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
  
  private BAdd addBlock;
  private BExecutionParams executionParams;

}
