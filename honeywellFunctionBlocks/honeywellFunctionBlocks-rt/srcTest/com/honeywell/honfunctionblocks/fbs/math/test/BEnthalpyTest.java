/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.math.BEnthalpy;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Enthalpy FB test as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 22, 2018
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
@NiagaraType

public class BEnthalpyTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BEnthalpyTest(2979906276)1.0$ @*/
/* Generated Thu Feb 22 16:31:33 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEnthalpyTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	@BeforeClass(alwaysRun=true)
	public void setUp() {
		enthalpyBlock = new BEnthalpy();
		executionParams = new BExecutionParams();
	}

	@AfterClass(alwaysRun=true)
	public void tearDown() {
		enthalpyBlock = null;
		executionParams = null;
	}

	@DataProvider(name = "provideInSlotNames")
	public Object[][] createInputSlotNames() {
		return new Object[][] { { "t" }, { "rth" }};
	}

	@DataProvider(name = "provideOutputSlotNames")
	public Object[][] createOutputSlotNames() {
		return new Object[][] { { "Y" } };
	}

	@DataProvider(name = "provideMiscSlotNames")
	public Object[][] createExecOrderSlotName() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	}

	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "In" }, { "onValue" }, { "OnVal" }, { "OffVal" }, { "OffValue" }, { "MinOn" } };
	}

	@Test(dataProvider = "provideInvalidSlotNames")
	public void testInvalidSlots(String slotName) {
		Assert.assertNull(enthalpyBlock.getSlot(slotName));
	}

	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		Assert.assertNotNull(enthalpyBlock.getSlot(slotName));
	}

	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_enthalpy.png");
		BIcon actualFbIcon = enthalpyBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		enthalpyBlock.setIcon(expectedFbIcon);
		actualFbIcon = enthalpyBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "provideSampleValues")
	public Object[][] sampleValues() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }, { 2.2250738585072014E-308 }, { -1.7976931348623157e+308 }, { Double.NEGATIVE_INFINITY }, { Double.POSITIVE_INFINITY }, { Double.NaN } };
	}

	@Test(dataProvider = "provideSampleValues")
	public void testSettingValueInenthalpyBlock(double snValue) {
		enthalpyBlock.setT(new BHonStatusNumeric(snValue));
		Assert.assertEquals(enthalpyBlock.getT().getValue(), snValue, 0.1);

		enthalpyBlock.setRth(new BHonStatusNumeric(snValue));
		Assert.assertEquals(enthalpyBlock.getRth().getValue(), snValue, 0.1);

		enthalpyBlock.setY(new BHonStatusNumeric(snValue));
		Assert.assertEquals(enthalpyBlock.getY().getValue(), snValue, 0.1);
	}

	@DataProvider(name = "provideTestData")
	public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Enthalpy_TestData.csv");
	}

	@Test(dataProvider = "provideTestData")
	public void testEnthalpyBlockWithTestData(List<String> inputs) throws BlockExecutionException, FileNotFoundException, BlockInitializationException {
		BEnthalpy enthalpyBlock = new BEnthalpy();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		setupNumericSlot(enthalpyBlock, BEnthalpy.t.getName(), inputs.get(1));
		setupNumericSlot(enthalpyBlock, BEnthalpy.rth.getName(), inputs.get(2));
		enthalpyBlock.initHoneywellComponent(executionParams);
		enthalpyBlock.executeHoneywellComponent(executionParams);
		double temp = TestDataHelper.getDouble(inputs.get(3), 0.0d);
		double expected;
		if((Double.isInfinite(temp) && temp > 0) || Double.isNaN(temp)) {
			expected = Double.POSITIVE_INFINITY;
		} else {
			expected = BigDecimal.valueOf(TestDataHelper.getDouble(inputs.get(3), 0.0d))
				  .setScale(6, RoundingMode.HALF_UP).doubleValue();
		}
		Assert.assertEquals(enthalpyBlock.getY().getValue(), expected, ""+inputs);
		enthalpyBlock = null;
	}

	//@Test
	public void testLinkRules() {
		BEnthalpy enthalpyBlock = new BEnthalpy();
		checkOutgoingLink(enthalpyBlock, BEnthalpy.t, false);
		checkOutgoingLink(enthalpyBlock, BEnthalpy.rth, false);
	}

	//@Test
	public void testLinkRules1() {
		BEnthalpy target = new BEnthalpy();
		BMaximum source = new BMaximum();		
		
		LinkCheck linkCheck = source.checkLink(target, target.getSlot("t"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("rth"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("t"), null);
		Assert.assertTrue(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("Y"), null);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = enthalpyBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BEnthalpy.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	public void setupNumericSlot(BEnthalpy enthalpyBlock, final String slotName, final String inputValue) {
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = enthalpyBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
               converter = new BStatusNumericToHonStatusNumeric();
               BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),enthalpyBlock.getSlot(slotName),converter);
               conversionLink.setEnabled(true);
               enthalpyBlock.add("Link?",conversionLink );                    
               conversionLink.activate();
               nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
          } else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),enthalpyBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				enthalpyBlock.add("Link?", conversionLink);				
				conversionLink.activate();
			}else{
				enthalpyBlock.linkTo(nm1, nm1.getSlot("out"), enthalpyBlock.getSlot(slotName));
			}			
			return;
		}

		switch (slotName) {
			case "t":
				enthalpyBlock.setT(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "rth":
				enthalpyBlock.setRth(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
		}
	}

	private void checkOutgoingLink(BEnthalpy block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = enthalpyBlock.checkLink(enthalpyBlock, enthalpyBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	private BEnthalpy enthalpyBlock;
	private BExecutionParams executionParams;

}
