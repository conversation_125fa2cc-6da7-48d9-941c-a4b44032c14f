/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BLimit;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Limit block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Lavanya B.
 * @since Feb 19, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BLimitTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BLimitTest(2979906276)1.0$ @*/
/* Generated Mon Feb 19 14:10:31 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLimitTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @BeforeClass
  public void setUp() {
	  limitBlock = new BLimit();
	  executionParams = new BExecutionParams();

  }
  
  @AfterClass
  public void tearDown() {
	  limitBlock = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"loLimit"}, {"hiLimit"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"LoLimit"}, {"LowLimit"}, {"HighLimit"}, {"HiLimit"}, {"TailOperation"}, {"IgnoreInvalidInput"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(limitBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(limitBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_limit.png");
	  BIcon actualFbIcon = limitBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  limitBlock.setIcon(expectedFbIcon);
	  actualFbIcon = limitBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInlimitBlock(double snValue) {
	  limitBlock.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(limitBlock.getX().getValue(), snValue, 0.1);
	  
	  limitBlock.setLoLimit(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(limitBlock.getLoLimit().getValue(), snValue, 0.1);	  
	  
	  limitBlock.setHiLimit(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(limitBlock.getHiLimit().getValue(), snValue, 0.1);
	  
	  limitBlock.setY(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(limitBlock.getY().getValue(), snValue, 0.1);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Limit_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testLimitBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BLimit limBlock = new BLimit();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(limBlock, BLimit.x.getName(), inputs.get(1));
	  setupNumericSlot(limBlock, BLimit.loLimit.getName(), inputs.get(2));
	  setupNumericSlot(limBlock, BLimit.hiLimit.getName(), inputs.get(3));	  
	  
	  limBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(limBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(4), 0.0d), Arrays.toString(inputs.toArray()));	  
	  limBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param limitBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BLimit limBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  limBlock.linkTo(nm1, nm1.getSlot("out"), limBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "x":
		  limBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "loLimit":
		  limBlock.setLoLimit(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;	
		  
	  case "hiLimit":
		  limBlock.setHiLimit(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  
  //@Test
	public void testLinkRules() {
		BLimit multiplyB = new BLimit();
		checkOutgoingLink(multiplyB, BLimit.x, false);
		checkOutgoingLink(multiplyB, BLimit.loLimit, false);
		checkOutgoingLink(multiplyB, BLimit.hiLimit, false);
	}

	private void checkOutgoingLink(BLimit block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = limitBlock.checkLink(limitBlock, limitBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
 
	 @Test
	  public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		  BLimit limBlock = new BLimit();
		  limBlock.setX(new BHonStatusNumeric(-75654));
		  limBlock.setLoLimit(new BHonStatusNumeric(-0.001));
		  limBlock.setHiLimit(new BHonStatusNumeric(-0.001));
		  limBlock.initHoneywellComponent(null);
		  executionParams.setIterationInterval(1000);
		  for(int i=0; i<5000; i++) {
			  limBlock.executeHoneywellComponent(executionParams);
			  Assert.assertEquals(limBlock.getY().getValue(), -0.001, "Failed in iteration #"+i);
		  }
		  
		  BogFileUtil bogUtil = new BogFileUtil();
		  try {
			  File bogFile = bogUtil.saveComponentToBogFile("Logarithm", limBlock);
			  BLimit logarithmSaved = (BLimit) bogUtil.getComponentFromBogFile(bogFile);

			  Assert.assertEquals(logarithmSaved.getX().getValue(), -75654, 0.1,"Failed to verify limit block input X after restart");
			  Assert.assertEquals(logarithmSaved.getLoLimit().getValue(), -0.001, 0.1,"Failed to verify limit block input loLimit after restart");
			  Assert.assertEquals(logarithmSaved.getHiLimit().getValue(), -0.001, 0.1,"Failed to verify limit block input hiLimit after restart");
			  Assert.assertEquals(logarithmSaved.getY().getValue(), Double.POSITIVE_INFINITY, 0.1,"Failed to verify limit block output after restart");
		  } catch (IOException e) {
			  e.printStackTrace();
		  }
		  limBlock = null;
	  }
	 
		@Test
		public void testInputPropertiesList() {
			List<Property> inputPropList = limitBlock.getInputPropertiesList();
			List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
			Arrays.sort(actualInputParamNames);
			String[] expectedInputParamNames = { BLimit.x.getName(),BLimit.loLimit.getName(),BLimit.hiLimit.getName()};
			Arrays.sort(expectedInputParamNames);

			Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
		}
		
		@Test
		public void testOutputPropertiesList() {
			List<Property> outputPropList = limitBlock.getOutputPropertiesList();
			List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
			Arrays.sort(actualOutputParamNames);
			String[] expectedOutputParamNames = {BLimit.Y.getName()};
			Arrays.sort(expectedOutputParamNames);

			Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
		}
	
 
  
  private BLimit limitBlock;
  private BExecutionParams executionParams;


}
