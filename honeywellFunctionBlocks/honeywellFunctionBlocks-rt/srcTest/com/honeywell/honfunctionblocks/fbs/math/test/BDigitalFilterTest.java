/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BDigitalFilter;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Digital Filter block test case as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test caes ID: TBD
 * <AUTHOR> - Lavanya B.
 * @since Feb 24, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BDigitalFilterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BDigitalFilterTest(2979906276)1.0$ @*/
/* Generated Sat Feb 24 11:47:56 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDigitalFilterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @BeforeClass(alwaysRun=true)
  public void setUp() throws Exception {
	  digitalFilter = new BDigitalFilter();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass(alwaysRun=true)
  public void tearDown() {
	  digitalFilter = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"tau"}, {"zeroInit"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"Tau"}, {"ZeroInit"}, {"TailOperation"}, {"IgnoreInvalidInput"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(digitalFilter.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(digitalFilter.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "digital_filter.png");
	  BIcon actualFbIcon = digitalFilter.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  digitalFilter.setIcon(expectedFbIcon);
	  actualFbIcon = digitalFilter.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInBDigitalFilter(double snValue) {
	  digitalFilter.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(digitalFilter.getX().getValue(), snValue, 0.1);
	  
	  digitalFilter.setTau(snValue);
	  Assert.assertEquals(digitalFilter.getTau(), snValue, 0.1);	  
	  
	  digitalFilter.setZeroInit((int)snValue);
	  Assert.assertEquals(digitalFilter.getZeroInit(), (int)snValue, 0.1);
	  
	  digitalFilter.setY(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(digitalFilter.getY().getValue(), snValue, 0.1);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
				  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/DigitalFilter_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testDigitalFilterBlockWithSequenceOfTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
	  BDigitalFilter digFilterBlock = new BDigitalFilter();
	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
		  List<String> inputs = iterator.next();
		  
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(digFilterBlock, BDigitalFilter.x.getName(), inputs.get(1));
		  digFilterBlock.setTau(TestDataHelper.getDouble(inputs.get(2), 1.0));
		  digFilterBlock.setZeroInit(TestDataHelper.getInt(inputs.get(3), 0));  
	
		  if(seqNo==1)	
			  digFilterBlock.initHoneywellComponent(executionParams);
		  digFilterBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(digFilterBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(4), 0.0d), "Failed at step #"+seqNo+" for input data "+inputs+"; ");
	  }
	  
	  digFilterBlock = null;
  
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param digitalFilter
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BDigitalFilter digFilterBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  digFilterBlock.linkTo(nm1, nm1.getSlot("out"), digFilterBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "x":
		  digFilterBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  @Test
  public void testConfigProperties() {
	  List<Property> configList = digitalFilter.getConfigPropertiesList();
	  Assert.assertEquals(configList.get(0).getName(), BDigitalFilter.zeroInit.getName());
  }
  
  //@Test
	public void testLinkRules() {
		BDigitalFilter multiplyB = new BDigitalFilter();
		checkOutgoingLink(multiplyB, BDigitalFilter.x, false);
		checkOutgoingLink(multiplyB, BDigitalFilter.tau, false);
		checkOutgoingLink(multiplyB, BDigitalFilter.zeroInit, false);
		
		checkOutgoingLink(multiplyB, BDigitalFilter.Y, false);
	}
  
  @Test
	public void testOutToIslotsLinkRules() {
	  BDigitalFilter src = new BDigitalFilter();
	  BDigitalFilter tgt = new BDigitalFilter();
		
		LinkCheckUtil.checkValidLink(src, src.getSlot(BDigitalFilter.Y.getName()), tgt, tgt.getSlot(BDigitalFilter.x.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BDigitalFilter.Y.getName()), tgt, tgt.getSlot(BDigitalFilter.tau.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BDigitalFilter.Y.getName()), tgt, tgt.getSlot(BDigitalFilter.zeroInit.getName()));
	}
	
	@Test
	public void testInToOutslotsLinkRules() {
		BDigitalFilter src = new BDigitalFilter();
		BDigitalFilter tgt = new BDigitalFilter();
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BDigitalFilter.x.getName()), tgt, tgt.getSlot(BDigitalFilter.Y.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BDigitalFilter.tau.getName()), tgt, tgt.getSlot(BDigitalFilter.Y.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BDigitalFilter.zeroInit.getName()), tgt, tgt.getSlot(BDigitalFilter.Y.getName()));
	}

	private void checkOutgoingLink(BDigitalFilter block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = digitalFilter.checkLink(digitalFilter, digitalFilter.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	/*@Test
	public void testIfTauMultiplierIsRecalculatedOnChange() throws BlockInitializationException {
		BDigitalFilter digFilterBlock = new BDigitalFilter();
		digFilterBlock.setX(new BHonStatusNumeric(5));
		digFilterBlock.setTau(1);
		digFilterBlock.setZeroInit(1);
		digFilterBlock.initHoneywellComponent(executionParams);
		digFilterBlock.executeBlock(executionParams);
		Assert.assertEquals(digFilterBlock.getY().getValue(), 3.160603,0.1);
		
		
		digFilterBlock.setX(new BHonStatusNumeric(200));
		digFilterBlock.setTau(10);
		digFilterBlock.setZeroInit(0);
		digFilterBlock.executeBlock(executionParams);
		Assert.assertEquals(digFilterBlock.getY().getValue(), 21.892348,0.1);
	}*/
 
	  @Test
	  public void testIfTauMultIsCalculatedOnAdd() throws Exception {
		  BDigitalFilter bDigitalFilter = new BDigitalFilter();
		  bDigitalFilter.setX(new BHonStatusNumeric(10, BStatus.ok));
		  
		  bDigitalFilter.executeHoneywellComponent(executionParams);
		  
		  double tauValue = bDigitalFilter.getTau();
		  double tauTemp = limitInput(tauValue,TAU_LOW_LIMIT,TAU_HIGH_LIMIT,TAU_INVALID);
		  double iterationIntervalInSec = SECOND/UnitConstants.THOUSAND_MILLI_SECOND;
		  tauTemp = -1.0 * (iterationIntervalInSec/tauTemp);
		  double tauMult = 1.0-Math.exp(tauTemp);
		  Assert.assertEquals(bDigitalFilter.getTauMult(), tauMult);
	  }
	  
		@Test
		public void testInputPropertiesList() {
			List<Property> inputPropList = digitalFilter.getInputPropertiesList();
			List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
			Arrays.sort(actualInputParamNames);
			String[] expectedInputParamNames = { BDigitalFilter.x.getName(), BDigitalFilter.tau.getName()};
			Arrays.sort(expectedInputParamNames);

			Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
		}
		
		@Test
		public void testOutputPropertiesList() {
			List<Property> outputPropList = digitalFilter.getOutputPropertiesList();
			List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
			Arrays.sort(actualOutputParamNames);
			String[] expectedOutputParamNames = {BDigitalFilter.Y.getName()};
			Arrays.sort(expectedOutputParamNames);

			Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
		}

	  private double limitInput(final double inValue, final double lowLimit, final double highLimit, final double invalidValue) {
			double limValue;
			limValue = inValue;
			if ( Double.isNaN(inValue) || (inValue>0 && Double.isInfinite(inValue))  )
		    {
		        limValue = invalidValue;
		    }
		    else if ( limValue > highLimit )
		    {
		        limValue = highLimit;
		    }
		    else if ( limValue < lowLimit )
		    {
		        limValue = lowLimit;
		    }
		    return limValue;
		}
		

  private BDigitalFilter digitalFilter;
  private BExecutionParams executionParams;
  private static final int SECOND = 1000;
  private static final double TAU_LOW_LIMIT = 1.0d;
  private static final double TAU_HIGH_LIMIT = 65535.0d;
  private static final double TAU_INVALID = 0.05d;

}
