/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BDivOperationEnum;
import com.honeywell.honfunctionblocks.fbs.math.BDivide;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Divide block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Suresh Khatri
 * @since Jan 31, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BDivideTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BDivideTest(2979906276)1.0$ @*/
/* Generated Wed Jan 31 13:03:29 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDivideTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	  @BeforeClass(alwaysRun=true)
	  public void setUp() {
		  divideBlock = new BDivide();
		  executionParams = new BExecutionParams();

	  }
	  
	  @AfterClass
	  public void tearDown() {
		  divideBlock = null;
		  executionParams = null;
	  }
	  
	  
	  @DataProvider(name="provideInSlotNames")
	  public Object[][] createInputSlotNames() {
		  return new Object[][]{{"x1"}, {"x2"}, {"ignoreInvalidInput"}, {"tailOperation"}, {"divOperation"}};
	  }
	  
	  @DataProvider(name="provideOutputSlotNames")
	  public Object[][] createOutputSlotNames() {
		  return new Object[][] {{"Y"}};
	  }
	  
	  @DataProvider(name="provideMiscSlotNames")
	  public Object[][] createExecOrderSlotName() {
		  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	  }
	  
	  @DataProvider(name="provideAllSlotNames")
	  public Object[][] createAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  }
	  
	  @DataProvider(name = "provideInvalidSlotNames")
	  public Object[][] invalidSlotNames() {
		  return new Object[][]{{"In"}, {"onValue"}, {"OnVal"}, {"OffVal"}, {"OffValue"}, {"MinOn"}, {"InvalidFlag"}, {"TailOperation"}};
	  }
	  
	  @Test(dataProvider="provideInvalidSlotNames")
	  public void testInvalidSlots(String slotName){
		  Assert.assertNull(divideBlock.getSlot(slotName));
	  }  
	  
	  @Test(dataProvider="provideAllSlotNames")
	  public void testSlotAvailability(String slotName) {
		  Assert.assertNotNull(divideBlock.getSlot(slotName));
	  }
	  
	  @Test(groups={"testIconSlot"})
	  public void testIconSlot(){
		  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_divide.png");
		  BIcon actualFbIcon = divideBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);

		  //check if new icon can be set on Divide to update modified state
		  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		  divideBlock.setIcon(expectedFbIcon);
		  actualFbIcon = divideBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);
	  }
	  
	  @DataProvider(name = "provideSampleValues")
	  public Object[][] sampleValues() {
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	  }

	  @Test(dataProvider="provideSampleValues")
	  public void testSettingValueIndivideBlock(double snValue) {
		  divideBlock.setX1(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(divideBlock.getX1().getValue(), snValue, 0.1);
		  
		  divideBlock.setX2(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(divideBlock.getX2().getValue(), snValue, 0.1);	  

		  divideBlock.setY(new BHonStatusNumeric(snValue)); 
		  Assert.assertEquals(divideBlock.getY().getValue(), snValue, 0.1);
		  
		  divideBlock.setIgnoreInvalidInput(true);
		  Assert.assertEquals(divideBlock.getIgnoreInvalidInput(), true);
		  
		  divideBlock.setIgnoreInvalidInput(false);
		  Assert.assertEquals(divideBlock.getIgnoreInvalidInput(), false);
	  }
	  
	  @DataProvider(name="provideTestData")
	  public Object[][] getTesData() {
		  return TestDataHelper.getTestDataInTestNGFormat(
				  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Divide_TestData.csv");
	  }
	  
	  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	  public void testDivideBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BDivide divideBlock = new BDivide();
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(divideBlock, BDivide.x1.getName(), inputs.get(2));
		  setupNumericSlot(divideBlock, BDivide.x2.getName(), inputs.get(3));
		  divideBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
		  divideBlock.setDivOperation(BDivOperationEnum.make(TestDataHelper.getInt(inputs.get(4),0)));
		  divideBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(5), 0)));
		  
		  divideBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(divideBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(6), 0.0d), Arrays.toString(inputs.toArray()));
		  divideBlock = null;
	  }
	  
	  /**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param divideBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BDivide divideBlock, final String slotName, final String inputValue){
		  if(TestDataHelper.isConnected(inputValue)){
			  BNumericConst nm1 = new BNumericConst();
			  divideBlock.linkTo(nm1, nm1.getSlot("out"), divideBlock.getSlot(slotName));
			  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			  return;
		  }

		  switch (slotName) {
		  case "x1":
			  divideBlock.setX1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;

		  case "x2":
			  divideBlock.setX2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;	
		  }
	  }
	  
	  //@Test(groups = { "testLinkRules" })
		public void testLinkRules() {
			BDivide divide = new BDivide();
			checkOutgoingLink(divide, BDivide.x1, false);
			checkOutgoingLink(divide, BDivide.x2, false);
			checkOutgoingLink(divide, BDivide.ignoreInvalidInput, false);
			checkOutgoingLink(divide, BDivide.divOperation, false);
			checkOutgoingLink(divide, BDivide.tailOperation, false);
		}

		private void checkOutgoingLink(BDivide block, Property prop, boolean isLinkValid) {
			LinkCheck checkLink = divideBlock.checkLink(divideBlock, divideBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
			Assert.assertEquals(checkLink.isValid(), isLinkValid);
		}
		
		@Test
		public void testConfigProperties() {
			List<Property> configList = divideBlock.getConfigPropertiesList();
			List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors .toList());
			String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
			Arrays.sort(actualConfigParamnames);
			String[] expectedConfigParams = { BDivide.ignoreInvalidInput.getName(), BDivide.tailOperation.getName(), BDivide.divOperation.getName() };
			Arrays.sort(expectedConfigParams);
			
			Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
		}
		
		@Test
		public void testInputPropertiesList() {
			List<Property> inputPropList = divideBlock.getInputPropertiesList();
			List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
			Arrays.sort(actualInputParamNames);
			String[] expectedInputParamNames = { BDivide.x1.getName(), BDivide.x2.getName()};
			Arrays.sort(expectedInputParamNames);

			Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
		}
		
		@Test
		public void testOutputPropertiesList() {
			List<Property> outputPropList = divideBlock.getOutputPropertiesList();
			List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
			Arrays.sort(actualOutputParamNames);
			String[] expectedOutputParamNames = {BDivide.Y.getName()};
			Arrays.sort(expectedOutputParamNames);

			Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
		}

	  
	  private BDivide divideBlock;
	  private BExecutionParams executionParams;

}
