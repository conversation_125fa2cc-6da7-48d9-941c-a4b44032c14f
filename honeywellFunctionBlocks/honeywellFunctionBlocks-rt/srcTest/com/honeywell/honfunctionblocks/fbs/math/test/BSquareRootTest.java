/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BSquareRoot;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 *
 * Testing of SquareRoot block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TBD
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 16, 2018
 */

@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BSquareRootTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BSquareRootTest(2979906276)1.0$ @*/
/* Generated Mon Feb 16 19:58:15 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSquareRootTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		squareRootBlock = new BSquareRoot();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass
	public void tearDown() {
		squareRootBlock = null;
		executionParams = null;
	}

	private Object[] getInputSlotNames() {
		return new Object[]{"x", "negInvalid"};
	}

	private Object[] getOutputSlotNames() {
		return new Object[]{"Y"};
	}

	private Object[] getConfigurationSlotNames() {
		return new Object[]{"tailOperation"};
	}

	private Object[] getExecOrderSlotNames() {
		return new Object[] {"ExecutionOrder", "toolVersion"};	  
	}

	@DataProvider(name="allSlotNames")
	public Object[] getAllSlotNames() {
		List<Object> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getConfigurationSlotNames()));
		slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		return slotArrayList.toArray(new Object[slotArrayList.size()]);
	}

	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(squareRootBlock.getSlot(slotName));
	}

	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"X"}, {"NegInvalid"}, {"ignoreInvalidInput"}, {"y"}, {"output"}, {"OUTPUT"}};
	}

	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(squareRootBlock.getSlot(slotName));
	}

	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_squareroot.png");
		BIcon actualFbIcon = squareRootBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		squareRootBlock.setIcon(expectedFbIcon);
		actualFbIcon = squareRootBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			{-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	}

	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		squareRootBlock.setX(new BHonStatusNumeric(value));
		Assert.assertEquals(squareRootBlock.getX().getValue(), value, 0.1);

		squareRootBlock.setY(new BHonStatusNumeric(value));
		Assert.assertEquals(squareRootBlock.getY().getValue(), value, 0.1);

		squareRootBlock.setNegInvalid(new BHonStatusBoolean(true, BStatus.ok));
		Assert.assertEquals(squareRootBlock.getNegInvalid().getBoolean(), true);

		squareRootBlock.setNegInvalid(new BHonStatusBoolean(false, BStatus.ok));
		Assert.assertEquals(squareRootBlock.getNegInvalid().getBoolean(), false);	  
	}

	//@Test(groups = { "testLinkRules" })
	public void testLinkRules() {
		BSquareRoot squareRootB = new BSquareRoot();
		checkOutgoingLink(squareRootB, BSquareRoot.x, false);
		checkOutgoingLink(squareRootB, BSquareRoot.negInvalid, false);
		checkOutgoingLink(squareRootB, BSquareRoot.tailOperation, false);
	}

	private void checkOutgoingLink(BSquareRoot block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = squareRootBlock.checkLink(squareRootBlock, squareRootBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BSquareRoot squareRootTemp = new BSquareRoot();
		executionParams.setIterationInterval(1000);
		squareRootTemp.setX(new BHonStatusNumeric(25));
		squareRootTemp.setNegInvalid(new BHonStatusBoolean(false, BStatus.ok));
		squareRootTemp.setTailOperation(BTailOperationEnum.DEFAULT);	
		for (int j = 0; j < 5000; j++) {
			squareRootTemp.executeHoneywellComponent(executionParams);
		}
		
		Assert.assertEquals(squareRootTemp.getY().getValue(), 5, 0.1);
		
		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("SquareRoot", squareRootTemp);
			BSquareRoot sqrtSaved = (BSquareRoot) bogUtil.getComponentFromBogFile(bogFile);
			
			Assert.assertEquals(sqrtSaved.getX().getValue(), 0, 25, "Failed to verify x");
			Assert.assertEquals(sqrtSaved.getNegInvalid().getBoolean(), false, "Failed to verify negInvalid");
			Assert.assertEquals(sqrtSaved.getY().getValue(), 0.0, "Failed to verify Y");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@DataProvider(name = "squareRootTestData")
	public Object[][] getsquareRootTestData() throws FileNotFoundException{
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/SquareRoot_TestData.csv");
	}

	@Test(dataProvider = "squareRootTestData")
	public void testsquareRootBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		BSquareRoot squareRootBlock = new BSquareRoot();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		setupNumericSlot(squareRootBlock, BSquareRoot.x.getName(), inputs.get(1));
		setupNumericSlot(squareRootBlock, BSquareRoot.negInvalid.getName(), inputs.get(2));
		squareRootBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(3), 0)));

		squareRootBlock.executeHoneywellComponent(executionParams);
		Assert.assertEquals(squareRootBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(4), 0.0d), 0.1);	  
		squareRootBlock = null;
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = squareRootBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BSquareRoot.tailOperation.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = squareRootBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BSquareRoot.x.getName(),BSquareRoot.negInvalid.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = squareRootBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BSquareRoot.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}


	/**
	 * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	 * @param subtractBlock
	 * @param slotName
	 * @param inputValue
	 */
	public void setupNumericSlot(BSquareRoot squareRootBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = squareRootBlock.getProperty(slotName).getType();		
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusBoolean.TYPE)) {
				BConverter converter = null;
				converter = new BStatusNumericToStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),squareRootBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				squareRootBlock.add("Link?",conversionLink );				
				conversionLink.activate();
			}else{
				squareRootBlock.linkTo(nm1, nm1.getSlot("out"), squareRootBlock.getSlot(slotName));
			}
			
			return;
		}

		switch (slotName) {
		case "x":
			squareRootBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;

		case "negInvalid":
			squareRootBlock.setNegInvalid(TestDataHelper.getHonStatusBoolean(inputValue));
			break;	
		}
	}

	private BSquareRoot squareRootBlock;
	private BExecutionParams executionParams;

}
