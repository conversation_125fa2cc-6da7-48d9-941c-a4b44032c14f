#Iteration Intervel,Ignore invalid input,x1,x2,tail operation,y out
1000,1,connected=10,connected=10,0,0.0
1000,1,10,10,0,0.0
1000,1,-10,-10,0,0.0
1000,1,0,0,0,0.0
1000,1,1,1,0,0.0
1000,1,0.001,0.001,0,0.0
1000,1,-0.001,-0.001,0,0.0
1000,1,unconnected,unconnected,0,0.0
1000,1,unconnected=5,unconnected=5,0,0.0
1000,1,unconnected=nan,unconnected=nan,0,0.0
1000,1,unconnected=-inf,unconnected=-inf,0,0.0
1000,1,connected=+inf,connected=+inf,0,+inf
1000,1,connected=-inf,connected=-inf,0,+inf
1000,1,connected=nan,connected=nan,0,+inf
1000,1,10,10,0,0.0
1000,1,10,-10,0,20.0
1000,1,10,0,0,10.0
1000,1,10,1,0,9.0
1000,1,10,0.001,0,9.999
1000,1,10,-0.001,0,10.001
1000,1,10,unconnected,0,10.0
1000,1,10,unconnected=5,0,10.0
1000,1,10,unconnected=nan,0,10.0
1000,1,10,unconnected=-inf,0,10.0
1000,1,10,connected=+inf,0,10.0
1000,1,10,connected=-inf,0,+inf
1000,1,10,connected=nan,0,10.0
1000,1,-10,10,0,-20.0
1000,1,-10,-10,0,0.0
1000,1,-10,0,0,-10.0
1000,1,-10,1,0,-11.0
1000,1,-10,0.001,0,-10.001
1000,1,-10,-0.001,0,-9.999
1000,1,-10,unconnected,0,-10.0
1000,1,-10,unconnected=5,0,-10.0
1000,1,-10,unconnected=nan,0,-10.0
1000,1,-10,unconnected=-inf,0,-10.0
1000,1,-10,connected=+inf,0,-10.0
1000,1,-10,connected=-inf,0,+inf
1000,1,-10,connected=nan,0,-10.0
1000,1,0,10,0,-10.0
1000,1,0,-10,0,10.0
1000,1,0,0,0,0.0
1000,1,0,1,0,-1.0
1000,1,0,0.001,0,-0.001
1000,1,0,-0.001,0,0.001
1000,1,0,unconnected,0,0.0
1000,1,0,unconnected=5,0,0.0
1000,1,0,unconnected=nan,0,0.0
1000,1,0,unconnected=-inf,0,0.0
1000,1,0,connected=+inf,0,0.0
1000,1,0,connected=-inf,0,+inf
1000,1,0,connected=nan,0,0.0
1000,1,1,10,0,-9.0
1000,1,1,-10,0,11.0
1000,1,1,0,0,1.0
1000,1,1,1,0,0.0
1000,1,1,0.001,0,0.999
1000,1,1,-0.001,0,1.001
1000,1,1,unconnected,0,1.0
1000,1,1,unconnected=5,0,1.0
1000,1,1,unconnected=nan,0,1.0
1000,1,1,unconnected=-inf,0,1.0
1000,1,1,connected=+inf,0,1.0
1000,1,1,connected=-inf,0,+inf
1000,1,1,connected=nan,0,1.0
1000,1,0.001,10,0,-9.999
1000,1,0.001,-10,0,10.001
1000,1,0.001,0,0,0.001
1000,1,0.001,1,0,-0.999
1000,1,0.001,0.001,0,0.0
1000,1,0.001,-0.001,0,0.002
1000,1,0.001,unconnected,0,0.001
1000,1,0.001,unconnected=5,0,0.001
1000,1,0.001,unconnected=nan,0,0.001
1000,1,0.001,unconnected=-inf,0,0.001
1000,1,0.001,connected=+inf,0,0.001
1000,1,0.001,connected=-inf,0,+inf
1000,1,0.001,connected=nan,0,0.001
1000,1,-0.001,10,0,-10.001
1000,1,-0.001,-10,0,9.999
1000,1,-0.001,0,0,-0.001
1000,1,-0.001,1,0,-1.001
1000,1,-0.001,0.001,0,-0.002
1000,1,-0.001,-0.001,0,0.0
1000,1,-0.001,unconnected,0,-0.001
1000,1,-0.001,unconnected=5,0,-0.001
1000,1,-0.001,unconnected=nan,0,-0.001
1000,1,-0.001,unconnected=-inf,0,-0.001
1000,1,-0.001,connected=+inf,0,-0.001
1000,1,-0.001,connected=-inf,0,+inf
1000,1,-0.001,connected=nan,0,-0.001
1000,1,unconnected,10,0,-10.0
1000,1,unconnected,-10,0,10.0
1000,1,unconnected,0,0,0.0
1000,1,unconnected,1,0,-1.0
1000,1,unconnected,0.001,0,-0.001
1000,1,unconnected,-0.001,0,0.001
1000,1,unconnected,unconnected,0,0.0
1000,1,unconnected,unconnected=5,0,0.0
1000,1,unconnected,unconnected=nan,0,0.0
1000,1,unconnected,unconnected=-inf,0,0.0
1000,1,unconnected,connected=+inf,0,0.0
1000,1,unconnected,connected=-inf,0,+inf
1000,1,unconnected,connected=nan,0,0.0
1000,1,unconnected=5,10,0,-10.0
1000,1,unconnected=5,-10,0,10.0
1000,1,unconnected=5,0,0,0.0
1000,1,unconnected=5,1,0,-1.0
1000,1,unconnected=5,0.001,0,-0.001
1000,1,unconnected=5,-0.001,0,0.001
1000,1,unconnected=5,unconnected,0,0.0
1000,1,unconnected=5,unconnected=5,0,0.0
1000,1,unconnected=5,unconnected=nan,0,0.0
1000,1,unconnected=5,unconnected=-inf,0,0.0
1000,1,unconnected=5,connected=+inf,0,0.0
1000,1,unconnected=5,connected=-inf,0,+inf
1000,1,unconnected=5,connected=nan,0,0.0
1000,1,unconnected=nan,10,0,-10.0
1000,1,unconnected=nan,-10,0,10.0
1000,1,unconnected=nan,0,0,0.0
1000,1,unconnected=nan,1,0,-1.0
1000,1,unconnected=nan,0.001,0,-0.001
1000,1,unconnected=nan,-0.001,0,0.001
1000,1,unconnected=nan,unconnected,0,0.0
1000,1,unconnected=nan,unconnected=5,0,0.0
1000,1,unconnected=nan,unconnected=nan,0,0.0
1000,1,unconnected=nan,unconnected=-inf,0,0.0
1000,1,unconnected=nan,connected=+inf,0,0.0
1000,1,unconnected=nan,connected=-inf,0,+inf
1000,1,unconnected=nan,connected=nan,0,0.0
1000,1,unconnected=-inf,10,0,-10.0
1000,1,unconnected=-inf,-10,0,10.0
1000,1,unconnected=-inf,0,0,0.0
1000,1,unconnected=-inf,1,0,-1.0
1000,1,unconnected=-inf,0.001,0,-0.001
1000,1,unconnected=-inf,-0.001,0,0.001
1000,1,unconnected=-inf,unconnected,0,0.0
1000,1,unconnected=-inf,unconnected=5,0,0.0
1000,1,unconnected=-inf,unconnected=nan,0,0.0
1000,1,unconnected=-inf,unconnected=-inf,0,0.0
1000,1,unconnected=-inf,connected=+inf,0,0.0
1000,1,unconnected=-inf,connected=-inf,0,+inf
1000,1,unconnected=-inf,connected=nan,0,0.0
1000,1,connected=+inf,10,0,-10.0
1000,1,connected=+inf,-10,0,10.0
1000,1,connected=+inf,0,0,0.0
1000,1,connected=+inf,1,0,-1.0
1000,1,connected=+inf,0.001,0,-0.001
1000,1,connected=+inf,-0.001,0,0.001
1000,1,connected=+inf,unconnected,0,0.0
1000,1,connected=+inf,unconnected=5,0,0.0
1000,1,connected=+inf,unconnected=nan,0,0.0
1000,1,connected=+inf,unconnected=-inf,0,0.0
1000,1,connected=+inf,connected=+inf,0,+inf
1000,1,connected=+inf,connected=-inf,0,+inf
1000,1,connected=+inf,connected=nan,0,+inf
1000,1,connected=-inf,10,0,-inf
1000,1,connected=-inf,-10,0,-inf
1000,1,connected=-inf,0,0,-inf
1000,1,connected=-inf,1,0,-inf
1000,1,connected=-inf,0.001,0,-inf
1000,1,connected=-inf,-0.001,0,-inf
1000,1,connected=-inf,unconnected,0,-inf
1000,1,connected=-inf,unconnected=5,0,-inf
1000,1,connected=-inf,unconnected=nan,0,-inf
1000,1,connected=-inf,unconnected=-inf,0,-inf
1000,1,connected=-inf,connected=+inf,0,-inf
1000,1,connected=-inf,connected=-inf,0,+inf
1000,1,connected=-inf,connected=nan,0,-inf
1000,1,connected=nan,10,0,-10.0
1000,1,connected=nan,-10,0,10.0
1000,1,connected=nan,0,0,0.0
1000,1,connected=nan,1,0,-1.0
1000,1,connected=nan,0.001,0,-0.001
1000,1,connected=nan,-0.001,0,0.001
1000,1,connected=nan,unconnected,0,0.0
1000,1,connected=nan,unconnected=5,0,0.0
1000,1,connected=nan,unconnected=nan,0,0.0
1000,1,connected=nan,unconnected=-inf,0,0.0
1000,1,connected=nan,connected=+inf,0,+inf
1000,1,connected=nan,connected=-inf,0,+inf
1000,1,connected=nan,connected=nan,0,+inf
1000,1,10,10,1,0.0
1000,1,-10,-10,1,0.0
1000,1,0,0,1,0.0
1000,1,1,1,1,0.0
1000,1,0.001,0.001,1,0.0
1000,1,-0.001,-0.001,1,0.0
1000,1,unconnected,unconnected,1,0.0
1000,1,unconnected=5,unconnected=5,1,0.0
1000,1,unconnected=nan,unconnected=nan,1,0.0
1000,1,unconnected=-inf,unconnected=-inf,1,0.0
1000,1,connected=+inf,connected=+inf,1,+inf
1000,1,connected=-inf,connected=-inf,1,+inf
1000,1,connected=nan,connected=nan,1,+inf
1000,1,10,10,1,0.0
1000,1,10,-10,1,20.0
1000,1,10,0,1,10.0
1000,1,10,1,1,9.0
1000,1,10,0.001,1,9.999
1000,1,10,-0.001,1,10.001
1000,1,10,unconnected,1,10.0
1000,1,10,unconnected=5,1,10.0
1000,1,10,unconnected=nan,1,10.0
1000,1,10,unconnected=-inf,1,10.0
1000,1,10,connected=+inf,1,10.0
1000,1,10,connected=-inf,1,+inf
1000,1,10,connected=nan,1,10.0
1000,1,-10,10,1,20.0
1000,1,-10,-10,1,0.0
1000,1,-10,0,1,10.0
1000,1,-10,1,1,11.0
1000,1,-10,0.001,1,10.001
1000,1,-10,-0.001,1,9.999
1000,1,-10,unconnected,1,10.0
1000,1,-10,unconnected=5,1,10.0
1000,1,-10,unconnected=nan,1,10.0
1000,1,-10,unconnected=-inf,1,10.0
1000,1,-10,connected=+inf,1,10.0
1000,1,-10,connected=-inf,1,+inf
1000,1,-10,connected=nan,1,10.0
1000,1,0,10,1,10.0
1000,1,0,-10,1,10.0
1000,1,0,0,1,0.0
1000,1,0,1,1,1.0
1000,1,0,0.001,1,0.001
1000,1,0,-0.001,1,0.001
1000,1,0,unconnected,1,0.0
1000,1,0,unconnected=5,1,0.0
1000,1,0,unconnected=nan,1,0.0
1000,1,0,unconnected=-inf,1,0.0
1000,1,0,connected=+inf,1,0.0
1000,1,0,connected=-inf,1,+inf
1000,1,0,connected=nan,1,0.0
1000,1,1,10,1,9.0
1000,1,1,-10,1,11.0
1000,1,1,0,1,1.0
1000,1,1,1,1,0.0
1000,1,1,0.001,1,0.999
1000,1,1,-0.001,1,1.001
1000,1,1,unconnected,1,1.0
1000,1,1,unconnected=5,1,1.0
1000,1,1,unconnected=nan,1,1.0
1000,1,1,unconnected=-inf,1,1.0
1000,1,1,connected=+inf,1,1.0
1000,1,1,connected=-inf,1,+inf
1000,1,1,connected=nan,1,1.0
1000,1,0.001,10,1,9.999
1000,1,0.001,-10,1,10.001
1000,1,0.001,0,1,0.001
1000,1,0.001,1,1,0.999
1000,1,0.001,0.001,1,0.0
1000,1,0.001,-0.001,1,0.002
1000,1,0.001,unconnected,1,0.001
1000,1,0.001,unconnected=5,1,0.001
1000,1,0.001,unconnected=nan,1,0.001
1000,1,0.001,unconnected=-inf,1,0.001
1000,1,0.001,connected=+inf,1,0.001
1000,1,0.001,connected=-inf,1,+inf
1000,1,0.001,connected=nan,1,0.001
1000,1,-0.001,10,1,10.001
1000,1,-0.001,-10,1,9.999
1000,1,-0.001,0,1,0.001
1000,1,-0.001,1,1,1.001
1000,1,-0.001,0.001,1,0.002
1000,1,-0.001,-0.001,1,0.0
1000,1,-0.001,unconnected,1,0.001
1000,1,-0.001,unconnected=5,1,0.001
1000,1,-0.001,unconnected=nan,1,0.001
1000,1,-0.001,unconnected=-inf,1,0.001
1000,1,-0.001,connected=+inf,1,0.001
1000,1,-0.001,connected=-inf,1,+inf
1000,1,-0.001,connected=nan,1,0.001
1000,1,unconnected,10,1,10.0
1000,1,unconnected,-10,1,10.0
1000,1,unconnected,0,1,0.0
1000,1,unconnected,1,1,1.0
1000,1,unconnected,0.001,1,0.001
1000,1,unconnected,-0.001,1,0.001
1000,1,unconnected,unconnected,1,0.0
1000,1,unconnected,unconnected=5,1,0.0
1000,1,unconnected,unconnected=nan,1,0.0
1000,1,unconnected,unconnected=-inf,1,0.0
1000,1,unconnected,connected=+inf,1,0.0
1000,1,unconnected,connected=-inf,1,+inf
1000,1,unconnected,connected=nan,1,0.0
1000,1,unconnected=5,10,1,10.0
1000,1,unconnected=5,-10,1,10.0
1000,1,unconnected=5,0,1,0.0
1000,1,unconnected=5,1,1,1.0
1000,1,unconnected=5,0.001,1,0.001
1000,1,unconnected=5,-0.001,1,0.001
1000,1,unconnected=5,unconnected,1,0.0
1000,1,unconnected=5,unconnected=5,1,0.0
1000,1,unconnected=5,unconnected=nan,1,0.0
1000,1,unconnected=5,unconnected=-inf,1,0.0
1000,1,unconnected=5,connected=+inf,1,0.0
1000,1,unconnected=5,connected=-inf,1,+inf
1000,1,unconnected=5,connected=nan,1,0.0
1000,1,unconnected=nan,10,1,10.0
1000,1,unconnected=nan,-10,1,10.0
1000,1,unconnected=nan,0,1,0.0
1000,1,unconnected=nan,1,1,1.0
1000,1,unconnected=nan,0.001,1,0.001
1000,1,unconnected=nan,-0.001,1,0.001
1000,1,unconnected=nan,unconnected,1,0.0
1000,1,unconnected=nan,unconnected=5,1,0.0
1000,1,unconnected=nan,unconnected=nan,1,0.0
1000,1,unconnected=nan,unconnected=-inf,1,0.0
1000,1,unconnected=nan,connected=+inf,1,0.0
1000,1,unconnected=nan,connected=-inf,1,+inf
1000,1,unconnected=nan,connected=nan,1,0.0
1000,1,unconnected=-inf,10,1,10.0
1000,1,unconnected=-inf,-10,1,10.0
1000,1,unconnected=-inf,0,1,0.0
1000,1,unconnected=-inf,1,1,1.0
1000,1,unconnected=-inf,0.001,1,0.001
1000,1,unconnected=-inf,-0.001,1,0.001
1000,1,unconnected=-inf,unconnected,1,0.0
1000,1,unconnected=-inf,unconnected=5,1,0.0
1000,1,unconnected=-inf,unconnected=nan,1,0.0
1000,1,unconnected=-inf,unconnected=-inf,1,0.0
1000,1,unconnected=-inf,connected=+inf,1,0.0
1000,1,unconnected=-inf,connected=-inf,1,+inf
1000,1,unconnected=-inf,connected=nan,1,0.0
1000,1,connected=+inf,10,1,10.0
1000,1,connected=+inf,-10,1,10.0
1000,1,connected=+inf,0,1,0.0
1000,1,connected=+inf,1,1,1.0
1000,1,connected=+inf,0.001,1,0.001
1000,1,connected=+inf,-0.001,1,0.001
1000,1,connected=+inf,unconnected,1,0.0
1000,1,connected=+inf,unconnected=5,1,0.0
1000,1,connected=+inf,unconnected=nan,1,0.0
1000,1,connected=+inf,unconnected=-inf,1,0.0
1000,1,connected=+inf,connected=+inf,1,+inf
1000,1,connected=+inf,connected=-inf,1,+inf
1000,1,connected=+inf,connected=nan,1,+inf
1000,1,connected=-inf,10,1,+inf
1000,1,connected=-inf,-10,1,+inf
1000,1,connected=-inf,0,1,+inf
1000,1,connected=-inf,1,1,+inf
1000,1,connected=-inf,0.001,1,+inf
1000,1,connected=-inf,-0.001,1,+inf
1000,1,connected=-inf,unconnected,1,+inf
1000,1,connected=-inf,unconnected=5,1,+inf
1000,1,connected=-inf,unconnected=nan,1,+inf
1000,1,connected=-inf,unconnected=-inf,1,+inf
1000,1,connected=-inf,connected=+inf,1,+inf
1000,1,connected=-inf,connected=-inf,1,+inf
1000,1,connected=-inf,connected=nan,1,+inf
1000,1,connected=nan,10,1,10.0
1000,1,connected=nan,-10,1,10.0
1000,1,connected=nan,0,1,0.0
1000,1,connected=nan,1,1,1.0
1000,1,connected=nan,0.001,1,0.001
1000,1,connected=nan,-0.001,1,0.001
1000,1,connected=nan,unconnected,1,0.0
1000,1,connected=nan,unconnected=5,1,0.0
1000,1,connected=nan,unconnected=nan,1,0.0
1000,1,connected=nan,unconnected=-inf,1,0.0
1000,1,connected=nan,connected=+inf,1,+inf
1000,1,connected=nan,connected=-inf,1,+inf
1000,1,connected=nan,connected=nan,1,+inf
1000,1,10,10,2,0.0
1000,1,-10,-10,2,0.0
1000,1,0,0,2,0.0
1000,1,1,1,2,0.0
1000,1,0.001,0.001,2,0.0
1000,1,-0.001,-0.001,2,0.0
1000,1,unconnected,unconnected,2,0.0
1000,1,unconnected=5,unconnected=5,2,0.0
1000,1,unconnected=nan,unconnected=nan,2,0.0
1000,1,unconnected=-inf,unconnected=-inf,2,0.0
1000,1,connected=+inf,connected=+inf,2,+inf
1000,1,connected=-inf,connected=-inf,2,+inf
1000,1,connected=nan,connected=nan,2,+inf
1000,1,10,10,2,0.0
1000,1,10,-10,2,20.0
1000,1,10,0,2,10.0
1000,1,10,1,2,9.0
1000,1,10,0.001,2,9.0
1000,1,10,-0.001,2,10.0
1000,1,10,unconnected,2,10.0
1000,1,10,unconnected=5,2,10.0
1000,1,10,unconnected=nan,2,10.0
1000,1,10,unconnected=-inf,2,10.0
1000,1,10,connected=+inf,2,10.0
1000,1,10,connected=-inf,2,+inf
1000,1,10,connected=nan,2,10.0
1000,1,-10,10,2,-20.0
1000,1,-10,-10,2,0.0
1000,1,-10,0,2,-10.0
1000,1,-10,1,2,-11.0
1000,1,-10,0.001,2,-10.0
1000,1,-10,-0.001,2,-9.0
1000,1,-10,unconnected,2,-10.0
1000,1,-10,unconnected=5,2,-10.0
1000,1,-10,unconnected=nan,2,-10.0
1000,1,-10,unconnected=-inf,2,-10.0
1000,1,-10,connected=+inf,2,-10.0
1000,1,-10,connected=-inf,2,+inf
1000,1,-10,connected=nan,2,-10.0
1000,1,0,10,2,-10.0
1000,1,0,-10,2,10.0
1000,1,0,0,2,0.0
1000,1,0,1,2,-1.0
1000,1,0,0.001,2,0.0
1000,1,0,-0.001,2,0.0
1000,1,0,unconnected,2,0.0
1000,1,0,unconnected=5,2,0.0
1000,1,0,unconnected=nan,2,0.0
1000,1,0,unconnected=-inf,2,0.0
1000,1,0,connected=+inf,2,0.0
1000,1,0,connected=-inf,2,+inf
1000,1,0,connected=nan,2,0.0
1000,1,1,10,2,-9.0
1000,1,1,-10,2,11.0
1000,1,1,0,2,1.0
1000,1,1,1,2,0.0
1000,1,1,0.001,2,0.0
1000,1,1,-0.001,2,1.0
1000,1,1,unconnected,2,1.0
1000,1,1,unconnected=5,2,1.0
1000,1,1,unconnected=nan,2,1.0
1000,1,1,unconnected=-inf,2,1.0
1000,1,1,connected=+inf,2,1.0
1000,1,1,connected=-inf,2,+inf
1000,1,1,connected=nan,2,1.0
1000,1,0.001,10,2,-9.0
1000,1,0.001,-10,2,10.0
1000,1,0.001,0,2,0.0
1000,1,0.001,1,2,0.0
1000,1,0.001,0.001,2,0.0
1000,1,0.001,-0.001,2,0.0
1000,1,0.001,unconnected,2,0.0
1000,1,0.001,unconnected=5,2,0.0
1000,1,0.001,unconnected=nan,2,0.0
1000,1,0.001,unconnected=-inf,2,0.0
1000,1,0.001,connected=+inf,2,0.0
1000,1,0.001,connected=-inf,2,+inf
1000,1,0.001,connected=nan,2,0.0
1000,1,-0.001,10,2,-10.0
1000,1,-0.001,-10,2,9.0
1000,1,-0.001,0,2,0.0
1000,1,-0.001,1,2,-1.0
1000,1,-0.001,0.001,2,0.0
1000,1,-0.001,-0.001,2,0.0
1000,1,-0.001,unconnected,2,0.0
1000,1,-0.001,unconnected=5,2,0.0
1000,1,-0.001,unconnected=nan,2,0.0
1000,1,-0.001,unconnected=-inf,2,0.0
1000,1,-0.001,connected=+inf,2,0.0
1000,1,-0.001,connected=-inf,2,+inf
1000,1,-0.001,connected=nan,2,0.0
1000,1,unconnected,10,2,-10.0
1000,1,unconnected,-10,2,10.0
1000,1,unconnected,0,2,0.0
1000,1,unconnected,1,2,-1.0
1000,1,unconnected,0.001,2,0.0
1000,1,unconnected,-0.001,2,0.0
1000,1,unconnected,unconnected,2,0.0
1000,1,unconnected,unconnected=5,2,0.0
1000,1,unconnected,unconnected=nan,2,0.0
1000,1,unconnected,unconnected=-inf,2,0.0
1000,1,unconnected,connected=+inf,2,0.0
1000,1,unconnected,connected=-inf,2,+inf
1000,1,unconnected,connected=nan,2,0.0
1000,1,unconnected=5,10,2,-10.0
1000,1,unconnected=5,-10,2,10.0
1000,1,unconnected=5,0,2,0.0
1000,1,unconnected=5,1,2,-1.0
1000,1,unconnected=5,0.001,2,0.0
1000,1,unconnected=5,-0.001,2,0.0
1000,1,unconnected=5,unconnected,2,0.0
1000,1,unconnected=5,unconnected=5,2,0.0
1000,1,unconnected=5,unconnected=nan,2,0.0
1000,1,unconnected=5,unconnected=-inf,2,0.0
1000,1,unconnected=5,connected=+inf,2,0.0
1000,1,unconnected=5,connected=-inf,2,+inf
1000,1,unconnected=5,connected=nan,2,0.0
1000,1,unconnected=nan,10,2,-10.0
1000,1,unconnected=nan,-10,2,10.0
1000,1,unconnected=nan,0,2,0.0
1000,1,unconnected=nan,1,2,-1.0
1000,1,unconnected=nan,0.001,2,0.0
1000,1,unconnected=nan,-0.001,2,0.0
1000,1,unconnected=nan,unconnected,2,0.0
1000,1,unconnected=nan,unconnected=5,2,0.0
1000,1,unconnected=nan,unconnected=nan,2,0.0
1000,1,unconnected=nan,unconnected=-inf,2,0.0
1000,1,unconnected=nan,connected=+inf,2,0.0
1000,1,unconnected=nan,connected=-inf,2,+inf
1000,1,unconnected=nan,connected=nan,2,0.0
1000,1,unconnected=-inf,10,2,-10.0
1000,1,unconnected=-inf,-10,2,10.0
1000,1,unconnected=-inf,0,2,0.0
1000,1,unconnected=-inf,1,2,-1.0
1000,1,unconnected=-inf,0.001,2,0.0
1000,1,unconnected=-inf,-0.001,2,0.0
1000,1,unconnected=-inf,unconnected,2,0.0
1000,1,unconnected=-inf,unconnected=5,2,0.0
1000,1,unconnected=-inf,unconnected=nan,2,0.0
1000,1,unconnected=-inf,unconnected=-inf,2,0.0
1000,1,unconnected=-inf,connected=+inf,2,0.0
1000,1,unconnected=-inf,connected=-inf,2,+inf
1000,1,unconnected=-inf,connected=nan,2,0.0
1000,1,connected=+inf,10,2,-10.0
1000,1,connected=+inf,-10,2,10.0
1000,1,connected=+inf,0,2,0.0
1000,1,connected=+inf,1,2,-1.0
1000,1,connected=+inf,0.001,2,0.0
1000,1,connected=+inf,-0.001,2,0.0
1000,1,connected=+inf,unconnected,2,0.0
1000,1,connected=+inf,unconnected=5,2,0.0
1000,1,connected=+inf,unconnected=nan,2,0.0
1000,1,connected=+inf,unconnected=-inf,2,0.0
1000,1,connected=+inf,connected=+inf,2,+inf
1000,1,connected=+inf,connected=-inf,2,+inf
1000,1,connected=+inf,connected=nan,2,+inf
1000,1,connected=-inf,10,2,-inf
1000,1,connected=-inf,-10,2,-inf
1000,1,connected=-inf,0,2,-inf
1000,1,connected=-inf,1,2,-inf
1000,1,connected=-inf,0.001,2,-inf
1000,1,connected=-inf,-0.001,2,-inf
1000,1,connected=-inf,unconnected,2,-inf
1000,1,connected=-inf,unconnected=5,2,-inf
1000,1,connected=-inf,unconnected=nan,2,-inf
1000,1,connected=-inf,unconnected=-inf,2,-inf
1000,1,connected=-inf,connected=+inf,2,-inf
1000,1,connected=-inf,connected=-inf,2,+inf
1000,1,connected=-inf,connected=nan,2,-inf
1000,1,connected=nan,10,2,-10.0
1000,1,connected=nan,-10,2,10.0
1000,1,connected=nan,0,2,0.0
1000,1,connected=nan,1,2,-1.0
1000,1,connected=nan,0.001,2,0.0
1000,1,connected=nan,-0.001,2,0.0
1000,1,connected=nan,unconnected,2,0.0
1000,1,connected=nan,unconnected=5,2,0.0
1000,1,connected=nan,unconnected=nan,2,0.0
1000,1,connected=nan,unconnected=-inf,2,0.0
1000,1,connected=nan,connected=+inf,2,+inf
1000,1,connected=nan,connected=-inf,2,+inf
1000,1,connected=nan,connected=nan,2,+inf
1000,1,10,10,3,0.0
1000,1,-10,-10,3,0.0
1000,1,0,0,3,0.0
1000,1,1,1,3,0.0
1000,1,0.001,0.001,3,0.0
1000,1,-0.001,-0.001,3,0.0
1000,1,unconnected,unconnected,3,0.0
1000,1,unconnected=5,unconnected=5,3,0.0
1000,1,unconnected=nan,unconnected=nan,3,0.0
1000,1,unconnected=-inf,unconnected=-inf,3,0.0
1000,1,connected=+inf,connected=+inf,3,+inf
1000,1,connected=-inf,connected=-inf,3,+inf
1000,1,connected=nan,connected=nan,3,+inf
1000,1,10,10,3,0.0
1000,1,10,-10,3,0.0
1000,1,10,0,3,0.0
1000,1,10,1,3,0.0
1000,1,10,0.001,3,0.999
1000,1,10,-0.001,3,0.001
1000,1,10,unconnected,3,0.0
1000,1,10,unconnected=5,3,0.0
1000,1,10,unconnected=nan,3,0.0
1000,1,10,unconnected=-inf,3,0.0
1000,1,10,connected=+inf,3,0.0
1000,1,10,connected=-inf,3,+inf
1000,1,10,connected=nan,3,0.0
1000,1,-10,10,3,0.0
1000,1,-10,-10,3,0.0
1000,1,-10,0,3,0.0
1000,1,-10,1,3,0.0
1000,1,-10,0.001,3,0.001
1000,1,-10,-0.001,3,0.999
1000,1,-10,unconnected,3,0.0
1000,1,-10,unconnected=5,3,0.0
1000,1,-10,unconnected=nan,3,0.0
1000,1,-10,unconnected=-inf,3,0.0
1000,1,-10,connected=+inf,3,0.0
1000,1,-10,connected=-inf,3,+inf
1000,1,-10,connected=nan,3,0.0
1000,1,0,10,3,0.0
1000,1,0,-10,3,0.0
1000,1,0,0,3,0.0
1000,1,0,1,3,0.0
1000,1,0,0.001,3,0.001
1000,1,0,-0.001,3,0.001
1000,1,0,unconnected,3,0.0
1000,1,0,unconnected=5,3,0.0
1000,1,0,unconnected=nan,3,0.0
1000,1,0,unconnected=-inf,3,0.0
1000,1,0,connected=+inf,3,0.0
1000,1,0,connected=-inf,3,+inf
1000,1,0,connected=nan,3,0.0
1000,1,1,10,3,0.0
1000,1,1,-10,3,0.0
1000,1,1,0,3,0.0
1000,1,1,1,3,0.0
1000,1,1,0.001,3,0.999
1000,1,1,-0.001,3,0.001
1000,1,1,unconnected,3,0.0
1000,1,1,unconnected=5,3,0.0
1000,1,1,unconnected=nan,3,0.0
1000,1,1,unconnected=-inf,3,0.0
1000,1,1,connected=+inf,3,0.0
1000,1,1,connected=-inf,3,+inf
1000,1,1,connected=nan,3,0.0
1000,1,0.001,10,3,0.999
1000,1,0.001,-10,3,0.001
1000,1,0.001,0,3,0.001
1000,1,0.001,1,3,0.999
1000,1,0.001,0.001,3,0.0
1000,1,0.001,-0.001,3,0.002
1000,1,0.001,unconnected,3,0.001
1000,1,0.001,unconnected=5,3,0.001
1000,1,0.001,unconnected=nan,3,0.001
1000,1,0.001,unconnected=-inf,3,0.001
1000,1,0.001,connected=+inf,3,0.001
1000,1,0.001,connected=-inf,3,+inf
1000,1,0.001,connected=nan,3,0.001
1000,1,-0.001,10,3,0.001
1000,1,-0.001,-10,3,0.999
1000,1,-0.001,0,3,0.001
1000,1,-0.001,1,3,0.001
1000,1,-0.001,0.001,3,0.002
1000,1,-0.001,-0.001,3,0.0
1000,1,-0.001,unconnected,3,0.001
1000,1,-0.001,unconnected=5,3,0.001
1000,1,-0.001,unconnected=nan,3,0.001
1000,1,-0.001,unconnected=-inf,3,0.001
1000,1,-0.001,connected=+inf,3,0.001
1000,1,-0.001,connected=-inf,3,+inf
1000,1,-0.001,connected=nan,3,0.001
1000,1,unconnected,10,3,0.0
1000,1,unconnected,-10,3,0.0
1000,1,unconnected,0,3,0.0
1000,1,unconnected,1,3,0.0
1000,1,unconnected,0.001,3,0.001
1000,1,unconnected,-0.001,3,0.001
1000,1,unconnected,unconnected,3,0.0
1000,1,unconnected,unconnected=5,3,0.0
1000,1,unconnected,unconnected=nan,3,0.0
1000,1,unconnected,unconnected=-inf,3,0.0
1000,1,unconnected,connected=+inf,3,0.0
1000,1,unconnected,connected=-inf,3,+inf
1000,1,unconnected,connected=nan,3,0.0
1000,1,unconnected=5,10,3,0.0
1000,1,unconnected=5,-10,3,0.0
1000,1,unconnected=5,0,3,0.0
1000,1,unconnected=5,1,3,0.0
1000,1,unconnected=5,0.001,3,0.001
1000,1,unconnected=5,-0.001,3,0.001
1000,1,unconnected=5,unconnected,3,0.0
1000,1,unconnected=5,unconnected=5,3,0.0
1000,1,unconnected=5,unconnected=nan,3,0.0
1000,1,unconnected=5,unconnected=-inf,3,0.0
1000,1,unconnected=5,connected=+inf,3,0.0
1000,1,unconnected=5,connected=-inf,3,+inf
1000,1,unconnected=5,connected=nan,3,0.0
1000,1,unconnected=nan,10,3,0.0
1000,1,unconnected=nan,-10,3,0.0
1000,1,unconnected=nan,0,3,0.0
1000,1,unconnected=nan,1,3,0.0
1000,1,unconnected=nan,0.001,3,0.001
1000,1,unconnected=nan,-0.001,3,0.001
1000,1,unconnected=nan,unconnected,3,0.0
1000,1,unconnected=nan,unconnected=5,3,0.0
1000,1,unconnected=nan,unconnected=nan,3,0.0
1000,1,unconnected=nan,unconnected=-inf,3,0.0
1000,1,unconnected=nan,connected=+inf,3,0.0
1000,1,unconnected=nan,connected=-inf,3,+inf
1000,1,unconnected=nan,connected=nan,3,0.0
1000,1,unconnected=-inf,10,3,0.0
1000,1,unconnected=-inf,-10,3,0.0
1000,1,unconnected=-inf,0,3,0.0
1000,1,unconnected=-inf,1,3,0.0
1000,1,unconnected=-inf,0.001,3,0.001
1000,1,unconnected=-inf,-0.001,3,0.001
1000,1,unconnected=-inf,unconnected,3,0.0
1000,1,unconnected=-inf,unconnected=5,3,0.0
1000,1,unconnected=-inf,unconnected=nan,3,0.0
1000,1,unconnected=-inf,unconnected=-inf,3,0.0
1000,1,unconnected=-inf,connected=+inf,3,0.0
1000,1,unconnected=-inf,connected=-inf,3,+inf
1000,1,unconnected=-inf,connected=nan,3,0.0
1000,1,connected=+inf,10,3,0.0
1000,1,connected=+inf,-10,3,0.0
1000,1,connected=+inf,0,3,0.0
1000,1,connected=+inf,1,3,0.0
1000,1,connected=+inf,0.001,3,0.001
1000,1,connected=+inf,-0.001,3,0.001
1000,1,connected=+inf,unconnected,3,0.0
1000,1,connected=+inf,unconnected=5,3,0.0
1000,1,connected=+inf,unconnected=nan,3,0.0
1000,1,connected=+inf,unconnected=-inf,3,0.0
1000,1,connected=+inf,connected=+inf,3,+inf
1000,1,connected=+inf,connected=-inf,3,+inf
1000,1,connected=+inf,connected=nan,3,+inf
1000,1,connected=-inf,10,3,+inf
1000,1,connected=-inf,-10,3,+inf
1000,1,connected=-inf,0,3,+inf
1000,1,connected=-inf,1,3,+inf
1000,1,connected=-inf,0.001,3,+inf
1000,1,connected=-inf,-0.001,3,+inf
1000,1,connected=-inf,unconnected,3,+inf
1000,1,connected=-inf,unconnected=5,3,+inf
1000,1,connected=-inf,unconnected=nan,3,+inf
1000,1,connected=-inf,unconnected=-inf,3,+inf
1000,1,connected=-inf,connected=+inf,3,+inf
1000,1,connected=-inf,connected=-inf,3,+inf
1000,1,connected=-inf,connected=nan,3,+inf
1000,1,connected=nan,10,3,0.0
1000,1,connected=nan,-10,3,0.0
1000,1,connected=nan,0,3,0.0
1000,1,connected=nan,1,3,0.0
1000,1,connected=nan,0.001,3,0.001
1000,1,connected=nan,-0.001,3,0.001
1000,1,connected=nan,unconnected,3,0.0
1000,1,connected=nan,unconnected=5,3,0.0
1000,1,connected=nan,unconnected=nan,3,0.0
1000,1,connected=nan,unconnected=-inf,3,0.0
1000,1,connected=nan,connected=+inf,3,+inf
1000,1,connected=nan,connected=-inf,3,+inf
1000,1,connected=nan,connected=nan,3,+inf
1000,0,10,10,0,0.0
1000,0,-10,-10,0,0.0
1000,0,0,0,0,0.0
1000,0,1,1,0,0.0
1000,0,0.001,0.001,0,0.0
1000,0,-0.001,-0.001,0,0.0
1000,0,unconnected,unconnected,0,0.0
1000,0,unconnected=5,unconnected=5,0,0.0
1000,0,unconnected=nan,unconnected=nan,0,0.0
1000,0,unconnected=-inf,unconnected=-inf,0,0.0
1000,0,connected=+inf,connected=+inf,0,+inf
1000,0,connected=-inf,connected=-inf,0,+inf
1000,0,connected=nan,connected=nan,0,+inf
1000,0,10,10,0,0.0
1000,0,10,-10,0,20.0
1000,0,10,0,0,10.0
1000,0,10,1,0,9.0
1000,0,10,0.001,0,9.999
1000,0,10,-0.001,0,10.001
1000,0,10,unconnected,0,10.0
1000,0,10,unconnected=5,0,10.0
1000,0,10,unconnected=nan,0,10.0
1000,0,10,unconnected=-inf,0,10.0
1000,0,10,connected=+inf,0,+inf
1000,0,10,connected=-inf,0,+inf
1000,0,10,connected=nan,0,+inf
1000,0,-10,10,0,-20.0
1000,0,-10,-10,0,0.0
1000,0,-10,0,0,-10.0
1000,0,-10,1,0,-11.0
1000,0,-10,0.001,0,-10.001
1000,0,-10,-0.001,0,-9.999
1000,0,-10,unconnected,0,-10.0
1000,0,-10,unconnected=5,0,-10.0
1000,0,-10,unconnected=nan,0,-10.0
1000,0,-10,unconnected=-inf,0,-10.0
1000,0,-10,connected=+inf,0,+inf
1000,0,-10,connected=-inf,0,+inf
1000,0,-10,connected=nan,0,+inf
1000,0,0,10,0,-10.0
1000,0,0,-10,0,10.0
1000,0,0,0,0,0.0
1000,0,0,1,0,-1.0
1000,0,0,0.001,0,-0.001
1000,0,0,-0.001,0,0.001
1000,0,0,unconnected,0,0.0
1000,0,0,unconnected=5,0,0.0
1000,0,0,unconnected=nan,0,0.0
1000,0,0,unconnected=-inf,0,0.0
1000,0,0,connected=+inf,0,+inf
1000,0,0,connected=-inf,0,+inf
1000,0,0,connected=nan,0,+inf
1000,0,1,10,0,-9.0
1000,0,1,-10,0,11.0
1000,0,1,0,0,1.0
1000,0,1,1,0,0.0
1000,0,1,0.001,0,0.999
1000,0,1,-0.001,0,1.001
1000,0,1,unconnected,0,1.0
1000,0,1,unconnected=5,0,1.0
1000,0,1,unconnected=nan,0,1.0
1000,0,1,unconnected=-inf,0,1.0
1000,0,1,connected=+inf,0,+inf
1000,0,1,connected=-inf,0,+inf
1000,0,1,connected=nan,0,+inf
1000,0,0.001,10,0,-9.999
1000,0,0.001,-10,0,10.001
1000,0,0.001,0,0,0.001
1000,0,0.001,1,0,-0.999
1000,0,0.001,0.001,0,0.0
1000,0,0.001,-0.001,0,0.002
1000,0,0.001,unconnected,0,0.001
1000,0,0.001,unconnected=5,0,0.001
1000,0,0.001,unconnected=nan,0,0.001
1000,0,0.001,unconnected=-inf,0,0.001
1000,0,0.001,connected=+inf,0,+inf
1000,0,0.001,connected=-inf,0,+inf
1000,0,0.001,connected=nan,0,+inf
1000,0,-0.001,10,0,-10.001
1000,0,-0.001,-10,0,9.999
1000,0,-0.001,0,0,-0.001
1000,0,-0.001,1,0,-1.001
1000,0,-0.001,0.001,0,-0.002
1000,0,-0.001,-0.001,0,0.0
1000,0,-0.001,unconnected,0,-0.001
1000,0,-0.001,unconnected=5,0,-0.001
1000,0,-0.001,unconnected=nan,0,-0.001
1000,0,-0.001,unconnected=-inf,0,-0.001
1000,0,-0.001,connected=+inf,0,+inf
1000,0,-0.001,connected=-inf,0,+inf
1000,0,-0.001,connected=nan,0,+inf
1000,0,unconnected,10,0,-10.0
1000,0,unconnected,-10,0,10.0
1000,0,unconnected,0,0,0.0
1000,0,unconnected,1,0,-1.0
1000,0,unconnected,0.001,0,-0.001
1000,0,unconnected,-0.001,0,0.001
1000,0,unconnected,unconnected,0,0.0
1000,0,unconnected,unconnected=5,0,0.0
1000,0,unconnected,unconnected=nan,0,0.0
1000,0,unconnected,unconnected=-inf,0,0.0
1000,0,unconnected,connected=+inf,0,+inf
1000,0,unconnected,connected=-inf,0,+inf
1000,0,unconnected,connected=nan,0,+inf
1000,0,unconnected=5,10,0,-10.0
1000,0,unconnected=5,-10,0,10.0
1000,0,unconnected=5,0,0,0.0
1000,0,unconnected=5,1,0,-1.0
1000,0,unconnected=5,0.001,0,-0.001
1000,0,unconnected=5,-0.001,0,0.001
1000,0,unconnected=5,unconnected,0,0.0
1000,0,unconnected=5,unconnected=5,0,0.0
1000,0,unconnected=5,unconnected=nan,0,0.0
1000,0,unconnected=5,unconnected=-inf,0,0.0
1000,0,unconnected=5,connected=+inf,0,+inf
1000,0,unconnected=5,connected=-inf,0,+inf
1000,0,unconnected=5,connected=nan,0,+inf
1000,0,unconnected=nan,10,0,-10.0
1000,0,unconnected=nan,-10,0,10.0
1000,0,unconnected=nan,0,0,0.0
1000,0,unconnected=nan,1,0,-1.0
1000,0,unconnected=nan,0.001,0,-0.001
1000,0,unconnected=nan,-0.001,0,0.001
1000,0,unconnected=nan,unconnected,0,0.0
1000,0,unconnected=nan,unconnected=5,0,0.0
1000,0,unconnected=nan,unconnected=nan,0,0.0
1000,0,unconnected=nan,unconnected=-inf,0,0.0
1000,0,unconnected=nan,connected=+inf,0,+inf
1000,0,unconnected=nan,connected=-inf,0,+inf
1000,0,unconnected=nan,connected=nan,0,+inf
1000,0,unconnected=-inf,10,0,-10.0
1000,0,unconnected=-inf,-10,0,10.0
1000,0,unconnected=-inf,0,0,0.0
1000,0,unconnected=-inf,1,0,-1.0
1000,0,unconnected=-inf,0.001,0,-0.001
1000,0,unconnected=-inf,-0.001,0,0.001
1000,0,unconnected=-inf,unconnected,0,0.0
1000,0,unconnected=-inf,unconnected=5,0,0.0
1000,0,unconnected=-inf,unconnected=nan,0,0.0
1000,0,unconnected=-inf,unconnected=-inf,0,0.0
1000,0,unconnected=-inf,connected=+inf,0,+inf
1000,0,unconnected=-inf,connected=-inf,0,+inf
1000,0,unconnected=-inf,connected=nan,0,+inf
1000,0,connected=+inf,10,0,+inf
1000,0,connected=+inf,-10,0,+inf
1000,0,connected=+inf,0,0,+inf
1000,0,connected=+inf,1,0,+inf
1000,0,connected=+inf,0.001,0,+inf
1000,0,connected=+inf,-0.001,0,+inf
1000,0,connected=+inf,unconnected,0,+inf
1000,0,connected=+inf,unconnected=5,0,+inf
1000,0,connected=+inf,unconnected=nan,0,+inf
1000,0,connected=+inf,unconnected=-inf,0,+inf
1000,0,connected=+inf,connected=+inf,0,+inf
1000,0,connected=+inf,connected=-inf,0,+inf
1000,0,connected=+inf,connected=nan,0,+inf
1000,0,connected=-inf,10,0,-inf
1000,0,connected=-inf,-10,0,-inf
1000,0,connected=-inf,0,0,-inf
1000,0,connected=-inf,1,0,-inf
1000,0,connected=-inf,0.001,0,-inf
1000,0,connected=-inf,-0.001,0,-inf
1000,0,connected=-inf,unconnected,0,-inf
1000,0,connected=-inf,unconnected=5,0,-inf
1000,0,connected=-inf,unconnected=nan,0,-inf
1000,0,connected=-inf,unconnected=-inf,0,-inf
1000,0,connected=-inf,connected=+inf,0,+inf
1000,0,connected=-inf,connected=-inf,0,+inf
1000,0,connected=-inf,connected=nan,0,+inf
1000,0,connected=nan,10,0,+inf
1000,0,connected=nan,-10,0,+inf
1000,0,connected=nan,0,0,+inf
1000,0,connected=nan,1,0,+inf
1000,0,connected=nan,0.001,0,+inf
1000,0,connected=nan,-0.001,0,+inf
1000,0,connected=nan,unconnected,0,+inf
1000,0,connected=nan,unconnected=5,0,+inf
1000,0,connected=nan,unconnected=nan,0,+inf
1000,0,connected=nan,unconnected=-inf,0,+inf
1000,0,connected=nan,connected=+inf,0,+inf
1000,0,connected=nan,connected=-inf,0,+inf
1000,0,connected=nan,connected=nan,0,+inf
1000,0,10,10,1,0.0
1000,0,-10,-10,1,0.0
1000,0,0,0,1,0.0
1000,0,1,1,1,0.0
1000,0,0.001,0.001,1,0.0
1000,0,-0.001,-0.001,1,0.0
1000,0,unconnected,unconnected,1,0.0
1000,0,unconnected=5,unconnected=5,1,0.0
1000,0,unconnected=nan,unconnected=nan,1,0.0
1000,0,unconnected=-inf,unconnected=-inf,1,0.0
1000,0,connected=+inf,connected=+inf,1,+inf
1000,0,connected=-inf,connected=-inf,1,+inf
1000,0,connected=nan,connected=nan,1,+inf
1000,0,10,10,1,0.0
1000,0,10,-10,1,20.0
1000,0,10,0,1,10.0
1000,0,10,1,1,9.0
1000,0,10,0.001,1,9.999
1000,0,10,-0.001,1,10.001
1000,0,10,unconnected,1,10.0
1000,0,10,unconnected=5,1,10.0
1000,0,10,unconnected=nan,1,10.0
1000,0,10,unconnected=-inf,1,10.0
1000,0,10,connected=+inf,1,+inf
1000,0,10,connected=-inf,1,+inf
1000,0,10,connected=nan,1,+inf
1000,0,-10,10,1,20.0
1000,0,-10,-10,1,0.0
1000,0,-10,0,1,10.0
1000,0,-10,1,1,11.0
1000,0,-10,0.001,1,10.001
1000,0,-10,-0.001,1,9.999
1000,0,-10,unconnected,1,10.0
1000,0,-10,unconnected=5,1,10.0
1000,0,-10,unconnected=nan,1,10.0
1000,0,-10,unconnected=-inf,1,10.0
1000,0,-10,connected=+inf,1,+inf
1000,0,-10,connected=-inf,1,+inf
1000,0,-10,connected=nan,1,+inf
1000,0,0,10,1,10.0
1000,0,0,-10,1,10.0
1000,0,0,0,1,0.0
1000,0,0,1,1,1.0
1000,0,0,0.001,1,0.001
1000,0,0,-0.001,1,0.001
1000,0,0,unconnected,1,0.0
1000,0,0,unconnected=5,1,0.0
1000,0,0,unconnected=nan,1,0.0
1000,0,0,unconnected=-inf,1,0.0
1000,0,0,connected=+inf,1,+inf
1000,0,0,connected=-inf,1,+inf
1000,0,0,connected=nan,1,+inf
1000,0,1,10,1,9.0
1000,0,1,-10,1,11.0
1000,0,1,0,1,1.0
1000,0,1,1,1,0.0
1000,0,1,0.001,1,0.999
1000,0,1,-0.001,1,1.001
1000,0,1,unconnected,1,1.0
1000,0,1,unconnected=5,1,1.0
1000,0,1,unconnected=nan,1,1.0
1000,0,1,unconnected=-inf,1,1.0
1000,0,1,connected=+inf,1,+inf
1000,0,1,connected=-inf,1,+inf
1000,0,1,connected=nan,1,+inf
1000,0,0.001,10,1,9.999
1000,0,0.001,-10,1,10.001
1000,0,0.001,0,1,0.001
1000,0,0.001,1,1,0.999
1000,0,0.001,0.001,1,0.0
1000,0,0.001,-0.001,1,0.002
1000,0,0.001,unconnected,1,0.001
1000,0,0.001,unconnected=5,1,0.001
1000,0,0.001,unconnected=nan,1,0.001
1000,0,0.001,unconnected=-inf,1,0.001
1000,0,0.001,connected=+inf,1,+inf
1000,0,0.001,connected=-inf,1,+inf
1000,0,0.001,connected=nan,1,+inf
1000,0,-0.001,10,1,10.001
1000,0,-0.001,-10,1,9.999
1000,0,-0.001,0,1,0.001
1000,0,-0.001,1,1,1.001
1000,0,-0.001,0.001,1,0.002
1000,0,-0.001,-0.001,1,0.0
1000,0,-0.001,unconnected,1,0.001
1000,0,-0.001,unconnected=5,1,0.001
1000,0,-0.001,unconnected=nan,1,0.001
1000,0,-0.001,unconnected=-inf,1,0.001
1000,0,-0.001,connected=+inf,1,+inf
1000,0,-0.001,connected=-inf,1,+inf
1000,0,-0.001,connected=nan,1,+inf
1000,0,unconnected,10,1,10.0
1000,0,unconnected,-10,1,10.0
1000,0,unconnected,0,1,0.0
1000,0,unconnected,1,1,1.0
1000,0,unconnected,0.001,1,0.001
1000,0,unconnected,-0.001,1,0.001
1000,0,unconnected,unconnected,1,0.0
1000,0,unconnected,unconnected=5,1,0.0
1000,0,unconnected,unconnected=nan,1,0.0
1000,0,unconnected,unconnected=-inf,1,0.0
1000,0,unconnected,connected=+inf,1,+inf
1000,0,unconnected,connected=-inf,1,+inf
1000,0,unconnected,connected=nan,1,+inf
1000,0,unconnected=5,10,1,10.0
1000,0,unconnected=5,-10,1,10.0
1000,0,unconnected=5,0,1,0.0
1000,0,unconnected=5,1,1,1.0
1000,0,unconnected=5,0.001,1,0.001
1000,0,unconnected=5,-0.001,1,0.001
1000,0,unconnected=5,unconnected,1,0.0
1000,0,unconnected=5,unconnected=5,1,0.0
1000,0,unconnected=5,unconnected=nan,1,0.0
1000,0,unconnected=5,unconnected=-inf,1,0.0
1000,0,unconnected=5,connected=+inf,1,+inf
1000,0,unconnected=5,connected=-inf,1,+inf
1000,0,unconnected=5,connected=nan,1,+inf
1000,0,unconnected=nan,10,1,10.0
1000,0,unconnected=nan,-10,1,10.0
1000,0,unconnected=nan,0,1,0.0
1000,0,unconnected=nan,1,1,1.0
1000,0,unconnected=nan,0.001,1,0.001
1000,0,unconnected=nan,-0.001,1,0.001
1000,0,unconnected=nan,unconnected,1,0.0
1000,0,unconnected=nan,unconnected=5,1,0.0
1000,0,unconnected=nan,unconnected=nan,1,0.0
1000,0,unconnected=nan,unconnected=-inf,1,0.0
1000,0,unconnected=nan,connected=+inf,1,+inf
1000,0,unconnected=nan,connected=-inf,1,+inf
1000,0,unconnected=nan,connected=nan,1,+inf
1000,0,unconnected=-inf,10,1,10.0
1000,0,unconnected=-inf,-10,1,10.0
1000,0,unconnected=-inf,0,1,0.0
1000,0,unconnected=-inf,1,1,1.0
1000,0,unconnected=-inf,0.001,1,0.001
1000,0,unconnected=-inf,-0.001,1,0.001
1000,0,unconnected=-inf,unconnected,1,0.0
1000,0,unconnected=-inf,unconnected=5,1,0.0
1000,0,unconnected=-inf,unconnected=nan,1,0.0
1000,0,unconnected=-inf,unconnected=-inf,1,0.0
1000,0,unconnected=-inf,connected=+inf,1,+inf
1000,0,unconnected=-inf,connected=-inf,1,+inf
1000,0,unconnected=-inf,connected=nan,1,+inf
1000,0,connected=+inf,10,1,+inf
1000,0,connected=+inf,-10,1,+inf
1000,0,connected=+inf,0,1,+inf
1000,0,connected=+inf,1,1,+inf
1000,0,connected=+inf,0.001,1,+inf
1000,0,connected=+inf,-0.001,1,+inf
1000,0,connected=+inf,unconnected,1,+inf
1000,0,connected=+inf,unconnected=5,1,+inf
1000,0,connected=+inf,unconnected=nan,1,+inf
1000,0,connected=+inf,unconnected=-inf,1,+inf
1000,0,connected=+inf,connected=+inf,1,+inf
1000,0,connected=+inf,connected=-inf,1,+inf
1000,0,connected=+inf,connected=nan,1,+inf
1000,0,connected=-inf,10,1,+inf
1000,0,connected=-inf,-10,1,+inf
1000,0,connected=-inf,0,1,+inf
1000,0,connected=-inf,1,1,+inf
1000,0,connected=-inf,0.001,1,+inf
1000,0,connected=-inf,-0.001,1,+inf
1000,0,connected=-inf,unconnected,1,+inf
1000,0,connected=-inf,unconnected=5,1,+inf
1000,0,connected=-inf,unconnected=nan,1,+inf
1000,0,connected=-inf,unconnected=-inf,1,+inf
1000,0,connected=-inf,connected=+inf,1,+inf
1000,0,connected=-inf,connected=-inf,1,+inf
1000,0,connected=-inf,connected=nan,1,+inf
1000,0,connected=nan,10,1,+inf
1000,0,connected=nan,-10,1,+inf
1000,0,connected=nan,0,1,+inf
1000,0,connected=nan,1,1,+inf
1000,0,connected=nan,0.001,1,+inf
1000,0,connected=nan,-0.001,1,+inf
1000,0,connected=nan,unconnected,1,+inf
1000,0,connected=nan,unconnected=5,1,+inf
1000,0,connected=nan,unconnected=nan,1,+inf
1000,0,connected=nan,unconnected=-inf,1,+inf
1000,0,connected=nan,connected=+inf,1,+inf
1000,0,connected=nan,connected=-inf,1,+inf
1000,0,connected=nan,connected=nan,1,+inf
1000,0,10,10,2,0.0
1000,0,-10,-10,2,0.0
1000,0,0,0,2,0.0
1000,0,1,1,2,0.0
1000,0,0.001,0.001,2,0.0
1000,0,-0.001,-0.001,2,0.0
1000,0,unconnected,unconnected,2,0.0
1000,0,unconnected=5,unconnected=5,2,0.0
1000,0,unconnected=nan,unconnected=nan,2,0.0
1000,0,unconnected=-inf,unconnected=-inf,2,0.0
1000,0,connected=+inf,connected=+inf,2,+inf
1000,0,connected=-inf,connected=-inf,2,+inf
1000,0,connected=nan,connected=nan,2,+inf
1000,0,10,10,2,0.0
1000,0,10,-10,2,20.0
1000,0,10,0,2,10.0
1000,0,10,1,2,9.0
1000,0,10,0.001,2,9.0
1000,0,10,-0.001,2,10.0
1000,0,10,unconnected,2,10.0
1000,0,10,unconnected=5,2,10.0
1000,0,10,unconnected=nan,2,10.0
1000,0,10,unconnected=-inf,2,10.0
1000,0,10,connected=+inf,2,+inf
1000,0,10,connected=-inf,2,+inf
1000,0,10,connected=nan,2,+inf
1000,0,-10,10,2,-20.0
1000,0,-10,-10,2,0.0
1000,0,-10,0,2,-10.0
1000,0,-10,1,2,-11.0
1000,0,-10,0.001,2,-10.0
1000,0,-10,-0.001,2,-9.0
1000,0,-10,unconnected,2,-10.0
1000,0,-10,unconnected=5,2,-10.0
1000,0,-10,unconnected=nan,2,-10.0
1000,0,-10,unconnected=-inf,2,-10.0
1000,0,-10,connected=+inf,2,+inf
1000,0,-10,connected=-inf,2,+inf
1000,0,-10,connected=nan,2,+inf
1000,0,0,10,2,-10.0
1000,0,0,-10,2,10.0
1000,0,0,0,2,0.0
1000,0,0,1,2,-1.0
1000,0,0,0.001,2,0.0
1000,0,0,-0.001,2,0.0
1000,0,0,unconnected,2,0.0
1000,0,0,unconnected=5,2,0.0
1000,0,0,unconnected=nan,2,0.0
1000,0,0,unconnected=-inf,2,0.0
1000,0,0,connected=+inf,2,+inf
1000,0,0,connected=-inf,2,+inf
1000,0,0,connected=nan,2,+inf
1000,0,1,10,2,-9.0
1000,0,1,-10,2,11.0
1000,0,1,0,2,1.0
1000,0,1,1,2,0.0
1000,0,1,0.001,2,0.0
1000,0,1,-0.001,2,1.0
1000,0,1,unconnected,2,1.0
1000,0,1,unconnected=5,2,1.0
1000,0,1,unconnected=nan,2,1.0
1000,0,1,unconnected=-inf,2,1.0
1000,0,1,connected=+inf,2,+inf
1000,0,1,connected=-inf,2,+inf
1000,0,1,connected=nan,2,+inf
1000,0,0.001,10,2,-9.0
1000,0,0.001,-10,2,10.0
1000,0,0.001,0,2,0.0
1000,0,0.001,1,2,0.0
1000,0,0.001,0.001,2,0.0
1000,0,0.001,-0.001,2,0.0
1000,0,0.001,unconnected,2,0.0
1000,0,0.001,unconnected=5,2,0.0
1000,0,0.001,unconnected=nan,2,0.0
1000,0,0.001,unconnected=-inf,2,0.0
1000,0,0.001,connected=+inf,2,+inf
1000,0,0.001,connected=-inf,2,+inf
1000,0,0.001,connected=nan,2,+inf
1000,0,-0.001,10,2,-10.0
1000,0,-0.001,-10,2,9.0
1000,0,-0.001,0,2,0.0
1000,0,-0.001,1,2,-1.0
1000,0,-0.001,0.001,2,0.0
1000,0,-0.001,-0.001,2,0.0
1000,0,-0.001,unconnected,2,0.0
1000,0,-0.001,unconnected=5,2,0.0
1000,0,-0.001,unconnected=nan,2,0.0
1000,0,-0.001,unconnected=-inf,2,0.0
1000,0,-0.001,connected=+inf,2,+inf
1000,0,-0.001,connected=-inf,2,+inf
1000,0,-0.001,connected=nan,2,+inf
1000,0,unconnected,10,2,-10.0
1000,0,unconnected,-10,2,10.0
1000,0,unconnected,0,2,0.0
1000,0,unconnected,1,2,-1.0
1000,0,unconnected,0.001,2,0.0
1000,0,unconnected,-0.001,2,0.0
1000,0,unconnected,unconnected,2,0.0
1000,0,unconnected,unconnected=5,2,0.0
1000,0,unconnected,unconnected=nan,2,0.0
1000,0,unconnected,unconnected=-inf,2,0.0
1000,0,unconnected,connected=+inf,2,+inf
1000,0,unconnected,connected=-inf,2,+inf
1000,0,unconnected,connected=nan,2,+inf
1000,0,unconnected=5,10,2,-10.0
1000,0,unconnected=5,-10,2,10.0
1000,0,unconnected=5,0,2,0.0
1000,0,unconnected=5,1,2,-1.0
1000,0,unconnected=5,0.001,2,0.0
1000,0,unconnected=5,-0.001,2,0.0
1000,0,unconnected=5,unconnected,2,0.0
1000,0,unconnected=5,unconnected=5,2,0.0
1000,0,unconnected=5,unconnected=nan,2,0.0
1000,0,unconnected=5,unconnected=-inf,2,0.0
1000,0,unconnected=5,connected=+inf,2,+inf
1000,0,unconnected=5,connected=-inf,2,+inf
1000,0,unconnected=5,connected=nan,2,+inf
1000,0,unconnected=nan,10,2,-10.0
1000,0,unconnected=nan,-10,2,10.0
1000,0,unconnected=nan,0,2,0.0
1000,0,unconnected=nan,1,2,-1.0
1000,0,unconnected=nan,0.001,2,0.0
1000,0,unconnected=nan,-0.001,2,0.0
1000,0,unconnected=nan,unconnected,2,0.0
1000,0,unconnected=nan,unconnected=5,2,0.0
1000,0,unconnected=nan,unconnected=nan,2,0.0
1000,0,unconnected=nan,unconnected=-inf,2,0.0
1000,0,unconnected=nan,connected=+inf,2,+inf
1000,0,unconnected=nan,connected=-inf,2,+inf
1000,0,unconnected=nan,connected=nan,2,+inf
1000,0,unconnected=-inf,10,2,-10.0
1000,0,unconnected=-inf,-10,2,10.0
1000,0,unconnected=-inf,0,2,0.0
1000,0,unconnected=-inf,1,2,-1.0
1000,0,unconnected=-inf,0.001,2,0.0
1000,0,unconnected=-inf,-0.001,2,0.0
1000,0,unconnected=-inf,unconnected,2,0.0
1000,0,unconnected=-inf,unconnected=5,2,0.0
1000,0,unconnected=-inf,unconnected=nan,2,0.0
1000,0,unconnected=-inf,unconnected=-inf,2,0.0
1000,0,unconnected=-inf,connected=+inf,2,+inf
1000,0,unconnected=-inf,connected=-inf,2,+inf
1000,0,unconnected=-inf,connected=nan,2,+inf
1000,0,connected=+inf,10,2,+inf
1000,0,connected=+inf,-10,2,+inf
1000,0,connected=+inf,0,2,+inf
1000,0,connected=+inf,1,2,+inf
1000,0,connected=+inf,0.001,2,+inf
1000,0,connected=+inf,-0.001,2,+inf
1000,0,connected=+inf,unconnected,2,+inf
1000,0,connected=+inf,unconnected=5,2,+inf
1000,0,connected=+inf,unconnected=nan,2,+inf
1000,0,connected=+inf,unconnected=-inf,2,+inf
1000,0,connected=+inf,connected=+inf,2,+inf
1000,0,connected=+inf,connected=-inf,2,+inf
1000,0,connected=+inf,connected=nan,2,+inf
1000,0,connected=-inf,10,2,-inf
1000,0,connected=-inf,-10,2,-inf
1000,0,connected=-inf,0,2,-inf
1000,0,connected=-inf,1,2,-inf
1000,0,connected=-inf,0.001,2,-inf
1000,0,connected=-inf,-0.001,2,-inf
1000,0,connected=-inf,unconnected,2,-inf
1000,0,connected=-inf,unconnected=5,2,-inf
1000,0,connected=-inf,unconnected=nan,2,-inf
1000,0,connected=-inf,unconnected=-inf,2,-inf
1000,0,connected=-inf,connected=+inf,2,+inf
1000,0,connected=-inf,connected=-inf,2,+inf
1000,0,connected=-inf,connected=nan,2,+inf
1000,0,connected=nan,10,2,+inf
1000,0,connected=nan,-10,2,+inf
1000,0,connected=nan,0,2,+inf
1000,0,connected=nan,1,2,+inf
1000,0,connected=nan,0.001,2,+inf
1000,0,connected=nan,-0.001,2,+inf
1000,0,connected=nan,unconnected,2,+inf
1000,0,connected=nan,unconnected=5,2,+inf
1000,0,connected=nan,unconnected=nan,2,+inf
1000,0,connected=nan,unconnected=-inf,2,+inf
1000,0,connected=nan,connected=+inf,2,+inf
1000,0,connected=nan,connected=-inf,2,+inf
1000,0,connected=nan,connected=nan,2,+inf
1000,0,10,10,3,0.0
1000,0,-10,-10,3,0.0
1000,0,0,0,3,0.0
1000,0,1,1,3,0.0
1000,0,0.001,0.001,3,0.0
1000,0,-0.001,-0.001,3,0.0
1000,0,unconnected,unconnected,3,0.0
1000,0,unconnected=5,unconnected=5,3,0.0
1000,0,unconnected=nan,unconnected=nan,3,0.0
1000,0,unconnected=-inf,unconnected=-inf,3,0.0
1000,0,connected=+inf,connected=+inf,3,+inf
1000,0,connected=-inf,connected=-inf,3,+inf
1000,0,connected=nan,connected=nan,3,+inf
1000,0,10,10,3,0.0
1000,0,10,-10,3,0.0
1000,0,10,0,3,0.0
1000,0,10,1,3,0.0
1000,0,10,0.001,3,0.999
1000,0,10,-0.001,3,0.001
1000,0,10,unconnected,3,0.0
1000,0,10,unconnected=5,3,0.0
1000,0,10,unconnected=nan,3,0.0
1000,0,10,unconnected=-inf,3,0.0
1000,0,10,connected=+inf,3,+inf
1000,0,10,connected=-inf,3,+inf
1000,0,10,connected=nan,3,+inf
1000,0,-10,10,3,0.0
1000,0,-10,-10,3,0.0
1000,0,-10,0,3,0.0
1000,0,-10,1,3,0.0
1000,0,-10,0.001,3,0.001
1000,0,-10,-0.001,3,0.999
1000,0,-10,unconnected,3,0.0
1000,0,-10,unconnected=5,3,0.0
1000,0,-10,unconnected=nan,3,0.0
1000,0,-10,unconnected=-inf,3,0.0
1000,0,-10,connected=+inf,3,+inf
1000,0,-10,connected=-inf,3,+inf
1000,0,-10,connected=nan,3,+inf
1000,0,0,10,3,0.0
1000,0,0,-10,3,0.0
1000,0,0,0,3,0.0
1000,0,0,1,3,0.0
1000,0,0,0.001,3,0.001
1000,0,0,-0.001,3,0.001
1000,0,0,unconnected,3,0.0
1000,0,0,unconnected=5,3,0.0
1000,0,0,unconnected=nan,3,0.0
1000,0,0,unconnected=-inf,3,0.0
1000,0,0,connected=+inf,3,+inf
1000,0,0,connected=-inf,3,+inf
1000,0,0,connected=nan,3,+inf
1000,0,1,10,3,0.0
1000,0,1,-10,3,0.0
1000,0,1,0,3,0.0
1000,0,1,1,3,0.0
1000,0,1,0.001,3,0.999
1000,0,1,-0.001,3,0.001
1000,0,1,unconnected,3,0.0
1000,0,1,unconnected=5,3,0.0
1000,0,1,unconnected=nan,3,0.0
1000,0,1,unconnected=-inf,3,0.0
1000,0,1,connected=+inf,3,+inf
1000,0,1,connected=-inf,3,+inf
1000,0,1,connected=nan,3,+inf
1000,0,0.001,10,3,0.999
1000,0,0.001,-10,3,0.001
1000,0,0.001,0,3,0.001
1000,0,0.001,1,3,0.999
1000,0,0.001,0.001,3,0.0
1000,0,0.001,-0.001,3,0.002
1000,0,0.001,unconnected,3,0.001
1000,0,0.001,unconnected=5,3,0.001
1000,0,0.001,unconnected=nan,3,0.001
1000,0,0.001,unconnected=-inf,3,0.001
1000,0,0.001,connected=+inf,3,+inf
1000,0,0.001,connected=-inf,3,+inf
1000,0,0.001,connected=nan,3,+inf
1000,0,-0.001,10,3,0.001
1000,0,-0.001,-10,3,0.999
1000,0,-0.001,0,3,0.001
1000,0,-0.001,1,3,0.001
1000,0,-0.001,0.001,3,0.002
1000,0,-0.001,-0.001,3,0.0
1000,0,-0.001,unconnected,3,0.001
1000,0,-0.001,unconnected=5,3,0.001
1000,0,-0.001,unconnected=nan,3,0.001
1000,0,-0.001,unconnected=-inf,3,0.001
1000,0,-0.001,connected=+inf,3,+inf
1000,0,-0.001,connected=-inf,3,+inf
1000,0,-0.001,connected=nan,3,+inf
1000,0,unconnected,10,3,0.0
1000,0,unconnected,-10,3,0.0
1000,0,unconnected,0,3,0.0
1000,0,unconnected,1,3,0.0
1000,0,unconnected,0.001,3,0.001
1000,0,unconnected,-0.001,3,0.001
1000,0,unconnected,unconnected,3,0.0
1000,0,unconnected,unconnected=5,3,0.0
1000,0,unconnected,unconnected=nan,3,0.0
1000,0,unconnected,unconnected=-inf,3,0.0
1000,0,unconnected,connected=+inf,3,+inf
1000,0,unconnected,connected=-inf,3,+inf
1000,0,unconnected,connected=nan,3,+inf
1000,0,unconnected=5,10,3,0.0
1000,0,unconnected=5,-10,3,0.0
1000,0,unconnected=5,0,3,0.0
1000,0,unconnected=5,1,3,0.0
1000,0,unconnected=5,0.001,3,0.001
1000,0,unconnected=5,-0.001,3,0.001
1000,0,unconnected=5,unconnected,3,0.0
1000,0,unconnected=5,unconnected=5,3,0.0
1000,0,unconnected=5,unconnected=nan,3,0.0
1000,0,unconnected=5,unconnected=-inf,3,0.0
1000,0,unconnected=5,connected=+inf,3,+inf
1000,0,unconnected=5,connected=-inf,3,+inf
1000,0,unconnected=5,connected=nan,3,+inf
1000,0,unconnected=nan,10,3,0.0
1000,0,unconnected=nan,-10,3,0.0
1000,0,unconnected=nan,0,3,0.0
1000,0,unconnected=nan,1,3,0.0
1000,0,unconnected=nan,0.001,3,0.001
1000,0,unconnected=nan,-0.001,3,0.001
1000,0,unconnected=nan,unconnected,3,0.0
1000,0,unconnected=nan,unconnected=5,3,0.0
1000,0,unconnected=nan,unconnected=nan,3,0.0
1000,0,unconnected=nan,unconnected=-inf,3,0.0
1000,0,unconnected=nan,connected=+inf,3,+inf
1000,0,unconnected=nan,connected=-inf,3,+inf
1000,0,unconnected=nan,connected=nan,3,+inf
1000,0,unconnected=-inf,10,3,0.0
1000,0,unconnected=-inf,-10,3,0.0
1000,0,unconnected=-inf,0,3,0.0
1000,0,unconnected=-inf,1,3,0.0
1000,0,unconnected=-inf,0.001,3,0.001
1000,0,unconnected=-inf,-0.001,3,0.001
1000,0,unconnected=-inf,unconnected,3,0.0
1000,0,unconnected=-inf,unconnected=5,3,0.0
1000,0,unconnected=-inf,unconnected=nan,3,0.0
1000,0,unconnected=-inf,unconnected=-inf,3,0.0
1000,0,unconnected=-inf,connected=+inf,3,+inf
1000,0,unconnected=-inf,connected=-inf,3,+inf
1000,0,unconnected=-inf,connected=nan,3,+inf
1000,0,connected=+inf,10,3,+inf
1000,0,connected=+inf,-10,3,+inf
1000,0,connected=+inf,0,3,+inf
1000,0,connected=+inf,1,3,+inf
1000,0,connected=+inf,0.001,3,+inf
1000,0,connected=+inf,-0.001,3,+inf
1000,0,connected=+inf,unconnected,3,+inf
1000,0,connected=+inf,unconnected=5,3,+inf
1000,0,connected=+inf,unconnected=nan,3,+inf
1000,0,connected=+inf,unconnected=-inf,3,+inf
1000,0,connected=+inf,connected=+inf,3,+inf
1000,0,connected=+inf,connected=-inf,3,+inf
1000,0,connected=+inf,connected=nan,3,+inf
1000,0,connected=-inf,10,3,+inf
1000,0,connected=-inf,-10,3,+inf
1000,0,connected=-inf,0,3,+inf
1000,0,connected=-inf,1,3,+inf
1000,0,connected=-inf,0.001,3,+inf
1000,0,connected=-inf,-0.001,3,+inf
1000,0,connected=-inf,unconnected,3,+inf
1000,0,connected=-inf,unconnected=5,3,+inf
1000,0,connected=-inf,unconnected=nan,3,+inf
1000,0,connected=-inf,unconnected=-inf,3,+inf
1000,0,connected=-inf,connected=+inf,3,+inf
1000,0,connected=-inf,connected=-inf,3,+inf
1000,0,connected=-inf,connected=nan,3,+inf
1000,0,connected=nan,10,3,+inf
1000,0,connected=nan,-10,3,+inf
1000,0,connected=nan,0,3,+inf
1000,0,connected=nan,1,3,+inf
1000,0,connected=nan,0.001,3,+inf
1000,0,connected=nan,-0.001,3,+inf
1000,0,connected=nan,unconnected,3,+inf
1000,0,connected=nan,unconnected=5,3,+inf
1000,0,connected=nan,unconnected=nan,3,+inf
1000,0,connected=nan,unconnected=-inf,3,+inf
1000,0,connected=nan,connected=+inf,3,+inf
1000,0,connected=nan,connected=-inf,3,+inf
1000,0,connected=nan,connected=nan,3,+inf
