/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BLogTypeEnum;
import com.honeywell.honfunctionblocks.fbs.math.BLogarithm;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Logarithm block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Lavanya B.
 * @since Feb 16, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BLogarithmTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BLogarithmTest(2979906276)1.0$ @*/
/* Generated Fri Feb 16 10:59:28 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLogarithmTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
  @BeforeClass
  public void setUp() {
	  logarithmBlock = new BLogarithm();
	  executionParams = new BExecutionParams();

  }
  
  @AfterClass
  public void tearDown() {
	  logarithmBlock = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"eOR10"}, {"tailOperation"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"Eor10"}, {"TailOperation"}, {"y"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(logarithmBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(logarithmBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_logarithm.png");
	  BIcon actualFbIcon = logarithmBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  logarithmBlock.setIcon(expectedFbIcon);
	  actualFbIcon = logarithmBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {0.1}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInlogarithmBlock(double snValue) {
	  logarithmBlock.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(logarithmBlock.getX().getValue(), snValue, 0.1);
	  
	  logarithmBlock.setY(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(logarithmBlock.getY().getValue(), snValue, 0.1);
	  
	  logarithmBlock.setEOR10(BLogTypeEnum.Base10);
	  Assert.assertEquals(logarithmBlock.getEOR10(), BLogTypeEnum.Base10);
	  
	  logarithmBlock.setEOR10(BLogTypeEnum.Natural);
	  Assert.assertEquals(logarithmBlock.getEOR10(), BLogTypeEnum.Natural);	  
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Logarithm_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testSubtractBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BLogarithm logBlock = new BLogarithm();
	  try {
		logBlock.initHoneywellComponent(null);
	} catch (BlockInitializationException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(logBlock, BLogarithm.x.getName(), inputs.get(1));
	  logBlock.setEOR10(BLogTypeEnum.make(TestDataHelper.getInt(inputs.get(2),0)));
	  logBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(3), 0)));
	  
	  logBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(logBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(8), 0.0d));
	  logBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param logarithmBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BLogarithm additionBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  additionBlock.linkTo(nm1, nm1.getSlot("out"), additionBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "x":
		  additionBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  //@Test()
	public void testLinkRules() {
		BLogarithm logBlock = new BLogarithm();
		checkOutgoingLink(logBlock, BLogarithm.x, false);
		checkOutgoingLink(logBlock, BLogarithm.eOR10, false);
		checkOutgoingLink(logBlock, BLogarithm.tailOperation, false);
	}

	private void checkOutgoingLink(BLogarithm block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = logarithmBlock.checkLink(logarithmBlock, logarithmBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	/*@Test
	public void testDisplayNameChangeForYSlot() {
		logarithmBlock.setEOR10(BLogTypeEnum.make(BLogTypeEnum.BASE_10));
		Assert.assertEquals(logarithmBlock.getDisplayName(BLogarithm.Y, null),"Y(BASE10)");
		logarithmBlock.setEOR10(BLogTypeEnum.make(BLogTypeEnum.NATURAL));
		Assert.assertEquals(logarithmBlock.getDisplayName(BLogarithm.Y, null),"Y(NATURAL)");
	}*/
	
	
	 @Test
	  public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		  BLogarithm logBlock = new BLogarithm();
		  logBlock.setX(new BHonStatusNumeric(0.001));
		  logBlock.setEOR10(BLogTypeEnum.make(BLogTypeEnum.NATURAL));
		  logBlock.setTailOperation(BTailOperationEnum.Fractional);
		  logBlock.initHoneywellComponent(null);
		  executionParams.setIterationInterval(1000);
		  for(int i=0; i<5000; i++) {
			  logBlock.executeHoneywellComponent(executionParams);
			  Assert.assertEquals(logBlock.getY().getValue(), 0.907755279, 0.1,"Failed in iteration #"+i);
		  }
		  
		  BogFileUtil bogUtil = new BogFileUtil();
		  try {
			  File bogFile = bogUtil.saveComponentToBogFile("Logarithm", logBlock);
			  BLogarithm logarithmSaved = (BLogarithm) bogUtil.getComponentFromBogFile(bogFile);

			  Assert.assertEquals(logarithmSaved.getX().getValue(), 0.001, 0.1,"Failed to verify logarithm block after restart");
			  Assert.assertEquals(logarithmSaved.getEOR10().getOrdinal(), BLogTypeEnum.NATURAL);
			  Assert.assertEquals(logarithmSaved.getTailOperation(),BTailOperationEnum.Fractional);
			  Assert.assertEquals(logarithmSaved.getY().getValue(), Double.POSITIVE_INFINITY, 0.1,"Failed to verify logarithm block after restart");
		  } catch (IOException e) {
			  e.printStackTrace();
		  }
		  logBlock = null;
	  }

	@Test
	public void testConfigProperties() {
		List<Property> configList = logarithmBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BLogarithm.tailOperation.getName(), BLogarithm.eOR10.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = logarithmBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BLogarithm.x.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = logarithmBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BLogarithm.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  private BLogarithm logarithmBlock;
  private BExecutionParams executionParams;

}
