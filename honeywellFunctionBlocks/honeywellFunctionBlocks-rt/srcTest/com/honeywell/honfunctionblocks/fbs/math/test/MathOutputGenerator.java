/*
 * Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 * This file contains trade secrets of Honeywell International, Inc. No part may be reproduced or
 * transmitted in any form by any means or for any purpose without the express written permission of
 * Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;

import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;

/**
 *
 * <AUTHOR> - <PERSON><PERSON>
 * @since Jan 29, 2018
 */
public class MathOutputGenerator {

	/**
	 *
	 * @since Jan 29, 2018
	 * @param args
	 * @return {@link void}
	 */
	public static void main(String[] args) {		
		String operation = "ADD";
		
		
		MathOutputGenerator math = new MathOutputGenerator();
//		ArrayList<String[]> testData = readTestDataFromCSVFile("C:\\D\\SVN_Checkouts\\38840-F1-IP-Products\\SourceCode\\programmingTool\\programmingTool-rt\\srcTest\\com\\honeywell\\programmingtool\\fbs\\math\\test\\Add_InputData.csv");
		File testDataFile = TestDataHelper.getTestDataFileReference(MathOutputGenerator.class, "Add_InputData.csv");
		ArrayList<String[]> testData = readTestDataFromCSVFile(testDataFile);
		
		double output = 0.0;
		for (int i = 1; i < testData.size(); i++) {
			String[] data = testData.get(i);
			if(operation.equals("SUB") || operation.equals("MUL")) {
				ArrayList<String> input = new ArrayList<>();				
				input.add(data[2].toString());
				input.add(data[3].toString());
				output = math.generateOutput(input, TestDataHelper.getBoolean(data[1].toString()), operation, 0, data[4].toString());
			}else if(operation.equals("DIV")){
				ArrayList<String> input = new ArrayList<>();				
				input.add(data[2].toString());
				input.add(data[3].toString());
				output = math.generateOutput(input, TestDataHelper.getBoolean(data[1].toString()), operation, Integer.parseInt(data[4]), data[5].toString());				
			}else if(operation.equals("ADD")){
			ArrayList<String> input = new ArrayList<>();
				input.add(data[2].toString());
				input.add(data[3].toString());
				input.add(data[4].toString());
				input.add(data[5].toString());
				input.add(data[6].toString());
				input.add(data[7].toString());
				input.add(data[8].toString());
				input.add(data[9].toString());			
				output = math.generateOutput(input, TestDataHelper.getBoolean(data[1].toString()), operation, 0, data[10].toString());
			}
			String output1 = ""+output;
			if(Double.isInfinite(output) && output < 0) {
				output1 = "-inf";
			}else  if(Double.isInfinite(output)){
				output1 = "+inf";
			} else {
				BigDecimal bigDecimal = BigDecimal.valueOf(output).setScale(6, RoundingMode.HALF_UP);
				output1 = "" + bigDecimal.doubleValue();
			}

			System.out.println(Arrays.toString(data) + ", " + output1);
		}

	}

	/**
	 *
	 * @since Jan 29, 2018
	 * @param i
	 * @return {@link void}
	 */
	private double generateOutput(ArrayList<String> numInputs, boolean invalidFlag, String operation, int divOperation, String mathTailOperation) {
		double resultValue;
		double inValue;
		int i;
		int numOfInvalids = 0;
		int numOfUnconn = 0;

		boolean performMathTailOperation = false;

		if (mathTailOperation != null)
			performMathTailOperation = true;
		// Set the result to INVALID by default. Process each input operand.

		if(operation.equals("ADD")) {
			resultValue = 0.0;
		} else 
			resultValue = Double.POSITIVE_INFINITY;
		for (i = 0; i != numInputs.size(); i++) {
			String inputData = numInputs.get(i);
			if (TestDataHelper.isUnconnected(inputData)) {
				inValue = 0.0;
				numOfUnconn++;
			} else {
				inValue = TestDataHelper.getDouble(inputData, 0.0);
				if ((TestDataHelper.getDouble(inputData, 0.0) > 0 && Double.isInfinite(TestDataHelper.getDouble(inputData, 0.0))) || Double.isNaN(TestDataHelper.getDouble(inputData, 0.0))) {
					if (!(invalidFlag))// if invalid flag is not set it makes the result invalid if any inputs are invalid
					{
						resultValue = Double.POSITIVE_INFINITY;
						break;
					}

					else {
						if (operation.equals("ADD")) {
							inValue = 0.0;
							numOfInvalids++;
							if ((numOfInvalids + numOfUnconn) >= numInputs.size()) // if all inputs are invalid result is invalid per Paul Wacker 03/11/08
							{
								resultValue = Double.POSITIVE_INFINITY;
								break;
							}
						}
						// #if FB_ENABLE_SUB
						else if (operation.equals("SUB")) // if all inputs are invalid result is invalid per Paul Wacker 03/11/08
						{
							inValue = 0.0;
							numOfInvalids++;
							if (numOfInvalids >= 2) {
								resultValue = Double.POSITIVE_INFINITY;
								break;
							}
						}
						// #endif // FB_ENABLE_SUB
						// #if (FB_ENABLE_MUL || FB_ENABLE_DIV)
						else if (operation.equals("MUL") || operation.equals("DIV")) // if all inputs are invalid result is invalid per Paul Wacker 03/11/08
						{
							inValue = 1.0;
							numOfInvalids++;
							if (numOfInvalids >= 2) {
								resultValue = Double.POSITIVE_INFINITY;
								break;
							}
						}
						// #endif // (FB_ENABLE_MUL || FB_ENABLE_DIV)

						// If the loop does not support an invalidFlag, set the
						// output to INVALID on an INVALID input (base
						// behavior).

						else {
							resultValue = Double.POSITIVE_INFINITY;
							break;
						}
					}
				}
			}

			// If this is the first input, apply a unary operation to it, or
			// initialize the result to it. If we are past the first input,
			// apply the binary operation between the result and the new input,
			// updating the result.

			if (i == 0) {
				switch (operation) {
					// //#if FB_ENABLE_SQRT
					// case SQRT:
					// {
					// boolean negInvalid;
					//
					// // Locate the negInvalid flag for the operation. Set it
					// // to TRUE if undefined or invalid.
					//
					// //**pv = (PublicVariableID *)((UBYTE *)pLoop+offsetTable[numInputs+1]);
					// //**negInvalid = GetFValAsBoolean(*pv, TRUE);
					//
					// // If the input is negative and we are to treat this as
					// // invalid, set the result to invalid. If it is OK to
					// // proceed, use the absolute value of the input.
					//
					// if ( inValue < 0.0 )
					// {
					// if ( negInvalid )
					// {
					// resultValue = Double.POSITIVE_INFINITY;
					// break;
					// }
					// inValue = -inValue;
					// }
					// resultValue = sqrtf(inValue);
					// break;
					// }
					// //#endif // FB_ENABLE_SQRT
					//
					// //#if FB_ENABLE_LOGARITHM
					// case LOGARITHM:
					//
					// // If the input is negative, set the result to invalid.
					// // Calculate the logarithm using the selected base (e or
					// // 10).
					//
					// if ( inValue < 0.0 )
					// {
					// resultValue = Double.POSITIVE_INFINITY;
					// break;
					// }
					// if ( pLoop->byt.logarithmLoop.base10 )
					// {
					// resultValue = log10f(inValue);
					// }
					// else
					// {
					// resultValue = logf(inValue);
					// }
					// break;
					// //#endif // FB_ENABLE_LOGARITHM
					//
					default:
						resultValue = inValue;
						break;
				}
			} else {
				switch (operation) {
					// #if FB_ENABLE_ADD
					case "ADD":
						resultValue += inValue;
						if ((numOfInvalids >= 1) && ((numOfInvalids + numOfUnconn) >= numInputs.size())) // if all inputs are invalid result is invalid per Paul Wacker 03/11/08
						{
							resultValue = Double.POSITIVE_INFINITY;
						}
						break;
					// #endif // FB_ENABLE_ADD
					// #if FB_ENABLE_SUB
					case "SUB":
						resultValue -= inValue;
						break;
					// #endif // FB_ENABLE_SUB
					// #if FB_ENABLE_MUL
					case "MUL":
						resultValue *= inValue;
						break;
					// #endif // FB_ENABLE_MUL
					// #if FB_ENABLE_DIV
					case "DIV":
						if (inValue == 0) {
							resultValue = Double.POSITIVE_INFINITY;
							break;
						}

						// The DIV block performs either a divide or modulo
						// operation, based on a configuration selection.

						if (divOperation==1) {
							resultValue %= inValue;
						} else if(divOperation==0){
							resultValue /= inValue;
						}
						break;
					// #endif // FB_ENABLE_DIV
					// #if FB_ENABLE_POW
					// case POW:
					// {
					// BOOLEAN negInvalid;
					//
					// // Locate the negInvalid flag for the operation. Set it
					// // to TRUE if undefined or invalid.
					//
					// //**pv = (PublicVariableID *)((UBYTE *)pLoop+offsetTable[numInputs+1]);
					// //**negInvalid = GetFValAsBoolean(*pv, TRUE);
					//
					// // If the exponent is not an integer and the base is
					// // negative, and we are to consider this situation
					// // invalid, set the result to invalid. If we are to
					// // proceed with the operation, use the absolute value of
					// // the base.
					//
					// if ( floorf(inValue) != inValue )
					// {
					// if ( resultValue < 0.0 )
					// {
					// if ( negInvalid )
					// {
					// resultValue = Double.POSITIVE_INFINITY;
					// break;
					// }
					// resultValue = -resultValue;
					// }
					// }
					//
					// // Perform the exponentiation. If an overflow results,
					// // we rely on the PutFVal of the output to convert the
					// // resulting NaN to FL_INVALID.
					//
					// resultValue = powf(resultValue,inValue);
					// break;
					// }
					// #endif // FB_ENABLE_POW
				}
			}
		}

		// The code that follows assumes that all blocks that support a tail end
		// operation use the same configuration byte to specify it. Verify that
		// this is the case. This code should optimize away if satisified, and
		// cause an unreachable code warning if not. If the compiler fails to
		// issue an unreachable code warning, the math function blocks will not
		// work correctly.

		if (!performMathTailOperation) {
			if (Double.isNaN(resultValue)) {
				resultValue = Double.POSITIVE_INFINITY;
			}
			return resultValue;
		}

		// If the result is not INVALID, apply a tail end operation as specified
		// in the math loop configuration.

		if (!(Double.isInfinite(resultValue) && resultValue > 0) && !Double.isNaN(resultValue)) {
			switch (mathTailOperation) {
				case "1": {
					resultValue = Math.abs(resultValue);
					break;
				}
				case "2": {

					// We want to truncate the fractional part, leaving the
					// integer part with its sign. If the number is positive,
					// take the floor of the number. If the number is negative,
					// take the ceiling.

					if (resultValue < 0.0) {
						resultValue = Math.ceil(resultValue);
					} else {
						resultValue = Math.floor(resultValue);
					}
					break;
				}
				case "3": {

					// We want to remove the integer part, along with the sign.
					// Take the absolute value and subtract the floor of it.

					resultValue = Math.abs(resultValue);
					resultValue -= Math.floor(resultValue);
					break;
				}

				// If a NOP or an unrecognized tail operation code is supplied,
				// leave the result unchanged.

				default: {
					break;
				}
			}
		}

		// Write the result to the output.

		// **pv = (PublicVariableID *)((UBYTE *)pLoop+offsetTable[numInputs]);
		// **PutFVal(*pv,resultValue);
		if (Double.isNaN(resultValue)) {
			resultValue = Double.POSITIVE_INFINITY;
		}
		return resultValue;
	}

	public static ArrayList<String[]> readTestDataFromCSVFile(File completeFile) {
		BufferedReader br = null;
		String line = "";
		String cvsSplitBy = ",";
		ArrayList<String[]> testData = new ArrayList<>();
		try {
			br = new BufferedReader(new FileReader(completeFile));
			while ((line = br.readLine()) != null) {
				String[] data = line.split(cvsSplitBy);
				testData.add(data);
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return testData;
	}
}
