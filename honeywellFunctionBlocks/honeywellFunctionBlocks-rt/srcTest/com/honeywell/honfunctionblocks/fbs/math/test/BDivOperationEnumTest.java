/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.math.BDivOperationEnum;

/**
 * Implementation of Divide block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: TBD
 * <AUTHOR> - <PERSON><PERSON>ya B.
 * @since Jan 30, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BDivOperationEnumTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BDivOperationEnumTest(**********)1.0$ @*/
/* Generated Thu Feb 01 15:58:02 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDivOperationEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  @DataProvider(name ="enumOrdinal")
  public Object[][] getDataToTestEnumOrdinal(){
	  return new Object[][] { {Integer.valueOf(BDivOperationEnum.DIVIDE),BDivOperationEnum.Divide},
		  {Integer.valueOf(BDivOperationEnum.MODULO),BDivOperationEnum.Modulo}};
  }
  
  @Test(dataProvider="enumOrdinal")
  public void testOperationByMakeOrdinal(Integer ordinal, BDivOperationEnum tOEnum) {
	  Assert.assertEquals(BDivOperationEnum.make(ordinal),tOEnum);	  
  }
  
  
  @DataProvider(name ="enumTag")
  public Object[][] getDataToTestEnumTag(){
	  return new Object[][] { {BDivOperationEnum.Divide.getTag(),BDivOperationEnum.Divide},
		  {BDivOperationEnum.Modulo.getTag(),BDivOperationEnum.Modulo}};
  }
  
  @Test(dataProvider="enumTag")
  public void testOperationByMakeTag(String tag, BDivOperationEnum fcuEnum) {
	  Assert.assertEquals(BDivOperationEnum.make(tag),fcuEnum);
  } 
}
