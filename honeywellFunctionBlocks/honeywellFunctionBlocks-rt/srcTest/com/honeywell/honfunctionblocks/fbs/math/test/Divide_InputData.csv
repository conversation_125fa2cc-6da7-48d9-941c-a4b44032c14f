Iteration Intervel,Ignore invalid input,x1,x2,div operation,tail operation,y out
1000,1,connected=10,connected=10,0,0,
1000,1,-10,-10,0,0,
1000,1,0,0,0,0,
1000,1,1,1,0,0,
1000,1,0.001,0.001,0,0,
1000,1,-0.001,-0.001,0,0,
1000,1,unconnected,unconnected,0,0,
1000,1,unconnected=5,unconnected=5,0,0,
1000,1,unconnected=nan,unconnected=nan,0,0,
1000,1,unconnected=-inf,unconnected=-inf,0,0,
1000,1,connected=+inf,connected=+inf,0,0,
1000,1,connected=-inf,connected=-inf,0,0,
1000,1,connected=nan,connected=nan,0,0,
1000,1,10,10,0,0,
1000,1,10,-10,0,0,
1000,1,10,0,0,0,
1000,1,10,1,0,0,
1000,1,10,0.001,0,0,
1000,1,10,-0.001,0,0,
1000,1,10,unconnected,0,0,
1000,1,10,unconnected=5,0,0,
1000,1,10,unconnected=nan,0,0,
1000,1,10,unconnected=-inf,0,0,
1000,1,10,connected=+inf,0,0,
1000,1,10,connected=-inf,0,0,
1000,1,10,connected=nan,0,0,
1000,1,-10,10,0,0,
1000,1,-10,-10,0,0,
1000,1,-10,0,0,0,
1000,1,-10,1,0,0,
1000,1,-10,0.001,0,0,
1000,1,-10,-0.001,0,0,
1000,1,-10,unconnected,0,0,
1000,1,-10,unconnected=5,0,0,
1000,1,-10,unconnected=nan,0,0,
1000,1,-10,unconnected=-inf,0,0,
1000,1,-10,connected=+inf,0,0,
1000,1,-10,connected=-inf,0,0,
1000,1,-10,connected=nan,0,0,
1000,1,0,10,0,0,
1000,1,0,-10,0,0,
1000,1,0,0,0,0,
1000,1,0,1,0,0,
1000,1,0,0.001,0,0,
1000,1,0,-0.001,0,0,
1000,1,0,unconnected,0,0,
1000,1,0,unconnected=5,0,0,
1000,1,0,unconnected=nan,0,0,
1000,1,0,unconnected=-inf,0,0,
1000,1,0,connected=+inf,0,0,
1000,1,0,connected=-inf,0,0,
1000,1,0,connected=nan,0,0,
1000,1,1,10,0,0,
1000,1,1,-10,0,0,
1000,1,1,0,0,0,
1000,1,1,1,0,0,
1000,1,1,0.001,0,0,
1000,1,1,-0.001,0,0,
1000,1,1,unconnected,0,0,
1000,1,1,unconnected=5,0,0,
1000,1,1,unconnected=nan,0,0,
1000,1,1,unconnected=-inf,0,0,
1000,1,1,connected=+inf,0,0,
1000,1,1,connected=-inf,0,0,
1000,1,1,connected=nan,0,0,
1000,1,0.001,10,0,0,
1000,1,0.001,-10,0,0,
1000,1,0.001,0,0,0,
1000,1,0.001,1,0,0,
1000,1,0.001,0.001,0,0,
1000,1,0.001,-0.001,0,0,
1000,1,0.001,unconnected,0,0,
1000,1,0.001,unconnected=5,0,0,
1000,1,0.001,unconnected=nan,0,0,
1000,1,0.001,unconnected=-inf,0,0,
1000,1,0.001,connected=+inf,0,0,
1000,1,0.001,connected=-inf,0,0,
1000,1,0.001,connected=nan,0,0,
1000,1,-0.001,10,0,0,
1000,1,-0.001,-10,0,0,
1000,1,-0.001,0,0,0,
1000,1,-0.001,1,0,0,
1000,1,-0.001,0.001,0,0,
1000,1,-0.001,-0.001,0,0,
1000,1,-0.001,unconnected,0,0,
1000,1,-0.001,unconnected=5,0,0,
1000,1,-0.001,unconnected=nan,0,0,
1000,1,-0.001,unconnected=-inf,0,0,
1000,1,-0.001,connected=+inf,0,0,
1000,1,-0.001,connected=-inf,0,0,
1000,1,-0.001,connected=nan,0,0,
1000,1,unconnected,10,0,0,
1000,1,unconnected,-10,0,0,
1000,1,unconnected,0,0,0,
1000,1,unconnected,1,0,0,
1000,1,unconnected,0.001,0,0,
1000,1,unconnected,-0.001,0,0,
1000,1,unconnected,unconnected,0,0,
1000,1,unconnected,unconnected=5,0,0,
1000,1,unconnected,unconnected=nan,0,0,
1000,1,unconnected,unconnected=-inf,0,0,
1000,1,unconnected,connected=+inf,0,0,
1000,1,unconnected,connected=-inf,0,0,
1000,1,unconnected,connected=nan,0,0,
1000,1,unconnected=5,10,0,0,
1000,1,unconnected=5,-10,0,0,
1000,1,unconnected=5,0,0,0,
1000,1,unconnected=5,1,0,0,
1000,1,unconnected=5,0.001,0,0,
1000,1,unconnected=5,-0.001,0,0,
1000,1,unconnected=5,unconnected,0,0,
1000,1,unconnected=5,unconnected=5,0,0,
1000,1,unconnected=5,unconnected=nan,0,0,
1000,1,unconnected=5,unconnected=-inf,0,0,
1000,1,unconnected=5,connected=+inf,0,0,
1000,1,unconnected=5,connected=-inf,0,0,
1000,1,unconnected=5,connected=nan,0,0,
1000,1,unconnected=nan,10,0,0,
1000,1,unconnected=nan,-10,0,0,
1000,1,unconnected=nan,0,0,0,
1000,1,unconnected=nan,1,0,0,
1000,1,unconnected=nan,0.001,0,0,
1000,1,unconnected=nan,-0.001,0,0,
1000,1,unconnected=nan,unconnected,0,0,
1000,1,unconnected=nan,unconnected=5,0,0,
1000,1,unconnected=nan,unconnected=nan,0,0,
1000,1,unconnected=nan,unconnected=-inf,0,0,
1000,1,unconnected=nan,connected=+inf,0,0,
1000,1,unconnected=nan,connected=-inf,0,0,
1000,1,unconnected=nan,connected=nan,0,0,
1000,1,unconnected=-inf,10,0,0,
1000,1,unconnected=-inf,-10,0,0,
1000,1,unconnected=-inf,0,0,0,
1000,1,unconnected=-inf,1,0,0,
1000,1,unconnected=-inf,0.001,0,0,
1000,1,unconnected=-inf,-0.001,0,0,
1000,1,unconnected=-inf,unconnected,0,0,
1000,1,unconnected=-inf,unconnected=5,0,0,
1000,1,unconnected=-inf,unconnected=nan,0,0,
1000,1,unconnected=-inf,unconnected=-inf,0,0,
1000,1,unconnected=-inf,connected=+inf,0,0,
1000,1,unconnected=-inf,connected=-inf,0,0,
1000,1,unconnected=-inf,connected=nan,0,0,
1000,1,connected=+inf,10,0,0,
1000,1,connected=+inf,-10,0,0,
1000,1,connected=+inf,0,0,0,
1000,1,connected=+inf,1,0,0,
1000,1,connected=+inf,0.001,0,0,
1000,1,connected=+inf,-0.001,0,0,
1000,1,connected=+inf,unconnected,0,0,
1000,1,connected=+inf,unconnected=5,0,0,
1000,1,connected=+inf,unconnected=nan,0,0,
1000,1,connected=+inf,unconnected=-inf,0,0,
1000,1,connected=+inf,connected=+inf,0,0,
1000,1,connected=+inf,connected=-inf,0,0,
1000,1,connected=+inf,connected=nan,0,0,
1000,1,connected=-inf,10,0,0,
1000,1,connected=-inf,-10,0,0,
1000,1,connected=-inf,0,0,0,
1000,1,connected=-inf,1,0,0,
1000,1,connected=-inf,0.001,0,0,
1000,1,connected=-inf,-0.001,0,0,
1000,1,connected=-inf,unconnected,0,0,
1000,1,connected=-inf,unconnected=5,0,0,
1000,1,connected=-inf,unconnected=nan,0,0,
1000,1,connected=-inf,unconnected=-inf,0,0,
1000,1,connected=-inf,connected=+inf,0,0,
1000,1,connected=-inf,connected=-inf,0,0,
1000,1,connected=-inf,connected=nan,0,0,
1000,1,connected=nan,10,0,0,
1000,1,connected=nan,-10,0,0,
1000,1,connected=nan,0,0,0,
1000,1,connected=nan,1,0,0,
1000,1,connected=nan,0.001,0,0,
1000,1,connected=nan,-0.001,0,0,
1000,1,connected=nan,unconnected,0,0,
1000,1,connected=nan,unconnected=5,0,0,
1000,1,connected=nan,unconnected=nan,0,0,
1000,1,connected=nan,unconnected=-inf,0,0,
1000,1,connected=nan,connected=+inf,0,0,
1000,1,connected=nan,connected=-inf,0,0,
1000,1,connected=nan,connected=nan,0,0,
1000,1,10,10,0,1,
1000,1,-10,-10,0,1,
1000,1,0,0,0,1,
1000,1,1,1,0,1,
1000,1,0.001,0.001,0,1,
1000,1,-0.001,-0.001,0,1,
1000,1,unconnected,unconnected,0,1,
1000,1,unconnected=5,unconnected=5,0,1,
1000,1,unconnected=nan,unconnected=nan,0,1,
1000,1,unconnected=-inf,unconnected=-inf,0,1,
1000,1,connected=+inf,connected=+inf,0,1,
1000,1,connected=-inf,connected=-inf,0,1,
1000,1,connected=nan,connected=nan,0,1,
1000,1,10,10,0,1,
1000,1,10,-10,0,1,
1000,1,10,0,0,1,
1000,1,10,1,0,1,
1000,1,10,0.001,0,1,
1000,1,10,-0.001,0,1,
1000,1,10,unconnected,0,1,
1000,1,10,unconnected=5,0,1,
1000,1,10,unconnected=nan,0,1,
1000,1,10,unconnected=-inf,0,1,
1000,1,10,connected=+inf,0,1,
1000,1,10,connected=-inf,0,1,
1000,1,10,connected=nan,0,1,
1000,1,-10,10,0,1,
1000,1,-10,-10,0,1,
1000,1,-10,0,0,1,
1000,1,-10,1,0,1,
1000,1,-10,0.001,0,1,
1000,1,-10,-0.001,0,1,
1000,1,-10,unconnected,0,1,
1000,1,-10,unconnected=5,0,1,
1000,1,-10,unconnected=nan,0,1,
1000,1,-10,unconnected=-inf,0,1,
1000,1,-10,connected=+inf,0,1,
1000,1,-10,connected=-inf,0,1,
1000,1,-10,connected=nan,0,1,
1000,1,0,10,0,1,
1000,1,0,-10,0,1,
1000,1,0,0,0,1,
1000,1,0,1,0,1,
1000,1,0,0.001,0,1,
1000,1,0,-0.001,0,1,
1000,1,0,unconnected,0,1,
1000,1,0,unconnected=5,0,1,
1000,1,0,unconnected=nan,0,1,
1000,1,0,unconnected=-inf,0,1,
1000,1,0,connected=+inf,0,1,
1000,1,0,connected=-inf,0,1,
1000,1,0,connected=nan,0,1,
1000,1,1,10,0,1,
1000,1,1,-10,0,1,
1000,1,1,0,0,1,
1000,1,1,1,0,1,
1000,1,1,0.001,0,1,
1000,1,1,-0.001,0,1,
1000,1,1,unconnected,0,1,
1000,1,1,unconnected=5,0,1,
1000,1,1,unconnected=nan,0,1,
1000,1,1,unconnected=-inf,0,1,
1000,1,1,connected=+inf,0,1,
1000,1,1,connected=-inf,0,1,
1000,1,1,connected=nan,0,1,
1000,1,0.001,10,0,1,
1000,1,0.001,-10,0,1,
1000,1,0.001,0,0,1,
1000,1,0.001,1,0,1,
1000,1,0.001,0.001,0,1,
1000,1,0.001,-0.001,0,1,
1000,1,0.001,unconnected,0,1,
1000,1,0.001,unconnected=5,0,1,
1000,1,0.001,unconnected=nan,0,1,
1000,1,0.001,unconnected=-inf,0,1,
1000,1,0.001,connected=+inf,0,1,
1000,1,0.001,connected=-inf,0,1,
1000,1,0.001,connected=nan,0,1,
1000,1,-0.001,10,0,1,
1000,1,-0.001,-10,0,1,
1000,1,-0.001,0,0,1,
1000,1,-0.001,1,0,1,
1000,1,-0.001,0.001,0,1,
1000,1,-0.001,-0.001,0,1,
1000,1,-0.001,unconnected,0,1,
1000,1,-0.001,unconnected=5,0,1,
1000,1,-0.001,unconnected=nan,0,1,
1000,1,-0.001,unconnected=-inf,0,1,
1000,1,-0.001,connected=+inf,0,1,
1000,1,-0.001,connected=-inf,0,1,
1000,1,-0.001,connected=nan,0,1,
1000,1,unconnected,10,0,1,
1000,1,unconnected,-10,0,1,
1000,1,unconnected,0,0,1,
1000,1,unconnected,1,0,1,
1000,1,unconnected,0.001,0,1,
1000,1,unconnected,-0.001,0,1,
1000,1,unconnected,unconnected,0,1,
1000,1,unconnected,unconnected=5,0,1,
1000,1,unconnected,unconnected=nan,0,1,
1000,1,unconnected,unconnected=-inf,0,1,
1000,1,unconnected,connected=+inf,0,1,
1000,1,unconnected,connected=-inf,0,1,
1000,1,unconnected,connected=nan,0,1,
1000,1,unconnected=5,10,0,1,
1000,1,unconnected=5,-10,0,1,
1000,1,unconnected=5,0,0,1,
1000,1,unconnected=5,1,0,1,
1000,1,unconnected=5,0.001,0,1,
1000,1,unconnected=5,-0.001,0,1,
1000,1,unconnected=5,unconnected,0,1,
1000,1,unconnected=5,unconnected=5,0,1,
1000,1,unconnected=5,unconnected=nan,0,1,
1000,1,unconnected=5,unconnected=-inf,0,1,
1000,1,unconnected=5,connected=+inf,0,1,
1000,1,unconnected=5,connected=-inf,0,1,
1000,1,unconnected=5,connected=nan,0,1,
1000,1,unconnected=nan,10,0,1,
1000,1,unconnected=nan,-10,0,1,
1000,1,unconnected=nan,0,0,1,
1000,1,unconnected=nan,1,0,1,
1000,1,unconnected=nan,0.001,0,1,
1000,1,unconnected=nan,-0.001,0,1,
1000,1,unconnected=nan,unconnected,0,1,
1000,1,unconnected=nan,unconnected=5,0,1,
1000,1,unconnected=nan,unconnected=nan,0,1,
1000,1,unconnected=nan,unconnected=-inf,0,1,
1000,1,unconnected=nan,connected=+inf,0,1,
1000,1,unconnected=nan,connected=-inf,0,1,
1000,1,unconnected=nan,connected=nan,0,1,
1000,1,unconnected=-inf,10,0,1,
1000,1,unconnected=-inf,-10,0,1,
1000,1,unconnected=-inf,0,0,1,
1000,1,unconnected=-inf,1,0,1,
1000,1,unconnected=-inf,0.001,0,1,
1000,1,unconnected=-inf,-0.001,0,1,
1000,1,unconnected=-inf,unconnected,0,1,
1000,1,unconnected=-inf,unconnected=5,0,1,
1000,1,unconnected=-inf,unconnected=nan,0,1,
1000,1,unconnected=-inf,unconnected=-inf,0,1,
1000,1,unconnected=-inf,connected=+inf,0,1,
1000,1,unconnected=-inf,connected=-inf,0,1,
1000,1,unconnected=-inf,connected=nan,0,1,
1000,1,connected=+inf,10,0,1,
1000,1,connected=+inf,-10,0,1,
1000,1,connected=+inf,0,0,1,
1000,1,connected=+inf,1,0,1,
1000,1,connected=+inf,0.001,0,1,
1000,1,connected=+inf,-0.001,0,1,
1000,1,connected=+inf,unconnected,0,1,
1000,1,connected=+inf,unconnected=5,0,1,
1000,1,connected=+inf,unconnected=nan,0,1,
1000,1,connected=+inf,unconnected=-inf,0,1,
1000,1,connected=+inf,connected=+inf,0,1,
1000,1,connected=+inf,connected=-inf,0,1,
1000,1,connected=+inf,connected=nan,0,1,
1000,1,connected=-inf,10,0,1,
1000,1,connected=-inf,-10,0,1,
1000,1,connected=-inf,0,0,1,
1000,1,connected=-inf,1,0,1,
1000,1,connected=-inf,0.001,0,1,
1000,1,connected=-inf,-0.001,0,1,
1000,1,connected=-inf,unconnected,0,1,
1000,1,connected=-inf,unconnected=5,0,1,
1000,1,connected=-inf,unconnected=nan,0,1,
1000,1,connected=-inf,unconnected=-inf,0,1,
1000,1,connected=-inf,connected=+inf,0,1,
1000,1,connected=-inf,connected=-inf,0,1,
1000,1,connected=-inf,connected=nan,0,1,
1000,1,connected=nan,10,0,1,
1000,1,connected=nan,-10,0,1,
1000,1,connected=nan,0,0,1,
1000,1,connected=nan,1,0,1,
1000,1,connected=nan,0.001,0,1,
1000,1,connected=nan,-0.001,0,1,
1000,1,connected=nan,unconnected,0,1,
1000,1,connected=nan,unconnected=5,0,1,
1000,1,connected=nan,unconnected=nan,0,1,
1000,1,connected=nan,unconnected=-inf,0,1,
1000,1,connected=nan,connected=+inf,0,1,
1000,1,connected=nan,connected=-inf,0,1,
1000,1,connected=nan,connected=nan,0,1,
1000,1,10,10,0,2,
1000,1,-10,-10,0,2,
1000,1,0,0,0,2,
1000,1,1,1,0,2,
1000,1,0.001,0.001,0,2,
1000,1,-0.001,-0.001,0,2,
1000,1,unconnected,unconnected,0,2,
1000,1,unconnected=5,unconnected=5,0,2,
1000,1,unconnected=nan,unconnected=nan,0,2,
1000,1,unconnected=-inf,unconnected=-inf,0,2,
1000,1,connected=+inf,connected=+inf,0,2,
1000,1,connected=-inf,connected=-inf,0,2,
1000,1,connected=nan,connected=nan,0,2,
1000,1,10,10,0,2,
1000,1,10,-10,0,2,
1000,1,10,0,0,2,
1000,1,10,1,0,2,
1000,1,10,0.001,0,2,
1000,1,10,-0.001,0,2,
1000,1,10,unconnected,0,2,
1000,1,10,unconnected=5,0,2,
1000,1,10,unconnected=nan,0,2,
1000,1,10,unconnected=-inf,0,2,
1000,1,10,connected=+inf,0,2,
1000,1,10,connected=-inf,0,2,
1000,1,10,connected=nan,0,2,
1000,1,-10,10,0,2,
1000,1,-10,-10,0,2,
1000,1,-10,0,0,2,
1000,1,-10,1,0,2,
1000,1,-10,0.001,0,2,
1000,1,-10,-0.001,0,2,
1000,1,-10,unconnected,0,2,
1000,1,-10,unconnected=5,0,2,
1000,1,-10,unconnected=nan,0,2,
1000,1,-10,unconnected=-inf,0,2,
1000,1,-10,connected=+inf,0,2,
1000,1,-10,connected=-inf,0,2,
1000,1,-10,connected=nan,0,2,
1000,1,0,10,0,2,
1000,1,0,-10,0,2,
1000,1,0,0,0,2,
1000,1,0,1,0,2,
1000,1,0,0.001,0,2,
1000,1,0,-0.001,0,2,
1000,1,0,unconnected,0,2,
1000,1,0,unconnected=5,0,2,
1000,1,0,unconnected=nan,0,2,
1000,1,0,unconnected=-inf,0,2,
1000,1,0,connected=+inf,0,2,
1000,1,0,connected=-inf,0,2,
1000,1,0,connected=nan,0,2,
1000,1,1,10,0,2,
1000,1,1,-10,0,2,
1000,1,1,0,0,2,
1000,1,1,1,0,2,
1000,1,1,0.001,0,2,
1000,1,1,-0.001,0,2,
1000,1,1,unconnected,0,2,
1000,1,1,unconnected=5,0,2,
1000,1,1,unconnected=nan,0,2,
1000,1,1,unconnected=-inf,0,2,
1000,1,1,connected=+inf,0,2,
1000,1,1,connected=-inf,0,2,
1000,1,1,connected=nan,0,2,
1000,1,0.001,10,0,2,
1000,1,0.001,-10,0,2,
1000,1,0.001,0,0,2,
1000,1,0.001,1,0,2,
1000,1,0.001,0.001,0,2,
1000,1,0.001,-0.001,0,2,
1000,1,0.001,unconnected,0,2,
1000,1,0.001,unconnected=5,0,2,
1000,1,0.001,unconnected=nan,0,2,
1000,1,0.001,unconnected=-inf,0,2,
1000,1,0.001,connected=+inf,0,2,
1000,1,0.001,connected=-inf,0,2,
1000,1,0.001,connected=nan,0,2,
1000,1,-0.001,10,0,2,
1000,1,-0.001,-10,0,2,
1000,1,-0.001,0,0,2,
1000,1,-0.001,1,0,2,
1000,1,-0.001,0.001,0,2,
1000,1,-0.001,-0.001,0,2,
1000,1,-0.001,unconnected,0,2,
1000,1,-0.001,unconnected=5,0,2,
1000,1,-0.001,unconnected=nan,0,2,
1000,1,-0.001,unconnected=-inf,0,2,
1000,1,-0.001,connected=+inf,0,2,
1000,1,-0.001,connected=-inf,0,2,
1000,1,-0.001,connected=nan,0,2,
1000,1,unconnected,10,0,2,
1000,1,unconnected,-10,0,2,
1000,1,unconnected,0,0,2,
1000,1,unconnected,1,0,2,
1000,1,unconnected,0.001,0,2,
1000,1,unconnected,-0.001,0,2,
1000,1,unconnected,unconnected,0,2,
1000,1,unconnected,unconnected=5,0,2,
1000,1,unconnected,unconnected=nan,0,2,
1000,1,unconnected,unconnected=-inf,0,2,
1000,1,unconnected,connected=+inf,0,2,
1000,1,unconnected,connected=-inf,0,2,
1000,1,unconnected,connected=nan,0,2,
1000,1,unconnected=5,10,0,2,
1000,1,unconnected=5,-10,0,2,
1000,1,unconnected=5,0,0,2,
1000,1,unconnected=5,1,0,2,
1000,1,unconnected=5,0.001,0,2,
1000,1,unconnected=5,-0.001,0,2,
1000,1,unconnected=5,unconnected,0,2,
1000,1,unconnected=5,unconnected=5,0,2,
1000,1,unconnected=5,unconnected=nan,0,2,
1000,1,unconnected=5,unconnected=-inf,0,2,
1000,1,unconnected=5,connected=+inf,0,2,
1000,1,unconnected=5,connected=-inf,0,2,
1000,1,unconnected=5,connected=nan,0,2,
1000,1,unconnected=nan,10,0,2,
1000,1,unconnected=nan,-10,0,2,
1000,1,unconnected=nan,0,0,2,
1000,1,unconnected=nan,1,0,2,
1000,1,unconnected=nan,0.001,0,2,
1000,1,unconnected=nan,-0.001,0,2,
1000,1,unconnected=nan,unconnected,0,2,
1000,1,unconnected=nan,unconnected=5,0,2,
1000,1,unconnected=nan,unconnected=nan,0,2,
1000,1,unconnected=nan,unconnected=-inf,0,2,
1000,1,unconnected=nan,connected=+inf,0,2,
1000,1,unconnected=nan,connected=-inf,0,2,
1000,1,unconnected=nan,connected=nan,0,2,
1000,1,unconnected=-inf,10,0,2,
1000,1,unconnected=-inf,-10,0,2,
1000,1,unconnected=-inf,0,0,2,
1000,1,unconnected=-inf,1,0,2,
1000,1,unconnected=-inf,0.001,0,2,
1000,1,unconnected=-inf,-0.001,0,2,
1000,1,unconnected=-inf,unconnected,0,2,
1000,1,unconnected=-inf,unconnected=5,0,2,
1000,1,unconnected=-inf,unconnected=nan,0,2,
1000,1,unconnected=-inf,unconnected=-inf,0,2,
1000,1,unconnected=-inf,connected=+inf,0,2,
1000,1,unconnected=-inf,connected=-inf,0,2,
1000,1,unconnected=-inf,connected=nan,0,2,
1000,1,connected=+inf,10,0,2,
1000,1,connected=+inf,-10,0,2,
1000,1,connected=+inf,0,0,2,
1000,1,connected=+inf,1,0,2,
1000,1,connected=+inf,0.001,0,2,
1000,1,connected=+inf,-0.001,0,2,
1000,1,connected=+inf,unconnected,0,2,
1000,1,connected=+inf,unconnected=5,0,2,
1000,1,connected=+inf,unconnected=nan,0,2,
1000,1,connected=+inf,unconnected=-inf,0,2,
1000,1,connected=+inf,connected=+inf,0,2,
1000,1,connected=+inf,connected=-inf,0,2,
1000,1,connected=+inf,connected=nan,0,2,
1000,1,connected=-inf,10,0,2,
1000,1,connected=-inf,-10,0,2,
1000,1,connected=-inf,0,0,2,
1000,1,connected=-inf,1,0,2,
1000,1,connected=-inf,0.001,0,2,
1000,1,connected=-inf,-0.001,0,2,
1000,1,connected=-inf,unconnected,0,2,
1000,1,connected=-inf,unconnected=5,0,2,
1000,1,connected=-inf,unconnected=nan,0,2,
1000,1,connected=-inf,unconnected=-inf,0,2,
1000,1,connected=-inf,connected=+inf,0,2,
1000,1,connected=-inf,connected=-inf,0,2,
1000,1,connected=-inf,connected=nan,0,2,
1000,1,connected=nan,10,0,2,
1000,1,connected=nan,-10,0,2,
1000,1,connected=nan,0,0,2,
1000,1,connected=nan,1,0,2,
1000,1,connected=nan,0.001,0,2,
1000,1,connected=nan,-0.001,0,2,
1000,1,connected=nan,unconnected,0,2,
1000,1,connected=nan,unconnected=5,0,2,
1000,1,connected=nan,unconnected=nan,0,2,
1000,1,connected=nan,unconnected=-inf,0,2,
1000,1,connected=nan,connected=+inf,0,2,
1000,1,connected=nan,connected=-inf,0,2,
1000,1,connected=nan,connected=nan,0,2,
1000,1,10,10,0,3,
1000,1,-10,-10,0,3,
1000,1,0,0,0,3,
1000,1,1,1,0,3,
1000,1,0.001,0.001,0,3,
1000,1,-0.001,-0.001,0,3,
1000,1,unconnected,unconnected,0,3,
1000,1,unconnected=5,unconnected=5,0,3,
1000,1,unconnected=nan,unconnected=nan,0,3,
1000,1,unconnected=-inf,unconnected=-inf,0,3,
1000,1,connected=+inf,connected=+inf,0,3,
1000,1,connected=-inf,connected=-inf,0,3,
1000,1,connected=nan,connected=nan,0,3,
1000,1,10,10,0,3,
1000,1,10,-10,0,3,
1000,1,10,0,0,3,
1000,1,10,1,0,3,
1000,1,10,0.001,0,3,
1000,1,10,-0.001,0,3,
1000,1,10,unconnected,0,3,
1000,1,10,unconnected=5,0,3,
1000,1,10,unconnected=nan,0,3,
1000,1,10,unconnected=-inf,0,3,
1000,1,10,connected=+inf,0,3,
1000,1,10,connected=-inf,0,3,
1000,1,10,connected=nan,0,3,
1000,1,-10,10,0,3,
1000,1,-10,-10,0,3,
1000,1,-10,0,0,3,
1000,1,-10,1,0,3,
1000,1,-10,0.001,0,3,
1000,1,-10,-0.001,0,3,
1000,1,-10,unconnected,0,3,
1000,1,-10,unconnected=5,0,3,
1000,1,-10,unconnected=nan,0,3,
1000,1,-10,unconnected=-inf,0,3,
1000,1,-10,connected=+inf,0,3,
1000,1,-10,connected=-inf,0,3,
1000,1,-10,connected=nan,0,3,
1000,1,0,10,0,3,
1000,1,0,-10,0,3,
1000,1,0,0,0,3,
1000,1,0,1,0,3,
1000,1,0,0.001,0,3,
1000,1,0,-0.001,0,3,
1000,1,0,unconnected,0,3,
1000,1,0,unconnected=5,0,3,
1000,1,0,unconnected=nan,0,3,
1000,1,0,unconnected=-inf,0,3,
1000,1,0,connected=+inf,0,3,
1000,1,0,connected=-inf,0,3,
1000,1,0,connected=nan,0,3,
1000,1,1,10,0,3,
1000,1,1,-10,0,3,
1000,1,1,0,0,3,
1000,1,1,1,0,3,
1000,1,1,0.001,0,3,
1000,1,1,-0.001,0,3,
1000,1,1,unconnected,0,3,
1000,1,1,unconnected=5,0,3,
1000,1,1,unconnected=nan,0,3,
1000,1,1,unconnected=-inf,0,3,
1000,1,1,connected=+inf,0,3,
1000,1,1,connected=-inf,0,3,
1000,1,1,connected=nan,0,3,
1000,1,0.001,10,0,3,
1000,1,0.001,-10,0,3,
1000,1,0.001,0,0,3,
1000,1,0.001,1,0,3,
1000,1,0.001,0.001,0,3,
1000,1,0.001,-0.001,0,3,
1000,1,0.001,unconnected,0,3,
1000,1,0.001,unconnected=5,0,3,
1000,1,0.001,unconnected=nan,0,3,
1000,1,0.001,unconnected=-inf,0,3,
1000,1,0.001,connected=+inf,0,3,
1000,1,0.001,connected=-inf,0,3,
1000,1,0.001,connected=nan,0,3,
1000,1,-0.001,10,0,3,
1000,1,-0.001,-10,0,3,
1000,1,-0.001,0,0,3,
1000,1,-0.001,1,0,3,
1000,1,-0.001,0.001,0,3,
1000,1,-0.001,-0.001,0,3,
1000,1,-0.001,unconnected,0,3,
1000,1,-0.001,unconnected=5,0,3,
1000,1,-0.001,unconnected=nan,0,3,
1000,1,-0.001,unconnected=-inf,0,3,
1000,1,-0.001,connected=+inf,0,3,
1000,1,-0.001,connected=-inf,0,3,
1000,1,-0.001,connected=nan,0,3,
1000,1,unconnected,10,0,3,
1000,1,unconnected,-10,0,3,
1000,1,unconnected,0,0,3,
1000,1,unconnected,1,0,3,
1000,1,unconnected,0.001,0,3,
1000,1,unconnected,-0.001,0,3,
1000,1,unconnected,unconnected,0,3,
1000,1,unconnected,unconnected=5,0,3,
1000,1,unconnected,unconnected=nan,0,3,
1000,1,unconnected,unconnected=-inf,0,3,
1000,1,unconnected,connected=+inf,0,3,
1000,1,unconnected,connected=-inf,0,3,
1000,1,unconnected,connected=nan,0,3,
1000,1,unconnected=5,10,0,3,
1000,1,unconnected=5,-10,0,3,
1000,1,unconnected=5,0,0,3,
1000,1,unconnected=5,1,0,3,
1000,1,unconnected=5,0.001,0,3,
1000,1,unconnected=5,-0.001,0,3,
1000,1,unconnected=5,unconnected,0,3,
1000,1,unconnected=5,unconnected=5,0,3,
1000,1,unconnected=5,unconnected=nan,0,3,
1000,1,unconnected=5,unconnected=-inf,0,3,
1000,1,unconnected=5,connected=+inf,0,3,
1000,1,unconnected=5,connected=-inf,0,3,
1000,1,unconnected=5,connected=nan,0,3,
1000,1,unconnected=nan,10,0,3,
1000,1,unconnected=nan,-10,0,3,
1000,1,unconnected=nan,0,0,3,
1000,1,unconnected=nan,1,0,3,
1000,1,unconnected=nan,0.001,0,3,
1000,1,unconnected=nan,-0.001,0,3,
1000,1,unconnected=nan,unconnected,0,3,
1000,1,unconnected=nan,unconnected=5,0,3,
1000,1,unconnected=nan,unconnected=nan,0,3,
1000,1,unconnected=nan,unconnected=-inf,0,3,
1000,1,unconnected=nan,connected=+inf,0,3,
1000,1,unconnected=nan,connected=-inf,0,3,
1000,1,unconnected=nan,connected=nan,0,3,
1000,1,unconnected=-inf,10,0,3,
1000,1,unconnected=-inf,-10,0,3,
1000,1,unconnected=-inf,0,0,3,
1000,1,unconnected=-inf,1,0,3,
1000,1,unconnected=-inf,0.001,0,3,
1000,1,unconnected=-inf,-0.001,0,3,
1000,1,unconnected=-inf,unconnected,0,3,
1000,1,unconnected=-inf,unconnected=5,0,3,
1000,1,unconnected=-inf,unconnected=nan,0,3,
1000,1,unconnected=-inf,unconnected=-inf,0,3,
1000,1,unconnected=-inf,connected=+inf,0,3,
1000,1,unconnected=-inf,connected=-inf,0,3,
1000,1,unconnected=-inf,connected=nan,0,3,
1000,1,connected=+inf,10,0,3,
1000,1,connected=+inf,-10,0,3,
1000,1,connected=+inf,0,0,3,
1000,1,connected=+inf,1,0,3,
1000,1,connected=+inf,0.001,0,3,
1000,1,connected=+inf,-0.001,0,3,
1000,1,connected=+inf,unconnected,0,3,
1000,1,connected=+inf,unconnected=5,0,3,
1000,1,connected=+inf,unconnected=nan,0,3,
1000,1,connected=+inf,unconnected=-inf,0,3,
1000,1,connected=+inf,connected=+inf,0,3,
1000,1,connected=+inf,connected=-inf,0,3,
1000,1,connected=+inf,connected=nan,0,3,
1000,1,connected=-inf,10,0,3,
1000,1,connected=-inf,-10,0,3,
1000,1,connected=-inf,0,0,3,
1000,1,connected=-inf,1,0,3,
1000,1,connected=-inf,0.001,0,3,
1000,1,connected=-inf,-0.001,0,3,
1000,1,connected=-inf,unconnected,0,3,
1000,1,connected=-inf,unconnected=5,0,3,
1000,1,connected=-inf,unconnected=nan,0,3,
1000,1,connected=-inf,unconnected=-inf,0,3,
1000,1,connected=-inf,connected=+inf,0,3,
1000,1,connected=-inf,connected=-inf,0,3,
1000,1,connected=-inf,connected=nan,0,3,
1000,1,connected=nan,10,0,3,
1000,1,connected=nan,-10,0,3,
1000,1,connected=nan,0,0,3,
1000,1,connected=nan,1,0,3,
1000,1,connected=nan,0.001,0,3,
1000,1,connected=nan,-0.001,0,3,
1000,1,connected=nan,unconnected,0,3,
1000,1,connected=nan,unconnected=5,0,3,
1000,1,connected=nan,unconnected=nan,0,3,
1000,1,connected=nan,unconnected=-inf,0,3,
1000,1,connected=nan,connected=+inf,0,3,
1000,1,connected=nan,connected=-inf,0,3,
1000,1,connected=nan,connected=nan,0,3,
1000,0,10,10,0,0,
1000,0,-10,-10,0,0,
1000,0,0,0,0,0,
1000,0,1,1,0,0,
1000,0,0.001,0.001,0,0,
1000,0,-0.001,-0.001,0,0,
1000,0,unconnected,unconnected,0,0,
1000,0,unconnected=5,unconnected=5,0,0,
1000,0,unconnected=nan,unconnected=nan,0,0,
1000,0,unconnected=-inf,unconnected=-inf,0,0,
1000,0,connected=+inf,connected=+inf,0,0,
1000,0,connected=-inf,connected=-inf,0,0,
1000,0,connected=nan,connected=nan,0,0,
1000,0,10,10,0,0,
1000,0,10,-10,0,0,
1000,0,10,0,0,0,
1000,0,10,1,0,0,
1000,0,10,0.001,0,0,
1000,0,10,-0.001,0,0,
1000,0,10,unconnected,0,0,
1000,0,10,unconnected=5,0,0,
1000,0,10,unconnected=nan,0,0,
1000,0,10,unconnected=-inf,0,0,
1000,0,10,connected=+inf,0,0,
1000,0,10,connected=-inf,0,0,
1000,0,10,connected=nan,0,0,
1000,0,-10,10,0,0,
1000,0,-10,-10,0,0,
1000,0,-10,0,0,0,
1000,0,-10,1,0,0,
1000,0,-10,0.001,0,0,
1000,0,-10,-0.001,0,0,
1000,0,-10,unconnected,0,0,
1000,0,-10,unconnected=5,0,0,
1000,0,-10,unconnected=nan,0,0,
1000,0,-10,unconnected=-inf,0,0,
1000,0,-10,connected=+inf,0,0,
1000,0,-10,connected=-inf,0,0,
1000,0,-10,connected=nan,0,0,
1000,0,0,10,0,0,
1000,0,0,-10,0,0,
1000,0,0,0,0,0,
1000,0,0,1,0,0,
1000,0,0,0.001,0,0,
1000,0,0,-0.001,0,0,
1000,0,0,unconnected,0,0,
1000,0,0,unconnected=5,0,0,
1000,0,0,unconnected=nan,0,0,
1000,0,0,unconnected=-inf,0,0,
1000,0,0,connected=+inf,0,0,
1000,0,0,connected=-inf,0,0,
1000,0,0,connected=nan,0,0,
1000,0,1,10,0,0,
1000,0,1,-10,0,0,
1000,0,1,0,0,0,
1000,0,1,1,0,0,
1000,0,1,0.001,0,0,
1000,0,1,-0.001,0,0,
1000,0,1,unconnected,0,0,
1000,0,1,unconnected=5,0,0,
1000,0,1,unconnected=nan,0,0,
1000,0,1,unconnected=-inf,0,0,
1000,0,1,connected=+inf,0,0,
1000,0,1,connected=-inf,0,0,
1000,0,1,connected=nan,0,0,
1000,0,0.001,10,0,0,
1000,0,0.001,-10,0,0,
1000,0,0.001,0,0,0,
1000,0,0.001,1,0,0,
1000,0,0.001,0.001,0,0,
1000,0,0.001,-0.001,0,0,
1000,0,0.001,unconnected,0,0,
1000,0,0.001,unconnected=5,0,0,
1000,0,0.001,unconnected=nan,0,0,
1000,0,0.001,unconnected=-inf,0,0,
1000,0,0.001,connected=+inf,0,0,
1000,0,0.001,connected=-inf,0,0,
1000,0,0.001,connected=nan,0,0,
1000,0,-0.001,10,0,0,
1000,0,-0.001,-10,0,0,
1000,0,-0.001,0,0,0,
1000,0,-0.001,1,0,0,
1000,0,-0.001,0.001,0,0,
1000,0,-0.001,-0.001,0,0,
1000,0,-0.001,unconnected,0,0,
1000,0,-0.001,unconnected=5,0,0,
1000,0,-0.001,unconnected=nan,0,0,
1000,0,-0.001,unconnected=-inf,0,0,
1000,0,-0.001,connected=+inf,0,0,
1000,0,-0.001,connected=-inf,0,0,
1000,0,-0.001,connected=nan,0,0,
1000,0,unconnected,10,0,0,
1000,0,unconnected,-10,0,0,
1000,0,unconnected,0,0,0,
1000,0,unconnected,1,0,0,
1000,0,unconnected,0.001,0,0,
1000,0,unconnected,-0.001,0,0,
1000,0,unconnected,unconnected,0,0,
1000,0,unconnected,unconnected=5,0,0,
1000,0,unconnected,unconnected=nan,0,0,
1000,0,unconnected,unconnected=-inf,0,0,
1000,0,unconnected,connected=+inf,0,0,
1000,0,unconnected,connected=-inf,0,0,
1000,0,unconnected,connected=nan,0,0,
1000,0,unconnected=5,10,0,0,
1000,0,unconnected=5,-10,0,0,
1000,0,unconnected=5,0,0,0,
1000,0,unconnected=5,1,0,0,
1000,0,unconnected=5,0.001,0,0,
1000,0,unconnected=5,-0.001,0,0,
1000,0,unconnected=5,unconnected,0,0,
1000,0,unconnected=5,unconnected=5,0,0,
1000,0,unconnected=5,unconnected=nan,0,0,
1000,0,unconnected=5,unconnected=-inf,0,0,
1000,0,unconnected=5,connected=+inf,0,0,
1000,0,unconnected=5,connected=-inf,0,0,
1000,0,unconnected=5,connected=nan,0,0,
1000,0,unconnected=nan,10,0,0,
1000,0,unconnected=nan,-10,0,0,
1000,0,unconnected=nan,0,0,0,
1000,0,unconnected=nan,1,0,0,
1000,0,unconnected=nan,0.001,0,0,
1000,0,unconnected=nan,-0.001,0,0,
1000,0,unconnected=nan,unconnected,0,0,
1000,0,unconnected=nan,unconnected=5,0,0,
1000,0,unconnected=nan,unconnected=nan,0,0,
1000,0,unconnected=nan,unconnected=-inf,0,0,
1000,0,unconnected=nan,connected=+inf,0,0,
1000,0,unconnected=nan,connected=-inf,0,0,
1000,0,unconnected=nan,connected=nan,0,0,
1000,0,unconnected=-inf,10,0,0,
1000,0,unconnected=-inf,-10,0,0,
1000,0,unconnected=-inf,0,0,0,
1000,0,unconnected=-inf,1,0,0,
1000,0,unconnected=-inf,0.001,0,0,
1000,0,unconnected=-inf,-0.001,0,0,
1000,0,unconnected=-inf,unconnected,0,0,
1000,0,unconnected=-inf,unconnected=5,0,0,
1000,0,unconnected=-inf,unconnected=nan,0,0,
1000,0,unconnected=-inf,unconnected=-inf,0,0,
1000,0,unconnected=-inf,connected=+inf,0,0,
1000,0,unconnected=-inf,connected=-inf,0,0,
1000,0,unconnected=-inf,connected=nan,0,0,
1000,0,connected=+inf,10,0,0,
1000,0,connected=+inf,-10,0,0,
1000,0,connected=+inf,0,0,0,
1000,0,connected=+inf,1,0,0,
1000,0,connected=+inf,0.001,0,0,
1000,0,connected=+inf,-0.001,0,0,
1000,0,connected=+inf,unconnected,0,0,
1000,0,connected=+inf,unconnected=5,0,0,
1000,0,connected=+inf,unconnected=nan,0,0,
1000,0,connected=+inf,unconnected=-inf,0,0,
1000,0,connected=+inf,connected=+inf,0,0,
1000,0,connected=+inf,connected=-inf,0,0,
1000,0,connected=+inf,connected=nan,0,0,
1000,0,connected=-inf,10,0,0,
1000,0,connected=-inf,-10,0,0,
1000,0,connected=-inf,0,0,0,
1000,0,connected=-inf,1,0,0,
1000,0,connected=-inf,0.001,0,0,
1000,0,connected=-inf,-0.001,0,0,
1000,0,connected=-inf,unconnected,0,0,
1000,0,connected=-inf,unconnected=5,0,0,
1000,0,connected=-inf,unconnected=nan,0,0,
1000,0,connected=-inf,unconnected=-inf,0,0,
1000,0,connected=-inf,connected=+inf,0,0,
1000,0,connected=-inf,connected=-inf,0,0,
1000,0,connected=-inf,connected=nan,0,0,
1000,0,connected=nan,10,0,0,
1000,0,connected=nan,-10,0,0,
1000,0,connected=nan,0,0,0,
1000,0,connected=nan,1,0,0,
1000,0,connected=nan,0.001,0,0,
1000,0,connected=nan,-0.001,0,0,
1000,0,connected=nan,unconnected,0,0,
1000,0,connected=nan,unconnected=5,0,0,
1000,0,connected=nan,unconnected=nan,0,0,
1000,0,connected=nan,unconnected=-inf,0,0,
1000,0,connected=nan,connected=+inf,0,0,
1000,0,connected=nan,connected=-inf,0,0,
1000,0,connected=nan,connected=nan,0,0,
1000,0,10,10,0,1,
1000,0,-10,-10,0,1,
1000,0,0,0,0,1,
1000,0,1,1,0,1,
1000,0,0.001,0.001,0,1,
1000,0,-0.001,-0.001,0,1,
1000,0,unconnected,unconnected,0,1,
1000,0,unconnected=5,unconnected=5,0,1,
1000,0,unconnected=nan,unconnected=nan,0,1,
1000,0,unconnected=-inf,unconnected=-inf,0,1,
1000,0,connected=+inf,connected=+inf,0,1,
1000,0,connected=-inf,connected=-inf,0,1,
1000,0,connected=nan,connected=nan,0,1,
1000,0,10,10,0,1,
1000,0,10,-10,0,1,
1000,0,10,0,0,1,
1000,0,10,1,0,1,
1000,0,10,0.001,0,1,
1000,0,10,-0.001,0,1,
1000,0,10,unconnected,0,1,
1000,0,10,unconnected=5,0,1,
1000,0,10,unconnected=nan,0,1,
1000,0,10,unconnected=-inf,0,1,
1000,0,10,connected=+inf,0,1,
1000,0,10,connected=-inf,0,1,
1000,0,10,connected=nan,0,1,
1000,0,-10,10,0,1,
1000,0,-10,-10,0,1,
1000,0,-10,0,0,1,
1000,0,-10,1,0,1,
1000,0,-10,0.001,0,1,
1000,0,-10,-0.001,0,1,
1000,0,-10,unconnected,0,1,
1000,0,-10,unconnected=5,0,1,
1000,0,-10,unconnected=nan,0,1,
1000,0,-10,unconnected=-inf,0,1,
1000,0,-10,connected=+inf,0,1,
1000,0,-10,connected=-inf,0,1,
1000,0,-10,connected=nan,0,1,
1000,0,0,10,0,1,
1000,0,0,-10,0,1,
1000,0,0,0,0,1,
1000,0,0,1,0,1,
1000,0,0,0.001,0,1,
1000,0,0,-0.001,0,1,
1000,0,0,unconnected,0,1,
1000,0,0,unconnected=5,0,1,
1000,0,0,unconnected=nan,0,1,
1000,0,0,unconnected=-inf,0,1,
1000,0,0,connected=+inf,0,1,
1000,0,0,connected=-inf,0,1,
1000,0,0,connected=nan,0,1,
1000,0,1,10,0,1,
1000,0,1,-10,0,1,
1000,0,1,0,0,1,
1000,0,1,1,0,1,
1000,0,1,0.001,0,1,
1000,0,1,-0.001,0,1,
1000,0,1,unconnected,0,1,
1000,0,1,unconnected=5,0,1,
1000,0,1,unconnected=nan,0,1,
1000,0,1,unconnected=-inf,0,1,
1000,0,1,connected=+inf,0,1,
1000,0,1,connected=-inf,0,1,
1000,0,1,connected=nan,0,1,
1000,0,0.001,10,0,1,
1000,0,0.001,-10,0,1,
1000,0,0.001,0,0,1,
1000,0,0.001,1,0,1,
1000,0,0.001,0.001,0,1,
1000,0,0.001,-0.001,0,1,
1000,0,0.001,unconnected,0,1,
1000,0,0.001,unconnected=5,0,1,
1000,0,0.001,unconnected=nan,0,1,
1000,0,0.001,unconnected=-inf,0,1,
1000,0,0.001,connected=+inf,0,1,
1000,0,0.001,connected=-inf,0,1,
1000,0,0.001,connected=nan,0,1,
1000,0,-0.001,10,0,1,
1000,0,-0.001,-10,0,1,
1000,0,-0.001,0,0,1,
1000,0,-0.001,1,0,1,
1000,0,-0.001,0.001,0,1,
1000,0,-0.001,-0.001,0,1,
1000,0,-0.001,unconnected,0,1,
1000,0,-0.001,unconnected=5,0,1,
1000,0,-0.001,unconnected=nan,0,1,
1000,0,-0.001,unconnected=-inf,0,1,
1000,0,-0.001,connected=+inf,0,1,
1000,0,-0.001,connected=-inf,0,1,
1000,0,-0.001,connected=nan,0,1,
1000,0,unconnected,10,0,1,
1000,0,unconnected,-10,0,1,
1000,0,unconnected,0,0,1,
1000,0,unconnected,1,0,1,
1000,0,unconnected,0.001,0,1,
1000,0,unconnected,-0.001,0,1,
1000,0,unconnected,unconnected,0,1,
1000,0,unconnected,unconnected=5,0,1,
1000,0,unconnected,unconnected=nan,0,1,
1000,0,unconnected,unconnected=-inf,0,1,
1000,0,unconnected,connected=+inf,0,1,
1000,0,unconnected,connected=-inf,0,1,
1000,0,unconnected,connected=nan,0,1,
1000,0,unconnected=5,10,0,1,
1000,0,unconnected=5,-10,0,1,
1000,0,unconnected=5,0,0,1,
1000,0,unconnected=5,1,0,1,
1000,0,unconnected=5,0.001,0,1,
1000,0,unconnected=5,-0.001,0,1,
1000,0,unconnected=5,unconnected,0,1,
1000,0,unconnected=5,unconnected=5,0,1,
1000,0,unconnected=5,unconnected=nan,0,1,
1000,0,unconnected=5,unconnected=-inf,0,1,
1000,0,unconnected=5,connected=+inf,0,1,
1000,0,unconnected=5,connected=-inf,0,1,
1000,0,unconnected=5,connected=nan,0,1,
1000,0,unconnected=nan,10,0,1,
1000,0,unconnected=nan,-10,0,1,
1000,0,unconnected=nan,0,0,1,
1000,0,unconnected=nan,1,0,1,
1000,0,unconnected=nan,0.001,0,1,
1000,0,unconnected=nan,-0.001,0,1,
1000,0,unconnected=nan,unconnected,0,1,
1000,0,unconnected=nan,unconnected=5,0,1,
1000,0,unconnected=nan,unconnected=nan,0,1,
1000,0,unconnected=nan,unconnected=-inf,0,1,
1000,0,unconnected=nan,connected=+inf,0,1,
1000,0,unconnected=nan,connected=-inf,0,1,
1000,0,unconnected=nan,connected=nan,0,1,
1000,0,unconnected=-inf,10,0,1,
1000,0,unconnected=-inf,-10,0,1,
1000,0,unconnected=-inf,0,0,1,
1000,0,unconnected=-inf,1,0,1,
1000,0,unconnected=-inf,0.001,0,1,
1000,0,unconnected=-inf,-0.001,0,1,
1000,0,unconnected=-inf,unconnected,0,1,
1000,0,unconnected=-inf,unconnected=5,0,1,
1000,0,unconnected=-inf,unconnected=nan,0,1,
1000,0,unconnected=-inf,unconnected=-inf,0,1,
1000,0,unconnected=-inf,connected=+inf,0,1,
1000,0,unconnected=-inf,connected=-inf,0,1,
1000,0,unconnected=-inf,connected=nan,0,1,
1000,0,connected=+inf,10,0,1,
1000,0,connected=+inf,-10,0,1,
1000,0,connected=+inf,0,0,1,
1000,0,connected=+inf,1,0,1,
1000,0,connected=+inf,0.001,0,1,
1000,0,connected=+inf,-0.001,0,1,
1000,0,connected=+inf,unconnected,0,1,
1000,0,connected=+inf,unconnected=5,0,1,
1000,0,connected=+inf,unconnected=nan,0,1,
1000,0,connected=+inf,unconnected=-inf,0,1,
1000,0,connected=+inf,connected=+inf,0,1,
1000,0,connected=+inf,connected=-inf,0,1,
1000,0,connected=+inf,connected=nan,0,1,
1000,0,connected=-inf,10,0,1,
1000,0,connected=-inf,-10,0,1,
1000,0,connected=-inf,0,0,1,
1000,0,connected=-inf,1,0,1,
1000,0,connected=-inf,0.001,0,1,
1000,0,connected=-inf,-0.001,0,1,
1000,0,connected=-inf,unconnected,0,1,
1000,0,connected=-inf,unconnected=5,0,1,
1000,0,connected=-inf,unconnected=nan,0,1,
1000,0,connected=-inf,unconnected=-inf,0,1,
1000,0,connected=-inf,connected=+inf,0,1,
1000,0,connected=-inf,connected=-inf,0,1,
1000,0,connected=-inf,connected=nan,0,1,
1000,0,connected=nan,10,0,1,
1000,0,connected=nan,-10,0,1,
1000,0,connected=nan,0,0,1,
1000,0,connected=nan,1,0,1,
1000,0,connected=nan,0.001,0,1,
1000,0,connected=nan,-0.001,0,1,
1000,0,connected=nan,unconnected,0,1,
1000,0,connected=nan,unconnected=5,0,1,
1000,0,connected=nan,unconnected=nan,0,1,
1000,0,connected=nan,unconnected=-inf,0,1,
1000,0,connected=nan,connected=+inf,0,1,
1000,0,connected=nan,connected=-inf,0,1,
1000,0,connected=nan,connected=nan,0,1,
1000,0,10,10,0,2,
1000,0,-10,-10,0,2,
1000,0,0,0,0,2,
1000,0,1,1,0,2,
1000,0,0.001,0.001,0,2,
1000,0,-0.001,-0.001,0,2,
1000,0,unconnected,unconnected,0,2,
1000,0,unconnected=5,unconnected=5,0,2,
1000,0,unconnected=nan,unconnected=nan,0,2,
1000,0,unconnected=-inf,unconnected=-inf,0,2,
1000,0,connected=+inf,connected=+inf,0,2,
1000,0,connected=-inf,connected=-inf,0,2,
1000,0,connected=nan,connected=nan,0,2,
1000,0,10,10,0,2,
1000,0,10,-10,0,2,
1000,0,10,0,0,2,
1000,0,10,1,0,2,
1000,0,10,0.001,0,2,
1000,0,10,-0.001,0,2,
1000,0,10,unconnected,0,2,
1000,0,10,unconnected=5,0,2,
1000,0,10,unconnected=nan,0,2,
1000,0,10,unconnected=-inf,0,2,
1000,0,10,connected=+inf,0,2,
1000,0,10,connected=-inf,0,2,
1000,0,10,connected=nan,0,2,
1000,0,-10,10,0,2,
1000,0,-10,-10,0,2,
1000,0,-10,0,0,2,
1000,0,-10,1,0,2,
1000,0,-10,0.001,0,2,
1000,0,-10,-0.001,0,2,
1000,0,-10,unconnected,0,2,
1000,0,-10,unconnected=5,0,2,
1000,0,-10,unconnected=nan,0,2,
1000,0,-10,unconnected=-inf,0,2,
1000,0,-10,connected=+inf,0,2,
1000,0,-10,connected=-inf,0,2,
1000,0,-10,connected=nan,0,2,
1000,0,0,10,0,2,
1000,0,0,-10,0,2,
1000,0,0,0,0,2,
1000,0,0,1,0,2,
1000,0,0,0.001,0,2,
1000,0,0,-0.001,0,2,
1000,0,0,unconnected,0,2,
1000,0,0,unconnected=5,0,2,
1000,0,0,unconnected=nan,0,2,
1000,0,0,unconnected=-inf,0,2,
1000,0,0,connected=+inf,0,2,
1000,0,0,connected=-inf,0,2,
1000,0,0,connected=nan,0,2,
1000,0,1,10,0,2,
1000,0,1,-10,0,2,
1000,0,1,0,0,2,
1000,0,1,1,0,2,
1000,0,1,0.001,0,2,
1000,0,1,-0.001,0,2,
1000,0,1,unconnected,0,2,
1000,0,1,unconnected=5,0,2,
1000,0,1,unconnected=nan,0,2,
1000,0,1,unconnected=-inf,0,2,
1000,0,1,connected=+inf,0,2,
1000,0,1,connected=-inf,0,2,
1000,0,1,connected=nan,0,2,
1000,0,0.001,10,0,2,
1000,0,0.001,-10,0,2,
1000,0,0.001,0,0,2,
1000,0,0.001,1,0,2,
1000,0,0.001,0.001,0,2,
1000,0,0.001,-0.001,0,2,
1000,0,0.001,unconnected,0,2,
1000,0,0.001,unconnected=5,0,2,
1000,0,0.001,unconnected=nan,0,2,
1000,0,0.001,unconnected=-inf,0,2,
1000,0,0.001,connected=+inf,0,2,
1000,0,0.001,connected=-inf,0,2,
1000,0,0.001,connected=nan,0,2,
1000,0,-0.001,10,0,2,
1000,0,-0.001,-10,0,2,
1000,0,-0.001,0,0,2,
1000,0,-0.001,1,0,2,
1000,0,-0.001,0.001,0,2,
1000,0,-0.001,-0.001,0,2,
1000,0,-0.001,unconnected,0,2,
1000,0,-0.001,unconnected=5,0,2,
1000,0,-0.001,unconnected=nan,0,2,
1000,0,-0.001,unconnected=-inf,0,2,
1000,0,-0.001,connected=+inf,0,2,
1000,0,-0.001,connected=-inf,0,2,
1000,0,-0.001,connected=nan,0,2,
1000,0,unconnected,10,0,2,
1000,0,unconnected,-10,0,2,
1000,0,unconnected,0,0,2,
1000,0,unconnected,1,0,2,
1000,0,unconnected,0.001,0,2,
1000,0,unconnected,-0.001,0,2,
1000,0,unconnected,unconnected,0,2,
1000,0,unconnected,unconnected=5,0,2,
1000,0,unconnected,unconnected=nan,0,2,
1000,0,unconnected,unconnected=-inf,0,2,
1000,0,unconnected,connected=+inf,0,2,
1000,0,unconnected,connected=-inf,0,2,
1000,0,unconnected,connected=nan,0,2,
1000,0,unconnected=5,10,0,2,
1000,0,unconnected=5,-10,0,2,
1000,0,unconnected=5,0,0,2,
1000,0,unconnected=5,1,0,2,
1000,0,unconnected=5,0.001,0,2,
1000,0,unconnected=5,-0.001,0,2,
1000,0,unconnected=5,unconnected,0,2,
1000,0,unconnected=5,unconnected=5,0,2,
1000,0,unconnected=5,unconnected=nan,0,2,
1000,0,unconnected=5,unconnected=-inf,0,2,
1000,0,unconnected=5,connected=+inf,0,2,
1000,0,unconnected=5,connected=-inf,0,2,
1000,0,unconnected=5,connected=nan,0,2,
1000,0,unconnected=nan,10,0,2,
1000,0,unconnected=nan,-10,0,2,
1000,0,unconnected=nan,0,0,2,
1000,0,unconnected=nan,1,0,2,
1000,0,unconnected=nan,0.001,0,2,
1000,0,unconnected=nan,-0.001,0,2,
1000,0,unconnected=nan,unconnected,0,2,
1000,0,unconnected=nan,unconnected=5,0,2,
1000,0,unconnected=nan,unconnected=nan,0,2,
1000,0,unconnected=nan,unconnected=-inf,0,2,
1000,0,unconnected=nan,connected=+inf,0,2,
1000,0,unconnected=nan,connected=-inf,0,2,
1000,0,unconnected=nan,connected=nan,0,2,
1000,0,unconnected=-inf,10,0,2,
1000,0,unconnected=-inf,-10,0,2,
1000,0,unconnected=-inf,0,0,2,
1000,0,unconnected=-inf,1,0,2,
1000,0,unconnected=-inf,0.001,0,2,
1000,0,unconnected=-inf,-0.001,0,2,
1000,0,unconnected=-inf,unconnected,0,2,
1000,0,unconnected=-inf,unconnected=5,0,2,
1000,0,unconnected=-inf,unconnected=nan,0,2,
1000,0,unconnected=-inf,unconnected=-inf,0,2,
1000,0,unconnected=-inf,connected=+inf,0,2,
1000,0,unconnected=-inf,connected=-inf,0,2,
1000,0,unconnected=-inf,connected=nan,0,2,
1000,0,connected=+inf,10,0,2,
1000,0,connected=+inf,-10,0,2,
1000,0,connected=+inf,0,0,2,
1000,0,connected=+inf,1,0,2,
1000,0,connected=+inf,0.001,0,2,
1000,0,connected=+inf,-0.001,0,2,
1000,0,connected=+inf,unconnected,0,2,
1000,0,connected=+inf,unconnected=5,0,2,
1000,0,connected=+inf,unconnected=nan,0,2,
1000,0,connected=+inf,unconnected=-inf,0,2,
1000,0,connected=+inf,connected=+inf,0,2,
1000,0,connected=+inf,connected=-inf,0,2,
1000,0,connected=+inf,connected=nan,0,2,
1000,0,connected=-inf,10,0,2,
1000,0,connected=-inf,-10,0,2,
1000,0,connected=-inf,0,0,2,
1000,0,connected=-inf,1,0,2,
1000,0,connected=-inf,0.001,0,2,
1000,0,connected=-inf,-0.001,0,2,
1000,0,connected=-inf,unconnected,0,2,
1000,0,connected=-inf,unconnected=5,0,2,
1000,0,connected=-inf,unconnected=nan,0,2,
1000,0,connected=-inf,unconnected=-inf,0,2,
1000,0,connected=-inf,connected=+inf,0,2,
1000,0,connected=-inf,connected=-inf,0,2,
1000,0,connected=-inf,connected=nan,0,2,
1000,0,connected=nan,10,0,2,
1000,0,connected=nan,-10,0,2,
1000,0,connected=nan,0,0,2,
1000,0,connected=nan,1,0,2,
1000,0,connected=nan,0.001,0,2,
1000,0,connected=nan,-0.001,0,2,
1000,0,connected=nan,unconnected,0,2,
1000,0,connected=nan,unconnected=5,0,2,
1000,0,connected=nan,unconnected=nan,0,2,
1000,0,connected=nan,unconnected=-inf,0,2,
1000,0,connected=nan,connected=+inf,0,2,
1000,0,connected=nan,connected=-inf,0,2,
1000,0,connected=nan,connected=nan,0,2,
1000,0,10,10,0,3,
1000,0,-10,-10,0,3,
1000,0,0,0,0,3,
1000,0,1,1,0,3,
1000,0,0.001,0.001,0,3,
1000,0,-0.001,-0.001,0,3,
1000,0,unconnected,unconnected,0,3,
1000,0,unconnected=5,unconnected=5,0,3,
1000,0,unconnected=nan,unconnected=nan,0,3,
1000,0,unconnected=-inf,unconnected=-inf,0,3,
1000,0,connected=+inf,connected=+inf,0,3,
1000,0,connected=-inf,connected=-inf,0,3,
1000,0,connected=nan,connected=nan,0,3,
1000,0,10,10,0,3,
1000,0,10,-10,0,3,
1000,0,10,0,0,3,
1000,0,10,1,0,3,
1000,0,10,0.001,0,3,
1000,0,10,-0.001,0,3,
1000,0,10,unconnected,0,3,
1000,0,10,unconnected=5,0,3,
1000,0,10,unconnected=nan,0,3,
1000,0,10,unconnected=-inf,0,3,
1000,0,10,connected=+inf,0,3,
1000,0,10,connected=-inf,0,3,
1000,0,10,connected=nan,0,3,
1000,0,-10,10,0,3,
1000,0,-10,-10,0,3,
1000,0,-10,0,0,3,
1000,0,-10,1,0,3,
1000,0,-10,0.001,0,3,
1000,0,-10,-0.001,0,3,
1000,0,-10,unconnected,0,3,
1000,0,-10,unconnected=5,0,3,
1000,0,-10,unconnected=nan,0,3,
1000,0,-10,unconnected=-inf,0,3,
1000,0,-10,connected=+inf,0,3,
1000,0,-10,connected=-inf,0,3,
1000,0,-10,connected=nan,0,3,
1000,0,0,10,0,3,
1000,0,0,-10,0,3,
1000,0,0,0,0,3,
1000,0,0,1,0,3,
1000,0,0,0.001,0,3,
1000,0,0,-0.001,0,3,
1000,0,0,unconnected,0,3,
1000,0,0,unconnected=5,0,3,
1000,0,0,unconnected=nan,0,3,
1000,0,0,unconnected=-inf,0,3,
1000,0,0,connected=+inf,0,3,
1000,0,0,connected=-inf,0,3,
1000,0,0,connected=nan,0,3,
1000,0,1,10,0,3,
1000,0,1,-10,0,3,
1000,0,1,0,0,3,
1000,0,1,1,0,3,
1000,0,1,0.001,0,3,
1000,0,1,-0.001,0,3,
1000,0,1,unconnected,0,3,
1000,0,1,unconnected=5,0,3,
1000,0,1,unconnected=nan,0,3,
1000,0,1,unconnected=-inf,0,3,
1000,0,1,connected=+inf,0,3,
1000,0,1,connected=-inf,0,3,
1000,0,1,connected=nan,0,3,
1000,0,0.001,10,0,3,
1000,0,0.001,-10,0,3,
1000,0,0.001,0,0,3,
1000,0,0.001,1,0,3,
1000,0,0.001,0.001,0,3,
1000,0,0.001,-0.001,0,3,
1000,0,0.001,unconnected,0,3,
1000,0,0.001,unconnected=5,0,3,
1000,0,0.001,unconnected=nan,0,3,
1000,0,0.001,unconnected=-inf,0,3,
1000,0,0.001,connected=+inf,0,3,
1000,0,0.001,connected=-inf,0,3,
1000,0,0.001,connected=nan,0,3,
1000,0,-0.001,10,0,3,
1000,0,-0.001,-10,0,3,
1000,0,-0.001,0,0,3,
1000,0,-0.001,1,0,3,
1000,0,-0.001,0.001,0,3,
1000,0,-0.001,-0.001,0,3,
1000,0,-0.001,unconnected,0,3,
1000,0,-0.001,unconnected=5,0,3,
1000,0,-0.001,unconnected=nan,0,3,
1000,0,-0.001,unconnected=-inf,0,3,
1000,0,-0.001,connected=+inf,0,3,
1000,0,-0.001,connected=-inf,0,3,
1000,0,-0.001,connected=nan,0,3,
1000,0,unconnected,10,0,3,
1000,0,unconnected,-10,0,3,
1000,0,unconnected,0,0,3,
1000,0,unconnected,1,0,3,
1000,0,unconnected,0.001,0,3,
1000,0,unconnected,-0.001,0,3,
1000,0,unconnected,unconnected,0,3,
1000,0,unconnected,unconnected=5,0,3,
1000,0,unconnected,unconnected=nan,0,3,
1000,0,unconnected,unconnected=-inf,0,3,
1000,0,unconnected,connected=+inf,0,3,
1000,0,unconnected,connected=-inf,0,3,
1000,0,unconnected,connected=nan,0,3,
1000,0,unconnected=5,10,0,3,
1000,0,unconnected=5,-10,0,3,
1000,0,unconnected=5,0,0,3,
1000,0,unconnected=5,1,0,3,
1000,0,unconnected=5,0.001,0,3,
1000,0,unconnected=5,-0.001,0,3,
1000,0,unconnected=5,unconnected,0,3,
1000,0,unconnected=5,unconnected=5,0,3,
1000,0,unconnected=5,unconnected=nan,0,3,
1000,0,unconnected=5,unconnected=-inf,0,3,
1000,0,unconnected=5,connected=+inf,0,3,
1000,0,unconnected=5,connected=-inf,0,3,
1000,0,unconnected=5,connected=nan,0,3,
1000,0,unconnected=nan,10,0,3,
1000,0,unconnected=nan,-10,0,3,
1000,0,unconnected=nan,0,0,3,
1000,0,unconnected=nan,1,0,3,
1000,0,unconnected=nan,0.001,0,3,
1000,0,unconnected=nan,-0.001,0,3,
1000,0,unconnected=nan,unconnected,0,3,
1000,0,unconnected=nan,unconnected=5,0,3,
1000,0,unconnected=nan,unconnected=nan,0,3,
1000,0,unconnected=nan,unconnected=-inf,0,3,
1000,0,unconnected=nan,connected=+inf,0,3,
1000,0,unconnected=nan,connected=-inf,0,3,
1000,0,unconnected=nan,connected=nan,0,3,
1000,0,unconnected=-inf,10,0,3,
1000,0,unconnected=-inf,-10,0,3,
1000,0,unconnected=-inf,0,0,3,
1000,0,unconnected=-inf,1,0,3,
1000,0,unconnected=-inf,0.001,0,3,
1000,0,unconnected=-inf,-0.001,0,3,
1000,0,unconnected=-inf,unconnected,0,3,
1000,0,unconnected=-inf,unconnected=5,0,3,
1000,0,unconnected=-inf,unconnected=nan,0,3,
1000,0,unconnected=-inf,unconnected=-inf,0,3,
1000,0,unconnected=-inf,connected=+inf,0,3,
1000,0,unconnected=-inf,connected=-inf,0,3,
1000,0,unconnected=-inf,connected=nan,0,3,
1000,0,connected=+inf,10,0,3,
1000,0,connected=+inf,-10,0,3,
1000,0,connected=+inf,0,0,3,
1000,0,connected=+inf,1,0,3,
1000,0,connected=+inf,0.001,0,3,
1000,0,connected=+inf,-0.001,0,3,
1000,0,connected=+inf,unconnected,0,3,
1000,0,connected=+inf,unconnected=5,0,3,
1000,0,connected=+inf,unconnected=nan,0,3,
1000,0,connected=+inf,unconnected=-inf,0,3,
1000,0,connected=+inf,connected=+inf,0,3,
1000,0,connected=+inf,connected=-inf,0,3,
1000,0,connected=+inf,connected=nan,0,3,
1000,0,connected=-inf,10,0,3,
1000,0,connected=-inf,-10,0,3,
1000,0,connected=-inf,0,0,3,
1000,0,connected=-inf,1,0,3,
1000,0,connected=-inf,0.001,0,3,
1000,0,connected=-inf,-0.001,0,3,
1000,0,connected=-inf,unconnected,0,3,
1000,0,connected=-inf,unconnected=5,0,3,
1000,0,connected=-inf,unconnected=nan,0,3,
1000,0,connected=-inf,unconnected=-inf,0,3,
1000,0,connected=-inf,connected=+inf,0,3,
1000,0,connected=-inf,connected=-inf,0,3,
1000,0,connected=-inf,connected=nan,0,3,
1000,0,connected=nan,10,0,3,
1000,0,connected=nan,-10,0,3,
1000,0,connected=nan,0,0,3,
1000,0,connected=nan,1,0,3,
1000,0,connected=nan,0.001,0,3,
1000,0,connected=nan,-0.001,0,3,
1000,0,connected=nan,unconnected,0,3,
1000,0,connected=nan,unconnected=5,0,3,
1000,0,connected=nan,unconnected=nan,0,3,
1000,0,connected=nan,unconnected=-inf,0,3,
1000,0,connected=nan,connected=+inf,0,3,
1000,0,connected=nan,connected=-inf,0,3,
1000,0,connected=nan,connected=nan,0,3,
1000,1,10,10,1,0,
1000,1,-10,-10,1,0,
1000,1,0,0,1,0,
1000,1,1,1,1,0,
1000,1,0.001,0.001,1,0,
1000,1,-0.001,-0.001,1,0,
1000,1,unconnected,unconnected,1,0,
1000,1,unconnected=5,unconnected=5,1,0,
1000,1,unconnected=nan,unconnected=nan,1,0,
1000,1,unconnected=-inf,unconnected=-inf,1,0,
1000,1,connected=+inf,connected=+inf,1,0,
1000,1,connected=-inf,connected=-inf,1,0,
1000,1,connected=nan,connected=nan,1,0,
1000,1,10,10,1,0,
1000,1,10,-10,1,0,
1000,1,10,0,1,0,
1000,1,10,1,1,0,
1000,1,10,0.001,1,0,
1000,1,10,-0.001,1,0,
1000,1,10,unconnected,1,0,
1000,1,10,unconnected=5,1,0,
1000,1,10,unconnected=nan,1,0,
1000,1,10,unconnected=-inf,1,0,
1000,1,10,connected=+inf,1,0,
1000,1,10,connected=-inf,1,0,
1000,1,10,connected=nan,1,0,
1000,1,-10,10,1,0,
1000,1,-10,-10,1,0,
1000,1,-10,0,1,0,
1000,1,-10,1,1,0,
1000,1,-10,0.001,1,0,
1000,1,-10,-0.001,1,0,
1000,1,-10,unconnected,1,0,
1000,1,-10,unconnected=5,1,0,
1000,1,-10,unconnected=nan,1,0,
1000,1,-10,unconnected=-inf,1,0,
1000,1,-10,connected=+inf,1,0,
1000,1,-10,connected=-inf,1,0,
1000,1,-10,connected=nan,1,0,
1000,1,0,10,1,0,
1000,1,0,-10,1,0,
1000,1,0,0,1,0,
1000,1,0,1,1,0,
1000,1,0,0.001,1,0,
1000,1,0,-0.001,1,0,
1000,1,0,unconnected,1,0,
1000,1,0,unconnected=5,1,0,
1000,1,0,unconnected=nan,1,0,
1000,1,0,unconnected=-inf,1,0,
1000,1,0,connected=+inf,1,0,
1000,1,0,connected=-inf,1,0,
1000,1,0,connected=nan,1,0,
1000,1,1,10,1,0,
1000,1,1,-10,1,0,
1000,1,1,0,1,0,
1000,1,1,1,1,0,
1000,1,1,0.001,1,0,
1000,1,1,-0.001,1,0,
1000,1,1,unconnected,1,0,
1000,1,1,unconnected=5,1,0,
1000,1,1,unconnected=nan,1,0,
1000,1,1,unconnected=-inf,1,0,
1000,1,1,connected=+inf,1,0,
1000,1,1,connected=-inf,1,0,
1000,1,1,connected=nan,1,0,
1000,1,0.001,10,1,0,
1000,1,0.001,-10,1,0,
1000,1,0.001,0,1,0,
1000,1,0.001,1,1,0,
1000,1,0.001,0.001,1,0,
1000,1,0.001,-0.001,1,0,
1000,1,0.001,unconnected,1,0,
1000,1,0.001,unconnected=5,1,0,
1000,1,0.001,unconnected=nan,1,0,
1000,1,0.001,unconnected=-inf,1,0,
1000,1,0.001,connected=+inf,1,0,
1000,1,0.001,connected=-inf,1,0,
1000,1,0.001,connected=nan,1,0,
1000,1,-0.001,10,1,0,
1000,1,-0.001,-10,1,0,
1000,1,-0.001,0,1,0,
1000,1,-0.001,1,1,0,
1000,1,-0.001,0.001,1,0,
1000,1,-0.001,-0.001,1,0,
1000,1,-0.001,unconnected,1,0,
1000,1,-0.001,unconnected=5,1,0,
1000,1,-0.001,unconnected=nan,1,0,
1000,1,-0.001,unconnected=-inf,1,0,
1000,1,-0.001,connected=+inf,1,0,
1000,1,-0.001,connected=-inf,1,0,
1000,1,-0.001,connected=nan,1,0,
1000,1,unconnected,10,1,0,
1000,1,unconnected,-10,1,0,
1000,1,unconnected,0,1,0,
1000,1,unconnected,1,1,0,
1000,1,unconnected,0.001,1,0,
1000,1,unconnected,-0.001,1,0,
1000,1,unconnected,unconnected,1,0,
1000,1,unconnected,unconnected=5,1,0,
1000,1,unconnected,unconnected=nan,1,0,
1000,1,unconnected,unconnected=-inf,1,0,
1000,1,unconnected,connected=+inf,1,0,
1000,1,unconnected,connected=-inf,1,0,
1000,1,unconnected,connected=nan,1,0,
1000,1,unconnected=5,10,1,0,
1000,1,unconnected=5,-10,1,0,
1000,1,unconnected=5,0,1,0,
1000,1,unconnected=5,1,1,0,
1000,1,unconnected=5,0.001,1,0,
1000,1,unconnected=5,-0.001,1,0,
1000,1,unconnected=5,unconnected,1,0,
1000,1,unconnected=5,unconnected=5,1,0,
1000,1,unconnected=5,unconnected=nan,1,0,
1000,1,unconnected=5,unconnected=-inf,1,0,
1000,1,unconnected=5,connected=+inf,1,0,
1000,1,unconnected=5,connected=-inf,1,0,
1000,1,unconnected=5,connected=nan,1,0,
1000,1,unconnected=nan,10,1,0,
1000,1,unconnected=nan,-10,1,0,
1000,1,unconnected=nan,0,1,0,
1000,1,unconnected=nan,1,1,0,
1000,1,unconnected=nan,0.001,1,0,
1000,1,unconnected=nan,-0.001,1,0,
1000,1,unconnected=nan,unconnected,1,0,
1000,1,unconnected=nan,unconnected=5,1,0,
1000,1,unconnected=nan,unconnected=nan,1,0,
1000,1,unconnected=nan,unconnected=-inf,1,0,
1000,1,unconnected=nan,connected=+inf,1,0,
1000,1,unconnected=nan,connected=-inf,1,0,
1000,1,unconnected=nan,connected=nan,1,0,
1000,1,unconnected=-inf,10,1,0,
1000,1,unconnected=-inf,-10,1,0,
1000,1,unconnected=-inf,0,1,0,
1000,1,unconnected=-inf,1,1,0,
1000,1,unconnected=-inf,0.001,1,0,
1000,1,unconnected=-inf,-0.001,1,0,
1000,1,unconnected=-inf,unconnected,1,0,
1000,1,unconnected=-inf,unconnected=5,1,0,
1000,1,unconnected=-inf,unconnected=nan,1,0,
1000,1,unconnected=-inf,unconnected=-inf,1,0,
1000,1,unconnected=-inf,connected=+inf,1,0,
1000,1,unconnected=-inf,connected=-inf,1,0,
1000,1,unconnected=-inf,connected=nan,1,0,
1000,1,connected=+inf,10,1,0,
1000,1,connected=+inf,-10,1,0,
1000,1,connected=+inf,0,1,0,
1000,1,connected=+inf,1,1,0,
1000,1,connected=+inf,0.001,1,0,
1000,1,connected=+inf,-0.001,1,0,
1000,1,connected=+inf,unconnected,1,0,
1000,1,connected=+inf,unconnected=5,1,0,
1000,1,connected=+inf,unconnected=nan,1,0,
1000,1,connected=+inf,unconnected=-inf,1,0,
1000,1,connected=+inf,connected=+inf,1,0,
1000,1,connected=+inf,connected=-inf,1,0,
1000,1,connected=+inf,connected=nan,1,0,
1000,1,connected=-inf,10,1,0,
1000,1,connected=-inf,-10,1,0,
1000,1,connected=-inf,0,1,0,
1000,1,connected=-inf,1,1,0,
1000,1,connected=-inf,0.001,1,0,
1000,1,connected=-inf,-0.001,1,0,
1000,1,connected=-inf,unconnected,1,0,
1000,1,connected=-inf,unconnected=5,1,0,
1000,1,connected=-inf,unconnected=nan,1,0,
1000,1,connected=-inf,unconnected=-inf,1,0,
1000,1,connected=-inf,connected=+inf,1,0,
1000,1,connected=-inf,connected=-inf,1,0,
1000,1,connected=-inf,connected=nan,1,0,
1000,1,connected=nan,10,1,0,
1000,1,connected=nan,-10,1,0,
1000,1,connected=nan,0,1,0,
1000,1,connected=nan,1,1,0,
1000,1,connected=nan,0.001,1,0,
1000,1,connected=nan,-0.001,1,0,
1000,1,connected=nan,unconnected,1,0,
1000,1,connected=nan,unconnected=5,1,0,
1000,1,connected=nan,unconnected=nan,1,0,
1000,1,connected=nan,unconnected=-inf,1,0,
1000,1,connected=nan,connected=+inf,1,0,
1000,1,connected=nan,connected=-inf,1,0,
1000,1,connected=nan,connected=nan,1,0,
1000,1,10,10,1,1,
1000,1,-10,-10,1,1,
1000,1,0,0,1,1,
1000,1,1,1,1,1,
1000,1,0.001,0.001,1,1,
1000,1,-0.001,-0.001,1,1,
1000,1,unconnected,unconnected,1,1,
1000,1,unconnected=5,unconnected=5,1,1,
1000,1,unconnected=nan,unconnected=nan,1,1,
1000,1,unconnected=-inf,unconnected=-inf,1,1,
1000,1,connected=+inf,connected=+inf,1,1,
1000,1,connected=-inf,connected=-inf,1,1,
1000,1,connected=nan,connected=nan,1,1,
1000,1,10,10,1,1,
1000,1,10,-10,1,1,
1000,1,10,0,1,1,
1000,1,10,1,1,1,
1000,1,10,0.001,1,1,
1000,1,10,-0.001,1,1,
1000,1,10,unconnected,1,1,
1000,1,10,unconnected=5,1,1,
1000,1,10,unconnected=nan,1,1,
1000,1,10,unconnected=-inf,1,1,
1000,1,10,connected=+inf,1,1,
1000,1,10,connected=-inf,1,1,
1000,1,10,connected=nan,1,1,
1000,1,-10,10,1,1,
1000,1,-10,-10,1,1,
1000,1,-10,0,1,1,
1000,1,-10,1,1,1,
1000,1,-10,0.001,1,1,
1000,1,-10,-0.001,1,1,
1000,1,-10,unconnected,1,1,
1000,1,-10,unconnected=5,1,1,
1000,1,-10,unconnected=nan,1,1,
1000,1,-10,unconnected=-inf,1,1,
1000,1,-10,connected=+inf,1,1,
1000,1,-10,connected=-inf,1,1,
1000,1,-10,connected=nan,1,1,
1000,1,0,10,1,1,
1000,1,0,-10,1,1,
1000,1,0,0,1,1,
1000,1,0,1,1,1,
1000,1,0,0.001,1,1,
1000,1,0,-0.001,1,1,
1000,1,0,unconnected,1,1,
1000,1,0,unconnected=5,1,1,
1000,1,0,unconnected=nan,1,1,
1000,1,0,unconnected=-inf,1,1,
1000,1,0,connected=+inf,1,1,
1000,1,0,connected=-inf,1,1,
1000,1,0,connected=nan,1,1,
1000,1,1,10,1,1,
1000,1,1,-10,1,1,
1000,1,1,0,1,1,
1000,1,1,1,1,1,
1000,1,1,0.001,1,1,
1000,1,1,-0.001,1,1,
1000,1,1,unconnected,1,1,
1000,1,1,unconnected=5,1,1,
1000,1,1,unconnected=nan,1,1,
1000,1,1,unconnected=-inf,1,1,
1000,1,1,connected=+inf,1,1,
1000,1,1,connected=-inf,1,1,
1000,1,1,connected=nan,1,1,
1000,1,0.001,10,1,1,
1000,1,0.001,-10,1,1,
1000,1,0.001,0,1,1,
1000,1,0.001,1,1,1,
1000,1,0.001,0.001,1,1,
1000,1,0.001,-0.001,1,1,
1000,1,0.001,unconnected,1,1,
1000,1,0.001,unconnected=5,1,1,
1000,1,0.001,unconnected=nan,1,1,
1000,1,0.001,unconnected=-inf,1,1,
1000,1,0.001,connected=+inf,1,1,
1000,1,0.001,connected=-inf,1,1,
1000,1,0.001,connected=nan,1,1,
1000,1,-0.001,10,1,1,
1000,1,-0.001,-10,1,1,
1000,1,-0.001,0,1,1,
1000,1,-0.001,1,1,1,
1000,1,-0.001,0.001,1,1,
1000,1,-0.001,-0.001,1,1,
1000,1,-0.001,unconnected,1,1,
1000,1,-0.001,unconnected=5,1,1,
1000,1,-0.001,unconnected=nan,1,1,
1000,1,-0.001,unconnected=-inf,1,1,
1000,1,-0.001,connected=+inf,1,1,
1000,1,-0.001,connected=-inf,1,1,
1000,1,-0.001,connected=nan,1,1,
1000,1,unconnected,10,1,1,
1000,1,unconnected,-10,1,1,
1000,1,unconnected,0,1,1,
1000,1,unconnected,1,1,1,
1000,1,unconnected,0.001,1,1,
1000,1,unconnected,-0.001,1,1,
1000,1,unconnected,unconnected,1,1,
1000,1,unconnected,unconnected=5,1,1,
1000,1,unconnected,unconnected=nan,1,1,
1000,1,unconnected,unconnected=-inf,1,1,
1000,1,unconnected,connected=+inf,1,1,
1000,1,unconnected,connected=-inf,1,1,
1000,1,unconnected,connected=nan,1,1,
1000,1,unconnected=5,10,1,1,
1000,1,unconnected=5,-10,1,1,
1000,1,unconnected=5,0,1,1,
1000,1,unconnected=5,1,1,1,
1000,1,unconnected=5,0.001,1,1,
1000,1,unconnected=5,-0.001,1,1,
1000,1,unconnected=5,unconnected,1,1,
1000,1,unconnected=5,unconnected=5,1,1,
1000,1,unconnected=5,unconnected=nan,1,1,
1000,1,unconnected=5,unconnected=-inf,1,1,
1000,1,unconnected=5,connected=+inf,1,1,
1000,1,unconnected=5,connected=-inf,1,1,
1000,1,unconnected=5,connected=nan,1,1,
1000,1,unconnected=nan,10,1,1,
1000,1,unconnected=nan,-10,1,1,
1000,1,unconnected=nan,0,1,1,
1000,1,unconnected=nan,1,1,1,
1000,1,unconnected=nan,0.001,1,1,
1000,1,unconnected=nan,-0.001,1,1,
1000,1,unconnected=nan,unconnected,1,1,
1000,1,unconnected=nan,unconnected=5,1,1,
1000,1,unconnected=nan,unconnected=nan,1,1,
1000,1,unconnected=nan,unconnected=-inf,1,1,
1000,1,unconnected=nan,connected=+inf,1,1,
1000,1,unconnected=nan,connected=-inf,1,1,
1000,1,unconnected=nan,connected=nan,1,1,
1000,1,unconnected=-inf,10,1,1,
1000,1,unconnected=-inf,-10,1,1,
1000,1,unconnected=-inf,0,1,1,
1000,1,unconnected=-inf,1,1,1,
1000,1,unconnected=-inf,0.001,1,1,
1000,1,unconnected=-inf,-0.001,1,1,
1000,1,unconnected=-inf,unconnected,1,1,
1000,1,unconnected=-inf,unconnected=5,1,1,
1000,1,unconnected=-inf,unconnected=nan,1,1,
1000,1,unconnected=-inf,unconnected=-inf,1,1,
1000,1,unconnected=-inf,connected=+inf,1,1,
1000,1,unconnected=-inf,connected=-inf,1,1,
1000,1,unconnected=-inf,connected=nan,1,1,
1000,1,connected=+inf,10,1,1,
1000,1,connected=+inf,-10,1,1,
1000,1,connected=+inf,0,1,1,
1000,1,connected=+inf,1,1,1,
1000,1,connected=+inf,0.001,1,1,
1000,1,connected=+inf,-0.001,1,1,
1000,1,connected=+inf,unconnected,1,1,
1000,1,connected=+inf,unconnected=5,1,1,
1000,1,connected=+inf,unconnected=nan,1,1,
1000,1,connected=+inf,unconnected=-inf,1,1,
1000,1,connected=+inf,connected=+inf,1,1,
1000,1,connected=+inf,connected=-inf,1,1,
1000,1,connected=+inf,connected=nan,1,1,
1000,1,connected=-inf,10,1,1,
1000,1,connected=-inf,-10,1,1,
1000,1,connected=-inf,0,1,1,
1000,1,connected=-inf,1,1,1,
1000,1,connected=-inf,0.001,1,1,
1000,1,connected=-inf,-0.001,1,1,
1000,1,connected=-inf,unconnected,1,1,
1000,1,connected=-inf,unconnected=5,1,1,
1000,1,connected=-inf,unconnected=nan,1,1,
1000,1,connected=-inf,unconnected=-inf,1,1,
1000,1,connected=-inf,connected=+inf,1,1,
1000,1,connected=-inf,connected=-inf,1,1,
1000,1,connected=-inf,connected=nan,1,1,
1000,1,connected=nan,10,1,1,
1000,1,connected=nan,-10,1,1,
1000,1,connected=nan,0,1,1,
1000,1,connected=nan,1,1,1,
1000,1,connected=nan,0.001,1,1,
1000,1,connected=nan,-0.001,1,1,
1000,1,connected=nan,unconnected,1,1,
1000,1,connected=nan,unconnected=5,1,1,
1000,1,connected=nan,unconnected=nan,1,1,
1000,1,connected=nan,unconnected=-inf,1,1,
1000,1,connected=nan,connected=+inf,1,1,
1000,1,connected=nan,connected=-inf,1,1,
1000,1,connected=nan,connected=nan,1,1,
1000,1,10,10,1,2,
1000,1,-10,-10,1,2,
1000,1,0,0,1,2,
1000,1,1,1,1,2,
1000,1,0.001,0.001,1,2,
1000,1,-0.001,-0.001,1,2,
1000,1,unconnected,unconnected,1,2,
1000,1,unconnected=5,unconnected=5,1,2,
1000,1,unconnected=nan,unconnected=nan,1,2,
1000,1,unconnected=-inf,unconnected=-inf,1,2,
1000,1,connected=+inf,connected=+inf,1,2,
1000,1,connected=-inf,connected=-inf,1,2,
1000,1,connected=nan,connected=nan,1,2,
1000,1,10,10,1,2,
1000,1,10,-10,1,2,
1000,1,10,0,1,2,
1000,1,10,1,1,2,
1000,1,10,0.001,1,2,
1000,1,10,-0.001,1,2,
1000,1,10,unconnected,1,2,
1000,1,10,unconnected=5,1,2,
1000,1,10,unconnected=nan,1,2,
1000,1,10,unconnected=-inf,1,2,
1000,1,10,connected=+inf,1,2,
1000,1,10,connected=-inf,1,2,
1000,1,10,connected=nan,1,2,
1000,1,-10,10,1,2,
1000,1,-10,-10,1,2,
1000,1,-10,0,1,2,
1000,1,-10,1,1,2,
1000,1,-10,0.001,1,2,
1000,1,-10,-0.001,1,2,
1000,1,-10,unconnected,1,2,
1000,1,-10,unconnected=5,1,2,
1000,1,-10,unconnected=nan,1,2,
1000,1,-10,unconnected=-inf,1,2,
1000,1,-10,connected=+inf,1,2,
1000,1,-10,connected=-inf,1,2,
1000,1,-10,connected=nan,1,2,
1000,1,0,10,1,2,
1000,1,0,-10,1,2,
1000,1,0,0,1,2,
1000,1,0,1,1,2,
1000,1,0,0.001,1,2,
1000,1,0,-0.001,1,2,
1000,1,0,unconnected,1,2,
1000,1,0,unconnected=5,1,2,
1000,1,0,unconnected=nan,1,2,
1000,1,0,unconnected=-inf,1,2,
1000,1,0,connected=+inf,1,2,
1000,1,0,connected=-inf,1,2,
1000,1,0,connected=nan,1,2,
1000,1,1,10,1,2,
1000,1,1,-10,1,2,
1000,1,1,0,1,2,
1000,1,1,1,1,2,
1000,1,1,0.001,1,2,
1000,1,1,-0.001,1,2,
1000,1,1,unconnected,1,2,
1000,1,1,unconnected=5,1,2,
1000,1,1,unconnected=nan,1,2,
1000,1,1,unconnected=-inf,1,2,
1000,1,1,connected=+inf,1,2,
1000,1,1,connected=-inf,1,2,
1000,1,1,connected=nan,1,2,
1000,1,0.001,10,1,2,
1000,1,0.001,-10,1,2,
1000,1,0.001,0,1,2,
1000,1,0.001,1,1,2,
1000,1,0.001,0.001,1,2,
1000,1,0.001,-0.001,1,2,
1000,1,0.001,unconnected,1,2,
1000,1,0.001,unconnected=5,1,2,
1000,1,0.001,unconnected=nan,1,2,
1000,1,0.001,unconnected=-inf,1,2,
1000,1,0.001,connected=+inf,1,2,
1000,1,0.001,connected=-inf,1,2,
1000,1,0.001,connected=nan,1,2,
1000,1,-0.001,10,1,2,
1000,1,-0.001,-10,1,2,
1000,1,-0.001,0,1,2,
1000,1,-0.001,1,1,2,
1000,1,-0.001,0.001,1,2,
1000,1,-0.001,-0.001,1,2,
1000,1,-0.001,unconnected,1,2,
1000,1,-0.001,unconnected=5,1,2,
1000,1,-0.001,unconnected=nan,1,2,
1000,1,-0.001,unconnected=-inf,1,2,
1000,1,-0.001,connected=+inf,1,2,
1000,1,-0.001,connected=-inf,1,2,
1000,1,-0.001,connected=nan,1,2,
1000,1,unconnected,10,1,2,
1000,1,unconnected,-10,1,2,
1000,1,unconnected,0,1,2,
1000,1,unconnected,1,1,2,
1000,1,unconnected,0.001,1,2,
1000,1,unconnected,-0.001,1,2,
1000,1,unconnected,unconnected,1,2,
1000,1,unconnected,unconnected=5,1,2,
1000,1,unconnected,unconnected=nan,1,2,
1000,1,unconnected,unconnected=-inf,1,2,
1000,1,unconnected,connected=+inf,1,2,
1000,1,unconnected,connected=-inf,1,2,
1000,1,unconnected,connected=nan,1,2,
1000,1,unconnected=5,10,1,2,
1000,1,unconnected=5,-10,1,2,
1000,1,unconnected=5,0,1,2,
1000,1,unconnected=5,1,1,2,
1000,1,unconnected=5,0.001,1,2,
1000,1,unconnected=5,-0.001,1,2,
1000,1,unconnected=5,unconnected,1,2,
1000,1,unconnected=5,unconnected=5,1,2,
1000,1,unconnected=5,unconnected=nan,1,2,
1000,1,unconnected=5,unconnected=-inf,1,2,
1000,1,unconnected=5,connected=+inf,1,2,
1000,1,unconnected=5,connected=-inf,1,2,
1000,1,unconnected=5,connected=nan,1,2,
1000,1,unconnected=nan,10,1,2,
1000,1,unconnected=nan,-10,1,2,
1000,1,unconnected=nan,0,1,2,
1000,1,unconnected=nan,1,1,2,
1000,1,unconnected=nan,0.001,1,2,
1000,1,unconnected=nan,-0.001,1,2,
1000,1,unconnected=nan,unconnected,1,2,
1000,1,unconnected=nan,unconnected=5,1,2,
1000,1,unconnected=nan,unconnected=nan,1,2,
1000,1,unconnected=nan,unconnected=-inf,1,2,
1000,1,unconnected=nan,connected=+inf,1,2,
1000,1,unconnected=nan,connected=-inf,1,2,
1000,1,unconnected=nan,connected=nan,1,2,
1000,1,unconnected=-inf,10,1,2,
1000,1,unconnected=-inf,-10,1,2,
1000,1,unconnected=-inf,0,1,2,
1000,1,unconnected=-inf,1,1,2,
1000,1,unconnected=-inf,0.001,1,2,
1000,1,unconnected=-inf,-0.001,1,2,
1000,1,unconnected=-inf,unconnected,1,2,
1000,1,unconnected=-inf,unconnected=5,1,2,
1000,1,unconnected=-inf,unconnected=nan,1,2,
1000,1,unconnected=-inf,unconnected=-inf,1,2,
1000,1,unconnected=-inf,connected=+inf,1,2,
1000,1,unconnected=-inf,connected=-inf,1,2,
1000,1,unconnected=-inf,connected=nan,1,2,
1000,1,connected=+inf,10,1,2,
1000,1,connected=+inf,-10,1,2,
1000,1,connected=+inf,0,1,2,
1000,1,connected=+inf,1,1,2,
1000,1,connected=+inf,0.001,1,2,
1000,1,connected=+inf,-0.001,1,2,
1000,1,connected=+inf,unconnected,1,2,
1000,1,connected=+inf,unconnected=5,1,2,
1000,1,connected=+inf,unconnected=nan,1,2,
1000,1,connected=+inf,unconnected=-inf,1,2,
1000,1,connected=+inf,connected=+inf,1,2,
1000,1,connected=+inf,connected=-inf,1,2,
1000,1,connected=+inf,connected=nan,1,2,
1000,1,connected=-inf,10,1,2,
1000,1,connected=-inf,-10,1,2,
1000,1,connected=-inf,0,1,2,
1000,1,connected=-inf,1,1,2,
1000,1,connected=-inf,0.001,1,2,
1000,1,connected=-inf,-0.001,1,2,
1000,1,connected=-inf,unconnected,1,2,
1000,1,connected=-inf,unconnected=5,1,2,
1000,1,connected=-inf,unconnected=nan,1,2,
1000,1,connected=-inf,unconnected=-inf,1,2,
1000,1,connected=-inf,connected=+inf,1,2,
1000,1,connected=-inf,connected=-inf,1,2,
1000,1,connected=-inf,connected=nan,1,2,
1000,1,connected=nan,10,1,2,
1000,1,connected=nan,-10,1,2,
1000,1,connected=nan,0,1,2,
1000,1,connected=nan,1,1,2,
1000,1,connected=nan,0.001,1,2,
1000,1,connected=nan,-0.001,1,2,
1000,1,connected=nan,unconnected,1,2,
1000,1,connected=nan,unconnected=5,1,2,
1000,1,connected=nan,unconnected=nan,1,2,
1000,1,connected=nan,unconnected=-inf,1,2,
1000,1,connected=nan,connected=+inf,1,2,
1000,1,connected=nan,connected=-inf,1,2,
1000,1,connected=nan,connected=nan,1,2,
1000,1,10,10,1,3,
1000,1,-10,-10,1,3,
1000,1,0,0,1,3,
1000,1,1,1,1,3,
1000,1,0.001,0.001,1,3,
1000,1,-0.001,-0.001,1,3,
1000,1,unconnected,unconnected,1,3,
1000,1,unconnected=5,unconnected=5,1,3,
1000,1,unconnected=nan,unconnected=nan,1,3,
1000,1,unconnected=-inf,unconnected=-inf,1,3,
1000,1,connected=+inf,connected=+inf,1,3,
1000,1,connected=-inf,connected=-inf,1,3,
1000,1,connected=nan,connected=nan,1,3,
1000,1,10,10,1,3,
1000,1,10,-10,1,3,
1000,1,10,0,1,3,
1000,1,10,1,1,3,
1000,1,10,0.001,1,3,
1000,1,10,-0.001,1,3,
1000,1,10,unconnected,1,3,
1000,1,10,unconnected=5,1,3,
1000,1,10,unconnected=nan,1,3,
1000,1,10,unconnected=-inf,1,3,
1000,1,10,connected=+inf,1,3,
1000,1,10,connected=-inf,1,3,
1000,1,10,connected=nan,1,3,
1000,1,-10,10,1,3,
1000,1,-10,-10,1,3,
1000,1,-10,0,1,3,
1000,1,-10,1,1,3,
1000,1,-10,0.001,1,3,
1000,1,-10,-0.001,1,3,
1000,1,-10,unconnected,1,3,
1000,1,-10,unconnected=5,1,3,
1000,1,-10,unconnected=nan,1,3,
1000,1,-10,unconnected=-inf,1,3,
1000,1,-10,connected=+inf,1,3,
1000,1,-10,connected=-inf,1,3,
1000,1,-10,connected=nan,1,3,
1000,1,0,10,1,3,
1000,1,0,-10,1,3,
1000,1,0,0,1,3,
1000,1,0,1,1,3,
1000,1,0,0.001,1,3,
1000,1,0,-0.001,1,3,
1000,1,0,unconnected,1,3,
1000,1,0,unconnected=5,1,3,
1000,1,0,unconnected=nan,1,3,
1000,1,0,unconnected=-inf,1,3,
1000,1,0,connected=+inf,1,3,
1000,1,0,connected=-inf,1,3,
1000,1,0,connected=nan,1,3,
1000,1,1,10,1,3,
1000,1,1,-10,1,3,
1000,1,1,0,1,3,
1000,1,1,1,1,3,
1000,1,1,0.001,1,3,
1000,1,1,-0.001,1,3,
1000,1,1,unconnected,1,3,
1000,1,1,unconnected=5,1,3,
1000,1,1,unconnected=nan,1,3,
1000,1,1,unconnected=-inf,1,3,
1000,1,1,connected=+inf,1,3,
1000,1,1,connected=-inf,1,3,
1000,1,1,connected=nan,1,3,
1000,1,0.001,10,1,3,
1000,1,0.001,-10,1,3,
1000,1,0.001,0,1,3,
1000,1,0.001,1,1,3,
1000,1,0.001,0.001,1,3,
1000,1,0.001,-0.001,1,3,
1000,1,0.001,unconnected,1,3,
1000,1,0.001,unconnected=5,1,3,
1000,1,0.001,unconnected=nan,1,3,
1000,1,0.001,unconnected=-inf,1,3,
1000,1,0.001,connected=+inf,1,3,
1000,1,0.001,connected=-inf,1,3,
1000,1,0.001,connected=nan,1,3,
1000,1,-0.001,10,1,3,
1000,1,-0.001,-10,1,3,
1000,1,-0.001,0,1,3,
1000,1,-0.001,1,1,3,
1000,1,-0.001,0.001,1,3,
1000,1,-0.001,-0.001,1,3,
1000,1,-0.001,unconnected,1,3,
1000,1,-0.001,unconnected=5,1,3,
1000,1,-0.001,unconnected=nan,1,3,
1000,1,-0.001,unconnected=-inf,1,3,
1000,1,-0.001,connected=+inf,1,3,
1000,1,-0.001,connected=-inf,1,3,
1000,1,-0.001,connected=nan,1,3,
1000,1,unconnected,10,1,3,
1000,1,unconnected,-10,1,3,
1000,1,unconnected,0,1,3,
1000,1,unconnected,1,1,3,
1000,1,unconnected,0.001,1,3,
1000,1,unconnected,-0.001,1,3,
1000,1,unconnected,unconnected,1,3,
1000,1,unconnected,unconnected=5,1,3,
1000,1,unconnected,unconnected=nan,1,3,
1000,1,unconnected,unconnected=-inf,1,3,
1000,1,unconnected,connected=+inf,1,3,
1000,1,unconnected,connected=-inf,1,3,
1000,1,unconnected,connected=nan,1,3,
1000,1,unconnected=5,10,1,3,
1000,1,unconnected=5,-10,1,3,
1000,1,unconnected=5,0,1,3,
1000,1,unconnected=5,1,1,3,
1000,1,unconnected=5,0.001,1,3,
1000,1,unconnected=5,-0.001,1,3,
1000,1,unconnected=5,unconnected,1,3,
1000,1,unconnected=5,unconnected=5,1,3,
1000,1,unconnected=5,unconnected=nan,1,3,
1000,1,unconnected=5,unconnected=-inf,1,3,
1000,1,unconnected=5,connected=+inf,1,3,
1000,1,unconnected=5,connected=-inf,1,3,
1000,1,unconnected=5,connected=nan,1,3,
1000,1,unconnected=nan,10,1,3,
1000,1,unconnected=nan,-10,1,3,
1000,1,unconnected=nan,0,1,3,
1000,1,unconnected=nan,1,1,3,
1000,1,unconnected=nan,0.001,1,3,
1000,1,unconnected=nan,-0.001,1,3,
1000,1,unconnected=nan,unconnected,1,3,
1000,1,unconnected=nan,unconnected=5,1,3,
1000,1,unconnected=nan,unconnected=nan,1,3,
1000,1,unconnected=nan,unconnected=-inf,1,3,
1000,1,unconnected=nan,connected=+inf,1,3,
1000,1,unconnected=nan,connected=-inf,1,3,
1000,1,unconnected=nan,connected=nan,1,3,
1000,1,unconnected=-inf,10,1,3,
1000,1,unconnected=-inf,-10,1,3,
1000,1,unconnected=-inf,0,1,3,
1000,1,unconnected=-inf,1,1,3,
1000,1,unconnected=-inf,0.001,1,3,
1000,1,unconnected=-inf,-0.001,1,3,
1000,1,unconnected=-inf,unconnected,1,3,
1000,1,unconnected=-inf,unconnected=5,1,3,
1000,1,unconnected=-inf,unconnected=nan,1,3,
1000,1,unconnected=-inf,unconnected=-inf,1,3,
1000,1,unconnected=-inf,connected=+inf,1,3,
1000,1,unconnected=-inf,connected=-inf,1,3,
1000,1,unconnected=-inf,connected=nan,1,3,
1000,1,connected=+inf,10,1,3,
1000,1,connected=+inf,-10,1,3,
1000,1,connected=+inf,0,1,3,
1000,1,connected=+inf,1,1,3,
1000,1,connected=+inf,0.001,1,3,
1000,1,connected=+inf,-0.001,1,3,
1000,1,connected=+inf,unconnected,1,3,
1000,1,connected=+inf,unconnected=5,1,3,
1000,1,connected=+inf,unconnected=nan,1,3,
1000,1,connected=+inf,unconnected=-inf,1,3,
1000,1,connected=+inf,connected=+inf,1,3,
1000,1,connected=+inf,connected=-inf,1,3,
1000,1,connected=+inf,connected=nan,1,3,
1000,1,connected=-inf,10,1,3,
1000,1,connected=-inf,-10,1,3,
1000,1,connected=-inf,0,1,3,
1000,1,connected=-inf,1,1,3,
1000,1,connected=-inf,0.001,1,3,
1000,1,connected=-inf,-0.001,1,3,
1000,1,connected=-inf,unconnected,1,3,
1000,1,connected=-inf,unconnected=5,1,3,
1000,1,connected=-inf,unconnected=nan,1,3,
1000,1,connected=-inf,unconnected=-inf,1,3,
1000,1,connected=-inf,connected=+inf,1,3,
1000,1,connected=-inf,connected=-inf,1,3,
1000,1,connected=-inf,connected=nan,1,3,
1000,1,connected=nan,10,1,3,
1000,1,connected=nan,-10,1,3,
1000,1,connected=nan,0,1,3,
1000,1,connected=nan,1,1,3,
1000,1,connected=nan,0.001,1,3,
1000,1,connected=nan,-0.001,1,3,
1000,1,connected=nan,unconnected,1,3,
1000,1,connected=nan,unconnected=5,1,3,
1000,1,connected=nan,unconnected=nan,1,3,
1000,1,connected=nan,unconnected=-inf,1,3,
1000,1,connected=nan,connected=+inf,1,3,
1000,1,connected=nan,connected=-inf,1,3,
1000,1,connected=nan,connected=nan,1,3,
1000,0,10,10,1,0,
1000,0,-10,-10,1,0,
1000,0,0,0,1,0,
1000,0,1,1,1,0,
1000,0,0.001,0.001,1,0,
1000,0,-0.001,-0.001,1,0,
1000,0,unconnected,unconnected,1,0,
1000,0,unconnected=5,unconnected=5,1,0,
1000,0,unconnected=nan,unconnected=nan,1,0,
1000,0,unconnected=-inf,unconnected=-inf,1,0,
1000,0,connected=+inf,connected=+inf,1,0,
1000,0,connected=-inf,connected=-inf,1,0,
1000,0,connected=nan,connected=nan,1,0,
1000,0,10,10,1,0,
1000,0,10,-10,1,0,
1000,0,10,0,1,0,
1000,0,10,1,1,0,
1000,0,10,0.001,1,0,
1000,0,10,-0.001,1,0,
1000,0,10,unconnected,1,0,
1000,0,10,unconnected=5,1,0,
1000,0,10,unconnected=nan,1,0,
1000,0,10,unconnected=-inf,1,0,
1000,0,10,connected=+inf,1,0,
1000,0,10,connected=-inf,1,0,
1000,0,10,connected=nan,1,0,
1000,0,-10,10,1,0,
1000,0,-10,-10,1,0,
1000,0,-10,0,1,0,
1000,0,-10,1,1,0,
1000,0,-10,0.001,1,0,
1000,0,-10,-0.001,1,0,
1000,0,-10,unconnected,1,0,
1000,0,-10,unconnected=5,1,0,
1000,0,-10,unconnected=nan,1,0,
1000,0,-10,unconnected=-inf,1,0,
1000,0,-10,connected=+inf,1,0,
1000,0,-10,connected=-inf,1,0,
1000,0,-10,connected=nan,1,0,
1000,0,0,10,1,0,
1000,0,0,-10,1,0,
1000,0,0,0,1,0,
1000,0,0,1,1,0,
1000,0,0,0.001,1,0,
1000,0,0,-0.001,1,0,
1000,0,0,unconnected,1,0,
1000,0,0,unconnected=5,1,0,
1000,0,0,unconnected=nan,1,0,
1000,0,0,unconnected=-inf,1,0,
1000,0,0,connected=+inf,1,0,
1000,0,0,connected=-inf,1,0,
1000,0,0,connected=nan,1,0,
1000,0,1,10,1,0,
1000,0,1,-10,1,0,
1000,0,1,0,1,0,
1000,0,1,1,1,0,
1000,0,1,0.001,1,0,
1000,0,1,-0.001,1,0,
1000,0,1,unconnected,1,0,
1000,0,1,unconnected=5,1,0,
1000,0,1,unconnected=nan,1,0,
1000,0,1,unconnected=-inf,1,0,
1000,0,1,connected=+inf,1,0,
1000,0,1,connected=-inf,1,0,
1000,0,1,connected=nan,1,0,
1000,0,0.001,10,1,0,
1000,0,0.001,-10,1,0,
1000,0,0.001,0,1,0,
1000,0,0.001,1,1,0,
1000,0,0.001,0.001,1,0,
1000,0,0.001,-0.001,1,0,
1000,0,0.001,unconnected,1,0,
1000,0,0.001,unconnected=5,1,0,
1000,0,0.001,unconnected=nan,1,0,
1000,0,0.001,unconnected=-inf,1,0,
1000,0,0.001,connected=+inf,1,0,
1000,0,0.001,connected=-inf,1,0,
1000,0,0.001,connected=nan,1,0,
1000,0,-0.001,10,1,0,
1000,0,-0.001,-10,1,0,
1000,0,-0.001,0,1,0,
1000,0,-0.001,1,1,0,
1000,0,-0.001,0.001,1,0,
1000,0,-0.001,-0.001,1,0,
1000,0,-0.001,unconnected,1,0,
1000,0,-0.001,unconnected=5,1,0,
1000,0,-0.001,unconnected=nan,1,0,
1000,0,-0.001,unconnected=-inf,1,0,
1000,0,-0.001,connected=+inf,1,0,
1000,0,-0.001,connected=-inf,1,0,
1000,0,-0.001,connected=nan,1,0,
1000,0,unconnected,10,1,0,
1000,0,unconnected,-10,1,0,
1000,0,unconnected,0,1,0,
1000,0,unconnected,1,1,0,
1000,0,unconnected,0.001,1,0,
1000,0,unconnected,-0.001,1,0,
1000,0,unconnected,unconnected,1,0,
1000,0,unconnected,unconnected=5,1,0,
1000,0,unconnected,unconnected=nan,1,0,
1000,0,unconnected,unconnected=-inf,1,0,
1000,0,unconnected,connected=+inf,1,0,
1000,0,unconnected,connected=-inf,1,0,
1000,0,unconnected,connected=nan,1,0,
1000,0,unconnected=5,10,1,0,
1000,0,unconnected=5,-10,1,0,
1000,0,unconnected=5,0,1,0,
1000,0,unconnected=5,1,1,0,
1000,0,unconnected=5,0.001,1,0,
1000,0,unconnected=5,-0.001,1,0,
1000,0,unconnected=5,unconnected,1,0,
1000,0,unconnected=5,unconnected=5,1,0,
1000,0,unconnected=5,unconnected=nan,1,0,
1000,0,unconnected=5,unconnected=-inf,1,0,
1000,0,unconnected=5,connected=+inf,1,0,
1000,0,unconnected=5,connected=-inf,1,0,
1000,0,unconnected=5,connected=nan,1,0,
1000,0,unconnected=nan,10,1,0,
1000,0,unconnected=nan,-10,1,0,
1000,0,unconnected=nan,0,1,0,
1000,0,unconnected=nan,1,1,0,
1000,0,unconnected=nan,0.001,1,0,
1000,0,unconnected=nan,-0.001,1,0,
1000,0,unconnected=nan,unconnected,1,0,
1000,0,unconnected=nan,unconnected=5,1,0,
1000,0,unconnected=nan,unconnected=nan,1,0,
1000,0,unconnected=nan,unconnected=-inf,1,0,
1000,0,unconnected=nan,connected=+inf,1,0,
1000,0,unconnected=nan,connected=-inf,1,0,
1000,0,unconnected=nan,connected=nan,1,0,
1000,0,unconnected=-inf,10,1,0,
1000,0,unconnected=-inf,-10,1,0,
1000,0,unconnected=-inf,0,1,0,
1000,0,unconnected=-inf,1,1,0,
1000,0,unconnected=-inf,0.001,1,0,
1000,0,unconnected=-inf,-0.001,1,0,
1000,0,unconnected=-inf,unconnected,1,0,
1000,0,unconnected=-inf,unconnected=5,1,0,
1000,0,unconnected=-inf,unconnected=nan,1,0,
1000,0,unconnected=-inf,unconnected=-inf,1,0,
1000,0,unconnected=-inf,connected=+inf,1,0,
1000,0,unconnected=-inf,connected=-inf,1,0,
1000,0,unconnected=-inf,connected=nan,1,0,
1000,0,connected=+inf,10,1,0,
1000,0,connected=+inf,-10,1,0,
1000,0,connected=+inf,0,1,0,
1000,0,connected=+inf,1,1,0,
1000,0,connected=+inf,0.001,1,0,
1000,0,connected=+inf,-0.001,1,0,
1000,0,connected=+inf,unconnected,1,0,
1000,0,connected=+inf,unconnected=5,1,0,
1000,0,connected=+inf,unconnected=nan,1,0,
1000,0,connected=+inf,unconnected=-inf,1,0,
1000,0,connected=+inf,connected=+inf,1,0,
1000,0,connected=+inf,connected=-inf,1,0,
1000,0,connected=+inf,connected=nan,1,0,
1000,0,connected=-inf,10,1,0,
1000,0,connected=-inf,-10,1,0,
1000,0,connected=-inf,0,1,0,
1000,0,connected=-inf,1,1,0,
1000,0,connected=-inf,0.001,1,0,
1000,0,connected=-inf,-0.001,1,0,
1000,0,connected=-inf,unconnected,1,0,
1000,0,connected=-inf,unconnected=5,1,0,
1000,0,connected=-inf,unconnected=nan,1,0,
1000,0,connected=-inf,unconnected=-inf,1,0,
1000,0,connected=-inf,connected=+inf,1,0,
1000,0,connected=-inf,connected=-inf,1,0,
1000,0,connected=-inf,connected=nan,1,0,
1000,0,connected=nan,10,1,0,
1000,0,connected=nan,-10,1,0,
1000,0,connected=nan,0,1,0,
1000,0,connected=nan,1,1,0,
1000,0,connected=nan,0.001,1,0,
1000,0,connected=nan,-0.001,1,0,
1000,0,connected=nan,unconnected,1,0,
1000,0,connected=nan,unconnected=5,1,0,
1000,0,connected=nan,unconnected=nan,1,0,
1000,0,connected=nan,unconnected=-inf,1,0,
1000,0,connected=nan,connected=+inf,1,0,
1000,0,connected=nan,connected=-inf,1,0,
1000,0,connected=nan,connected=nan,1,0,
1000,0,10,10,1,1,
1000,0,-10,-10,1,1,
1000,0,0,0,1,1,
1000,0,1,1,1,1,
1000,0,0.001,0.001,1,1,
1000,0,-0.001,-0.001,1,1,
1000,0,unconnected,unconnected,1,1,
1000,0,unconnected=5,unconnected=5,1,1,
1000,0,unconnected=nan,unconnected=nan,1,1,
1000,0,unconnected=-inf,unconnected=-inf,1,1,
1000,0,connected=+inf,connected=+inf,1,1,
1000,0,connected=-inf,connected=-inf,1,1,
1000,0,connected=nan,connected=nan,1,1,
1000,0,10,10,1,1,
1000,0,10,-10,1,1,
1000,0,10,0,1,1,
1000,0,10,1,1,1,
1000,0,10,0.001,1,1,
1000,0,10,-0.001,1,1,
1000,0,10,unconnected,1,1,
1000,0,10,unconnected=5,1,1,
1000,0,10,unconnected=nan,1,1,
1000,0,10,unconnected=-inf,1,1,
1000,0,10,connected=+inf,1,1,
1000,0,10,connected=-inf,1,1,
1000,0,10,connected=nan,1,1,
1000,0,-10,10,1,1,
1000,0,-10,-10,1,1,
1000,0,-10,0,1,1,
1000,0,-10,1,1,1,
1000,0,-10,0.001,1,1,
1000,0,-10,-0.001,1,1,
1000,0,-10,unconnected,1,1,
1000,0,-10,unconnected=5,1,1,
1000,0,-10,unconnected=nan,1,1,
1000,0,-10,unconnected=-inf,1,1,
1000,0,-10,connected=+inf,1,1,
1000,0,-10,connected=-inf,1,1,
1000,0,-10,connected=nan,1,1,
1000,0,0,10,1,1,
1000,0,0,-10,1,1,
1000,0,0,0,1,1,
1000,0,0,1,1,1,
1000,0,0,0.001,1,1,
1000,0,0,-0.001,1,1,
1000,0,0,unconnected,1,1,
1000,0,0,unconnected=5,1,1,
1000,0,0,unconnected=nan,1,1,
1000,0,0,unconnected=-inf,1,1,
1000,0,0,connected=+inf,1,1,
1000,0,0,connected=-inf,1,1,
1000,0,0,connected=nan,1,1,
1000,0,1,10,1,1,
1000,0,1,-10,1,1,
1000,0,1,0,1,1,
1000,0,1,1,1,1,
1000,0,1,0.001,1,1,
1000,0,1,-0.001,1,1,
1000,0,1,unconnected,1,1,
1000,0,1,unconnected=5,1,1,
1000,0,1,unconnected=nan,1,1,
1000,0,1,unconnected=-inf,1,1,
1000,0,1,connected=+inf,1,1,
1000,0,1,connected=-inf,1,1,
1000,0,1,connected=nan,1,1,
1000,0,0.001,10,1,1,
1000,0,0.001,-10,1,1,
1000,0,0.001,0,1,1,
1000,0,0.001,1,1,1,
1000,0,0.001,0.001,1,1,
1000,0,0.001,-0.001,1,1,
1000,0,0.001,unconnected,1,1,
1000,0,0.001,unconnected=5,1,1,
1000,0,0.001,unconnected=nan,1,1,
1000,0,0.001,unconnected=-inf,1,1,
1000,0,0.001,connected=+inf,1,1,
1000,0,0.001,connected=-inf,1,1,
1000,0,0.001,connected=nan,1,1,
1000,0,-0.001,10,1,1,
1000,0,-0.001,-10,1,1,
1000,0,-0.001,0,1,1,
1000,0,-0.001,1,1,1,
1000,0,-0.001,0.001,1,1,
1000,0,-0.001,-0.001,1,1,
1000,0,-0.001,unconnected,1,1,
1000,0,-0.001,unconnected=5,1,1,
1000,0,-0.001,unconnected=nan,1,1,
1000,0,-0.001,unconnected=-inf,1,1,
1000,0,-0.001,connected=+inf,1,1,
1000,0,-0.001,connected=-inf,1,1,
1000,0,-0.001,connected=nan,1,1,
1000,0,unconnected,10,1,1,
1000,0,unconnected,-10,1,1,
1000,0,unconnected,0,1,1,
1000,0,unconnected,1,1,1,
1000,0,unconnected,0.001,1,1,
1000,0,unconnected,-0.001,1,1,
1000,0,unconnected,unconnected,1,1,
1000,0,unconnected,unconnected=5,1,1,
1000,0,unconnected,unconnected=nan,1,1,
1000,0,unconnected,unconnected=-inf,1,1,
1000,0,unconnected,connected=+inf,1,1,
1000,0,unconnected,connected=-inf,1,1,
1000,0,unconnected,connected=nan,1,1,
1000,0,unconnected=5,10,1,1,
1000,0,unconnected=5,-10,1,1,
1000,0,unconnected=5,0,1,1,
1000,0,unconnected=5,1,1,1,
1000,0,unconnected=5,0.001,1,1,
1000,0,unconnected=5,-0.001,1,1,
1000,0,unconnected=5,unconnected,1,1,
1000,0,unconnected=5,unconnected=5,1,1,
1000,0,unconnected=5,unconnected=nan,1,1,
1000,0,unconnected=5,unconnected=-inf,1,1,
1000,0,unconnected=5,connected=+inf,1,1,
1000,0,unconnected=5,connected=-inf,1,1,
1000,0,unconnected=5,connected=nan,1,1,
1000,0,unconnected=nan,10,1,1,
1000,0,unconnected=nan,-10,1,1,
1000,0,unconnected=nan,0,1,1,
1000,0,unconnected=nan,1,1,1,
1000,0,unconnected=nan,0.001,1,1,
1000,0,unconnected=nan,-0.001,1,1,
1000,0,unconnected=nan,unconnected,1,1,
1000,0,unconnected=nan,unconnected=5,1,1,
1000,0,unconnected=nan,unconnected=nan,1,1,
1000,0,unconnected=nan,unconnected=-inf,1,1,
1000,0,unconnected=nan,connected=+inf,1,1,
1000,0,unconnected=nan,connected=-inf,1,1,
1000,0,unconnected=nan,connected=nan,1,1,
1000,0,unconnected=-inf,10,1,1,
1000,0,unconnected=-inf,-10,1,1,
1000,0,unconnected=-inf,0,1,1,
1000,0,unconnected=-inf,1,1,1,
1000,0,unconnected=-inf,0.001,1,1,
1000,0,unconnected=-inf,-0.001,1,1,
1000,0,unconnected=-inf,unconnected,1,1,
1000,0,unconnected=-inf,unconnected=5,1,1,
1000,0,unconnected=-inf,unconnected=nan,1,1,
1000,0,unconnected=-inf,unconnected=-inf,1,1,
1000,0,unconnected=-inf,connected=+inf,1,1,
1000,0,unconnected=-inf,connected=-inf,1,1,
1000,0,unconnected=-inf,connected=nan,1,1,
1000,0,connected=+inf,10,1,1,
1000,0,connected=+inf,-10,1,1,
1000,0,connected=+inf,0,1,1,
1000,0,connected=+inf,1,1,1,
1000,0,connected=+inf,0.001,1,1,
1000,0,connected=+inf,-0.001,1,1,
1000,0,connected=+inf,unconnected,1,1,
1000,0,connected=+inf,unconnected=5,1,1,
1000,0,connected=+inf,unconnected=nan,1,1,
1000,0,connected=+inf,unconnected=-inf,1,1,
1000,0,connected=+inf,connected=+inf,1,1,
1000,0,connected=+inf,connected=-inf,1,1,
1000,0,connected=+inf,connected=nan,1,1,
1000,0,connected=-inf,10,1,1,
1000,0,connected=-inf,-10,1,1,
1000,0,connected=-inf,0,1,1,
1000,0,connected=-inf,1,1,1,
1000,0,connected=-inf,0.001,1,1,
1000,0,connected=-inf,-0.001,1,1,
1000,0,connected=-inf,unconnected,1,1,
1000,0,connected=-inf,unconnected=5,1,1,
1000,0,connected=-inf,unconnected=nan,1,1,
1000,0,connected=-inf,unconnected=-inf,1,1,
1000,0,connected=-inf,connected=+inf,1,1,
1000,0,connected=-inf,connected=-inf,1,1,
1000,0,connected=-inf,connected=nan,1,1,
1000,0,connected=nan,10,1,1,
1000,0,connected=nan,-10,1,1,
1000,0,connected=nan,0,1,1,
1000,0,connected=nan,1,1,1,
1000,0,connected=nan,0.001,1,1,
1000,0,connected=nan,-0.001,1,1,
1000,0,connected=nan,unconnected,1,1,
1000,0,connected=nan,unconnected=5,1,1,
1000,0,connected=nan,unconnected=nan,1,1,
1000,0,connected=nan,unconnected=-inf,1,1,
1000,0,connected=nan,connected=+inf,1,1,
1000,0,connected=nan,connected=-inf,1,1,
1000,0,connected=nan,connected=nan,1,1,
1000,0,10,10,1,2,
1000,0,-10,-10,1,2,
1000,0,0,0,1,2,
1000,0,1,1,1,2,
1000,0,0.001,0.001,1,2,
1000,0,-0.001,-0.001,1,2,
1000,0,unconnected,unconnected,1,2,
1000,0,unconnected=5,unconnected=5,1,2,
1000,0,unconnected=nan,unconnected=nan,1,2,
1000,0,unconnected=-inf,unconnected=-inf,1,2,
1000,0,connected=+inf,connected=+inf,1,2,
1000,0,connected=-inf,connected=-inf,1,2,
1000,0,connected=nan,connected=nan,1,2,
1000,0,10,10,1,2,
1000,0,10,-10,1,2,
1000,0,10,0,1,2,
1000,0,10,1,1,2,
1000,0,10,0.001,1,2,
1000,0,10,-0.001,1,2,
1000,0,10,unconnected,1,2,
1000,0,10,unconnected=5,1,2,
1000,0,10,unconnected=nan,1,2,
1000,0,10,unconnected=-inf,1,2,
1000,0,10,connected=+inf,1,2,
1000,0,10,connected=-inf,1,2,
1000,0,10,connected=nan,1,2,
1000,0,-10,10,1,2,
1000,0,-10,-10,1,2,
1000,0,-10,0,1,2,
1000,0,-10,1,1,2,
1000,0,-10,0.001,1,2,
1000,0,-10,-0.001,1,2,
1000,0,-10,unconnected,1,2,
1000,0,-10,unconnected=5,1,2,
1000,0,-10,unconnected=nan,1,2,
1000,0,-10,unconnected=-inf,1,2,
1000,0,-10,connected=+inf,1,2,
1000,0,-10,connected=-inf,1,2,
1000,0,-10,connected=nan,1,2,
1000,0,0,10,1,2,
1000,0,0,-10,1,2,
1000,0,0,0,1,2,
1000,0,0,1,1,2,
1000,0,0,0.001,1,2,
1000,0,0,-0.001,1,2,
1000,0,0,unconnected,1,2,
1000,0,0,unconnected=5,1,2,
1000,0,0,unconnected=nan,1,2,
1000,0,0,unconnected=-inf,1,2,
1000,0,0,connected=+inf,1,2,
1000,0,0,connected=-inf,1,2,
1000,0,0,connected=nan,1,2,
1000,0,1,10,1,2,
1000,0,1,-10,1,2,
1000,0,1,0,1,2,
1000,0,1,1,1,2,
1000,0,1,0.001,1,2,
1000,0,1,-0.001,1,2,
1000,0,1,unconnected,1,2,
1000,0,1,unconnected=5,1,2,
1000,0,1,unconnected=nan,1,2,
1000,0,1,unconnected=-inf,1,2,
1000,0,1,connected=+inf,1,2,
1000,0,1,connected=-inf,1,2,
1000,0,1,connected=nan,1,2,
1000,0,0.001,10,1,2,
1000,0,0.001,-10,1,2,
1000,0,0.001,0,1,2,
1000,0,0.001,1,1,2,
1000,0,0.001,0.001,1,2,
1000,0,0.001,-0.001,1,2,
1000,0,0.001,unconnected,1,2,
1000,0,0.001,unconnected=5,1,2,
1000,0,0.001,unconnected=nan,1,2,
1000,0,0.001,unconnected=-inf,1,2,
1000,0,0.001,connected=+inf,1,2,
1000,0,0.001,connected=-inf,1,2,
1000,0,0.001,connected=nan,1,2,
1000,0,-0.001,10,1,2,
1000,0,-0.001,-10,1,2,
1000,0,-0.001,0,1,2,
1000,0,-0.001,1,1,2,
1000,0,-0.001,0.001,1,2,
1000,0,-0.001,-0.001,1,2,
1000,0,-0.001,unconnected,1,2,
1000,0,-0.001,unconnected=5,1,2,
1000,0,-0.001,unconnected=nan,1,2,
1000,0,-0.001,unconnected=-inf,1,2,
1000,0,-0.001,connected=+inf,1,2,
1000,0,-0.001,connected=-inf,1,2,
1000,0,-0.001,connected=nan,1,2,
1000,0,unconnected,10,1,2,
1000,0,unconnected,-10,1,2,
1000,0,unconnected,0,1,2,
1000,0,unconnected,1,1,2,
1000,0,unconnected,0.001,1,2,
1000,0,unconnected,-0.001,1,2,
1000,0,unconnected,unconnected,1,2,
1000,0,unconnected,unconnected=5,1,2,
1000,0,unconnected,unconnected=nan,1,2,
1000,0,unconnected,unconnected=-inf,1,2,
1000,0,unconnected,connected=+inf,1,2,
1000,0,unconnected,connected=-inf,1,2,
1000,0,unconnected,connected=nan,1,2,
1000,0,unconnected=5,10,1,2,
1000,0,unconnected=5,-10,1,2,
1000,0,unconnected=5,0,1,2,
1000,0,unconnected=5,1,1,2,
1000,0,unconnected=5,0.001,1,2,
1000,0,unconnected=5,-0.001,1,2,
1000,0,unconnected=5,unconnected,1,2,
1000,0,unconnected=5,unconnected=5,1,2,
1000,0,unconnected=5,unconnected=nan,1,2,
1000,0,unconnected=5,unconnected=-inf,1,2,
1000,0,unconnected=5,connected=+inf,1,2,
1000,0,unconnected=5,connected=-inf,1,2,
1000,0,unconnected=5,connected=nan,1,2,
1000,0,unconnected=nan,10,1,2,
1000,0,unconnected=nan,-10,1,2,
1000,0,unconnected=nan,0,1,2,
1000,0,unconnected=nan,1,1,2,
1000,0,unconnected=nan,0.001,1,2,
1000,0,unconnected=nan,-0.001,1,2,
1000,0,unconnected=nan,unconnected,1,2,
1000,0,unconnected=nan,unconnected=5,1,2,
1000,0,unconnected=nan,unconnected=nan,1,2,
1000,0,unconnected=nan,unconnected=-inf,1,2,
1000,0,unconnected=nan,connected=+inf,1,2,
1000,0,unconnected=nan,connected=-inf,1,2,
1000,0,unconnected=nan,connected=nan,1,2,
1000,0,unconnected=-inf,10,1,2,
1000,0,unconnected=-inf,-10,1,2,
1000,0,unconnected=-inf,0,1,2,
1000,0,unconnected=-inf,1,1,2,
1000,0,unconnected=-inf,0.001,1,2,
1000,0,unconnected=-inf,-0.001,1,2,
1000,0,unconnected=-inf,unconnected,1,2,
1000,0,unconnected=-inf,unconnected=5,1,2,
1000,0,unconnected=-inf,unconnected=nan,1,2,
1000,0,unconnected=-inf,unconnected=-inf,1,2,
1000,0,unconnected=-inf,connected=+inf,1,2,
1000,0,unconnected=-inf,connected=-inf,1,2,
1000,0,unconnected=-inf,connected=nan,1,2,
1000,0,connected=+inf,10,1,2,
1000,0,connected=+inf,-10,1,2,
1000,0,connected=+inf,0,1,2,
1000,0,connected=+inf,1,1,2,
1000,0,connected=+inf,0.001,1,2,
1000,0,connected=+inf,-0.001,1,2,
1000,0,connected=+inf,unconnected,1,2,
1000,0,connected=+inf,unconnected=5,1,2,
1000,0,connected=+inf,unconnected=nan,1,2,
1000,0,connected=+inf,unconnected=-inf,1,2,
1000,0,connected=+inf,connected=+inf,1,2,
1000,0,connected=+inf,connected=-inf,1,2,
1000,0,connected=+inf,connected=nan,1,2,
1000,0,connected=-inf,10,1,2,
1000,0,connected=-inf,-10,1,2,
1000,0,connected=-inf,0,1,2,
1000,0,connected=-inf,1,1,2,
1000,0,connected=-inf,0.001,1,2,
1000,0,connected=-inf,-0.001,1,2,
1000,0,connected=-inf,unconnected,1,2,
1000,0,connected=-inf,unconnected=5,1,2,
1000,0,connected=-inf,unconnected=nan,1,2,
1000,0,connected=-inf,unconnected=-inf,1,2,
1000,0,connected=-inf,connected=+inf,1,2,
1000,0,connected=-inf,connected=-inf,1,2,
1000,0,connected=-inf,connected=nan,1,2,
1000,0,connected=nan,10,1,2,
1000,0,connected=nan,-10,1,2,
1000,0,connected=nan,0,1,2,
1000,0,connected=nan,1,1,2,
1000,0,connected=nan,0.001,1,2,
1000,0,connected=nan,-0.001,1,2,
1000,0,connected=nan,unconnected,1,2,
1000,0,connected=nan,unconnected=5,1,2,
1000,0,connected=nan,unconnected=nan,1,2,
1000,0,connected=nan,unconnected=-inf,1,2,
1000,0,connected=nan,connected=+inf,1,2,
1000,0,connected=nan,connected=-inf,1,2,
1000,0,connected=nan,connected=nan,1,2,
1000,0,10,10,1,3,
1000,0,-10,-10,1,3,
1000,0,0,0,1,3,
1000,0,1,1,1,3,
1000,0,0.001,0.001,1,3,
1000,0,-0.001,-0.001,1,3,
1000,0,unconnected,unconnected,1,3,
1000,0,unconnected=5,unconnected=5,1,3,
1000,0,unconnected=nan,unconnected=nan,1,3,
1000,0,unconnected=-inf,unconnected=-inf,1,3,
1000,0,connected=+inf,connected=+inf,1,3,
1000,0,connected=-inf,connected=-inf,1,3,
1000,0,connected=nan,connected=nan,1,3,
1000,0,10,10,1,3,
1000,0,10,-10,1,3,
1000,0,10,0,1,3,
1000,0,10,1,1,3,
1000,0,10,0.001,1,3,
1000,0,10,-0.001,1,3,
1000,0,10,unconnected,1,3,
1000,0,10,unconnected=5,1,3,
1000,0,10,unconnected=nan,1,3,
1000,0,10,unconnected=-inf,1,3,
1000,0,10,connected=+inf,1,3,
1000,0,10,connected=-inf,1,3,
1000,0,10,connected=nan,1,3,
1000,0,-10,10,1,3,
1000,0,-10,-10,1,3,
1000,0,-10,0,1,3,
1000,0,-10,1,1,3,
1000,0,-10,0.001,1,3,
1000,0,-10,-0.001,1,3,
1000,0,-10,unconnected,1,3,
1000,0,-10,unconnected=5,1,3,
1000,0,-10,unconnected=nan,1,3,
1000,0,-10,unconnected=-inf,1,3,
1000,0,-10,connected=+inf,1,3,
1000,0,-10,connected=-inf,1,3,
1000,0,-10,connected=nan,1,3,
1000,0,0,10,1,3,
1000,0,0,-10,1,3,
1000,0,0,0,1,3,
1000,0,0,1,1,3,
1000,0,0,0.001,1,3,
1000,0,0,-0.001,1,3,
1000,0,0,unconnected,1,3,
1000,0,0,unconnected=5,1,3,
1000,0,0,unconnected=nan,1,3,
1000,0,0,unconnected=-inf,1,3,
1000,0,0,connected=+inf,1,3,
1000,0,0,connected=-inf,1,3,
1000,0,0,connected=nan,1,3,
1000,0,1,10,1,3,
1000,0,1,-10,1,3,
1000,0,1,0,1,3,
1000,0,1,1,1,3,
1000,0,1,0.001,1,3,
1000,0,1,-0.001,1,3,
1000,0,1,unconnected,1,3,
1000,0,1,unconnected=5,1,3,
1000,0,1,unconnected=nan,1,3,
1000,0,1,unconnected=-inf,1,3,
1000,0,1,connected=+inf,1,3,
1000,0,1,connected=-inf,1,3,
1000,0,1,connected=nan,1,3,
1000,0,0.001,10,1,3,
1000,0,0.001,-10,1,3,
1000,0,0.001,0,1,3,
1000,0,0.001,1,1,3,
1000,0,0.001,0.001,1,3,
1000,0,0.001,-0.001,1,3,
1000,0,0.001,unconnected,1,3,
1000,0,0.001,unconnected=5,1,3,
1000,0,0.001,unconnected=nan,1,3,
1000,0,0.001,unconnected=-inf,1,3,
1000,0,0.001,connected=+inf,1,3,
1000,0,0.001,connected=-inf,1,3,
1000,0,0.001,connected=nan,1,3,
1000,0,-0.001,10,1,3,
1000,0,-0.001,-10,1,3,
1000,0,-0.001,0,1,3,
1000,0,-0.001,1,1,3,
1000,0,-0.001,0.001,1,3,
1000,0,-0.001,-0.001,1,3,
1000,0,-0.001,unconnected,1,3,
1000,0,-0.001,unconnected=5,1,3,
1000,0,-0.001,unconnected=nan,1,3,
1000,0,-0.001,unconnected=-inf,1,3,
1000,0,-0.001,connected=+inf,1,3,
1000,0,-0.001,connected=-inf,1,3,
1000,0,-0.001,connected=nan,1,3,
1000,0,unconnected,10,1,3,
1000,0,unconnected,-10,1,3,
1000,0,unconnected,0,1,3,
1000,0,unconnected,1,1,3,
1000,0,unconnected,0.001,1,3,
1000,0,unconnected,-0.001,1,3,
1000,0,unconnected,unconnected,1,3,
1000,0,unconnected,unconnected=5,1,3,
1000,0,unconnected,unconnected=nan,1,3,
1000,0,unconnected,unconnected=-inf,1,3,
1000,0,unconnected,connected=+inf,1,3,
1000,0,unconnected,connected=-inf,1,3,
1000,0,unconnected,connected=nan,1,3,
1000,0,unconnected=5,10,1,3,
1000,0,unconnected=5,-10,1,3,
1000,0,unconnected=5,0,1,3,
1000,0,unconnected=5,1,1,3,
1000,0,unconnected=5,0.001,1,3,
1000,0,unconnected=5,-0.001,1,3,
1000,0,unconnected=5,unconnected,1,3,
1000,0,unconnected=5,unconnected=5,1,3,
1000,0,unconnected=5,unconnected=nan,1,3,
1000,0,unconnected=5,unconnected=-inf,1,3,
1000,0,unconnected=5,connected=+inf,1,3,
1000,0,unconnected=5,connected=-inf,1,3,
1000,0,unconnected=5,connected=nan,1,3,
1000,0,unconnected=nan,10,1,3,
1000,0,unconnected=nan,-10,1,3,
1000,0,unconnected=nan,0,1,3,
1000,0,unconnected=nan,1,1,3,
1000,0,unconnected=nan,0.001,1,3,
1000,0,unconnected=nan,-0.001,1,3,
1000,0,unconnected=nan,unconnected,1,3,
1000,0,unconnected=nan,unconnected=5,1,3,
1000,0,unconnected=nan,unconnected=nan,1,3,
1000,0,unconnected=nan,unconnected=-inf,1,3,
1000,0,unconnected=nan,connected=+inf,1,3,
1000,0,unconnected=nan,connected=-inf,1,3,
1000,0,unconnected=nan,connected=nan,1,3,
1000,0,unconnected=-inf,10,1,3,
1000,0,unconnected=-inf,-10,1,3,
1000,0,unconnected=-inf,0,1,3,
1000,0,unconnected=-inf,1,1,3,
1000,0,unconnected=-inf,0.001,1,3,
1000,0,unconnected=-inf,-0.001,1,3,
1000,0,unconnected=-inf,unconnected,1,3,
1000,0,unconnected=-inf,unconnected=5,1,3,
1000,0,unconnected=-inf,unconnected=nan,1,3,
1000,0,unconnected=-inf,unconnected=-inf,1,3,
1000,0,unconnected=-inf,connected=+inf,1,3,
1000,0,unconnected=-inf,connected=-inf,1,3,
1000,0,unconnected=-inf,connected=nan,1,3,
1000,0,connected=+inf,10,1,3,
1000,0,connected=+inf,-10,1,3,
1000,0,connected=+inf,0,1,3,
1000,0,connected=+inf,1,1,3,
1000,0,connected=+inf,0.001,1,3,
1000,0,connected=+inf,-0.001,1,3,
1000,0,connected=+inf,unconnected,1,3,
1000,0,connected=+inf,unconnected=5,1,3,
1000,0,connected=+inf,unconnected=nan,1,3,
1000,0,connected=+inf,unconnected=-inf,1,3,
1000,0,connected=+inf,connected=+inf,1,3,
1000,0,connected=+inf,connected=-inf,1,3,
1000,0,connected=+inf,connected=nan,1,3,
1000,0,connected=-inf,10,1,3,
1000,0,connected=-inf,-10,1,3,
1000,0,connected=-inf,0,1,3,
1000,0,connected=-inf,1,1,3,
1000,0,connected=-inf,0.001,1,3,
1000,0,connected=-inf,-0.001,1,3,
1000,0,connected=-inf,unconnected,1,3,
1000,0,connected=-inf,unconnected=5,1,3,
1000,0,connected=-inf,unconnected=nan,1,3,
1000,0,connected=-inf,unconnected=-inf,1,3,
1000,0,connected=-inf,connected=+inf,1,3,
1000,0,connected=-inf,connected=-inf,1,3,
1000,0,connected=-inf,connected=nan,1,3,
1000,0,connected=nan,10,1,3,
1000,0,connected=nan,-10,1,3,
1000,0,connected=nan,0,1,3,
1000,0,connected=nan,1,1,3,
1000,0,connected=nan,0.001,1,3,
1000,0,connected=nan,-0.001,1,3,
1000,0,connected=nan,unconnected,1,3,
1000,0,connected=nan,unconnected=5,1,3,
1000,0,connected=nan,unconnected=nan,1,3,
1000,0,connected=nan,unconnected=-inf,1,3,
1000,0,connected=nan,connected=+inf,1,3,
1000,0,connected=nan,connected=-inf,1,3,
1000,0,connected=nan,connected=nan,1,3,
