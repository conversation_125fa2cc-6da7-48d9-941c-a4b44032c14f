/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.math.BRatio;
import com.honeywell.honfunctionblocks.fbs.math.enums.BRatioOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation Ratio block test cases as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: 
 * <AUTHOR> - Suresh Khatri
 * @since Feb 9, 2018
 */

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
@NiagaraType

public class BRatioTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BRatioTest(2979906276)1.0$ @*/
/* Generated Fri Feb 09 15:42:50 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRatioTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	  
	@BeforeClass(alwaysRun=true)
	public void setUp() {
		ratioBlock = new BRatio();
		executionParams = new BExecutionParams();

	}

	@AfterClass(alwaysRun=true)
	public void tearDown() {
		ratioBlock = null;
		executionParams = null;
	}

	@DataProvider(name = "provideInSlotNames")
	public Object[][] createInputSlotNames() {
		return new Object[][] { { "x" }, { "x1" }, { "x2" }, { "y1" }, { "y2" } };
	}

	@DataProvider(name = "provideConfigSlotNames")
	public Object[][] createConfigSlotNames() {
		return new Object[][] { { "ignoreInvalidInput" }, { "operation" } };
	}

	@DataProvider(name = "provideOutputSlotNames")
	public Object[][] createOutputSlotNames() {
		return new Object[][] { { "OUTPUT" } };
	}

	@DataProvider(name = "provideMiscSlotNames")
	public Object[][] createExecOrderSlotName() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
		slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	}

	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "In" }, { "onValue" }, { "OnVal" }, { "OffVal" }, { "OffValue" }, { "MinOn" } };
	}

	@Test(dataProvider = "provideInvalidSlotNames")
	public void testInvalidSlots(String slotName) {
		Assert.assertNull(ratioBlock.getSlot(slotName));
	}

	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		Assert.assertNotNull(ratioBlock.getSlot(slotName));
	}

	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_ratio.png");
		BIcon actualFbIcon = ratioBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		// check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		ratioBlock.setIcon(expectedFbIcon);
		actualFbIcon = ratioBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "provideSampleValues")
	public Object[][] sampleValues() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }, { 2.2250738585072014E-308 }, { -1.7976931348623157e+308 }, { Double.NEGATIVE_INFINITY }, { Double.POSITIVE_INFINITY }, { Double.NaN } };
	}

	@Test(dataProvider = "provideSampleValues")
	public void testSettingValueInratioBlock(double snValue) {
		ratioBlock.setX(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getX().getValue(), snValue, 0.1);

		ratioBlock.setX1(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getX1().getValue(), snValue, 0.1);

		ratioBlock.setX2(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getX2().getValue(), snValue, 0.1);

		ratioBlock.setY1(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getY1().getValue(), snValue, 0.1);

		ratioBlock.setY2(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getY2().getValue(), snValue, 0.1);

		ratioBlock.setIgnoreInvalidInput(false);
		Assert.assertEquals(ratioBlock.getIgnoreInvalidInput(), false);

		ratioBlock.setOperation(BRatioOperationEnum.Endpt_Limited);
		Assert.assertEquals(ratioBlock.getOperation().getOrdinal(), BRatioOperationEnum.ENDPT_LIMITED, 0.1);

		ratioBlock.setOUTPUT(new BHonStatusNumeric(snValue));
		Assert.assertEquals(ratioBlock.getOUTPUT().getValue(), snValue, 0.1);
	}

	@DataProvider(name = "provideTestData")
	public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Ratio_TestData.csv");
	}

	@Test(dataProvider = "provideTestData", dependsOnMethods = { "testSlotAvailability" })
	public void testRatioBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		BRatio ratioBlock = new BRatio();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(1), 1000));
		setupNumericSlot(ratioBlock, BRatio.x.getName(), inputs.get(2));
		setupNumericSlot(ratioBlock, BRatio.x1.getName(), inputs.get(3));
		setupNumericSlot(ratioBlock, BRatio.y1.getName(), inputs.get(4));
		setupNumericSlot(ratioBlock, BRatio.x2.getName(), inputs.get(5));
		setupNumericSlot(ratioBlock, BRatio.y2.getName(), inputs.get(6));

		ratioBlock.setOperation(BRatioOperationEnum.make(TestDataHelper.getInt(inputs.get(7), 0)));
		ratioBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(8)));
		ratioBlock.executeHoneywellComponent(executionParams);
		Assert.assertEquals(ratioBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), Arrays.toString(inputs.toArray()));
		ratioBlock = null;
	}

	/**
	 * Configure numeric value to the given slot as per test data (either set constant value or by link
	 * propagation)
	 * 
	 * @param ratioBlock
	 * @param slotName
	 * @param inputValue
	 */
	public void setupNumericSlot(BRatio ratioBlock, final String slotName, final String inputValue) {
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = ratioBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
	                     converter = new BStatusNumericToHonStatusNumeric();
	                     BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),ratioBlock.getSlot(slotName),converter);
	                     conversionLink.setEnabled(true);
	                     ratioBlock.add("Link?",conversionLink );                    
	                     conversionLink.activate();
	                     nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
	                } else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),ratioBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				ratioBlock.add("Link?", conversionLink);				
				conversionLink.activate();
			}else{
				ratioBlock.linkTo(nm1, nm1.getSlot("out"), ratioBlock.getSlot(slotName));
			}			
			return;
		}

		switch (slotName) {
			case "x":
				ratioBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "x1":
				ratioBlock.setX1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "x2":
				ratioBlock.setX2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "y1":
				ratioBlock.setY1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "y2":
				ratioBlock.setY2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "operation":
				ratioBlock.setOperation(getRatioOperationEnum(TestDataHelper.getInt(inputValue, 0)));
				break;

			case "ignoreInvalidInput":
				ratioBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputValue));
				break;
		}
	}

	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BRatio ratioBlock = new BRatio();
		ratioBlock.setX(new BHonStatusNumeric(10));
		ratioBlock.setX1(new BHonStatusNumeric(20));
		ratioBlock.setX2(new BHonStatusNumeric(30));
		ratioBlock.setY1(new BHonStatusNumeric(40));
		ratioBlock.setY2(new BHonStatusNumeric(50));
		ratioBlock.setIgnoreInvalidInput(false);
		ratioBlock.setOperation(BRatioOperationEnum.DEFAULT);
		ratioBlock.initHoneywellComponent(null);
		
		executionParams.setIterationInterval(1000);
		ratioBlock.executeHoneywellComponent(executionParams);
		Assert.assertEquals(ratioBlock.getOUTPUT().getValue(), 40.0);
		
		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("Ratio", ratioBlock);
			BRatio ratioSaved = (BRatio) bogUtil.getComponentFromBogFile(bogFile);
			
			Assert.assertEquals(ratioSaved.getX().getValue(), 10, 0.1, "Failed to verify x");
			Assert.assertEquals(ratioSaved.getX1().getValue(), 20, 0.1, "Failed to verify x1");
			Assert.assertEquals(ratioSaved.getX2().getValue(), 30, 0.1, "Failed to verify x2");
			Assert.assertEquals(ratioSaved.getY1().getValue(), 40, 0.1, "Failed to verify y1");
			Assert.assertEquals(ratioSaved.getY2().getValue(), 50, 0.1, "Failed to verify y2");
			Assert.assertEquals(ratioSaved.getIgnoreInvalidInput(), false, "Failed to verify ignoreInvalidInput");
			Assert.assertEquals(ratioSaved.getOperation(), BRatioOperationEnum.DEFAULT, "Failed to verify operation");
			
			Assert.assertEquals(ratioSaved.getOUTPUT().getValue(), Double.POSITIVE_INFINITY, "Failed to verify OUTPUT");
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		ratioBlock = null;
	}

	//@Test
	public void testLinkRules() {
		BRatio ratioBlock = new BRatio();
		checkOutgoingLink(ratioBlock, BRatio.x, false);
		checkOutgoingLink(ratioBlock, BRatio.x1, false);
		checkOutgoingLink(ratioBlock, BRatio.x2, false);
		checkOutgoingLink(ratioBlock, BRatio.y1, false);
		checkOutgoingLink(ratioBlock, BRatio.y2, false);
		checkOutgoingLink(ratioBlock, BRatio.ignoreInvalidInput, false);
		checkOutgoingLink(ratioBlock, BRatio.operation, false);
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = ratioBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BRatio.ignoreInvalidInput.getName(), BRatio.operation.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}

	//@Test
	public void testLinkRules1() {
		BRatio target = new BRatio();
		BMaximum source = new BMaximum();		
		
		LinkCheck linkCheck = source.checkLink(target, target.getSlot("x"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("x1"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("x2"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("y1"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("y2"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("operation"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("ignoreInvalidInput"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		BComponent c = new BComponent();
		c.add("out1", BRatioOperationEnum.make(0));
		linkCheck = target.checkLink(c, c.getSlot("out1"), target.getSlot("operation"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		c = new BComponent();
		c.add("out2", BBoolean.make(true));
		linkCheck = target.checkLink(c, c.getSlot("out2"), target.getSlot("ignoreInvalidInput"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("x1"), null);
		Assert.assertTrue(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("OUTPUT"), null);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = ratioBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BRatio.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	private void checkOutgoingLink(BRatio block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = ratioBlock.checkLink(ratioBlock, ratioBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	private BRatioOperationEnum getRatioOperationEnum(int i) {
		try {
			return BRatioOperationEnum.make(i);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return BRatioOperationEnum.DEFAULT;
	}
	
	private BRatio ratioBlock;
	private BExecutionParams executionParams;

}
