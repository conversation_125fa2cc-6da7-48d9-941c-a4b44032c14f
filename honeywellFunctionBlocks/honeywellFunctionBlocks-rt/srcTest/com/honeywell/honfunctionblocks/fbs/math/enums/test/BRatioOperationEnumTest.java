/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.math.enums.BRatioOperationEnum;

/**
 * <AUTHOR> - RSH.<PERSON><PERSON>
 * @since Feb 20, 2018
 */
@NiagaraType
public class BRatioOperationEnumTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.enums.test.BRatioOperationEnumTest(**********)1.0$ @*/
/* Generated Tue Feb 20 12:20:03 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRatioOperationEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
	@DataProvider(name ="enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal(){
		return new Object[][] { {0,BRatioOperationEnum.Unlimited},
			{1,BRatioOperationEnum.Vav_Flow_Bal},
			{2,BRatioOperationEnum.Endpt_Limited}};
	}

	@Test(dataProvider="enumOrdinal")
	public void testOperationByMakeOrdinal(int ordinal, BRatioOperationEnum toEnum) {
		Assert.assertEquals(BRatioOperationEnum.make(ordinal),toEnum);	  
	}

	@DataProvider(name ="enumTag")
	public Object[][] getDataToTestEnumTag(){
		return new Object[][] { {"Unlimited",BRatioOperationEnum.Unlimited},
			{"Vav_Flow_Bal",BRatioOperationEnum.Vav_Flow_Bal},
			{"Endpt_Limited",BRatioOperationEnum.Endpt_Limited}};
	}

	@Test(dataProvider="enumTag")
	public void testOperationByMakeTag(String tag, BRatioOperationEnum fcuEnum) {
		Assert.assertEquals(BRatioOperationEnum.make(tag),fcuEnum);
	} 

}
