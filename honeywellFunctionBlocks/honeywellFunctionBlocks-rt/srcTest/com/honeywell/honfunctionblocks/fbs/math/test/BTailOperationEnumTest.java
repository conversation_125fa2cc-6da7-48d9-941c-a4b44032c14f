/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;

/**
 * Implementation of Math Function blocks as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: TBD
 * <AUTHOR> - <PERSON><PERSON>ya B.
 * @since Jan 30, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BTailOperationEnumTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BTailOperationEnumTest(**********)1.0$ @*/
/* Generated Tue Jan 30 14:27:37 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTailOperationEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
  @DataProvider(name ="enumOrdinal")
  public Object[][] getDataToTestEnumOrdinal(){
	  return new Object[][] { {Integer.valueOf(BTailOperationEnum.NO_CHANGE),BTailOperationEnum.NoChange},
		  {Integer.valueOf(BTailOperationEnum.ABSOLUTE),BTailOperationEnum.Absolute},
		  {Integer.valueOf(BTailOperationEnum.INTEGER),BTailOperationEnum.Integer},
		  {Integer.valueOf(BTailOperationEnum.FRACTIONAL),BTailOperationEnum.Fractional}};
  }
  
  @Test(dataProvider="enumOrdinal")
  public void testOperationByMakeOrdinal(Integer ordinal, BTailOperationEnum tOEnum) {
	  Assert.assertEquals(BTailOperationEnum.make(ordinal),tOEnum);	  
  }
  
  
  @DataProvider(name ="enumTag")
  public Object[][] getDataToTestEnumTag(){
	  return new Object[][] { {BTailOperationEnum.NoChange.getTag(),BTailOperationEnum.NoChange},
		  {BTailOperationEnum.Absolute.getTag(),BTailOperationEnum.Absolute},
		  {BTailOperationEnum.Integer.getTag(),BTailOperationEnum.Integer},
		  {BTailOperationEnum.Fractional.getTag(),BTailOperationEnum.Fractional}};
  }
  
  @Test(dataProvider="enumTag")
  public void testOperationByMakeTag(String tag, BTailOperationEnum fcuEnum) {
	  Assert.assertEquals(BTailOperationEnum.make(tag),fcuEnum);
  } 

}
