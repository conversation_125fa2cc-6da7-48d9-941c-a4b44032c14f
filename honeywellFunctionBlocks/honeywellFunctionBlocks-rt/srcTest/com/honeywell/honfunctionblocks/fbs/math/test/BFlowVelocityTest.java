/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BFlowVelocity;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Flow Velocity block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO 
 * <AUTHOR> - Lavanya B.
 * @since Feb 22, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BFlowVelocityTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BFlowVelocityTest(2979906276)1.0$ @*/
/* Generated Thu Feb 22 14:24:27 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowVelocityTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {
	  flowVelocityBlock = new BFlowVelocity();
	  executionParams = new BExecutionParams();

  }
  
  @AfterClass(alwaysRun=true)
  public void tearDown() {
	  flowVelocityBlock = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"press"}, {"kFactor"}, {"autoSetOffset"}, {"clearOffset"}, {"area"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"FLOW"}, {"OFFSET"}, {"VEL"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"pressure"}, {"Pressure"}, {"KFactor"}, {"AutoSetOffset"}, {"ClearOffset"}, {"Flow"}, {"Vel"}, {"Offset"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(flowVelocityBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(flowVelocityBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_flow_velocity.png");
	  BIcon actualFbIcon = flowVelocityBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  flowVelocityBlock.setIcon(expectedFbIcon);
	  actualFbIcon = flowVelocityBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInflowVelocityBlock(double snValue) {
	  flowVelocityBlock.setPress(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getPress().getValue(), snValue, 0.1);
	  
	  flowVelocityBlock.setKFactor(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getKFactor().getValue(), snValue, 0.1);	  
	  
	  flowVelocityBlock.setArea(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getArea().getValue(), snValue, 0.1);
	  
	  flowVelocityBlock.setOFFSET(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getOFFSET().getValue(), snValue, 0.1);
	  
	  flowVelocityBlock.setFLOW(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getFLOW().getValue(), snValue, 0.1);
	  
	  flowVelocityBlock.setVEL(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(flowVelocityBlock.getVEL().getValue(), snValue, 0.1);
  }
  
  @Test
  public void testSettingSamplesValuesToOffsetSlots() {
	  flowVelocityBlock.setAutoSetOffset(new BFiniteStatusBoolean(true));
	  Assert.assertEquals(flowVelocityBlock.getAutoSetOffset().getValue(),true);
	  
	  flowVelocityBlock.setClearOffset(new BFiniteStatusBoolean(true));
	  Assert.assertEquals(flowVelocityBlock.getClearOffset().getValue(),true);
	  
	  flowVelocityBlock.setAutoSetOffset(new BFiniteStatusBoolean(false));
	  Assert.assertEquals(flowVelocityBlock.getAutoSetOffset().getValue(),false);
	  
	  flowVelocityBlock.setClearOffset(new BFiniteStatusBoolean(false));
	  Assert.assertEquals(flowVelocityBlock.getClearOffset().getValue(),false);
  }
  
  
  @DataProvider(name="provideSequencedTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/FlowVelocity_TestData.csv");
  }
  
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideSequencedTestData")
  public void testFlowVelBlockWithSequenceOfTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
	  BFlowVelocity flowVelBlock = new BFlowVelocity();
	  
	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
		  List<String> inputs = iterator.next();
		  
		  if(seqNo == 416)
			  System.out.println();
			  

		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(flowVelBlock, BFlowVelocity.press.getName(), inputs.get(1));
		  setupNumericSlot(flowVelBlock, BFlowVelocity.kFactor.getName(), inputs.get(5));
		  setupNumericSlot(flowVelBlock, BFlowVelocity.autoSetOffset.getName(), inputs.get(2));
		  setupNumericSlot(flowVelBlock, BFlowVelocity.clearOffset.getName(), inputs.get(3));
		  setupNumericSlot(flowVelBlock, BFlowVelocity.area.getName(), inputs.get(4));

		  if(seqNo==1)	flowVelBlock.initHoneywellComponent(executionParams);
		  flowVelBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(flowVelBlock.getFLOW().getValue(), TestDataHelper.getDouble(inputs.get(6), 0.0d),
				  "Failed for FLOW at step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(flowVelBlock.getOFFSET().getValue(), TestDataHelper.getDouble(inputs.get(7), 0.0d),
				  "Failed for OFFSET at step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(flowVelBlock.getVEL().getValue(), TestDataHelper.getDouble(inputs.get(8), 0.0d),
				  "Failed for VEL at step #"+seqNo+" for input data "+inputs+"; ");
		  
	  }
	  
	  flowVelBlock = null;
  }

  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param flowVelocityBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BFlowVelocity flowVelBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = flowVelBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),flowVelBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				if(flowVelBlock.get("Link"+slotName)!=null) {
					flowVelBlock.remove("Link"+slotName);
				}
				flowVelBlock.add("Link"+slotName,conversionLink);				
				conversionLink.activate();
			}else{
				flowVelBlock.linkTo(nm1, nm1.getSlot("out"), flowVelBlock.getSlot(slotName));
			}			
			
			return;
		}

	  switch (slotName) {
	  case "press":
		  flowVelBlock.setPress(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "kFactor":
		  flowVelBlock.setKFactor(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;	
		  
	  case "autoSetOffset":
		  flowVelBlock.setAutoSetOffset(TestDataHelper.getFiniteStatusBoolean(inputValue));
		  break;
		  
	  case "clearOffset":
		  flowVelBlock.setClearOffset(TestDataHelper.getFiniteStatusBoolean(inputValue));
		  break;
	  
	  case "area":
		  flowVelBlock.setArea(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  
  //@Test
	public void testLinkRules() {
		BFlowVelocity flowVelBlock = new BFlowVelocity();
		checkOutgoingLink(flowVelBlock, BFlowVelocity.press, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.kFactor, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.autoSetOffset, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.clearOffset, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.area, false);
		
		checkOutgoingLink(flowVelBlock, BFlowVelocity.FLOW, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.OFFSET, false);
		checkOutgoingLink(flowVelBlock, BFlowVelocity.VEL, false);
	}

	private void checkOutgoingLink(BFlowVelocity block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = flowVelocityBlock.checkLink(flowVelocityBlock, flowVelocityBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	@Test
	public void testOutToIslotsLinkRules() {
		BFlowVelocity src = new BFlowVelocity();
		BFlowVelocity tgt = new BFlowVelocity();
		
		LinkCheckUtil.checkValidLink(src, src.getSlot(BFlowVelocity.FLOW.getName()), tgt, tgt.getSlot(BFlowVelocity.area.getName()));
		LinkCheckUtil.checkValidLink(src, src.getSlot(BFlowVelocity.OFFSET.getName()), tgt, tgt.getSlot(BFlowVelocity.area.getName()));
		LinkCheckUtil.checkValidLink(src, src.getSlot(BFlowVelocity.VEL.getName()), tgt, tgt.getSlot(BFlowVelocity.area.getName()));
	}
	
	@Test
	public void testInToOutslotsLinkRules() {
		BFlowVelocity src = new BFlowVelocity();
		BFlowVelocity tgt = new BFlowVelocity();
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.FLOW.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.OFFSET.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.VEL.getName()));
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.kFactor.getName()), tgt, tgt.getSlot(BFlowVelocity.FLOW.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.kFactor.getName()), tgt, tgt.getSlot(BFlowVelocity.OFFSET.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.kFactor.getName()), tgt, tgt.getSlot(BFlowVelocity.VEL.getName()));
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.autoSetOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.FLOW.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.autoSetOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.OFFSET.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.autoSetOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.VEL.getName()));
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.clearOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.FLOW.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.clearOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.OFFSET.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.clearOffset.getName()), tgt, tgt.getSlot(BFlowVelocity.VEL.getName()));
		
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.FLOW.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.OFFSET.getName()));
		LinkCheckUtil.checkInvalidLink(src, src.getSlot(BFlowVelocity.area.getName()), tgt, tgt.getSlot(BFlowVelocity.VEL.getName()));
	}
	

 
	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BFlowVelocity flowVelBlock = new BFlowVelocity();
		flowVelBlock.setPress(new BHonStatusNumeric(-0.00209));
		flowVelBlock.setKFactor(new BHonStatusNumeric(Double.POSITIVE_INFINITY));
		flowVelBlock.setAutoSetOffset(new BFiniteStatusBoolean(true));
		flowVelBlock.setClearOffset(new BFiniteStatusBoolean(false));
		flowVelBlock.setArea(new BHonStatusNumeric(0.54));
		executionParams.setIterationInterval(1000);
		flowVelBlock.initHoneywellComponent(executionParams);		
		for(int i=0; i<5000; i++) {
			flowVelBlock.executeHoneywellComponent(executionParams);
			Assert.assertEquals(flowVelBlock.getFLOW().getValue(), Double.POSITIVE_INFINITY,0.1, "Failed in iteration #"+i);
			Assert.assertEquals(flowVelBlock.getOFFSET().getValue(), -0.002425,0.1, "Failed in iteration #"+i);
			Assert.assertEquals(flowVelBlock.getVEL().getValue(), Double.POSITIVE_INFINITY,0.1, "Failed in iteration #"+i);
		}

		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("Logarithm", flowVelBlock);
			BFlowVelocity flowVelocitySaved = (BFlowVelocity) bogUtil.getComponentFromBogFile(bogFile);

			Assert.assertEquals(flowVelocitySaved.getPress().getValue(), -0.00209, 0.1,"Failed to verify limit block input X after restart");
			Assert.assertEquals(flowVelocitySaved.getKFactor().getValue(), Double.POSITIVE_INFINITY, 0.1,"Failed to verify limit block input loLimit after restart");
			Assert.assertEquals(flowVelocitySaved.getAutoSetOffset().getValue(), true, "Failed to verify limit block input hiLimit after restart");
			Assert.assertEquals(flowVelocitySaved.getClearOffset().getValue(), false, "Failed to verify limit block output after restart");
			Assert.assertEquals(flowVelocitySaved.getArea().getValue(), 0.54, 0.1,"Failed to verify limit block output after restart");
			
			Assert.assertEquals(flowVelocitySaved.getFLOW().getValue(), Double.POSITIVE_INFINITY, 0.1,"Failed to verify limit block output after restart");
			Assert.assertEquals(flowVelocitySaved.getOFFSET().getValue(), -0.002425, 0.1,"Failed to verify limit block output after restart");
			Assert.assertEquals(flowVelocitySaved.getVEL().getValue(), Double.POSITIVE_INFINITY, 0.1,"Failed to verify limit block output after restart");
		} catch (IOException e) {
			e.printStackTrace();
		}
		flowVelBlock = null;
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = flowVelocityBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BFlowVelocity.press.getName(), BFlowVelocity.kFactor.getName(), BFlowVelocity.autoSetOffset.getName(), BFlowVelocity.clearOffset.getName(), BFlowVelocity.area.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = flowVelocityBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BFlowVelocity.OFFSET.getName(),BFlowVelocity.FLOW.getName(),BFlowVelocity.VEL.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  private BFlowVelocity flowVelocityBlock;
  private BExecutionParams executionParams;


}
