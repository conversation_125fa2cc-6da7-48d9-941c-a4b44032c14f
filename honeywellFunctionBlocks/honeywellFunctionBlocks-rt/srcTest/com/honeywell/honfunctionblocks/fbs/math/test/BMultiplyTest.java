/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BMultiply;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 *
 * Testing of Multiply block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TBD
 * <AUTHOR> - Suresh Khatri
 * @since Jan 29, 2018
 */

@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BMultiplyTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BMultiplyTest(2979906276)1.0$ @*/
/* Generated Mon Jan 29 20:58:15 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMultiplyTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		multiply = getMultiply();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass
	public void tearDown() {
		multiply = null;
		executionParams = null;
	}
	
	private Object[][] getInputSlotNames() {
		return new Object[][]{{"x1"}, {"x2"}};
	}
	
	private Object[][] getOutputSlotNames() {
		return new Object[][]{{"Y"}};
	}
	
	private Object[][] getConfigurationSlotNames() {
		return new Object[][]{{"ignoreInvalidInput"}, {"tailOperation"}};
	}
	
	private Object[][] getExecOrderSlotNames() {
		return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	}
	
	@DataProvider(name="allSlotNames")
	public Object[][] getAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getConfigurationSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  
	}
	
	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(multiply.getSlot(slotName));
	}
	
	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"Invalid1"}, {"Invalid2"}};
	}
	
	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(multiply.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_multiply.png");
		BIcon actualFbIcon = multiply.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		multiply.setIcon(expectedFbIcon);
		actualFbIcon = multiply.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	}
	
	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		multiply.setX1(new BHonStatusNumeric(value));
		Assert.assertEquals(multiply.getX1().getValue(), value, 0.1);
		
		multiply.setX2(new BHonStatusNumeric(value));
		Assert.assertEquals(multiply.getX2().getValue(), value, 0.1);

		multiply.setY(new BHonStatusNumeric(value));
		Assert.assertEquals(multiply.getY().getValue(), value, 0.1);
		
		
		multiply.setIgnoreInvalidInput(true);
		Assert.assertEquals(multiply.getIgnoreInvalidInput(), true);
  
		multiply.setIgnoreInvalidInput(false);
		Assert.assertEquals(multiply.getIgnoreInvalidInput(), false);	  
	}
	
	//@Test(groups = { "testLinkRules" })
	public void testLinkRules() {
		BMultiply multiplyB = getMultiply();
		checkOutgoingLink(multiplyB, BMultiply.x1, false);
		checkOutgoingLink(multiplyB, BMultiply.x2, false);
		checkOutgoingLink(multiplyB, BMultiply.ignoreInvalidInput, false);
		checkOutgoingLink(multiplyB, BMultiply.tailOperation, false);
	}

	private void checkOutgoingLink(BMultiply block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = multiply.checkLink(multiply, multiply.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	@DataProvider(name = "multiplyTestData")
	public Object[][] getMultiplyTestData() throws FileNotFoundException{
	    	return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Multiply_TestData.csv");
	}

	@Test(dataProvider = "multiplyTestData")
	 public void testMultiplyBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BMultiply mulBlock = new BMultiply();
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(mulBlock, BMultiply.x1.getName(), inputs.get(2));
		  setupNumericSlot(mulBlock, BMultiply.x2.getName(), inputs.get(3));
		  mulBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
		  mulBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(4), 0)));
		  
		  mulBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(mulBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(5), 0.0d));	  
		  mulBlock = null;
	  }
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = multiply.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BMultiply.ignoreInvalidInput.getName(), BMultiply.tailOperation.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = multiply.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BMultiply.x1.getName(), BMultiply.x2.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = multiply.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BMultiply.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	
	/**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param subtractBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BMultiply multiplyBlock, final String slotName, final String inputValue){
		  if(TestDataHelper.isConnected(inputValue)){
			  BNumericConst nm1 = new BNumericConst();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				Type srcType = nm1.getOut().getType();
				Type targetType = multiplyBlock.getProperty(slotName).getType();		
				if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
					BConverter converter = null;
					if(multiplyBlock.get("Link"+slotName)!=null) {
						multiplyBlock.remove("Link"+slotName);
					}
					converter = new BStatusNumericToHonStatusNumeric();
					BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),multiplyBlock.getSlot(slotName),converter);
					conversionLink.setEnabled(true);
					multiplyBlock.add("Link"+slotName,conversionLink );				
					conversionLink.activate();
					nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				}else{
					multiplyBlock.linkTo(nm1, nm1.getSlot("out"), multiplyBlock.getSlot(slotName));
				}
		  }

		  switch (slotName) {
		  case "x1":
			  multiplyBlock.setX1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;

		  case "x2":
			  multiplyBlock.setX2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;	
		  }
	  }
	
	
	private BMultiply getMultiply() {
		BMultiply multiply = new BMultiply();
		try {
			multiply.initHoneywellComponent(null);
		} catch (BlockInitializationException e) {
			e.printStackTrace();
		}
		return multiply;
	}
	
	private BMultiply multiply;
	private BExecutionParams executionParams;
  
}
