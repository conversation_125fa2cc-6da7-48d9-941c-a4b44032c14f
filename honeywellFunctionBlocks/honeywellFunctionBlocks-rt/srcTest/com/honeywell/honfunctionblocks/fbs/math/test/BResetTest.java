/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.math.BReset;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Reset FB as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 21, 2018
 */


@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
@NiagaraType

public class BResetTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BResetTest(2979906276)1.0$ @*/
/* Generated Wed Feb 21 20:28:40 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BResetTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		resetBlock = new BReset();
		executionParams = new BExecutionParams();

	}

	@AfterClass(alwaysRun=true)
	public void tearDown() {
		resetBlock = null;
		executionParams = null;
	}

	@DataProvider(name = "provideInSlotNames")
	public Object[][] createInputSlotNames() {
		return new Object[][] { { "input" }, { "sensor" }, { "zeroPctResetVal" }, { "hundredPctResetVal" }, { "resetAmount" } };
	}

	@DataProvider(name = "provideOutputSlotNames")
	public Object[][] createOutputSlotNames() {
		return new Object[][] { { "OUTPUT" } };
	}

	@DataProvider(name = "provideMiscSlotNames")
	public Object[][] createExecOrderSlotName() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	}

	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "In" }, { "onValue" }, { "OnVal" }, { "OffVal" }, { "OffValue" }, { "MinOn" } };
	}

	@Test(dataProvider = "provideInvalidSlotNames")
	public void testInvalidSlots(String slotName) {
		Assert.assertNull(resetBlock.getSlot(slotName));
	}

	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		Assert.assertNotNull(resetBlock.getSlot(slotName));
	}

	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_reset.png");
		BIcon actualFbIcon = resetBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		resetBlock.setIcon(expectedFbIcon);
		actualFbIcon = resetBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "provideSampleValues")
	public Object[][] sampleValues() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }, { 2.2250738585072014E-308 }, { -1.7976931348623157e+308 }, { Double.NEGATIVE_INFINITY }, { Double.POSITIVE_INFINITY }, { Double.NaN } };
	}

	@Test(dataProvider = "provideSampleValues")
	public void testSettingValueInresetBlock(double snValue) {
		resetBlock.setInput(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getInput().getValue(), snValue, 0.1);

		resetBlock.setSensor(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getSensor().getValue(), snValue, 0.1);

		resetBlock.setZeroPctResetVal(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getZeroPctResetVal().getValue(), snValue, 0.1);

		resetBlock.setHundredPctResetVal(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getHundredPctResetVal().getValue(), snValue, 0.1);

		resetBlock.setResetAmount(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getResetAmount().getValue(), snValue, 0.1);

		resetBlock.setOUTPUT(new BHonStatusNumeric(snValue));
		Assert.assertEquals(resetBlock.getOUTPUT().getValue(), snValue, 0.1);
	}

	@DataProvider(name = "provideTestData")
	public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Reset_TestData.csv");
	}

	@Test(dataProvider = "provideTestData")
	public void testResetBlockWithTestData(List<String> inputs) throws BlockExecutionException, FileNotFoundException {
		BReset resetBlock = new BReset();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(1), 1000));
		setupNumericSlot(resetBlock, BReset.input.getName(), inputs.get(2));
		setupNumericSlot(resetBlock, BReset.sensor.getName(), inputs.get(3));
		setupNumericSlot(resetBlock, BReset.zeroPctResetVal.getName(), inputs.get(4));
		setupNumericSlot(resetBlock, BReset.hundredPctResetVal.getName(), inputs.get(5));
		setupNumericSlot(resetBlock, BReset.resetAmount.getName(), inputs.get(6));
		resetBlock.executeHoneywellComponent(executionParams);
		Assert.assertEquals(resetBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(7), 0.0d), ""+inputs);
		resetBlock = null;
	}

	//@Test
	public void testLinkRules() {
		BReset resetBlock = new BReset();
		checkOutgoingLink(resetBlock, BReset.input, false);
		checkOutgoingLink(resetBlock, BReset.sensor, false);
		checkOutgoingLink(resetBlock, BReset.zeroPctResetVal, false);
		checkOutgoingLink(resetBlock, BReset.hundredPctResetVal, false);
		checkOutgoingLink(resetBlock, BReset.resetAmount, false);
	}

	//@Test
	public void testLinkRules1() {
		BReset target = new BReset();
		BMaximum source = new BMaximum();		
		
		LinkCheck linkCheck = source.checkLink(target, target.getSlot("input"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("sensor"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("zeroPctResetVal"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("hundredPctResetVal"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("resetAmount"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("input"), null);
		Assert.assertTrue(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("OUTPUT"), null);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = resetBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BReset.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	public void setupNumericSlot(BReset resetBlock, final String slotName, final String inputValue) {
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = resetBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
                 converter = new BStatusNumericToHonStatusNumeric();
                 BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),resetBlock.getSlot(slotName),converter);
                 conversionLink.setEnabled(true);
                 resetBlock.add("Link?",conversionLink );                    
                 conversionLink.activate();
                 nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
            } else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),resetBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				resetBlock.add("Link?", conversionLink);				
				conversionLink.activate();
			}else{
				resetBlock.linkTo(nm1, nm1.getSlot("out"), resetBlock.getSlot(slotName));
			}			
			return;
		}

		switch (slotName) {
			case "input":
				resetBlock.setInput(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "sensor":
				resetBlock.setSensor(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "zeroPctResetVal":
				resetBlock.setZeroPctResetVal(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "hundredPctResetVal":
				resetBlock.setHundredPctResetVal(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "resetAmount":
				resetBlock.setResetAmount(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
		}
	}

	private void checkOutgoingLink(BReset block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = resetBlock.checkLink(resetBlock, resetBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	private BReset resetBlock;
	private BExecutionParams executionParams;

  
}
