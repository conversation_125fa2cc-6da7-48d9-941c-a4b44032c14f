/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BExponential;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 *
 * Testing of Exponential block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TBD
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 24, 2018
 */

@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BExponentialTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BExponentialTest(2979906276)1.0$ @*/
/* Generated Mon Feb 16 19:58:15 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BExponentialTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		exponentialBlock = new BExponential();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass(alwaysRun=true)
	public void tearDown() {
		exponentialBlock = null;
		executionParams = null;
	}

	private Object[] getInputSlotNames() {
		return new Object[]{"x", "y", "negInvalid"};
	}

	private Object[] getOutputSlotNames() {
		return new Object[]{"Z"};
	}

	private Object[] getConfigurationSlotNames() {
		return new Object[]{"tailOperation"};
	}

	private Object[] getExecOrderSlotNames() {
		return new Object[] {"ExecutionOrder", "toolVersion"};	  
	}

	@DataProvider(name="allSlotNames")
	public Object[] getAllSlotNames() {
		List<Object> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getConfigurationSlotNames()));
		slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		return slotArrayList.toArray(new Object[slotArrayList.size()]);
	}

	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(exponentialBlock.getSlot(slotName));
	}

	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"X"}, {"NegInvalid"}, {"ignoreInvalidInput"}, {"z"}, {"output"}, {"OUTPUT"}};
	}

	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(exponentialBlock.getSlot(slotName));
	}

	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_exponential.png");
		BIcon actualFbIcon = exponentialBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		exponentialBlock.setIcon(expectedFbIcon);
		actualFbIcon = exponentialBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			{-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	}

	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		exponentialBlock.setX(new BHonStatusNumeric(value));
		Assert.assertEquals(exponentialBlock.getX().getValue(), value, 0.1);

		exponentialBlock.setY(new BHonStatusNumeric(value));
		Assert.assertEquals(exponentialBlock.getY().getValue(), value, 0.1);
		
		exponentialBlock.setZ(new BHonStatusNumeric(value));
		Assert.assertEquals(exponentialBlock.getZ().getValue(), value, 0.1);

		exponentialBlock.setNegInvalid(new BHonStatusBoolean(true, BStatus.ok));
		Assert.assertEquals(exponentialBlock.getNegInvalid().getBoolean(), true);

		exponentialBlock.setNegInvalid(new BHonStatusBoolean(false, BStatus.ok));
		Assert.assertEquals(exponentialBlock.getNegInvalid().getBoolean(), false);	  
	}

	//@Test(groups = { "testLinkRules" })
	public void testLinkRules() {
		BExponential squareRootB = new BExponential();
		checkOutgoingLink(squareRootB, BExponential.x, false);
		checkOutgoingLink(squareRootB, BExponential.y, false);
		checkOutgoingLink(squareRootB, BExponential.negInvalid, false);
		checkOutgoingLink(squareRootB, BExponential.tailOperation, false);
	}

	private void checkOutgoingLink(BExponential block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = exponentialBlock.checkLink(exponentialBlock, exponentialBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BExponential tempExponential = new BExponential();
		executionParams.setIterationInterval(1000);
		tempExponential.setX(new BHonStatusNumeric(5));
		tempExponential.setY(new BHonStatusNumeric(3));
		tempExponential.setNegInvalid(new BHonStatusBoolean(false, BStatus.ok));
		tempExponential.setTailOperation(BTailOperationEnum.DEFAULT);
		for (int j = 0; j < 5000; j++) {
			tempExponential.executeHoneywellComponent(executionParams);
		}
		
		Assert.assertEquals(tempExponential.getZ().getValue(), 125, 0.1);
		
		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("Exponential", tempExponential);
			BExponential sqrtSaved = (BExponential) bogUtil.getComponentFromBogFile(bogFile);
			
			Assert.assertEquals(sqrtSaved.getX().getValue(), 0, 5, "Failed to verify x");
			Assert.assertEquals(sqrtSaved.getY().getValue(), 3, 0.1, "Failed to verify y");
			Assert.assertEquals(sqrtSaved.getNegInvalid().getBoolean(), false, "Failed to verify negInvalid");
			Assert.assertEquals(sqrtSaved.getZ().getValue(), 0.0, "Failed to verify Z");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@DataProvider(name = "exponentialTestData")
	public Object[][] getExponentialTestData() throws FileNotFoundException{
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Exponential_TestData.csv");
	}

	@Test(dataProvider = "exponentialTestData")
	public void testExponentialBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		BExponential exponentialBlock = new BExponential();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		setupNumericSlot(exponentialBlock, BExponential.x.getName(), inputs.get(1));
		setupNumericSlot(exponentialBlock, BExponential.y.getName(), inputs.get(2));
		setupNumericSlot(exponentialBlock, BExponential.negInvalid.getName(), inputs.get(3));
		exponentialBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(4), 0)));

		exponentialBlock.executeHoneywellComponent(executionParams);
		Assert.assertEquals(exponentialBlock.getZ().getValue(), TestDataHelper.getDouble(inputs.get(5), 0.0d), 0.1);	  
		exponentialBlock = null;
	}


	/**
	 * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	 * @param subtractBlock
	 * @param slotName
	 * @param inputValue
	 */
	public void setupNumericSlot(BExponential exponentialBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = exponentialBlock.getProperty(slotName).getType();		
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusBoolean.TYPE)) {
				BConverter converter = null;
				converter = new BStatusNumericToStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),exponentialBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				exponentialBlock.add("Link?",conversionLink );				
				conversionLink.activate();
			}else{
				exponentialBlock.linkTo(nm1, nm1.getSlot("out"), exponentialBlock.getSlot(slotName));
			}
			
			return;
		}

		switch (slotName) {
		case "x":
			exponentialBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "y":
			exponentialBlock.setY(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;

		case "negInvalid":
			exponentialBlock.setNegInvalid(TestDataHelper.getHonStatusBoolean(inputValue));
			break;	
		}
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = exponentialBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BExponential.tailOperation.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = exponentialBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BExponential.x.getName(),BExponential.y.getName(),BExponential.negInvalid.getName(),BExponential.tailOperation.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = exponentialBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BExponential.Z.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}

	private BExponential exponentialBlock;
	private BExecutionParams executionParams;

}
