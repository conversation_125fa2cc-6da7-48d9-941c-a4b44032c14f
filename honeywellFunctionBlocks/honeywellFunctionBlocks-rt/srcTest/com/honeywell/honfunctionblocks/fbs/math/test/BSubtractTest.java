/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.math.BSubtract;
import com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Subtract block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Lavanya B.
 * @since Jan 29, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BSubtractTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BSubtractTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:42 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSubtractTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @BeforeClass
  public void setUp() {
	  subtractBlock = new BSubtract();
	  executionParams = new BExecutionParams();

  }
  
  @AfterClass
  public void tearDown() {
	  subtractBlock = null;
	  executionParams = null;
  }
  
  
  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x1"}, {"x2"}, {"ignoreInvalidInput"}, {"tailOperation"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"In"}, {"onValue"}, {"OnVal"}, {"OffVal"}, {"OffValue"}, {"MinOn"}, {"InvalidFlag"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(subtractBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(subtractBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "math_func_subtract.png");
	  BIcon actualFbIcon = subtractBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  subtractBlock.setIcon(expectedFbIcon);
	  actualFbIcon = subtractBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
		  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInsubtractBlock(double snValue) {
	  subtractBlock.setX1(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(subtractBlock.getX1().getValue(), snValue, 0.1);
	  
	  subtractBlock.setX2(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(subtractBlock.getX2().getValue(), snValue, 0.1);	  

	  subtractBlock.setY(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(subtractBlock.getY().getValue(), snValue, 0.1);
	  
	  subtractBlock.setIgnoreInvalidInput(true);
	  Assert.assertEquals(subtractBlock.getIgnoreInvalidInput(), true);
	  
	  subtractBlock.setIgnoreInvalidInput(false);
	  Assert.assertEquals(subtractBlock.getIgnoreInvalidInput(), false);	  
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/math/test/Subtract_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testSubtractBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BSubtract subtractBlock = new BSubtract();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(subtractBlock, BSubtract.x1.getName(), inputs.get(2));
	  setupNumericSlot(subtractBlock, BSubtract.x2.getName(), inputs.get(3));
	  subtractBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
	  subtractBlock.setTailOperation(BTailOperationEnum.make(TestDataHelper.getInt(inputs.get(4), 0)));
	  
	  subtractBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(subtractBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(5), 0.0d), Arrays.toString(inputs.toArray()));	  
	  subtractBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param subtractBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BSubtract subtractBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  subtractBlock.linkTo(nm1, nm1.getSlot("out"), subtractBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "x1":
		  subtractBlock.setX1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "x2":
		  subtractBlock.setX2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;	
	  }
  }
  
	//@Test
	public void testLinkRules() {
		BSubtract subtractBlock = new BSubtract();
		checkOutgoingLink(subtractBlock, BSubtract.x1, false);
		checkOutgoingLink(subtractBlock, BSubtract.x2, false);
		checkOutgoingLink(subtractBlock, BSubtract.ignoreInvalidInput, false);
		checkOutgoingLink(subtractBlock, BSubtract.tailOperation, false);
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = subtractBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BSubtract.ignoreInvalidInput.getName(), BSubtract.tailOperation.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = subtractBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BSubtract.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}

	private void checkOutgoingLink(BSubtract block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = subtractBlock.checkLink(subtractBlock, subtractBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

  
  private BSubtract subtractBlock;
  private BExecutionParams executionParams;

}
