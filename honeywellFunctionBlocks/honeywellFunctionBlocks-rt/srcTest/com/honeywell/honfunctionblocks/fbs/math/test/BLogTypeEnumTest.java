/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.math.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.math.BLogTypeEnum;

/**
 * Implementation of Logarithm block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - <PERSON><PERSON><PERSON>.
 * @since Feb 16, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BLogTypeEnumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.math.test.BLogTypeEnumTest(**********)1.0$ @*/
/* Generated Fri Feb 16 16:33:19 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLogTypeEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
 	public Object[][] getDataToTestEnumOrdinal() {
 	  return new Object[][] {{Integer.valueOf(BLogTypeEnum.BASE_10),BLogTypeEnum.Base10},
 		  {Integer.valueOf(BLogTypeEnum.NATURAL),BLogTypeEnum.Natural}};
 	}

 	@Test(dataProvider = "enumOrdinal")
 	public void testOperationByMakeOrdinal(Integer ordinal, BLogTypeEnum oe) {
 		Assert.assertEquals(BLogTypeEnum.make(ordinal.intValue()), oe);
 	}
 	
 	@DataProvider(name = "enumTag")
 	public Object[][] getDataToTestEnumTag() {
 		return new Object[][] {{BLogTypeEnum.Base10.getTag(),BLogTypeEnum.Base10},
 	 		  {BLogTypeEnum.Natural.getTag(),BLogTypeEnum.Natural}};
 	}
 	
 	@Test(dataProvider = "enumTag")
 	public void testOperationByMakeTag(String tag, BLogTypeEnum oe) {
 		Assert.assertEquals(BLogTypeEnum.make(tag), oe);
 	}
}
