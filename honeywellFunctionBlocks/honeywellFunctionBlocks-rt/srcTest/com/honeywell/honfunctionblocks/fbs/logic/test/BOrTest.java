/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.logic.BLogicBlock;
import com.honeywell.honfunctionblocks.fbs.logic.BOr;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of Or block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-257
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 28, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BOrTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.logic.test.BOrTest(2979906276)1.0$ @*/
/* Generated Thu Nov 30 17:38:31 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOrTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  orBlock = new BOr();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  orBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"trueDelay"}, {"falseDelay"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"In1"}, {"TrueDelay"}, {"FalseDelay"}, {"out"}, {"IN1"}, {"TRUEDELAY"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(orBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(orBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "or.png");
	  BIcon actualFbIcon = orBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  orBlock.setIcon(expectedFbIcon);
	  actualFbIcon = orBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInOrBlock(double snValue) {
	  orBlock.setIn1(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true)); 
	  orBlock.getIn1().setNegate(true);
	  Assert.assertEquals(orBlock.getIn1().getValue(), false);
	  Assert.assertEquals(orBlock.getIn1().getNegate(), true);
	  
	  orBlock.setIn2(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  orBlock.getIn2().setNegate(true);
	  Assert.assertEquals(orBlock.getIn2().getValue(), false);
	  Assert.assertEquals(orBlock.getIn2().getNegate(), true);
	  
	  orBlock.setIn3(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
	  orBlock.getIn3().setNegate(true);
	  Assert.assertEquals(orBlock.getIn3().getValue(), true);
	  Assert.assertEquals(orBlock.getIn3().getNegate(), true);
	  
	  orBlock.setIn4(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
	  orBlock.getIn4().setNegate(true);
	  Assert.assertEquals(orBlock.getIn4().getValue(), true);
	  Assert.assertEquals(orBlock.getIn4().getNegate(), true);
	  
	  orBlock.setIn5(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  orBlock.getIn5().setNegate(true);
	  Assert.assertEquals(orBlock.getIn5().getValue(), false);
	  Assert.assertEquals(orBlock.getIn5().getNegate(), true);
	  
	  orBlock.setIn6(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  orBlock.getIn6().setNegate(true);
	  Assert.assertEquals(orBlock.getIn6().getValue(), false);
	  Assert.assertEquals(orBlock.getIn6().getNegate(), true);
	  
	  orBlock.setTrueDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(orBlock.getTrueDelay().getValue(), snValue, 0.1);
	  orBlock.setFalseDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(orBlock.getFalseDelay().getValue(), snValue, 0.1);
	  
	  orBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, false)); 
	  orBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(orBlock.getOUTPUT().getValue(), false);
	  Assert.assertEquals(orBlock.getOUTPUT().getNegate(), false);
	  
	  orBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, true));
	  orBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(orBlock.getOUTPUT().getValue(), false);
	  Assert.assertEquals(orBlock.getOUTPUT().getNegate(), true);
	  
	  orBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, false));
	  orBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(orBlock.getOUTPUT().getValue(), true);
	  Assert.assertEquals(orBlock.getOUTPUT().getNegate(), false);
	  
	  orBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  orBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(orBlock.getOUTPUT().getValue(), true);
	  Assert.assertEquals(orBlock.getOUTPUT().getNegate(), true);
  }
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/logic/test/Or_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testOrBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BOr orBlock = new BOr();

	  //Set Given values to all the slot including output slot as trueDelay/falseDelay depends on output value
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(orBlock, BLogicBlock.in1.getName(), inputs.get(1), inputs.get(2));
	  setupNumericSlot(orBlock, BLogicBlock.in2.getName(), inputs.get(3), inputs.get(4));
	  setupNumericSlot(orBlock, BLogicBlock.in3.getName(), inputs.get(5), inputs.get(6));
	  setupNumericSlot(orBlock, BLogicBlock.in4.getName(), inputs.get(7), inputs.get(8));
	  setupNumericSlot(orBlock, BLogicBlock.in5.getName(), inputs.get(9), inputs.get(10));
	  setupNumericSlot(orBlock, BLogicBlock.in6.getName(), inputs.get(11),inputs.get(12));
	  setupNumericSlot(orBlock, BLogicBlock.trueDelay.getName(), inputs.get(13), null);
	  setupNumericSlot(orBlock, BLogicBlock.falseDelay.getName(), inputs.get(14), null);
	  orBlock.getOUTPUT().setNegate(TestDataHelper.getBoolean(inputs.get(16)));

	  int waitTime = 0;
	  switch (TestDataHelper.getInt(inputs.get(18), 0)) {
	  case 1:
		  setupNumericSlot(orBlock, BLogicBlock.OUTPUT.getName(), inputs.get(15), inputs.get(16));
		  waitTime = (int) orBlock.getTrueDelay().getValue();
		  break;

	  case 2:
		  setupNumericSlot(orBlock, BLogicBlock.OUTPUT.getName(), inputs.get(15), inputs.get(16));
		  waitTime = (int) orBlock.getFalseDelay().getValue();
		  break;
	  }

	  if(waitTime>0)
		  Reporter.log("Waiting for "+waitTime+"sec as per TestData waitEnum="+TestDataHelper.getInt(inputs.get(18), 0));
	  for (int i = 0; i < waitTime; i++) {
		  orBlock.executeHoneywellComponent(executionParams);
		  Assert.assertNotEquals(orBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)), "Reached the value in loop#"+i);
	  }

	  orBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(orBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)));

	  orBlock = null;
  }
  
  //@Test(groups = { "testLinkRules" })
  public void testLinkRules() {
	  BOr tempBlock = new BOr();
	  checkOutgoingLink(tempBlock, BLogicBlock.in1, false);   
	  checkOutgoingLink(tempBlock, BLogicBlock.in2, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in3, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in4, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in5, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in6, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.trueDelay, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.falseDelay, false);
  }

  private void checkOutgoingLink(BOr tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = orBlock.checkLink(orBlock, orBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param orBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BOr orBlock, final String slotName, final String inputValue, final String negateString){
	  boolean negate=false;
	  if(negateString!=null) {
		  negate = TestDataHelper.getBoolean(negateString);

		  //Set negate value
		  INegatableStatusValue nsb = (INegatableStatusValue) orBlock.get(slotName);
		  nsb.setNegate(negate);
	  }

	  if(TestDataHelper.isConnected(inputValue)){
		  //Create numeric constant and link to given slotName. Set the given value to the numeric constant
		  BNumericConst nm1 = new BNumericConst();
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  Type srcType = nm1.getOut().getType();
		  Type targetType = orBlock.getProperty(slotName).getType();			
		  BConverter converter = null;
		  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
			  converter = new BStatusNumericToFiniteStatusBoolean();
			  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),orBlock.getSlot(slotName),converter);
			  conversionLink.setEnabled(true);
			  orBlock.add("Link?",conversionLink );				
			  conversionLink.activate();
		  }else{
			  orBlock.linkTo(nm1, nm1.getSlot("out"), orBlock.getSlot(slotName));
		  }
		  
		  return;
	  }

	  switch (slotName) {
	  case "in1":
		  orBlock.setIn1(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in2":
		  orBlock.setIn2(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in3":
		  orBlock.setIn3(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in4":
		  orBlock.setIn4(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in5":
		  orBlock.setIn5(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in6":
		  orBlock.setIn6(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "trueDelay":
		  orBlock.setTrueDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "falseDelay":
		  orBlock.setFalseDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "OUTPUT":
		  orBlock.setOUTPUT(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
		  break;
		  
	  default:
		  break;
	  }
  }
  
  BOr orBlock;
  BExecutionParams executionParams;

}
