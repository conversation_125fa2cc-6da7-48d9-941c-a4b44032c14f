/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BIcon;
import javax.baja.sys.BRelTime;
import javax.baja.sys.BStation;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;
import javax.baja.util.BFolder;

import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.FBSlotConstants;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFBOverrideAction;
import com.honeywell.honfunctionblocks.fbs.BFBOverrideProperties;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.logic.BAnd;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of And block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-256
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 27, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BAndTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.logic.test.BAndTest(2979906276)1.0$ @*/
/* Generated Mon Nov 27 17:03:16 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAndTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	  @BeforeClass(alwaysRun=true)
	public void setUp() throws Exception {
		andBlock = new BAnd();
		executionParams = new BExecutionParams();
		stationHandler = BTest.createTestStation();
		stationHandler.startStation();
		station = stationHandler.getStation();

		stationHandler.startStation();
	}
	  
	  @AfterClass
	public void tearDown() {
		andBlock = null;
		executionParams = null;
		andBlockOverride = null;
		fbContainer = null;
		stationHandler.stopStation();
		stationHandler.releaseStation();
		
		station = null;

		stationHandler = null;
	}

	  @DataProvider(name="provideInSlotNames")
	  public Object[][] createInputSlotNames() {
		  return new Object[][]{{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"trueDelay"}, {"falseDelay"}};	  
	  }
	  
	  @DataProvider(name="provideOutputSlotNames")
	  public Object[][] createOutputSlotNames() {
		  return new Object[][] {{"OUTPUT"}};
	  }
	  
	  @DataProvider(name="provideMiscSlotNames")
	  public Object[][] createExecOrderSlotName() {
		  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};
	  }
	  
	  @DataProvider(name="provideAllSlotNames")
	  public Object[][] createAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  }
	  
	  @DataProvider(name = "provideInvalidSlotNames")
	  public Object[][] invalidSlotNames() {
		  return new Object[][]{{"In1"}, {"TrueDelay"}, {"FalseDelay"}, {"out"}, {"IN1"}, {"TRUEDELAY"}};
	  }
	  
	  @Test(dataProvider="provideInvalidSlotNames")
	  public void testInvalidSlots(String slotName){
		  Assert.assertNull(andBlock.getSlot(slotName));
	  }  
	  
	  @Test(dataProvider="provideAllSlotNames")
	  public void testSlotAvailability(String slotName) {
		  Assert.assertNotNull(andBlock.getSlot(slotName));
	  }
	  
	  @Test(groups={"testIconSlot"})
	  public void testIconSlot(){
		  //check if correct icon is used for AIA
		  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		  BIcon actualFbIcon = andBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);

		  //check if new icon can be set on AIA to update modified state
		  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "or.png");
		  andBlock.setIcon(expectedFbIcon);
		  actualFbIcon = andBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);
	  }
	  
	  @DataProvider(name = "provideSampleValues")
	  public Object[][] sampleValues() {
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
	  }

	  @Test(dataProvider="provideSampleValues")
	  public void testSettingValueInAndBlock(double snValue) {
		  andBlock.setIn1(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  andBlock.getIn1().setNegate(true);
		  Assert.assertEquals(andBlock.getIn1().getValue(), false);
		  Assert.assertEquals(andBlock.getIn1().getNegate(), true);
		  
		  andBlock.setIn2(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  andBlock.getIn2().setNegate(true);
		  Assert.assertEquals(andBlock.getIn2().getValue(), false);
		  Assert.assertEquals(andBlock.getIn2().getNegate(), true);
		  
		  andBlock.setIn3(new BNegatableStatusBoolean(true, BStatus.ok, true));
		  andBlock.getIn3().setNegate(true);
		  Assert.assertEquals(andBlock.getIn3().getValue(), true);
		  Assert.assertEquals(andBlock.getIn3().getNegate(), true);
		  
		  andBlock.setIn4(new BNegatableStatusBoolean(true, BStatus.ok, true));
		  andBlock.getIn4().setNegate(true);
		  Assert.assertEquals(andBlock.getIn4().getValue(), true);
		  Assert.assertEquals(andBlock.getIn4().getNegate(), true);
		  
		  andBlock.setIn5(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  andBlock.getIn5().setNegate(true);
		  Assert.assertEquals(andBlock.getIn5().getValue(), false);
		  Assert.assertEquals(andBlock.getIn5().getNegate(), true);
		  
		  andBlock.setIn6(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  andBlock.getIn6().setNegate(true);
		  Assert.assertEquals(andBlock.getIn6().getValue(), false);
		  Assert.assertEquals(andBlock.getIn6().getNegate(), true);
		  
		  andBlock.setTrueDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(andBlock.getTrueDelay().getValue(), snValue, 0.1);
		  andBlock.setFalseDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(andBlock.getFalseDelay().getValue(), snValue, 0.1);
		  
		  andBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, false));
		  andBlock.getOUTPUT().setNegate(false);
		  Assert.assertEquals(andBlock.getOUTPUT().getValue(), false);
		  Assert.assertEquals(andBlock.getOUTPUT().getNegate(), false);
		  
		  andBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  andBlock.getOUTPUT().setNegate(true);
		  Assert.assertEquals(andBlock.getOUTPUT().getValue(), false);
		  Assert.assertEquals(andBlock.getOUTPUT().getNegate(), true);
		  
		  andBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, false));
		  andBlock.getOUTPUT().setNegate(false);
		  Assert.assertEquals(andBlock.getOUTPUT().getValue(), true);
		  Assert.assertEquals(andBlock.getOUTPUT().getNegate(), false);
		  
		  andBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, true));
		  andBlock.getOUTPUT().setNegate(true);
		  Assert.assertEquals(andBlock.getOUTPUT().getValue(), true);
		  Assert.assertEquals(andBlock.getOUTPUT().getNegate(), true);
	  }
	  
	  @DataProvider(name="provideTestData")
		public Object[][] getTesData() {
			BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/logic/test/And_TestData.csv").get();
			CsvReader readValidInputs;
			ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
			try {
				readValidInputs = new CsvReader(file.getInputStream());
				List<String> rec;
				while ((rec = readValidInputs.read()) != null) {
					validInputs.add(rec);
				}
				readValidInputs.close();
			} catch (IOException e) {
				validInputs = null;
			}

			Object[][] objArray = new Object[validInputs.size()][];
			for (int i = 0; i < validInputs.size(); i++) {
				objArray[i] = new Object[1];
				objArray[i][0] = validInputs.get(i);
			}

			return objArray;
		}
	  
	  @SuppressWarnings("squid:S2925")
	  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	  public void testAndBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BAnd andBlock = new BAnd();
		  
		  //Set Given values to all the slot including output slot as trueDelay/falseDelay depends on output value
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(andBlock, BAnd.in1.getName(), inputs.get(1), inputs.get(2));
		  setupNumericSlot(andBlock, BAnd.in2.getName(), inputs.get(3), inputs.get(4));
		  setupNumericSlot(andBlock, BAnd.in3.getName(), inputs.get(5), inputs.get(6));
		  setupNumericSlot(andBlock, BAnd.in4.getName(), inputs.get(7), inputs.get(8));
		  setupNumericSlot(andBlock, BAnd.in5.getName(), inputs.get(9), inputs.get(10));
		  setupNumericSlot(andBlock, BAnd.in6.getName(), inputs.get(11),inputs.get(12));
		  setupNumericSlot(andBlock, BAnd.trueDelay.getName(), inputs.get(13), null);
		  setupNumericSlot(andBlock, BAnd.falseDelay.getName(), inputs.get(14), null);
		  andBlock.getOUTPUT().setNegate(TestDataHelper.getBoolean(inputs.get(16)));
		  
		  int waitTime = 0;
		  switch (TestDataHelper.getInt(inputs.get(18), 0)) {
		  case 1:
			  setupNumericSlot(andBlock, BAnd.OUTPUT.getName(), inputs.get(15), inputs.get(16));
			  waitTime = (int) andBlock.getTrueDelay().getValue();
			  break;
			  
		  case 2:
			  setupNumericSlot(andBlock, BAnd.OUTPUT.getName(), inputs.get(15), inputs.get(16));
			  waitTime = (int) andBlock.getFalseDelay().getValue();
			  break;
		  }
		  
		  if(waitTime>0)
			  Reporter.log("Waiting for "+waitTime+"sec as per TestData waitEnum="+TestDataHelper.getInt(inputs.get(18), 0));
		  for (int i = 0; i < waitTime; i++) {
			  andBlock.executeHoneywellComponent(executionParams);
			  Assert.assertNotEquals(andBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)), "Reached the value in loop#"+i);
		  }
		  
		  andBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(andBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)));
		  
		  andBlock = null;
	  }
	  
		@Test
		public void testInputPropertiesList() {
			List<Property> inputPropList = andBlock.getInputPropertiesList();
			List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
			Arrays.sort(actualInputParamNames);
			String[] expectedInputParamNames = { BAnd.in1.getName(), BAnd.in2.getName(),BAnd.in3.getName(),BAnd.in4.getName(),BAnd.in5.getName(),BAnd.in6.getName(),
					BAnd.trueDelay.getName(),BAnd.falseDelay.getName()};
			Arrays.sort(expectedInputParamNames);

			Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
		}
		
		@Test
		public void testOutputPropertiesList() {
			List<Property> outputPropList = andBlock.getOutputPropertiesList();
			List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
			Arrays.sort(actualOutputParamNames);
			String[] expectedOutputParamNames = {BAnd.OUTPUT.getName()};
			Arrays.sort(expectedOutputParamNames);

			Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
		}
		
		@Test
		public void testInitHoneywellComponent() {
			BAnd andBlk = new BAnd();
			BExecutionParams exeParm = new BExecutionParams();
			try {
				andBlk.initHoneywellComponent(exeParm);
				Assert.assertEquals(andBlk.getDelayServed(), 0.0);
				Assert.assertEquals(andBlk.getLastOutput(), false);
			} catch (Exception e) {
				
			} finally {
				andBlk = null;
				exeParm = null;
			}
		}
		
		@Test
		public void testSetAndGetNegateValueRPCCall() {
			BAnd andBlk = new BAnd();
			andBlk.setIn1(new BNegatableStatusBoolean(true, BStatus.nullStatus, false));
			boolean negateValeToBeSet = true;
			andBlk.setNegateValueFromRPCCall(andBlk.getIn1().getName(), negateValeToBeSet, Context.NULL);
			boolean getNegateValue = andBlk.getNegateValueToRPCCall(andBlk.getIn1().getName(), Context.NULL);
			Assert.assertEquals(negateValeToBeSet, getNegateValue);
		}
		
	@Test
	public void testOverrideAutoAction() {
		if (null == station.get("FB")) {
			fbContainer = new BFolder();
			station.add("FB", fbContainer);
			andBlockOverride = new BAnd();
			fbContainer.add("AND", andBlockOverride);
		}
		BFBOverrideProperties fbOverrideProperty = new BFBOverrideProperties();
		fbOverrideProperty.setOverrideDuration(BRelTime.makeMinutes(1));
		List<Property> properties = ((BFunctionBlock) andBlockOverride).getOutputPropertiesList();

		for (int i = 0; i < properties.size(); i++) {

			BValue outPutSlotValue = andBlockOverride.get(properties.get(i).getName());
			if (outPutSlotValue instanceof BStatusEnum) {
				BEnum f1SlotEnum = ((BStatusEnum) outPutSlotValue).getEnum();
				fbOverrideProperty.add(properties.get(i).getName(), f1SlotEnum.newCopy());

			} else if (outPutSlotValue instanceof BStatusBoolean) {
				boolean isTrueValueSet = ((BStatusBoolean) outPutSlotValue).getValue();
				fbOverrideProperty.add(properties.get(i).getName(), BBoolean.make(isTrueValueSet));

			} else if (outPutSlotValue instanceof BStatusNumeric) {
				fbOverrideProperty.add(properties.get(i).getName(),
						BDouble.make(((BStatusNumeric) outPutSlotValue).getValue()));

			} 
		}

		andBlockOverride.doOverride(fbOverrideProperty);
		Assert.assertNotEquals(andBlockOverride.getOverrideExpiration(), BAbsTime.NULL);
		
		andBlockOverride.doAuto();
		Assert.assertEquals(andBlockOverride.getOverrideExpiration(), BAbsTime.NULL);

		if (null != station.get("FB"))
			station.remove((BFolder)station.get("FB"));

	}
	
	@Test
	public void testBFBOverrideProperties() throws Exception {
		BAnd andBlockOverride1 = new BAnd();
		if (null == station.get("FB1")) {
			BFolder fbContainer1 = new BFolder();
			station.add("FB1", fbContainer1);
			fbContainer1.add("AND1", andBlockOverride1);
		}
		BFBOverrideProperties fbOverrideProperty = new BFBOverrideProperties();
		fbOverrideProperty.setOverrideDuration(BRelTime.makeMinutes(3));
		BFBOverrideAction overrideAction = new BFBOverrideAction();
		if (null == andBlockOverride1.get(FBSlotConstants.OVERRIDE)) {
			andBlockOverride1.add(FBSlotConstants.OVERRIDE, overrideAction, 2);
		}

		overrideAction.invoke(andBlockOverride1, fbOverrideProperty);
		Assert.assertEquals(fbOverrideProperty.getOverrideDuration(), BRelTime.makeMinutes(3));
		BFBOverrideAction action = ((BFBOverrideAction) (andBlockOverride1.get(FBSlotConstants.OVERRIDE)));
		BFBOverrideProperties defaultValue = (BFBOverrideProperties) action.getParameterDefault();
		Assert.assertNotNull(defaultValue.get(BAnd.OUTPUT.getName()));
		Assert.assertEquals(BFBOverrideProperties.TYPE, action.getParameterType());
		Assert.assertNotEquals(andBlockOverride1.getOverrideExpiration(), BAbsTime.NULL);

		if (null != station.get("FB1"))
			station.remove((BFolder) station.get("FB1"));
	}
	  
	  /**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param andBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BAnd andBlock, final String slotName, final String inputValue, final String negateString){
		  boolean negate=false;
		  if(negateString!=null) {
			  negate = TestDataHelper.getBoolean(negateString);

			  //Set negate value
			  BNegatableStatusBoolean nsb = (BNegatableStatusBoolean) andBlock.get(slotName);
			  nsb.setNegate(negate);
		  }

		  if(TestDataHelper.isConnected(inputValue)){
			  //Create numeric constant and link to given slotName. Set the given value to the numeric constant
			  BNumericConst nm1 = new BNumericConst();
			  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			  Type srcType = nm1.getOut().getType();
			  Type targetType = andBlock.getProperty(slotName).getType();			
			  BConverter converter = null;
			  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusBoolean.TYPE)) {				
				  converter = new BStatusNumericToNegatableStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),andBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  andBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  }else{
				  andBlock.linkTo(nm1, nm1.getSlot("out"), andBlock.getSlot(slotName));
			  }
			  
			  return;
		  }

		  switch (slotName) {
		  case "in1":
			  andBlock.setIn1(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "in2":
			  andBlock.setIn2(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "in3":
			  andBlock.setIn3(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "in4":
			  andBlock.setIn4(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "in5":
			  andBlock.setIn5(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "in6":
			  andBlock.setIn6(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;

		  case "trueDelay":
			  andBlock.setTrueDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;

		  case "falseDelay":
			  andBlock.setFalseDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;
			  
		  case "OUTPUT":
			  andBlock.setOUTPUT(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
			  break;
			  
		  default:
			  break;
		  }
	  }
	  
	//@Test(groups = { "testLinkRules" })
	public void testLinkRules() {
		BNumericConst constVal = new BNumericConst();
		constVal.getOut().setValue(10);
		LinkCheck checkLink = andBlock.checkLink(constVal, constVal.getSlot("out"), andBlock.getSlot(BAnd.OUTPUT.getName()), Context.commit);
		Assert.assertFalse(checkLink.isValid());

		BAnd tempBlock = new BAnd();
		checkOutgoingLink(tempBlock, BAnd.in1, false);
		checkOutgoingLink(tempBlock, BAnd.in2, false);
		checkOutgoingLink(tempBlock, BAnd.in3, false);
		checkOutgoingLink(tempBlock, BAnd.in4, false);
		checkOutgoingLink(tempBlock, BAnd.in5, false);
		checkOutgoingLink(tempBlock, BAnd.in6, false);
		checkOutgoingLink(tempBlock, BAnd.trueDelay, false);
		checkOutgoingLink(tempBlock, BAnd.falseDelay, false);
	}

	  private void checkOutgoingLink(BAnd tempBlock, Property prop, boolean isLinkValid) {
		  LinkCheck checkLink = andBlock.checkLink(andBlock, andBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
		  Assert.assertEquals(checkLink.isValid(), isLinkValid);
	  }
	  
	  BAnd andBlock;
	  BExecutionParams executionParams;
	  
	  
	 private BStation station = null;
	 private TestStationHandler stationHandler = null;
	 private BFolder fbContainer = null;
	 private BAnd andBlockOverride = null;
	  
}
