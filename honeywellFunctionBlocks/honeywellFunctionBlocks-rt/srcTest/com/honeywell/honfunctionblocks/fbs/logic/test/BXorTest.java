/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.logic.BLogicBlock;
import com.honeywell.honfunctionblocks.fbs.logic.BXor;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of Xor block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 28, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BXorTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.logic.test.BXorTest(2979906276)1.0$ @*/
/* Generated Thu Nov 30 17:38:31 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BXorTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  xorBlock = new BXor();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  xorBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"trueDelay"}, {"falseDelay"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"In1"}, {"TrueDelay"}, {"FalseDelay"}, {"out"}, {"IN1"}, {"TRUEDELAY"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(xorBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(xorBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "or.png");
	  BIcon actualFbIcon = xorBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  xorBlock.setIcon(expectedFbIcon);
	  actualFbIcon = xorBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInXorBlock(double snValue) {
	  xorBlock.setIn1(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true)); 
	  xorBlock.getIn1().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn1().getValue(), false);
	  Assert.assertEquals(xorBlock.getIn1().getNegate(), true);
	  
	  xorBlock.setIn2(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  xorBlock.getIn2().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn2().getValue(), false);
	  Assert.assertEquals(xorBlock.getIn2().getNegate(), true);
	  
	  xorBlock.setIn3(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
	  xorBlock.getIn3().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn3().getValue(), true);
	  Assert.assertEquals(xorBlock.getIn3().getNegate(), true);
	  
	  xorBlock.setIn4(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
	  xorBlock.getIn4().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn4().getValue(), true);
	  Assert.assertEquals(xorBlock.getIn4().getNegate(), true);
	  
	  xorBlock.setIn5(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  xorBlock.getIn5().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn5().getValue(), false);
	  Assert.assertEquals(xorBlock.getIn5().getNegate(), true);
	  
	  xorBlock.setIn6(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
	  xorBlock.getIn6().setNegate(true);
	  Assert.assertEquals(xorBlock.getIn6().getValue(), false);
	  Assert.assertEquals(xorBlock.getIn6().getNegate(), true);
	  
	  xorBlock.setTrueDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(xorBlock.getTrueDelay().getValue(), snValue, 0.1);
	  xorBlock.setFalseDelay(new BHonStatusNumeric(snValue)); Assert.assertEquals(xorBlock.getFalseDelay().getValue(), snValue, 0.1);
	  
	  xorBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  xorBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(xorBlock.getOUTPUT().getValue(), false);
	  Assert.assertEquals(xorBlock.getOUTPUT().getNegate(), false);
	  
	  xorBlock.setOUTPUT(new BNegatableStatusBoolean(false, BStatus.ok, true));
	  xorBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(xorBlock.getOUTPUT().getValue(), false);
	  Assert.assertEquals(xorBlock.getOUTPUT().getNegate(), true);
	  
	  xorBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, false));
	  xorBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(xorBlock.getOUTPUT().getValue(), true);
	  Assert.assertEquals(xorBlock.getOUTPUT().getNegate(), false);
	  
	  xorBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  xorBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(xorBlock.getOUTPUT().getValue(), true);
	  Assert.assertEquals(xorBlock.getOUTPUT().getNegate(), true);
  }
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/logic/test/Xor_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testXorBlockWithTestData(List<String> inputs) throws BlockExecutionException, BlockInitializationException {
	  BXor xorBlock = new BXor();
	  
	  //Set Given values to all the slot including output slot as trueDelay/falseDelay depends on output value
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(xorBlock, BLogicBlock.in1.getName(), inputs.get(1), inputs.get(2));
	  setupNumericSlot(xorBlock, BLogicBlock.in2.getName(), inputs.get(3), inputs.get(4));
	  setupNumericSlot(xorBlock, BLogicBlock.in3.getName(), inputs.get(5), inputs.get(6));
	  setupNumericSlot(xorBlock, BLogicBlock.in4.getName(), inputs.get(7), inputs.get(8));
	  setupNumericSlot(xorBlock, BLogicBlock.in5.getName(), inputs.get(9), inputs.get(10));
	  setupNumericSlot(xorBlock, BLogicBlock.in6.getName(), inputs.get(11),inputs.get(12));
	  xorBlock.getOUTPUT().setNegate(TestDataHelper.getBoolean(inputs.get(16)));
	  
	  int waitTime = 0;
	  switch (TestDataHelper.getInt(inputs.get(18), 0)) {
	  case 0:
		  setupNumericSlot(xorBlock, BLogicBlock.trueDelay.getName(), "0", null);
		  setupNumericSlot(xorBlock, BLogicBlock.falseDelay.getName(), "0", null);
		  break;
	  case 1:
		  setupNumericSlot(xorBlock, BLogicBlock.trueDelay.getName(), inputs.get(13), null);
		  setupNumericSlot(xorBlock, BLogicBlock.OUTPUT.getName(), inputs.get(15), inputs.get(16));
		  waitTime = (int) xorBlock.getTrueDelay().getValue();
		  break;
		  
	  case 2:
		  setupNumericSlot(xorBlock, BLogicBlock.falseDelay.getName(), inputs.get(14), null);
		  setupNumericSlot(xorBlock, BLogicBlock.OUTPUT.getName(), inputs.get(15), inputs.get(16));
		  waitTime = (int) xorBlock.getFalseDelay().getValue();
		  break;
	  }
	  
	  xorBlock.initHoneywellComponent(null);
	  if(waitTime>0)
		  Reporter.log("Waiting for "+waitTime+"sec as per TestData waitEnum="+TestDataHelper.getInt(inputs.get(18), 0));
	  for (int i = 0; i < waitTime; i++) {
		  xorBlock.executeHoneywellComponent(executionParams);
		  Assert.assertNotEquals(xorBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)), "Reached the value in loop#"+i);
	  }
	  
	  xorBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(xorBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(17)));
	  
	  xorBlock = null;
  }
  
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = xorBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BLogicBlock.in1.getName(), BLogicBlock.in2.getName(),BLogicBlock.in3.getName(),BLogicBlock.in4.getName(),BLogicBlock.in5.getName(),BLogicBlock.in6.getName(),
				BLogicBlock.trueDelay.getName(),BLogicBlock.falseDelay.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = xorBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BLogicBlock.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param xorBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BXor xorBlock, final String slotName, final String inputValue, final String negateString){
	  boolean negate=false;
	  if(negateString!=null) {
		  negate = TestDataHelper.getBoolean(negateString);

		  //Set negate value
		  INegatableStatusValue nsb = (INegatableStatusValue) xorBlock.get(slotName);
		  nsb.setNegate(negate);
	  }

	  if(TestDataHelper.isConnected(inputValue)){
		  //Create numeric constant and link to given slotName. Set the given value to the numeric constant
		  BNumericConst nm1 = new BNumericConst();
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  Type srcType = nm1.getOut().getType();
		  Type targetType = xorBlock.getProperty(slotName).getType();			
		  BConverter converter = null;
		  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BNegatableFiniteStatusBoolean.TYPE)) {
			  converter = new BStatusNumericToFiniteStatusBoolean();
			  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),xorBlock.getSlot(slotName),converter);
			  conversionLink.setEnabled(true);
			  xorBlock.add("Link?",conversionLink );
			  conversionLink.activate();
		  }else{
			  xorBlock.linkTo(nm1, nm1.getSlot("out"), xorBlock.getSlot(slotName));
		  }
		  
		  return;
	  }

	  switch (slotName) {
	  case "in1":
		  xorBlock.setIn1(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in2":
		  xorBlock.setIn2(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in3":
		  xorBlock.setIn3(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in4":
		  xorBlock.setIn4(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in5":
		  xorBlock.setIn5(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "in6":
		  xorBlock.setIn6(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
		  break;

	  case "trueDelay":
		  xorBlock.setTrueDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "falseDelay":
		  xorBlock.setFalseDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
		  
	  case "OUTPUT":
		  xorBlock.setOUTPUT(TestDataHelper.getNegatableStatusBoolean(inputValue, negate));
		  break;
		  
	  default:
		  break;
	  }
  }
  
  //@Test(groups = { "testLinkRules" })
  public void testLinkRules() {
	  BXor tempBlock = new BXor();
	  checkOutgoingLink(tempBlock, BLogicBlock.in1, false);   
	  checkOutgoingLink(tempBlock, BLogicBlock.in2, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in3, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in4, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in5, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.in6, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.trueDelay, false);
	  checkOutgoingLink(tempBlock, BLogicBlock.falseDelay, false);
  }

  private void checkOutgoingLink(BXor tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = xorBlock.checkLink(xorBlock, xorBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }
  
  BXor xorBlock;
  BExecutionParams executionParams;

}
