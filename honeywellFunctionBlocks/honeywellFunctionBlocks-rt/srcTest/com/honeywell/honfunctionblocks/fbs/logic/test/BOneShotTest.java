/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.logic.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BIcon;
import javax.baja.sys.Context;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.logic.BOneShot;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of OneShot block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-258
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Nov 30, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BOneShotTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.logic.test.BOneShotTest(2979906276)1.0$ @*/
/* Generated Tue Dec 05 14:36:20 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOneShotTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	  @BeforeClass(alwaysRun=true)
	  public void setUp() {	
		  oneshotBlock = new BOneShot();
		  executionParams = new BExecutionParams();
	  }
	  
	  @AfterClass
	  public void tearDown() {
		  oneshotBlock = null;
		  executionParams = null;
	  }

	  @DataProvider(name="provideInSlotNames")
	  public Object[][] createInputSlotNames() {
		  return new Object[][]{{"x"}, {"onTime"}};	  
	  }
	  
	  @DataProvider(name="provideOutputSlotNames")
	  public Object[][] createOutputSlotNames() {
		  return new Object[][] {{"Y"}};
	  }
	  
	  @DataProvider(name="provideMiscSlotNames")
	  public Object[][] createExecOrderSlotName() {
		  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};
	  }
	  
	  @DataProvider(name="provideAllSlotNames")
	  public Object[][] createAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  }
	  
	  @DataProvider(name = "provideInvalidSlotNames")
	  public Object[][] invalidSlotNames() {
		  return new Object[][]{{"X"}, {"OnTime"}, {"in"}, {"y"}, {"output"}};
	  }
	  
	  @Test(dataProvider="provideInvalidSlotNames")
	  public void testInvalidSlots(String slotName){
		  Assert.assertNull(oneshotBlock.getSlot(slotName));
	  }  
	  
	  @Test(dataProvider="provideAllSlotNames")
	  public void testSlotAvailability(String slotName) {
		  Assert.assertNotNull(oneshotBlock.getSlot(slotName));
	  }
	  
	  @Test(groups={"testIconSlot"})
	  public void testIconSlot(){
		  //check if correct icon is used for AIA
		  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "one_shot.png");
		  BIcon actualFbIcon = oneshotBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);

		  //check if new icon can be set on AIA to update modified state
		  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		  oneshotBlock.setIcon(expectedFbIcon);
		  actualFbIcon = oneshotBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);
	  }
	  
	  @DataProvider(name = "provideSampleValues")
	  public Object[][] sampleValues() {
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
	  }

	  @Test(dataProvider="provideSampleValues")
	  public void testSettingValueInOneShotBlock(double snValue) {
		  oneshotBlock.setX(new BNegatableHonStatusNumeric(snValue, BStatus.ok, true)); 
		  oneshotBlock.getX().setNegate(true);
		  Assert.assertEquals(oneshotBlock.getX().getValue(), snValue, 0.1);
		  Assert.assertEquals(oneshotBlock.getX().getNegate(), true);
		  
		  oneshotBlock.setX(new BNegatableHonStatusNumeric(snValue, BStatus.ok, false));
		  oneshotBlock.getX().setNegate(false);
		  Assert.assertEquals(oneshotBlock.getX().getValue(), snValue, 0.1);
		  Assert.assertEquals(oneshotBlock.getX().getNegate(), false);
		  
		  oneshotBlock.setOnTime(new BHonStatusNumeric(snValue)); Assert.assertEquals(oneshotBlock.getOnTime().getValue(), snValue, 0.1);
		  
		  oneshotBlock.setY(new BNegatableStatusBoolean(false, BStatus.ok, false));
		  oneshotBlock.getY().setNegate(false);
		  Assert.assertEquals(oneshotBlock.getY().getValue(), false);
		  Assert.assertEquals(oneshotBlock.getY().getNegate(), false);
		  
		  oneshotBlock.setY(new BNegatableStatusBoolean(false, BStatus.ok, true));
		  oneshotBlock.getY().setNegate(true);
		  Assert.assertEquals(oneshotBlock.getY().getValue(), false);
		  Assert.assertEquals(oneshotBlock.getY().getNegate(), true);
		  
		  oneshotBlock.setY(new BNegatableStatusBoolean(true, BStatus.ok, false));
		  oneshotBlock.getY().setNegate(false);
		  Assert.assertEquals(oneshotBlock.getY().getValue(), true);
		  Assert.assertEquals(oneshotBlock.getY().getNegate(), false);
		  
		  oneshotBlock.setY(new BNegatableStatusBoolean(true, BStatus.ok, true));
		  oneshotBlock.getY().setNegate(true);
		  Assert.assertEquals(oneshotBlock.getY().getValue(), true);
		  Assert.assertEquals(oneshotBlock.getY().getNegate(), true);
	  }
	  
	  @DataProvider(name="provideTestData")
	  public Object[][] getTesData() {
		  BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/logic/test/OneShot_TestData.csv").get();
		  CsvReader readValidInputs;
		  ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		  try {
			  readValidInputs = new CsvReader(file.getInputStream());
			  List<String> rec;
			  while ((rec = readValidInputs.read()) != null) {
				  validInputs.add(rec);
			  }
			  readValidInputs.close();
		  } catch (IOException e) {
			  validInputs = null;
		  }

		  Object[][] objArray = new Object[validInputs.size()][];
		  for (int i = 0; i < validInputs.size(); i++) {
			  objArray[i] = new Object[1];
			  objArray[i][0] = validInputs.get(i);
		  }

		  return objArray;
	  }
	  
	  @SuppressWarnings("squid:S2925")
	  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	  public void testOneShotBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BOneShot oneshotBlock = new BOneShot();

		  // Set Given values to all the slot including output slot as
		  // trueDelay/falseDelay depends on output value
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  oneshotBlock.setX(new BNegatableHonStatusNumeric(0, BStatus.ok, false));
		  oneshotBlock.setOnTime(new BHonStatusNumeric(1));
		  oneshotBlock.executeHoneywellComponent(executionParams);
		  
		  oneshotBlock.executeHoneywellComponent(executionParams);
		  
		  setupSlot(oneshotBlock, BOneShot.x.getName(), inputs.get(1), inputs.get(2));
		  setupSlot(oneshotBlock, BOneShot.onTime.getName(), inputs.get(3), null);
		  oneshotBlock.getY().setNegate(TestDataHelper.getBoolean(inputs.get(4)));

		  double onTime = getOnTimeAfterValidate(oneshotBlock);
		  Reporter.log("Waiting for onTime="+onTime+" [before validation = "+oneshotBlock.getOnTime().getValue()+"]");

		  for (int i = 0; i < onTime; i++) {
			  oneshotBlock.executeHoneywellComponent(executionParams);
			  Assert.assertEquals(oneshotBlock.getY().getValue(), TestDataHelper.getBoolean(inputs.get(5)), "Failed during onTime at loop-"+i);
		  }

		  oneshotBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(oneshotBlock.getY().getValue(), TestDataHelper.getBoolean(inputs.get(6)), "Failed after onTime");

		  oneshotBlock = null;
	  }
	  
		@Test
		public void testInputPropertiesList() {
			List<Property> inputPropList = oneshotBlock.getInputPropertiesList();
			List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
			Arrays.sort(actualInputParamNames);
			String[] expectedInputParamNames = { BOneShot.x.getName(), BOneShot.onTime.getName()};
			Arrays.sort(expectedInputParamNames);

			Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
		}
		
		@Test
		public void testOutputPropertiesList() {
			List<Property> outputPropList = oneshotBlock.getOutputPropertiesList();
			List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
			String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
			Arrays.sort(actualOutputParamNames);
			String[] expectedOutputParamNames = {BOneShot.Y.getName()};
			Arrays.sort(expectedOutputParamNames);

			Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
		}
		
		@Test
		public void testInitHoneywellComponent() {
			BOneShot oneshotBlk = new BOneShot();
			BExecutionParams exeParm = new BExecutionParams();
			try {
				oneshotBlk.initHoneywellComponent(exeParm);
				Assert.assertEquals(oneshotBlk.getHoldTimer(), 0.0);
				Assert.assertEquals(oneshotBlk.getLastInput(), false);
			} catch (Exception e) {
				
			} finally {
				oneshotBlk = null;
				exeParm = null;
			}
		}

	  private double getOnTimeAfterValidate(final BOneShot oneShot) {
			if(!oneshotBlock.isConfigured(BOneShot.onTime))
				return 0;
			
			double onTimeVal = oneShot.getOnTime().getValue();
			if(Double.isInfinite(onTimeVal) || Double.isNaN(onTimeVal)) {
				onTimeVal = 0;
			}
			
			if(onTimeVal<0)
				onTimeVal = 0;
			else if(onTimeVal > 65535)
				onTimeVal = 65535;
			
			return onTimeVal;
		}
	  
	  /**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param oneshotBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupSlot(BOneShot oneshotBlock, final String slotName, final String inputValue, final String negateString){
		  boolean negate=false;
		  if(negateString!=null) {
			  negate = TestDataHelper.getBoolean(negateString);

			  //Set negate value
			  INegatableStatusValue nsb = (INegatableStatusValue) oneshotBlock.get(slotName);
			  nsb.setNegate(negate);
		  }

		  if(TestDataHelper.isConnected(inputValue)){
			  //Create numeric constant and link to given slotName. Set the given value to the numeric constant
			  BNumericConst nm1 = new BNumericConst();
			  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			  oneshotBlock.linkTo(nm1, nm1.getSlot("out"), oneshotBlock.getSlot(slotName));
			  return;
		  }

		  switch (slotName) {
		  case "x":
			  oneshotBlock.setX(TestDataHelper.getNegatableHonStatusNumeric(inputValue, 0, negate));
			  break;

		  case "onTime":
			  oneshotBlock.setOnTime(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;
			  
		  default:
			  break;
		  }
	  }
	  
	  //@Test(groups = { "testLinkRules" })
	  public void testLinkRules() {
		  BNumericConst constVal = new BNumericConst();
		  constVal.getOut().setValue(10);
		  LinkCheck checkLink = oneshotBlock.checkLink(constVal, constVal.getSlot("out"), oneshotBlock.getSlot(BOneShot.Y.getName()), Context.commit);
		  Assert.assertFalse(checkLink.isValid());
		  
		  BOneShot tempBlock = new BOneShot();
		  checkOutgoingLink(tempBlock, BOneShot.x, false);   
		  checkOutgoingLink(tempBlock, BOneShot.onTime, false);
	  }

	  private void checkOutgoingLink(BOneShot tempBlock, Property prop, boolean isLinkValid) {
		  LinkCheck checkLink = oneshotBlock.checkLink(oneshotBlock, oneshotBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
		  Assert.assertEquals(checkLink.isValid(), isLinkValid);
	  }
	  
	  BOneShot oneshotBlock;
	  BExecutionParams executionParams;
	  
}
