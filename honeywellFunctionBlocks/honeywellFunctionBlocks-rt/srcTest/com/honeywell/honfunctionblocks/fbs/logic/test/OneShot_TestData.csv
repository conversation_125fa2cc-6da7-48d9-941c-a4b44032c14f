# Test data for Or block
# Requirement ID: F1PLT-ADR-405; TestCase ID: F1PLT-ATC-258
#
# TestData format: Iteration Intervel,X,negin,onTime,negout-Y,output 1,output 2 
#
# Note: Only boolean value can be set to in/out slots. To pass numeric values you should use connected/unconnected syntax
#
1000,1,FALSE,0,FALSE,FALSE,FALSE
1000,connected=1,TRUE,0,FALSE,FALSE,FALSE
1000,0,TRUE,0,FALSE,FALSE,FALSE
1000,0,FALSE,0,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,0,FALSE,FALSE,FALSE
1000,unconnected=-5,TRUE,0,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,0,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,0,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,0,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,0,FALSE,FALSE,FALSE
1000,0.234,FALSE,0,FALSE,FALSE,FALSE
1000,0.234,TRUE,0,FALSE,FALSE,FALSE
1000,-0.123,FALSE,0,FALSE,FALSE,FALSE
1000,-0.123,TRUE,0,FALSE,FALSE,FALSE
1000,5,FALSE,0,FALSE,FALSE,FALSE
1000,5,TRUE,0,FALSE,FALSE,FALSE
1000,-7,FALSE,0,FALSE,FALSE,FALSE
1000,-7,TRUE,0,FALSE,FALSE,FALSE
1000,1,FALSE,0,TRUE,TRUE,TRUE
1000,1,TRUE,0,TRUE,TRUE,TRUE
1000,0,TRUE,0,TRUE,TRUE,TRUE
1000,0,FALSE,0,TRUE,TRUE,TRUE
1000,unconnected=0,FALSE,0,TRUE,TRUE,TRUE
1000,unconnected=1,TRUE,0,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,0,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,0,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,0,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,0,TRUE,TRUE,TRUE
1000,0.234,FALSE,0,TRUE,TRUE,TRUE
1000,0.234,TRUE,0,TRUE,TRUE,TRUE
1000,-0.123,FALSE,0,TRUE,TRUE,TRUE
1000,-0.123,TRUE,0,TRUE,TRUE,TRUE
1000,5,FALSE,0,TRUE,TRUE,TRUE
1000,5,TRUE,0,TRUE,TRUE,TRUE
1000,-7,FALSE,0,TRUE,TRUE,TRUE
1000,-7,TRUE,0,TRUE,TRUE,TRUE
1000,1,FALSE,2,FALSE,TRUE,FALSE
1000,1,TRUE,2,FALSE,FALSE,FALSE
1000,0,TRUE,2,FALSE,TRUE,FALSE
1000,0,FALSE,2,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,2,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,2,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,2,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,2,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,2,FALSE,TRUE,FALSE
1000,connected=-inf,TRUE,2,FALSE,FALSE,FALSE
1000,0.234,FALSE,2,FALSE,TRUE,FALSE
1000,0.234,TRUE,2,FALSE,FALSE,FALSE
1000,-0.123,FALSE,2,FALSE,TRUE,FALSE
1000,-0.123,TRUE,2,FALSE,FALSE,FALSE
1000,5,FALSE,2,FALSE,TRUE,FALSE
1000,5,TRUE,2,FALSE,FALSE,FALSE
1000,-7,FALSE,2,FALSE,TRUE,FALSE
1000,-7,TRUE,2,FALSE,FALSE,FALSE
1000,1,FALSE,2,TRUE,FALSE,TRUE
1000,1,TRUE,2,TRUE,TRUE,TRUE
1000,0,TRUE,2,TRUE,FALSE,TRUE
1000,0,FALSE,2,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,2,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,2,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,2,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,2,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,2,TRUE,FALSE,TRUE
1000,connected=-inf,TRUE,2,TRUE,TRUE,TRUE
1000,0.234,FALSE,2,TRUE,FALSE,TRUE
1000,0.234,TRUE,2,TRUE,TRUE,TRUE
1000,-0.123,FALSE,2,TRUE,FALSE,TRUE
1000,-0.123,TRUE,2,TRUE,TRUE,TRUE
1000,5,FALSE,2,TRUE,FALSE,TRUE
1000,5,TRUE,2,TRUE,TRUE,TRUE
1000,-7,FALSE,2,TRUE,FALSE,TRUE
1000,-7,TRUE,2,TRUE,TRUE,TRUE
1000,1,FALSE,-3,FALSE,FALSE,FALSE
1000,1,TRUE,-3,FALSE,FALSE,FALSE
1000,0,TRUE,-3,FALSE,FALSE,FALSE
1000,0,FALSE,-3,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,-3,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,-3,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,-3,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,-3,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,-3,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,-3,FALSE,FALSE,FALSE
1000,0.234,FALSE,-3,FALSE,FALSE,FALSE
1000,0.234,TRUE,-3,FALSE,FALSE,FALSE
1000,-0.123,FALSE,-3,FALSE,FALSE,FALSE
1000,-0.123,TRUE,-3,FALSE,FALSE,FALSE
1000,5,FALSE,-3,FALSE,FALSE,FALSE
1000,5,TRUE,-3,FALSE,FALSE,FALSE
1000,-7,FALSE,-3,FALSE,FALSE,FALSE
1000,-7,TRUE,-3,FALSE,FALSE,FALSE
1000,1,FALSE,-3,TRUE,TRUE,TRUE
1000,1,TRUE,-3,TRUE,TRUE,TRUE
1000,0,TRUE,-3,TRUE,TRUE,TRUE
1000,0,FALSE,-3,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,-3,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,-3,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,-3,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,-3,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,-3,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,-3,TRUE,TRUE,TRUE
1000,0.234,FALSE,-3,TRUE,TRUE,TRUE
1000,0.234,TRUE,-3,TRUE,TRUE,TRUE
1000,-0.123,FALSE,-3,TRUE,TRUE,TRUE
1000,-0.123,TRUE,-3,TRUE,TRUE,TRUE
1000,5,FALSE,-3,TRUE,TRUE,TRUE
1000,5,TRUE,-3,TRUE,TRUE,TRUE
1000,-7,FALSE,-3,TRUE,TRUE,TRUE
1000,-7,TRUE,-3,TRUE,TRUE,TRUE
1000,1,FALSE,unconnected=-3,FALSE,FALSE,FALSE
1000,1,TRUE,unconnected=3,FALSE,FALSE,FALSE
1000,0,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,0,FALSE,unconnected=1,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,unconnected=0,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,unconnected=0,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,0.234,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,0.234,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,-0.123,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,-0.123,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,5,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,5,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,-7,FALSE,unconnected=5,FALSE,FALSE,FALSE
1000,-7,TRUE,unconnected=5,FALSE,FALSE,FALSE
1000,1,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,1,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,0,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,0,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,0.234,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,0.234,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,-0.123,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,-0.123,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,5,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,5,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,-7,FALSE,unconnected=5,TRUE,TRUE,TRUE
1000,-7,TRUE,unconnected=5,TRUE,TRUE,TRUE
1000,1,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,1,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,0,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,0,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,0.234,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,0.234,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,-0.123,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,-0.123,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,5,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,5,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,-7,FALSE,connected=+inf,FALSE,FALSE,FALSE
1000,-7,TRUE,connected=+inf,FALSE,FALSE,FALSE
1000,1,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,1,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,0,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,0,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,0.234,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,0.234,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,-0.123,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,-0.123,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,5,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,5,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,-7,FALSE,connected=+inf,TRUE,TRUE,TRUE
1000,-7,TRUE,connected=+inf,TRUE,TRUE,TRUE
1000,1,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,1,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,0,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,0,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,0.234,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,0.234,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,-0.123,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,-0.123,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,5,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,5,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,-7,FALSE,connected=-inf,FALSE,FALSE,FALSE
1000,-7,TRUE,connected=-inf,FALSE,FALSE,FALSE
1000,1,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,1,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,0,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,0,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,0.234,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,0.234,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,-0.123,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,-0.123,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,5,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,5,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,-7,FALSE,connected=-inf,TRUE,TRUE,TRUE
1000,-7,TRUE,connected=-inf,TRUE,TRUE,TRUE
1000,1,FALSE,1.2,FALSE,TRUE,FALSE
1000,1,TRUE,1.2,FALSE,FALSE,FALSE
1000,0,TRUE,1.2,FALSE,TRUE,FALSE
1000,0,FALSE,1.2,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,1.2,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,1.2,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,1.2,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,1.2,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,1.2,FALSE,TRUE,FALSE
1000,connected=-inf,TRUE,1.2,FALSE,FALSE,FALSE
1000,0.234,FALSE,1.2,FALSE,TRUE,FALSE
1000,0.234,TRUE,1.2,FALSE,FALSE,FALSE
1000,-0.123,FALSE,1.2,FALSE,TRUE,FALSE
1000,-0.123,TRUE,1.2,FALSE,FALSE,FALSE
1000,5,FALSE,1.2,FALSE,TRUE,FALSE
1000,5,TRUE,1.2,FALSE,FALSE,FALSE
1000,-7,FALSE,1.2,FALSE,TRUE,FALSE
1000,-7,TRUE,1.2,FALSE,FALSE,FALSE
1000,1,FALSE,1.2,TRUE,FALSE,TRUE
1000,1,TRUE,1.2,TRUE,TRUE,TRUE
1000,0,TRUE,1.2,TRUE,FALSE,TRUE
1000,0,FALSE,1.2,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,1.2,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,1.2,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,1.2,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,1.2,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,1.2,TRUE,FALSE,TRUE
1000,connected=-inf,TRUE,1.2,TRUE,TRUE,TRUE
1000,0.234,FALSE,1.2,TRUE,FALSE,TRUE
1000,0.234,TRUE,1.2,TRUE,TRUE,TRUE
1000,-0.123,FALSE,1.2,TRUE,FALSE,TRUE
1000,-0.123,TRUE,1.2,TRUE,TRUE,TRUE
1000,5,FALSE,1.2,TRUE,FALSE,TRUE
1000,5,TRUE,1.2,TRUE,TRUE,TRUE
1000,-7,FALSE,1.2,TRUE,FALSE,TRUE
1000,-7,TRUE,1.2,TRUE,TRUE,TRUE
1000,1,FALSE,-20.5,FALSE,FALSE,FALSE
1000,1,TRUE,-20.5,FALSE,FALSE,FALSE
1000,0,TRUE,-20.5,FALSE,FALSE,FALSE
1000,0,FALSE,-20.5,FALSE,FALSE,FALSE
1000,unconnected=5,FALSE,-20.5,FALSE,FALSE,FALSE
1000,unconnected=5,TRUE,-20.5,FALSE,FALSE,FALSE
1000,connected=+inf,FALSE,-20.5,FALSE,FALSE,FALSE
1000,connected=+inf,TRUE,-20.5,FALSE,FALSE,FALSE
1000,connected=-inf,FALSE,-20.5,FALSE,FALSE,FALSE
1000,connected=-inf,TRUE,-20.5,FALSE,FALSE,FALSE
1000,0.234,FALSE,-20.5,FALSE,FALSE,FALSE
1000,0.234,TRUE,-20.5,FALSE,FALSE,FALSE
1000,-0.123,FALSE,-20.5,FALSE,FALSE,FALSE
1000,-0.123,TRUE,-20.5,FALSE,FALSE,FALSE
1000,5,FALSE,-20.5,FALSE,FALSE,FALSE
1000,5,TRUE,-20.5,FALSE,FALSE,FALSE
1000,-7,FALSE,-20.5,FALSE,FALSE,FALSE
1000,-7,TRUE,-20.5,FALSE,FALSE,FALSE
1000,1,FALSE,-20.5,TRUE,TRUE,TRUE
1000,1,TRUE,-20.5,TRUE,TRUE,TRUE
1000,0,TRUE,-20.5,TRUE,TRUE,TRUE
1000,0,FALSE,-20.5,TRUE,TRUE,TRUE
1000,unconnected=5,FALSE,-20.5,TRUE,TRUE,TRUE
1000,unconnected=5,TRUE,-20.5,TRUE,TRUE,TRUE
1000,connected=+inf,FALSE,-20.5,TRUE,TRUE,TRUE
1000,connected=+inf,TRUE,-20.5,TRUE,TRUE,TRUE
1000,connected=-inf,FALSE,-20.5,TRUE,TRUE,TRUE
1000,connected=-inf,TRUE,-20.5,TRUE,TRUE,TRUE
1000,0.234,FALSE,-20.5,TRUE,TRUE,TRUE
1000,0.234,TRUE,-20.5,TRUE,TRUE,TRUE
1000,-0.123,FALSE,-20.5,TRUE,TRUE,TRUE
1000,-0.123,TRUE,-20.5,TRUE,TRUE,TRUE
1000,5,FALSE,-20.5,TRUE,TRUE,TRUE
1000,5,TRUE,-20.5,TRUE,TRUE,TRUE
1000,-7,FALSE,-20.5,TRUE,TRUE,TRUE
1000,-7,TRUE,-20.5,TRUE,TRUE,TRUE
