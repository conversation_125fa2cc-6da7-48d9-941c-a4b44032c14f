/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.enums.BCompareEnum;

@NiagaraType
@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })
public class BCompareEnumTest extends BTestNg {
/*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.enums.test.BCompareEnumTest(**********)1.0$ @*/
/* Generated Thu Aug 08 17:42:30 IST 2019 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
	  return new Object[][] {{Integer.valueOf(BCompareEnum.TRUE),BCompareEnum.True},
		  {Integer.valueOf(BCompareEnum.FALSE),BCompareEnum.False},
		  {Integer.valueOf(BCompareEnum.NULL),BCompareEnum.Null}};
	}

	@Test(dataProvider = "enumOrdinal")
	public void testOperationByMakeOrdinal(Integer ordinal, BCompareEnum oe) {
		Assert.assertEquals(BCompareEnum.make(ordinal.intValue()), oe);
	}
	
	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		 return new Object[][] {{BCompareEnum.True.getTag(),BCompareEnum.True},
			  {BCompareEnum.False.getTag(),BCompareEnum.False},
			  {BCompareEnum.Null.getTag(),BCompareEnum.Null}};
	}
	
	@Test(dataProvider = "enumTag")
	public void testOperationByMakeTag(String tag, BCompareEnum oe) {
		Assert.assertEquals(BCompareEnum.make(tag), oe);
	}
}

