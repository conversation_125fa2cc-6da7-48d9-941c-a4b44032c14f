/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;

/**
 * Testing OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405 
 * <AUTHOR> - Lavanya B.
 * @since Feb 12, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BOccupancyEnumTest extends BTestNg {
//region /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.enums.test.BOccupancyEnumTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:42 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOccupancyEnumTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
	  return new Object[][] {{Integer.valueOf(BOccupancyEnum.OCCUPIED),BOccupancyEnum.Occupied},
		  {Integer.valueOf(BOccupancyEnum.UNOCCUPIED),BOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BOccupancyEnum.STANDBY),BOccupancyEnum.Standby},
		  {Integer.valueOf(BOccupancyEnum.BYPASS),BOccupancyEnum.Bypass},
		  {Integer.valueOf(BOccupancyEnum.NULL),BOccupancyEnum.Null}};
	}

	@Test(dataProvider = "enumOrdinal")
	public void testOperationByMakeOrdinal(Integer ordinal, BOccupancyEnum oe) {
		Assert.assertEquals(BOccupancyEnum.make(ordinal.intValue()), oe);
	}
	
	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		 return new Object[][] {{BOccupancyEnum.Occupied.getTag(),BOccupancyEnum.Occupied},
			  {BOccupancyEnum.Unoccupied.getTag(),BOccupancyEnum.Unoccupied},
			  {BOccupancyEnum.Standby.getTag(),BOccupancyEnum.Standby},
			  {BOccupancyEnum.Bypass.getTag(),BOccupancyEnum.Bypass},
			  {BOccupancyEnum.Null.getTag(),BOccupancyEnum.Null}};
	}
	
	@Test(dataProvider = "enumTag")
	public void testOperationByMakeTag(String tag, BOccupancyEnum oe) {
		Assert.assertEquals(BOccupancyEnum.make(tag), oe);
	}
}
