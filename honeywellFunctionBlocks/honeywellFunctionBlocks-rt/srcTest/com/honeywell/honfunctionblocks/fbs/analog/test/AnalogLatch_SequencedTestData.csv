# Test data for Analog Latch block
# Requirement ID: F1PLT-ADR-405; TestCase ID: F1PLT-ATC-191
#
# TestData format: Iteration Interval,x,latch,negate-latch,expectedY
#
# Note: Given numeric values will be directly set into the slots. To pass numeric values (by creating link) you should use connected/unconnected syntax
#
start,scenario 1,,,
1000,unconnected,unconnected=5,FALSE,+inf,
1000,unconnected=5,unconnected=5,TRUE,+inf,
1000,unconnected=5,0,FALSE,+inf,
1000,unconnected=5,0,TRUE,+inf,
1000,unconnected=5,1,FALSE,+inf,
1000,unconnected=-5,1,TRUE,+inf,
1000,unconnected=-5,connected=-inf,FALSE,+inf,
1000,unconnected=-5,connected=-inf,TRUE,+inf,
1000,unconnected=-5,-0.123,FALSE,+inf,
1000,unconnected=-5,-0.123,TRUE,+inf,
1000,unconnected=-5,0.125,FALSE,+inf,
1000,unconnected=-5,0.125,TRUE,+inf,
1000,unconnected=-5,connected=+inf,FALSE,+inf,
1000,unconnected=-5,connected=+inf,TRUE,+inf,
1001,unconnected=-5,connected=nan,FALSE,+inf,
1002,unconnected=5,connected=nan,TRUE,+inf,
end,,,,
start,scenario 2,,,
1000,10,unconnected,FALSE,+inf,
1000,10,unconnected=5,TRUE,+inf,
1000,10,0,FALSE,+inf,
1000,20,1,FALSE,20,
1000,10,0,TRUE,10,
1000,15,1,FALSE,15,
1000,25,1,FALSE,15,
1000,25,1,TRUE,15,
1000,10,connected=-inf,FALSE,15,
1000,20,connected=-inf,TRUE,15,
1000,15,connected=-inf,TRUE,15,
1000,25,0,TRUE,25,
1000,45,connected=-inf,FALSE,45,
1000,10,-0.123,FALSE,45,
1000,18,-0.123,TRUE,45,
1000,18,0,TRUE,18,
1000,20,-0.123,TRUE,18,
1000,25,0.125,FALSE,18, 
1000,40,0.125,TRUE,18,
1000,41,0,TRUE,41,
1000,41,0.125,TRUE,41,
1000,10,connected=+inf,FALSE,41,
1000,15,connected=+inf,TRUE,41,
1001,20,connected=nan,FALSE,41,
1002,25,connected=nan,TRUE,41,
end,,,,
start,scenario 3,,,
1000,-10,unconnected,FALSE,+inf,
1000,-10,unconnected=5,TRUE,+inf,
1000,-10,0,FALSE,+inf,
1000,-20,1,FALSE,-20,
1000,-10,0,TRUE,-10,
1000,-15,1,FALSE,-15,
1000,-25,1,FALSE,-15,
1000,-25,1,TRUE,-15,
1000,-10,connected=-inf,FALSE,-15,
1000,-20,connected=-inf,TRUE,-15,
1000,-15,connected=-inf,TRUE,-15,
1000,-25,0,TRUE,-25,
1000,-45,connected=-inf,FALSE,-45,
1000,-10,-0.123,FALSE,-45,
1000,-18,-0.123,TRUE,-45,
1000,-18,0,TRUE,-18,
1000,-20,-0.123,TRUE,-18,
1000,-25,0.125,FALSE,-18,
1000,-40,0.125,TRUE,-18,
1000,-41,0,TRUE,-41,
1000,-41,0.125,TRUE,-41,
1000,-10,connected=+inf,FALSE,-41,
1000,-15,connected=+inf,TRUE,-41,
1001,-20,connected=nan,FALSE,-41,
1002,-25,connected=nan,TRUE,-41,
end,,,,
start,scenario 4,,,
1000,-10.5,unconnected,FALSE,+inf,
1000,-10.5,unconnected=-5,TRUE,+inf,
1000,-10.5,0,FALSE,+inf,
1000,-20.5,1,FALSE,-20.5,
1000,-10.5,0,TRUE,-10.5,
1000,-15.5,1,FALSE,-15.5,
1000,-25.5,1,FALSE,-15.5,
1000,-25.5,1,TRUE,-15.5,
1000,-10.5,connected=-inf,FALSE,-15.5,
1000,-20.5,connected=-inf,TRUE,-15.5,
1000,-15.5,connected=-inf,TRUE,-15.5,
1000,-25.5,0,TRUE,-25.5,
1000,-45.5,connected=-inf,FALSE,-45.5,
1000,-10.5,-0.123,FALSE,-45.5,
1000,-18.5,-0.123,TRUE,-45.5,
1000,-18.5,0,TRUE,-18.5,
1000,-20.5,-0.123,TRUE,-18.5,
1000,-25.5,0.125,FALSE,-18.5,
1000,-40.5,0.125,TRUE,-18.5,
1000,-41.5,0,TRUE,-41.5,
1000,-41.5,0.125,TRUE,-41.5,
1000,-10.5,connected=+inf,FALSE,-41.5,
1000,-15.5,connected=+inf,TRUE,-41.5,
1001,-20.5,connected=nan,FALSE,-41.5,
1002,-25.5,connected=nan,TRUE,-41.5,
end,,,,
start,scenario 5,,,
1000,0.01,unconnected,FALSE,+inf,
1000,0.01,unconnected=-5,TRUE,+inf,
1000,0.01,0,FALSE,+inf,
1000,0.02,1,FALSE,0.02,
1000,0.01,0,TRUE,0.01,
1000,0.015,1,FALSE,0.015,
1000,0.025,1,FALSE,0.015,
1000,0.025,1,TRUE,0.015,
1000,0.01,connected=-inf,FALSE,0.015,
1000,0.02,connected=-inf,TRUE,0.015,
1000,0.015,connected=-inf,TRUE,0.015,
1000,0.025,0,TRUE,0.025,
1000,0.045,connected=-inf,FALSE,0.045,
1000,0.01,-0.123,FALSE,0.045,
1000,0.018,-0.123,TRUE,0.045,
1000,0.018,0,TRUE,0.018,
1000,0.02,-0.123,TRUE,0.018,
1000,0.025,0.125,FALSE,0.018,
1000,0.04,0.125,TRUE,0.018,
1000,0.041,0,TRUE,0.041,
1000,0.041,0.125,TRUE,0.041,
1000,0.01,connected=+inf,FALSE,0.041,
1000,0.015,connected=+inf,TRUE,0.041,
1001,0.02,connected=nan,FALSE,0.041,
1002,0.025,connected=nan,TRUE,0.041,
end,,,,
start,scenario 6,,,
1000,connected=-inf,unconnected,FALSE,+inf,
1000,connected=-inf,unconnected=5,TRUE,+inf,
1000,connected=-inf,0,FALSE,+inf,
1000,connected=-inf,1,FALSE,-inf,
1000,connected=-inf,0,TRUE,-inf,
1000,connected=-inf,1,FALSE,-inf,
1000,connected=-inf,1,FALSE,-inf,
1000,connected=-inf,1,TRUE,-inf,
1000,connected=-inf,connected=-inf,FALSE,-inf,
1000,connected=-inf,connected=-inf,TRUE,-inf,
1000,connected=-inf,connected=-inf,TRUE,-inf,
1000,connected=-inf,connected=-inf,FALSE,-inf,
1000,connected=-inf,-0.123,FALSE,-inf,
1000,connected=-inf,-0.123,TRUE,-inf,
1000,connected=-inf,-0.123,TRUE,-inf,
1000,connected=-inf,0.125,FALSE,-inf,
1000,connected=-inf,0.125,TRUE,-inf,
1000,connected=-inf,0.125,TRUE,-inf,
1000,connected=-inf,connected=+inf,FALSE,-inf,
1000,connected=-inf,connected=+inf,TRUE,-inf,
1001,connected=-inf,connected=nan,FALSE,-inf,
1002,connected=-inf,connected=nan,TRUE,-inf,
end,,,,
start,scenario 7,,,
1000,connected=+inf,unconnected=5,FALSE,+inf,
1000,connected=+inf,unconnected=5,TRUE,+inf,
1000,connected=+inf,0,FALSE,+inf,
1000,connected=+inf,1,FALSE,+inf,
1000,connected=+inf,0,TRUE,+inf,
1000,connected=+inf,1,FALSE,+inf,
1000,connected=+inf,1,FALSE,+inf,
1000,connected=+inf,1,TRUE,+inf,
1000,connected=+inf,connected=-inf,FALSE,+inf,
1000,connected=+inf,connected=-inf,TRUE,+inf,
1000,connected=+inf,connected=-inf,TRUE,+inf,
1000,connected=+inf,connected=-inf,FALSE,+inf,
1000,connected=+inf,-0.123,FALSE,+inf,
1000,connected=+inf,-0.123,TRUE,+inf,
1000,connected=+inf,-0.123,TRUE,+inf,
1000,connected=+inf,0.125,FALSE,+inf,
1000,connected=+inf,0.125,TRUE,+inf,
1000,connected=+inf,0.125,TRUE,+inf,
1000,connected=+inf,connected=+inf,FALSE,+inf,
1000,connected=+inf,connected=+inf,TRUE,+inf,
1001,connected=+inf,connected=nan,FALSE,+inf,
1002,connected=+inf,connected=nan,TRUE,+inf,
end,,,,
start,scenario 8,,,
1000,connected=nan,unconnected=5,FALSE,+inf,
1000,connected=nan,unconnected=-5,TRUE,+inf,
1000,connected=nan,0,FALSE,+inf,
1000,connected=nan,1,FALSE,+inf,
1000,connected=nan,0,TRUE,+inf,
1000,connected=nan,1,FALSE,+inf,
1000,connected=nan,1,FALSE,+inf,
1000,connected=nan,1,TRUE,+inf,
1000,connected=nan,connected=-inf,FALSE,+inf,
1000,connected=nan,connected=-inf,TRUE,+inf,
1000,connected=nan,connected=-inf,TRUE,+inf,
1000,connected=nan,connected=-inf,FALSE,+inf,
1000,connected=nan,-0.123,FALSE,+inf,
1000,connected=nan,-0.123,TRUE,+inf,
1000,connected=nan,-0.123,TRUE,+inf,
1000,connected=nan,0.125,FALSE,+inf,
1000,connected=nan,0.125,TRUE,+inf,
1000,connected=nan,0.125,TRUE,+inf,
1000,connected=nan,connected=+inf,FALSE,+inf,
1000,connected=nan,connected=+inf,TRUE,+inf,
1001,connected=nan,connected=nan,FALSE,+inf,
1002,connected=nan,connected=nan,TRUE,+inf,
end,,,,
start,scenario 9,,,		
1000,unconnected,unconnected,0,+inf,
1000,unconnected,unconnected,1,+inf,
1000,unconnected,FALSE,0,+inf,
1000,unconnected,FALSE,1,+inf,
1000,unconnected,TRUE,0,+inf,
1000,unconnected,TRUE,1,+inf,
1000,unconnected,TRUE,0,+inf,
1000,unconnected,TRUE,1,+inf,
1000,unconnected,TRUE,0,+inf,
1000,unconnected,TRUE,1,+inf,
1000,unconnected,TRUE,0,+inf,
1000,unconnected,TRUE,1,+inf,
1000,unconnected,TRUE,0,+inf,
1000,unconnected,TRUE,1,+inf,
1001,unconnected,connected=nan,0,+inf,
1002,unconnected,connected=nan,1,+inf,
end,,,,		
start,scenario 10,,,		
1000,15,FALSE,0,+inf,
1000,10,TRUE,0,10,
1000,connected=+inf,TRUE,0,10,
1000,50,TRUE,1,10,
1000,50,FALSE,1,50,
1000,10,unconnected,0,50,
1000,10,unconnected,1,50,
1000,10,FALSE,0,50,
1000,20,TRUE,0,20,
1000,10,FALSE,1,10,
1000,15,TRUE,0,15,
1000,25,TRUE,0,15,
1000,25,TRUE,1,15,
1000,10,TRUE,0,15,
1000,20,TRUE,1,15,
1000,15,TRUE,1,15,
1000,25,FALSE,1,25,
1000,45,TRUE,0,45,
1000,10,TRUE,0,45,
1000,18,TRUE,1,45,
1000,18,FALSE,1,18,
1000,20,TRUE,1,18,
1000,25,TRUE,0,18,
1000,40,TRUE,1,18,
1000,41,FALSE,1,41,
1000,41,TRUE,1,41,
1000,10,TRUE,0,41,
1000,15,TRUE,1,41,
1001,20,connected=nan,0,41,
1002,25,connected=nan,1,41,
end,,,,		
start,scenario 11,,,	
1000,-15,FALSE,0,+inf,
1000,-10,TRUE,0,-10,
1000,connected=+inf,TRUE,0,-10,
1000,-50,TRUE,1,-10,
1000,-50,FALSE,1,-50,
1000,-10,unconnected,0,-50,
1000,-10,unconnected,1,-50,
1000,-10,FALSE,0,-50,
1000,-20,TRUE,0,-20,
1000,-10,FALSE,1,-10,
1000,-15,TRUE,0,-15 ,
1000,-25,TRUE,0,-15 ,
1000,-25,TRUE,1,-15 ,
1000,-10,TRUE,0,-15 ,
1000,-20,TRUE,1,-15 ,
1000,-15,TRUE,1,-15 ,
1000,-25,FALSE,1,-25,
1000,-45,TRUE,0,-45 ,
1000,-10,TRUE,0,-45 ,
1000,-18,TRUE,1,-45 ,
1000,-18,FALSE,1,-18,
1000,-20,TRUE,1,-18 ,
1000,-25,TRUE,0,-18 ,
1000,-40,TRUE,1,-18 ,
1000,-41,FALSE,1,-41,
1000,-41,TRUE,1,-41 ,
1000,-10,TRUE,0,-41 ,
1000,-15,TRUE,1,-41 ,
1001,-20,connected=nan,0,-41,
1002,-25,connected=nan,1,-41,
end,,,		
