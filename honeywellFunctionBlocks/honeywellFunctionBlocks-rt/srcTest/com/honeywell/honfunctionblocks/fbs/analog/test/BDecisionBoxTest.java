/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BDecisionBox;
import com.honeywell.honfunctionblocks.fbs.analog.BDecisionOperationEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Test suite to test DecisionBox as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-260
 * <AUTHOR> - Suresh Khatri
 * @since Jan 5, 2018
 */

@NiagaraType
public class BDecisionBoxTest extends BTestNg{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BDecisionBoxTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDecisionBoxTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		decisionBox = getDecisionBox();
		executionParams = new BExecutionParams();
	}

	@AfterClass
	public void tearDown() {
		decisionBox = null;
		executionParams = null;
	}

	private BDecisionBox getDecisionBox() {
		BDecisionBox decisionBox = new BDecisionBox();
		try {
			decisionBox.initHoneywellComponent(null);
		} catch (BlockInitializationException e) {
			e.printStackTrace();
		}
		return decisionBox;
	}

	@DataProvider(name = "provideInSlotNames")
	public Object[][] getInputSlotNames() {
		return new Object[][] { { "YorNinput" }, { "hysteresis" }, { "value1" }, { "value2" }, { "value3" }, { "value4" },
				{ "value5" }, { "value6" }, { "value7" }, { "value8" }, { "value9" }, { "value10" }, { "value11" },
				{ "value12" }, { "value13" }, { "value14" }, { "value15" }, { "value16" }, { "value17" }, { "value18" },
				{ "value19" }, { "value20" }, { "value21" }, { "value22" }, { "value23" }, { "value24" }, { "value25" },
				{ "value26" }, { "value27" }, { "value28" }, { "value29" }, { "value30" }, { "value31" }};
	}

	@DataProvider(name = "provideOutSlotNames")
	public Object[][] getOutputSlotNames() {
		return new Object[][] { { "Y_OUTPUT" }, { "N_OUTPUT" } };
	}

	@DataProvider(name = "provideConfigSlotNames")
	public Object[][] getConfigurationSlotNames() {
		return new Object[][] { { "operation" }, { "numValues" } };
	}

	private Object[][] getExecOrderSlotNames() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "allSlotNames")
	public Object[][] getAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getConfigurationSlotNames()));
		slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);

	}

	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(decisionBox.getSlot(slotName));
	}

	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][] { { "Invalid1" }, { "Invalid2" } };
	}

	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(decisionBox.getSlot(slotName));
	}

	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO() {
		return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {Float.NEGATIVE_INFINITY}, {Float.MAX_VALUE}};
	}

	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		decisionBox.setHysteresis(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getHysteresis().getValue(), value, 0.1);

		decisionBox.setValue1(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue1().getValue(), value, 0.1);

		decisionBox.setValue2(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue2().getValue(), value, 0.1);

		decisionBox.setValue3(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue3().getValue(), value, 0.1);

		decisionBox.setValue4(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue4().getValue(), value, 0.1);

		decisionBox.setValue5(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue5().getValue(), value, 0.1);

		decisionBox.setValue6(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue6().getValue(), value, 0.1);

		decisionBox.setValue7(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue7().getValue(), value, 0.1);

		decisionBox.setValue8(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue8().getValue(), value, 0.1);

		decisionBox.setValue9(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue9().getValue(), value, 0.1);

		decisionBox.setValue10(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue10().getValue(), value, 0.1);

		decisionBox.setValue11(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue11().getValue(), value, 0.1);

		decisionBox.setValue12(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue12().getValue(), value, 0.1);

		decisionBox.setValue13(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue13().getValue(), value, 0.1);

		decisionBox.setValue14(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue14().getValue(), value, 0.1);

		decisionBox.setValue15(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue15().getValue(), value, 0.1);

		decisionBox.setValue16(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue16().getValue(), value, 0.1);

		decisionBox.setValue17(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue17().getValue(), value, 0.1);

		decisionBox.setValue18(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue18().getValue(), value, 0.1);

		decisionBox.setValue19(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue19().getValue(), value, 0.1);

		decisionBox.setValue20(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue20().getValue(), value, 0.1);

		decisionBox.setValue21(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue21().getValue(), value, 0.1);

		decisionBox.setValue22(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue22().getValue(), value, 0.1);

		decisionBox.setValue23(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue23().getValue(), value, 0.1);

		decisionBox.setValue24(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue24().getValue(), value, 0.1);

		decisionBox.setValue25(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue25().getValue(), value, 0.1);

		decisionBox.setValue26(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue26().getValue(), value, 0.1);

		decisionBox.setValue27(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue27().getValue(), value, 0.1);

		decisionBox.setValue28(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue28().getValue(), value, 0.1);

		decisionBox.setValue29(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue29().getValue(), value, 0.1);

		decisionBox.setValue30(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue30().getValue(), value, 0.1);
		
		decisionBox.setValue31(new BHonStatusNumeric(value));
		Assert.assertEquals(decisionBox.getValue31().getValue(), value, 0.1);

		decisionBox.setNumValues(30);
		Assert.assertEquals(decisionBox.getNumValues(), 30);

		decisionBox.setY_OUTPUT(new BHonStatusBoolean(true, BStatus.ok));
		Assert.assertEquals(decisionBox.getY_OUTPUT().getValue(), true);
		
		decisionBox.setY_OUTPUT(new BHonStatusBoolean(false, BStatus.ok));
		Assert.assertEquals(decisionBox.getY_OUTPUT().getValue(), false);
		
		decisionBox.setN_OUTPUT(new BHonStatusBoolean(true, BStatus.ok));
		Assert.assertEquals(decisionBox.getN_OUTPUT().getValue(), true);
		
		decisionBox.setN_OUTPUT(new BHonStatusBoolean(false, BStatus.ok));
		Assert.assertEquals(decisionBox.getN_OUTPUT().getValue(), false);

	}

	//@Test(dataProvider="provideInSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForInSlot(String slotName) {
		BDecisionBox tempBlock = new BDecisionBox();
		LinkCheck checkLink = decisionBox.checkLink(decisionBox, decisionBox.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
		Assert.assertFalse(checkLink.isValid(), "Should not allow to create link from "+slotName);
	}
	
	//@Test(dataProvider="provideConfigSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForConfigSlots(String slotName) {
		BDecisionBox tempBlock = new BDecisionBox();
		LinkCheck checkLink = decisionBox.checkLink(decisionBox, decisionBox.getSlot(slotName), tempBlock.getSlot(slotName), null);
		Assert.assertFalse(checkLink.isValid(), "Should not allow to create link from "+slotName);
	}

	//@Test(dataProvider="provideOutSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForOutSlots(String slotName) {
		BNumericConst nm = new BNumericConst();
		LinkCheck checkLink = decisionBox.checkLink(nm, nm.getSlot("out"), decisionBox.getSlot(slotName), null);
		Assert.assertFalse(checkLink.isValid(), "Should not allow to create linking to "+slotName);
	}
	

	@DataProvider(name = "decisionBoxTestData")
	public Object[][] getDecisionBoxTestData() throws FileNotFoundException {
		return TestDataHelper.getTestDataInTestNGFormat(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/DecisionBox_TestData.csv");
	}

	@Test(dataProvider = "decisionBoxTestData")
	public void TestDecisionBoxBlockWithSequenceTestData(List<String> inputSequence) throws BlockExecutionException {
		BDecisionBox decisionBoxTemp = getDecisionBox();
		
		executionParams.setIterationInterval(TestDataHelper.getInt(inputSequence.get(0), 1000));
		setupInputSlot(decisionBoxTemp, BDecisionBox.YorNinput.getName(), inputSequence.get(1));
		setupInputSlot(decisionBoxTemp, BDecisionBox.hysteresis.getName(), inputSequence.get(2));
		setupInputSlot(decisionBoxTemp, BDecisionBox.value1.getName(), inputSequence.get(3));
		setupInputSlot(decisionBoxTemp, BDecisionBox.value2.getName(), inputSequence.get(4));
		setupInputSlot(decisionBoxTemp, BDecisionBox.value3.getName(), inputSequence.get(5));
		setupInputSlot(decisionBoxTemp, BDecisionBox.value4.getName(), inputSequence.get(6));
		setupInputSlot(decisionBoxTemp, BDecisionBox.value5.getName(), inputSequence.get(7));
		setupInputSlot(decisionBoxTemp, BDecisionBox.operation.getName(), inputSequence.get(8));
		setupInputSlot(decisionBoxTemp, BDecisionBox.numValues.getName(), inputSequence.get(9));

		decisionBoxTemp.executeHoneywellComponent(executionParams);

		Assert.assertEquals(decisionBoxTemp.getY_OUTPUT().getValue(), TestDataHelper.getBoolean(inputSequence.get(10)),
				"Failed for input data Y Output- " + inputSequence);
		Assert.assertEquals(decisionBoxTemp.getN_OUTPUT().getValue(), TestDataHelper.getBoolean(inputSequence.get(11)),
				"Failed for input data N Output- " + inputSequence);
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = decisionBox.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BDecisionBox.numValues.getName(), BDecisionBox.operation.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = decisionBox.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BDecisionBox.YorNinput.getName(), BDecisionBox.hysteresis.getName(), BDecisionBox.value1.getName(), BDecisionBox.value2.getName(), BDecisionBox.value3.getName(),
				BDecisionBox.value4.getName(), BDecisionBox.value5.getName(), BDecisionBox.value6.getName(), BDecisionBox.value7.getName(), BDecisionBox.value8.getName(),
				BDecisionBox.value9.getName(), BDecisionBox.value10.getName(), BDecisionBox.value11.getName(), BDecisionBox.value12.getName(), BDecisionBox.value13.getName(),
				BDecisionBox.value14.getName(), BDecisionBox.value15.getName(), BDecisionBox.value16.getName(), BDecisionBox.value17.getName(), BDecisionBox.value18.getName(),
				BDecisionBox.value19.getName(), BDecisionBox.value20.getName(), BDecisionBox.value21.getName(), BDecisionBox.value22.getName(), BDecisionBox.value23.getName(),
				BDecisionBox.value24.getName(), BDecisionBox.value25.getName(), BDecisionBox.value26.getName(), BDecisionBox.value27.getName(), BDecisionBox.value28.getName(),
				BDecisionBox.value29.getName(), BDecisionBox.value30.getName(), BDecisionBox.value31.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = decisionBox.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BDecisionBox.N_OUTPUT.getName(), BDecisionBox.Y_OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}

	private void setupInputSlot(BDecisionBox decisionBox, final String slotName, final String inputValue) {
		if (TestDataHelper.isConnected(inputValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = decisionBox.getProperty(slotName).getType();
			BConverter converter = null;
			if (srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusBoolean.TYPE)) {
				converter = new BStatusNumericToStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1, nm1.getSlot("out"),
						decisionBox.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				decisionBox.add("Link?", conversionLink);
				conversionLink.activate();
			} else if (srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
				converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nm1, nm1.getSlot("out"),
						decisionBox.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				decisionBox.add("Link?", conversionLink);
				conversionLink.activate();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			} else {
				decisionBox.linkTo(nm1, nm1.getSlot("out"), decisionBox.getSlot(slotName));
			}
			return;
		}
		switch (slotName) {
		case "YorNinput":
			decisionBox.setYorNinput(TestDataHelper.getFiniteStatusBoolean(inputValue));
			break;
		case "hysteresis":
			decisionBox.setHysteresis(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "value1":
			decisionBox.setValue1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "value2":
			decisionBox.setValue2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "value3":
			decisionBox.setValue3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "value4":
			decisionBox.setValue4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "value5":
			decisionBox.setValue5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "operation":
			decisionBox.setOperation(BDecisionOperationEnum.make(TestDataHelper.getInt(inputValue, 1)));
			break;
		case "numValues":
			decisionBox.setNumValues(TestDataHelper.getInt(inputValue, 2));
			break;
		}
	}

	private BDecisionBox decisionBox;
	private BExecutionParams executionParams;

}
