/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BCompare;
import com.honeywell.honfunctionblocks.fbs.analog.BCompareOperationEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BCompareEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BCompareStatusEnum;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Priority Select as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-190
 * <AUTHOR>
 * @since Dec 18, 2017
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BCompareTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BCompareTest(2979906276)1.0$ @*/
/* Generated Mon Dec 18 14:49:51 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
  private BCompare compareBlock = null;
  private BExecutionParams executionParams = null;
  
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  compareBlock = new BCompare();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  compareBlock = null;
  }
  

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"input1"}, {"input2"}, {"onHyst"}, {"offHyst"}};
		  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}, {"OUTPUT_ENUM"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"operation"}, {"negate"}, {"outSave"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"In1"}, {"output"}, {"Operation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(compareBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(compareBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "compare.png");
	  BIcon actualFbIcon = compareBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  compareBlock.setIcon(expectedFbIcon);
	  actualFbIcon = compareBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingInputValues() {
	  verifySettingValue(32767);
	  verifySettingValue(2.2250738585072014E-308);
	  verifySettingValue(4.9e-324);
	  verifySettingValue(1.7976931348623157e+308);
	  verifySettingValue(Double.NEGATIVE_INFINITY);
	  verifySettingValue(Double.POSITIVE_INFINITY);
	  verifySettingValue(Double.NaN);
	  verifySettingValue(-34);
	  verifySettingValue(34);
	  verifySettingValue(255);
	  verifySettingValue(0);
	  
	  compareBlock.setOperation(BCompareOperationEnum.DEFAULT);	  
	  Assert.assertEquals(compareBlock.getOperation(),BCompareOperationEnum.Equals);
	  compareBlock.setOperation(BCompareOperationEnum.Equals);	  
	  Assert.assertEquals(compareBlock.getOperation(),BCompareOperationEnum.Equals);
	  compareBlock.setOperation(BCompareOperationEnum.LessThan);	  
	  Assert.assertEquals(compareBlock.getOperation(),BCompareOperationEnum.LessThan);
	  compareBlock.setOperation(BCompareOperationEnum.GreaterThan);	  
	  Assert.assertEquals(compareBlock.getOperation(),BCompareOperationEnum.GreaterThan);
	  
	  compareBlock.setOUTPUT(new BNegatableStatusBoolean(false,BStatus.ok,false));
	  compareBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(compareBlock.getOUTPUT().getValue(),false);
	  Assert.assertEquals(compareBlock.getOUTPUT().getNegate(),false);
	  
	  compareBlock.setOUTPUT(new BNegatableStatusBoolean(false,BStatus.ok,true));
	  compareBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(compareBlock.getOUTPUT().getValue(),false);
	  Assert.assertEquals(compareBlock.getOUTPUT().getNegate(),true);
	  
	  compareBlock.setOUTPUT(new BNegatableStatusBoolean(true,BStatus.ok,false));
	  compareBlock.getOUTPUT().setNegate(false);
	  Assert.assertEquals(compareBlock.getOUTPUT().getValue(),true);
	  Assert.assertEquals(compareBlock.getOUTPUT().getNegate(),false);
	  
	  compareBlock.setOUTPUT(new BNegatableStatusBoolean(true,BStatus.ok,true));
	  compareBlock.getOUTPUT().setNegate(true);
	  Assert.assertEquals(compareBlock.getOUTPUT().getValue(),true);
	  Assert.assertEquals(compareBlock.getOUTPUT().getNegate(),true);	  
  }
  
  private void verifySettingValue(double snValue) {
	  compareBlock.setInput1(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(compareBlock.getInput1().getValue()));
	  else
		  Assert.assertEquals(compareBlock.getInput1().getValue(), snValue, 0.1);	  
	  
	  compareBlock.setInput2(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(compareBlock.getInput1().getValue()));
	  else
		  Assert.assertEquals(compareBlock.getInput2().getValue(), snValue, 0.1);
	  
	  
	  compareBlock.setOnHyst(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(compareBlock.getInput1().getValue()));
	  else
		  Assert.assertEquals(compareBlock.getOnHyst().getValue(), snValue, 0.1);
	  
	  compareBlock.setOffHyst(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(compareBlock.getInput1().getValue()));
	  else
		  Assert.assertEquals(compareBlock.getOffHyst().getValue(), snValue, 0.1);
}
  
  
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Compare_TestData.csv");
	}

	  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	  public void testCompareBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BCompare compareBlock = new BCompare();
		  setupNumericSlot(compareBlock, BCompare.input1.getName(), inputs.get(1));
		  setupNumericSlot(compareBlock, BCompare.input2.getName(), inputs.get(2));
		  setupNumericSlot(compareBlock, BCompare.onHyst.getName(), inputs.get(3));
		  setupNumericSlot(compareBlock, BCompare.offHyst.getName(), inputs.get(4));
		  compareBlock.setOperation(BCompareOperationEnum.make(inputs.get(5)));
		  compareBlock.setNegate(TestDataHelper.getBoolean(inputs.get(6)));
		  BCompareEnum compareEnum = TestDataHelper.getBoolean(inputs.get(17)) ? BCompareEnum.True : BCompareEnum.False;
		  BCompareEnum compareResultEnum = TestDataHelper.getBoolean(inputs.get(7)) ? BCompareEnum.True : BCompareEnum.False;
		  compareBlock.executeHoneywellComponent(executionParams);
		  
		  //Setting Previous Output
		  compareBlock.getOUTPUT().setValue(TestDataHelper.getBoolean(inputs.get(17)));
		  compareBlock.getOUTPUT_ENUM().setValue(compareEnum);
		  
		  
		  //Execute again so that OUTPUT_ENUM value is set as per logic
		  compareBlock.executeHoneywellComponent(executionParams);
		  
		  Assert.assertEquals(compareBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(7)));
		  Assert.assertEquals(compareBlock.getOUTPUT_ENUM().getValue().getOrdinal(), compareResultEnum.getOrdinal());
	  }

	private void setupNumericSlot(BCompare compBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = compBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),compBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				compBlock.add("Link?",conversionLink );			
				conversionLink.activate();
			}else{
				compBlock.linkTo(nm1, nm1.getSlot("out"), compBlock.getSlot(slotName));
			}			
			
			return;
		}
		
		switch (slotName) {
		case "input1":
			compBlock.setInput1(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input2":
			compBlock.setInput2(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "onHyst":
			compBlock.setOnHyst(TestDataHelper.getHonStatusNumeric(inputValue, 0));
			break;
			
		case "offHyst":
			compBlock.setOffHyst(TestDataHelper.getHonStatusNumeric(inputValue, 0));
			break;
			
		default:
			break;
		}
	}
	
	//@Test(groups={"testLinkRules"})
	public void testLinkCreation() {
	  BCompare src = new BCompare();
	  BCompare target = new BCompare();
	  
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("input1"), target, target.getSlot("input1"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("input2"), target, target.getSlot("input2"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("onHyst"), target, target.getSlot("onHyst"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("offHyst"), target, target.getSlot("offHyst"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("operation"), target, target.getSlot("operation"));	  
  }
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = compareBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BCompare.operation.getName());
		Assert.assertEquals(configList.get(1).getName(), BCompare.negate.getName());
		Assert.assertEquals(configList.get(2).getName(), BCompare.outSave.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = compareBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BCompare.input1.getName(), 
				BCompare.input2.getName(), BCompare.onHyst.getName(), BCompare.offHyst.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = compareBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BCompare.OUTPUT.getName(), BCompare.OUTPUT_ENUM.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testScenarioWithNoOutputChange() throws BlockExecutionException {
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.LessThan, false, false);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.LessThan, false, true);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.LessThan, true, false);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.LessThan, true, true);
		
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.GreaterThan, false, false);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.GreaterThan, false, true);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.GreaterThan, true, false);
		executeTestCase(50, 50, 1, 1, BCompareOperationEnum.GreaterThan, true, true);
	}
	
	private void executeTestCase(double in1, double in2, double onHyst, double offHyst, BCompareOperationEnum operation,
			boolean negate, Boolean prevOutput) throws BlockExecutionException {
		BCompare compareBlock = new BCompare();
		setupNumericSlot(compareBlock, BCompare.input1.getName(), in1+"");
		setupNumericSlot(compareBlock, BCompare.input2.getName(), in2+"");
		setupNumericSlot(compareBlock, BCompare.onHyst.getName(), onHyst+"");
		setupNumericSlot(compareBlock, BCompare.offHyst.getName(), offHyst+"");
		compareBlock.setOperation(operation);
		compareBlock.setNegate(negate);
		compareBlock.getOUTPUT().setValue(prevOutput);
		BCompareEnum prevEnum = BCompareEnum.Null;
		if(prevOutput != null) {
			if(prevOutput) {
				prevEnum = BCompareEnum.True;
			}else {
				prevEnum = BCompareEnum.False;
			}
		}
		

		compareBlock.executeHoneywellComponent(executionParams);
		compareBlock.setOUTPUT_ENUM(new BCompareStatusEnum(prevEnum, BStatus.ok));
		for(int i=0; i<5; i++) {
			compareBlock.executeHoneywellComponent(executionParams);
			Assert.assertEquals(compareBlock.getOUTPUT().getValue(), prevOutput.booleanValue(), "Failed for iteration-"+i);
		}
	}

}
