package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.analog.BMinMaxAverageBlock;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;


@NiagaraType
/**
 * Implementation of Maximum block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-188
 * <AUTHOR>
 *
 */
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BMaximumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BMaximumTest(2979906276)1.0$ @*/
/* Generated Mon Nov 06 14:10:28 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMaximumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  maxBlock = new BMaximum();
  }
  
  @AfterClass
  public void tearDown() {
	  maxBlock = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"in7"}, {"in8"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"ignoreInvalidInput"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"In1"}, {"out"}, {"IgnoreInvalidInput"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(maxBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(maxBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "maximum.png");
	  BIcon actualFbIcon = maxBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  maxBlock.setIcon(expectedFbIcon);
	  actualFbIcon = maxBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingInputValues() {
	verifySettingValue(32767);
	verifySettingValue(2.2250738585072014E-308);
	verifySettingValue(4.9e-324);
	verifySettingValue(1.7976931348623157e+308);
	verifySettingValue(Double.NEGATIVE_INFINITY);
	verifySettingValue(Double.POSITIVE_INFINITY);
  }

	private void verifySettingValue(double snValue) {
		maxBlock.setIgnoreInvalidInput(true); Assert.assertEquals(maxBlock.getIgnoreInvalidInput(), true);
		maxBlock.setIn1(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn1().getValue(), snValue, 0.1);
		maxBlock.setIn2(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn2().getValue(), snValue, 0.1);
		maxBlock.setIn3(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn3().getValue(), snValue, 0.1);
		maxBlock.setIn4(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn4().getValue(), snValue, 0.1);
		maxBlock.setIn5(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn5().getValue(), snValue, 0.1);
		maxBlock.setIn6(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn6().getValue(), snValue, 0.1);
		maxBlock.setIn7(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn7().getValue(), snValue, 0.1);
		maxBlock.setIn8(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getIn8().getValue(), snValue, 0.1);
		maxBlock.setOUTPUT(new BHonStatusNumeric(snValue)); Assert.assertEquals(maxBlock.getOUTPUT().getValue(), snValue, 0.1);
	}
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Maximum_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testMaxBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BMaximum maxBlock = new BMaximum();
	  maxBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in1.getName(), inputs.get(2));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in2.getName(), inputs.get(3));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in3.getName(), inputs.get(4));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in4.getName(), inputs.get(5));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in5.getName(), inputs.get(6));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in6.getName(), inputs.get(7));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in7.getName(), inputs.get(8));
	  setupNumericSlot(maxBlock, BMinMaxAverageBlock.in8.getName(), inputs.get(9));
	  
	  maxBlock.executeHoneywellComponent(null);
	  
	  Assert.assertEquals(maxBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(10), 0d), 0.1);
	  maxBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param maxBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BMaximum maxBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			maxBlock.linkTo(nm1, nm1.getSlot("out"), maxBlock.getSlot(slotName));
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			return;
		}
		
		switch (slotName) {
		case "in1":
			maxBlock.setIn1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in2":
			maxBlock.setIn2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in3":
			maxBlock.setIn3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in4":
			maxBlock.setIn4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in5":
			maxBlock.setIn5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in6":
			maxBlock.setIn6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in7":
			maxBlock.setIn7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in8":
			maxBlock.setIn8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		default:
			break;
		}
	}
  

  //@Test(dependsOnMethods={"testSlotAvailability"}, groups={"testLinkRules"})
  public void testLinkRules() {
	  BMaximum target = new BMaximum();
	  BMaximum src = new BMaximum();
	  
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in1"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in2"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in3"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in4"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in5"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in6"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in7"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in8"));
	  
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("OUTPUT"),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = maxBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BMinMaxAverageBlock.ignoreInvalidInput.getName());
	}
	
  BMaximum maxBlock;
}
