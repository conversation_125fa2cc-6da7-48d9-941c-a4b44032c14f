/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BAnalogLatch;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Analog Latch as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-191
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 13, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BAnalogLatchTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BAnalogLatchTest(2979906276)1.0$ @*/
/* Generated Mon Dec 11 12:03:53 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAnalogLatchTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  anaLatchBlock = new BAnalogLatch();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  anaLatchBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"latch"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"Y"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"Latch"}, {"y"}, {"Output"}, {"outptu"}, {"IgnoreInvalidInput"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(anaLatchBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(anaLatchBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "analog_latch.png");
	  BIcon actualFbIcon = anaLatchBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  anaLatchBlock.setIcon(expectedFbIcon);
	  actualFbIcon = anaLatchBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23,true}, {0,true}, {60,false}, {32767,false}, {32768,true}, {40000,true}, {Float.NEGATIVE_INFINITY,true}, {Float.MAX_VALUE,true}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInhystRelayBlock(double snValue,boolean latchVal) {
	  anaLatchBlock.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(anaLatchBlock.getX().getValue(), snValue, 0.1);
	  
	  anaLatchBlock.setLatch(new BNegatableFiniteStatusBoolean(latchVal, BStatus.ok, false));
	  anaLatchBlock.getLatch().setNegate(false);
	  Assert.assertEquals(anaLatchBlock.getLatch().getValue(), latchVal);
	  Assert.assertEquals(anaLatchBlock.getLatch().getNegate(), false);
	  
	  anaLatchBlock.setLatch(new BNegatableFiniteStatusBoolean(latchVal, BStatus.ok, true));
	  anaLatchBlock.getLatch().setNegate(true);
	  Assert.assertEquals(anaLatchBlock.getLatch().getValue(), latchVal);
	  Assert.assertEquals(anaLatchBlock.getLatch().getNegate(), true);
	  
	  anaLatchBlock.setY(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(anaLatchBlock.getY().getValue(), snValue, 0.1);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/AnalogLatch_TestData.csv");
  }

  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testAnalogLatchBlockWithTestData(List<String> inputs) throws BlockExecutionException, BlockInitializationException {
	  BAnalogLatch anaLatchBlock = new BAnalogLatch();
	  anaLatchBlock.initHoneywellComponent(null);
	  anaLatchBlock.getLatch().setValue(false);
	  anaLatchBlock.executeHoneywellComponent(executionParams);
	  
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericBooleanSlot(anaLatchBlock, BAnalogLatch.x.getName(), inputs.get(1), null);
	  setupNumericBooleanSlot(anaLatchBlock, BAnalogLatch.latch.getName(), inputs.get(2), inputs.get(3));
	  
	  anaLatchBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(anaLatchBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(4), Double.POSITIVE_INFINITY), 0.1);

	  anaLatchBlock = null;
  }
  
  @DataProvider(name="provideSequencedTestData")
  public Object[][] getSequencedTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/AnalogLatch_SequencedTestData.csv");
  }

  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideSequencedTestData")
  public void testAnalogLatchBlockWithSequenceOfTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
	  BAnalogLatch anaLatchBlock = new BAnalogLatch();
	  
	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
		  List<String> inputs = iterator.next();	  
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericBooleanSlot(anaLatchBlock, BAnalogLatch.x.getName(), inputs.get(1), null);
		  setupNumericBooleanSlot(anaLatchBlock, BAnalogLatch.latch.getName(), inputs.get(2), inputs.get(3));

		  if(seqNo==1)	anaLatchBlock.initHoneywellComponent(null);
		  anaLatchBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(anaLatchBlock.getY().getValue(), TestDataHelper.getDouble(inputs.get(4), Double.POSITIVE_INFINITY), 0.1,
					"Failed at step #" + seqNo + " for input data " + inputs + "; ");
	  }
	  anaLatchBlock = null;
  }
  
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = anaLatchBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BAnalogLatch.x.getName(),BAnalogLatch.latch.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = anaLatchBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BAnalogLatch.Y.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
	  /**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param analogLatchBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericBooleanSlot(BAnalogLatch analogLatchBlock, final String slotName, final String inputValue, final String negateString){
		  boolean negate=false;
		  if(negateString!=null) {
			  negate = TestDataHelper.getBoolean(negateString);

			  //Set negate value
			  INegatableStatusValue nsb = (INegatableStatusValue) analogLatchBlock.get(slotName);
			  nsb.setNegate(negate);
		  }

		  if(TestDataHelper.isConnected(inputValue)){
			  //Create numeric constant and link to given slotName. Set the given value to the numeric constant
			  BNumericConst nm1 = new BNumericConst();
			  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			  Type srcType = nm1.getOut().getType();
			  Type targetType = analogLatchBlock.getProperty(slotName).getType();			
			  BConverter converter = null;
			  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				  converter = new BStatusNumericToFiniteStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),analogLatchBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  analogLatchBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  }else{
				  analogLatchBlock.linkTo(nm1, nm1.getSlot("out"), analogLatchBlock.getSlot(slotName));
			  }
			  
			  return;
		  }

		  switch (slotName) {
		  case "x":
			  analogLatchBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			  break;

		  case "latch":
			  analogLatchBlock.setLatch(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, negate));
			  break;

		  default:
			  break;
		  }
	  }
	  

  
  //@Test(groups={"testLinkRules"})
  public void testLinkRules() {
	  BAnalogLatch tempBlock = new BAnalogLatch();
	  checkOutgoingLink(tempBlock, BAnalogLatch.x, false);   
	  checkOutgoingLink(tempBlock, BAnalogLatch.latch, false);
	  
	  //Make sure Output slot should NOT be a target slot
	  BNumericConst nm = new BNumericConst();
	  LinkCheck checkLink = anaLatchBlock.checkLink(nm, nm.getSlot("out"),
			  anaLatchBlock.getSlot(BAnalogLatch.Y.getName()), null);
	  Assert.assertFalse(checkLink.isValid());
  }
  
  private void checkOutgoingLink(BAnalogLatch tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = anaLatchBlock.checkLink(anaLatchBlock, anaLatchBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }

  BAnalogLatch anaLatchBlock;	
  BExecutionParams executionParams;
}
