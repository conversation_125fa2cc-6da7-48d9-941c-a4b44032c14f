package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BIcon;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMinMaxAverageBlock;
import com.honeywell.honfunctionblocks.fbs.analog.BMinimum;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;


@NiagaraType
/**
 * Implementation of Minimum block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-187
 * <AUTHOR>
 *
 */
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})

public class BMinimumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BMinimumTest(2979906276)1.0$ @*/
/* Generated Mon Nov 06 14:10:28 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMinimumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  minBlock = new BMinimum();
  }
  
  
  @AfterClass
  public void tearDown() {
	  minBlock = null;
  }


  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"in7"}, {"in8"}};	  
  }
  
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"ignoreInvalidInput"}};
  }
  
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
    
  
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"In1"}, {"out"}, {"IgnoreInvalidInput"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(minBlock.getSlot(slotName));
  }  
  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(minBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "minimum.png");
	  BIcon actualFbIcon = minBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  minBlock.setIcon(expectedFbIcon);
	  actualFbIcon = minBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test(dataProvider="provideInSlotNames", dependsOnMethods={"testSlotAvailability"})
  public void testSettingInputValues(String slotName) {
	BStatusNumeric snValue = new BHonStatusNumeric();
	snValue.setValue(1);
	minBlock.set(minBlock.getProperty(slotName), snValue);
	Assert.assertEquals(minBlock.get(minBlock.getProperty(slotName)), snValue);
	
	snValue.setValue(-1);
	minBlock.set(minBlock.getProperty(slotName), snValue);
	Assert.assertEquals(minBlock.get(minBlock.getProperty(slotName)), snValue);	
	
	snValue.setValue(Double.NEGATIVE_INFINITY);
	minBlock.set(minBlock.getProperty(slotName), snValue);
	Assert.assertEquals(minBlock.get(minBlock.getProperty(slotName)), snValue);
	
	snValue.setValue(Double.POSITIVE_INFINITY);
	minBlock.set(minBlock.getProperty(slotName), snValue);
	Assert.assertEquals(minBlock.get(minBlock.getProperty(slotName)), snValue);
  }
  
  
  
  @Test
  public void testSettingInputValues() {
	verifySettingValue(32767);
	verifySettingValue(2.2250738585072014E-308);
	verifySettingValue(4.9e-324);
	verifySettingValue(1.7976931348623157e+308);
	verifySettingValue(Double.NEGATIVE_INFINITY);
	verifySettingValue(Double.POSITIVE_INFINITY);
  }

  private void verifySettingValue(double snValue) {
	minBlock.setIgnoreInvalidInput(true); Assert.assertEquals(minBlock.getIgnoreInvalidInput(), true);
	minBlock.setIn1(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn1().getValue(), snValue, 0.1);
	minBlock.setIn2(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn2().getValue(), snValue, 0.1);
	minBlock.setIn3(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn3().getValue(), snValue, 0.1);
	minBlock.setIn4(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn4().getValue(), snValue, 0.1);
	minBlock.setIn5(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn5().getValue(), snValue, 0.1);
	minBlock.setIn6(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn6().getValue(), snValue, 0.1);
	minBlock.setIn7(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn7().getValue(), snValue, 0.1);
	minBlock.setIn8(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getIn8().getValue(), snValue, 0.1);
	minBlock.setOUTPUT(new BHonStatusNumeric(snValue)); Assert.assertEquals(minBlock.getOUTPUT().getValue(), snValue, 0.1);
  }
  
 
  @DataProvider(name="provideTestData")
  public Object[][] getTesData(){
	BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Min_ValidInputs.csv").get();
	CsvReader readValidInputs;
	ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
	try {
		readValidInputs = new CsvReader(file.getInputStream());
		List<String> rec;
		while ((rec = readValidInputs.read()) != null) {
			validInputs.add(rec);
		}
		readValidInputs.close();
	} catch (IOException e) {
		e.printStackTrace();
		validInputs = null;
	}
	
	Object [][] objArray = new Object[validInputs.size()][];
	for(int i=0;i< validInputs.size();i++){
		objArray[i] = new Object[1];
		objArray[i][0] = validInputs.get(i);
	} 
	
	return objArray;
}
  
  
  @Test(dataProvider="provideTestData")
  public void testMinBlockWithTestData(List<String> inputs) {	  
	  BMinimum tempMinBlock = new BMinimum();
	  BExecutionParams executionParams = new BExecutionParams();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
	  tempMinBlock.setIgnoreInvalidInput(TestDataHelper.getBoolean(inputs.get(1)));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in1.getName(), inputs.get(2));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in2.getName(), inputs.get(3));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in3.getName(), inputs.get(4));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in4.getName(), inputs.get(5));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in5.getName(), inputs.get(6));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in6.getName(), inputs.get(7));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in7.getName(), inputs.get(8));
	  setupNumericSlot(tempMinBlock, BMinMaxAverageBlock.in8.getName(), inputs.get(9));
	  
		  
	  try {
		  tempMinBlock.executeHoneywellComponent(executionParams);
	  } catch (BlockExecutionException e) {
		  e.printStackTrace();
	  }
	  Assert.assertEquals(tempMinBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(10), 0d), 0.1);
  }
  
  
  public void setupNumericSlot(BMinimum minimumBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();			
			minimumBlock.linkTo(nm1, nm1.getSlot("out"), minimumBlock.getSlot(slotName));
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			return;
		}
		
		switch (slotName) {
		case "in1":
			minimumBlock.setIn1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in2":
			minimumBlock.setIn2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in3":
			minimumBlock.setIn3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in4":
			minimumBlock.setIn4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in5":
			minimumBlock.setIn5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in6":
			minimumBlock.setIn6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in7":
			minimumBlock.setIn7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in8":
			minimumBlock.setIn8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;

		default:
			break;
		}
	}
  
  //@Test(dependsOnMethods={"testSlotAvailability"}, groups={"testLinkRules"})
  public void testLinkRules() {
	  BMinimum target = new BMinimum();
	  BMinimum src = new BMinimum();
	  
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in1"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in2"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in3"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in4"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in5"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in6"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in7"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot("OUTPUT"),target,target.getSlot("in8"));
	  
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("OUTPUT"),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = minBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BMinMaxAverageBlock.ignoreInvalidInput.getName());
	}
	
  
  BMinimum minBlock;
}
