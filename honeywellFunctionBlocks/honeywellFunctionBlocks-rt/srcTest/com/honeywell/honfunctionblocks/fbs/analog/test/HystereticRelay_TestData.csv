# Test data for HystereticRelay block
# Requirement ID: F1PLT-ADR-405; TestCase ID: F1PLT-ATC-193
#
# TestData format: Iteration Interval,in,onVal,offVal,minOn,minOff,OUTPUT,EnsureMinTime(0-NoWait,1=EnsureMinOn,2=EnsureMinOff),NegOUTPUT
#
# Note: Given numeric values will be directly set into the slots. To pass numeric values (by creating link) you should use connected/unconnected=5 syntax
#
1000,10,15,8,0,0,FALSE,0,0
1000,15,10,8,0,0,TRUE,0,0
1000,10,15,11,0,0,FALSE,0,0
1000,15,15,16,0,0,TRUE,0,0
1000,15,10,8,0,0,TRUE,0,0
1000,15,16,6,0,0,FALSE,0,0
1000,1,1,1,0,0,TRUE,0,0
1000,0,0,0,0,0,TRUE,0,0
1000,10,10,10,0,0,TRUE,0,0
1000,-10,-10,-10,0,0,TRUE,0,0
1000,20.567,20.567,20.567,0,0,TRUE,0,0
1000,-20.567,-20.567,-20.567,0,0,TRUE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,connected=nan,connected=nan,connected=nan,0,0,FALSE,0,0
1000,1,0,1,0,0,FALSE,0,0
1000,0,1,0,0,0,FALSE,0,0
1000,10,-10,10,0,0,FALSE,0,0
1000,-10,10,-10,0,0,FALSE,0,0
1000,20.567,-20.567,20.567,0,0,FALSE,0,0
1000,-20.567,20.567,-20.567,0,0,FALSE,0,0
1000,unconnected=5,10,unconnected=5,0,0,FALSE,0,0
1000,connected=+inf,10,connected=+inf,0,0,FALSE,0,0
1000,connected=-inf,10,connected=-inf,0,0,FALSE,0,0
1000,connected=nan,10,connected=nan,0,0,FALSE,0,0
1000,1,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,0,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,10,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,-10,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,20.567,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,-20.567,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,connected=+inf,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,connected=-inf,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,connected=nan,unconnected=5,unconnected=5,0,0,FALSE,0,0
1000,1,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,0,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,10,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,-10,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,20.567,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,-20.567,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,unconnected=5,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,connected=-inf,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,connected=nan,connected=+inf,connected=+inf,0,0,FALSE,0,0
1000,1,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,0,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,10,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,-10,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,20.567,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,-20.567,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,unconnected=5,connected=-inf,connected=-inf,0,0,FALSE,0,0
1000,connected=+inf,connected=-inf,connected=-inf,0,0,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,0,0,TRUE,0,0
1000,connected=nan,connected=-inf,connected=-inf,0,0,FALSE,0,0
1000,1,connected=nan,connected=nan,0,0,FALSE,0,0
1000,0,connected=nan,connected=nan,0,0,FALSE,0,0
1000,10,connected=nan,connected=nan,0,0,FALSE,0,0
1000,-10,connected=nan,connected=nan,0,0,FALSE,0,0
1000,20.567,connected=nan,connected=nan,0,0,FALSE,0,0
1000,-20.567,connected=nan,connected=nan,0,0,FALSE,0,0
1000,unconnected=5,connected=nan,connected=nan,0,0,FALSE,0,0
1000,connected=+inf,connected=nan,connected=nan,0,0,FALSE,0,0
1000,connected=-inf,connected=nan,connected=nan,0,0,FALSE,0,0
1000,connected=nan,connected=nan,connected=nan,0,0,FALSE,0,0
1000,1,1,1,0,0,TRUE,0,0
1000,0,1,1,0,0,FALSE,0,0
1000,10,1,1,0,0,TRUE,0,0
1000,-10,1,1,0,0,FALSE,0,0
1000,20.567,1,1,0,0,TRUE,0,0
1000,-20.567,1,1,0,0,FALSE,0,0
1000,unconnected=5,1,1,0,0,FALSE,0,0
1000,connected=+inf,1,1,0,0,FALSE,0,0
1000,connected=-inf,1,1,0,0,FALSE,0,0
1000,connected=nan,1,1,0,0,FALSE,0,0
1000,1,0,0,0,0,TRUE,0,0
1000,0,0,0,0,0,TRUE,0,0
1000,10,0,0,0,0,TRUE,0,0
1000,-10,0,0,0,0,FALSE,0,0
1000,20.567,0,0,0,0,TRUE,0,0
1000,-20.567,0,0,0,0,FALSE,0,0
1000,unconnected=5,0,0,0,0,FALSE,0,0
1000,connected=+inf,0,0,0,0,FALSE,0,0
1000,connected=-inf,0,0,0,0,FALSE,0,0
1000,connected=nan,0,0,0,0,FALSE,0,0
1000,1,10,10,0,0,FALSE,0,0
1000,0,10,10,0,0,FALSE,0,0
1000,10,10,10,0,0,TRUE,0,0
1000,-10,10,10,0,0,FALSE,0,0
1000,20.567,10,10,0,0,TRUE,0,0
1000,-20.567,10,10,0,0,FALSE,0,0
1000,unconnected=5,10,10,0,0,FALSE,0,0
1000,connected=+inf,10,10,0,0,FALSE,0,0
1000,connected=-inf,10,10,0,0,FALSE,0,0
1000,connected=nan,10,10,0,0,FALSE,0,0
1000,1,-10,-10,0,0,TRUE,0,0
1000,0,-10,-10,0,0,TRUE,0,0
1000,10,-10,-10,0,0,TRUE,0,0
1000,-10,-10,-10,0,0,TRUE,0,0
1000,20.567,-10,-10,0,0,TRUE,0,0
1000,-20.567,-10,-10,0,0,FALSE,0,0
1000,unconnected=5,-10,-10,0,0,FALSE,0,0
1000,connected=+inf,-10,-10,0,0,FALSE,0,0
1000,connected=-inf,-10,-10,0,0,FALSE,0,0
1000,connected=nan,-10,-10,0,0,FALSE,0,0
1000,1,20.567,20.567,0,0,FALSE,0,0
1000,0,20.567,20.567,0,0,FALSE,0,0
1000,10,20.567,20.567,0,0,FALSE,0,0
1000,-10,20.567,20.567,0,0,FALSE,0,0
1000,20.567,20.567,20.567,0,0,TRUE,0,0
1000,-20.567,20.567,20.567,0,0,FALSE,0,0
1000,unconnected=5,20.567,20.567,0,0,FALSE,0,0
1000,connected=+inf,20.567,20.567,0,0,FALSE,0,0
1000,connected=-inf,20.567,20.567,0,0,FALSE,0,0
1000,connected=nan,20.567,20.567,0,0,FALSE,0,0
1000,1,-20.567,-20.567,0,0,TRUE,0,0
1000,0,-20.567,-20.567,0,0,TRUE,0,0
1000,10,-20.567,-20.567,0,0,TRUE,0,0
1000,-10,-20.567,-20.567,0,0,TRUE,0,0
1000,20.567,-20.567,-20.567,0,0,TRUE,0,0
1000,-20.567,-20.567,-20.567,0,0,TRUE,0,0
1000,unconnected=5,-20.567,-20.567,0,0,FALSE,0,0
1000,connected=+inf,-20.567,-20.567,0,0,FALSE,0,0
1000,connected=-inf,-20.567,-20.567,0,0,FALSE,0,0
1000,connected=nan,-20.567,-20.567,0,0,FALSE,0,0
1000,10,15,8,2,0,FALSE,0,0
1000,15,10,8,2,0,TRUE,1,0
1000,10,15,11,2,0,FALSE,0,0
1000,15,15,16,2,0,TRUE,1,0
1000,15,10,8,2,0,TRUE,1,0
1000,15,16,6,2,0,FALSE,0,0
1000,1,1,1,2,0,TRUE,1,0
1000,0,0,0,2,0,TRUE,1,0
1000,10,10,10,2,0,TRUE,1,0
1000,-10,-10,-10,2,0,TRUE,1,0
1000,20.567,20.567,20.567,2,0,TRUE,1,0
1000,-20.567,-20.567,-20.567,2,0,TRUE,1,0
1000,unconnected=5,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,2,0,TRUE,0,0
1000,connected=nan,connected=nan,connected=nan,2,0,FALSE,0,0
1000,1,0,1,2,0,FALSE,1,0
1000,0,1,0,2,0,FALSE,0,0
1000,10,-10,10,2,0,FALSE,1,0
1000,-10,10,-10,2,0,FALSE,0,0
1000,20.567,-20.567,20.567,2,0,FALSE,1,0
1000,-20.567,20.567,-20.567,2,0,FALSE,0,0
1000,unconnected=5,10,unconnected=5,2,0,FALSE,0,0
1000,connected=+inf,10,connected=+inf,2,0,FALSE,0,0
1000,connected=-inf,10,connected=-inf,2,0,FALSE,0,0
1000,connected=nan,10,connected=nan,2,0,FALSE,0,0
1000,1,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,0,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,10,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,-10,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,20.567,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,-20.567,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,connected=+inf,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,connected=-inf,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,connected=nan,unconnected=5,unconnected=5,2,0,FALSE,0,0
1000,1,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,0,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,10,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,-10,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,20.567,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,-20.567,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,unconnected=5,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,connected=-inf,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,connected=nan,connected=+inf,connected=+inf,2,0,FALSE,0,0
1000,1,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,0,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,10,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,-10,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,20.567,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,-20.567,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,unconnected=5,connected=-inf,connected=-inf,2,0,FALSE,0,0
1000,connected=+inf,connected=-inf,connected=-inf,2,0,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,2,0,TRUE,1,0
1000,connected=nan,connected=-inf,connected=-inf,2,0,FALSE,0,0
1000,1,connected=nan,connected=nan,2,0,FALSE,0,0
1000,0,connected=nan,connected=nan,2,0,FALSE,0,0
1000,10,connected=nan,connected=nan,2,0,FALSE,0,0
1000,-10,connected=nan,connected=nan,2,0,FALSE,0,0
1000,20.567,connected=nan,connected=nan,2,0,FALSE,0,0
1000,-20.567,connected=nan,connected=nan,2,0,FALSE,0,0
1000,unconnected=5,connected=nan,connected=nan,2,0,FALSE,0,0
1000,connected=+inf,connected=nan,connected=nan,2,0,FALSE,0,0
1000,connected=-inf,connected=nan,connected=nan,2,0,FALSE,0,0
1000,connected=nan,connected=nan,connected=nan,2,0,FALSE,0,0
1000,1,1,1,2,0,TRUE,1,0
1000,0,1,1,2,0,FALSE,0,0
1000,10,1,1,2,0,TRUE,1,0
1000,-10,1,1,2,0,FALSE,0,0
1000,20.567,1,1,2,0,TRUE,1,0
1000,-20.567,1,1,2,0,FALSE,0,0
1000,unconnected=5,1,1,2,0,FALSE,0,0
1000,connected=+inf,1,1,2,0,FALSE,0,0
1000,connected=-inf,1,1,2,0,FALSE,0,0
1000,connected=nan,1,1,2,0,FALSE,0,0
1000,1,0,0,2,0,TRUE,1,0
1000,0,0,0,2,0,TRUE,1,0
1000,10,0,0,2,0,TRUE,1,0
1000,-10,0,0,2,0,FALSE,0,0
1000,20.567,0,0,2,0,TRUE,1,0
1000,-20.567,0,0,2,0,FALSE,0,0
1000,unconnected=5,0,0,2,0,FALSE,0,0
1000,connected=+inf,0,0,2,0,FALSE,0,0
1000,connected=-inf,0,0,2,0,FALSE,0,0
1000,connected=nan,0,0,2,0,FALSE,0,0
1000,1,10,10,2,0,FALSE,0,0
1000,0,10,10,2,0,FALSE,0,0
1000,10,10,10,2,0,TRUE,1,0
1000,-10,10,10,2,0,FALSE,0,0
1000,20.567,10,10,2,0,TRUE,1,0
1000,-20.567,10,10,2,0,FALSE,0,0
1000,unconnected=5,10,10,2,0,FALSE,0,0
1000,connected=+inf,10,10,2,0,FALSE,0,0
1000,connected=-inf,10,10,2,0,FALSE,0,0
1000,connected=nan,10,10,2,0,FALSE,0,0
1000,1,-10,-10,2,0,TRUE,1,0
1000,0,-10,-10,2,0,TRUE,1,0
1000,10,-10,-10,2,0,TRUE,1,0
1000,-10,-10,-10,2,0,TRUE,1,0
1000,20.567,-10,-10,2,0,TRUE,1,0
1000,-20.567,-10,-10,2,0,FALSE,0,0
1000,unconnected=5,-10,-10,2,0,FALSE,0,0
1000,connected=+inf,-10,-10,2,0,FALSE,0,0
1000,connected=-inf,-10,-10,2,0,FALSE,0,0
1000,connected=nan,-10,-10,2,0,FALSE,0,0
1000,1,20.567,20.567,2,0,FALSE,0,0
1000,0,20.567,20.567,2,0,FALSE,0,0
1000,10,20.567,20.567,2,0,FALSE,0,0
1000,-10,20.567,20.567,2,0,FALSE,0,0
1000,20.567,20.567,20.567,2,0,TRUE,1,0
1000,-20.567,20.567,20.567,2,0,FALSE,0,0
1000,unconnected=5,20.567,20.567,2,0,FALSE,0,0
1000,connected=+inf,20.567,20.567,2,0,FALSE,0,0
1000,connected=-inf,20.567,20.567,2,0,FALSE,0,0
1000,connected=nan,20.567,20.567,2,0,FALSE,0,0
1000,1,-20.567,-20.567,2,0,TRUE,1,0
1000,0,-20.567,-20.567,2,0,TRUE,1,0
1000,10,-20.567,-20.567,2,0,TRUE,1,0
1000,-10,-20.567,-20.567,2,0,TRUE,1,0
1000,20.567,-20.567,-20.567,2,0,TRUE,1,0
1000,-20.567,-20.567,-20.567,2,0,TRUE,1,0
1000,unconnected=5,-20.567,-20.567,2,0,FALSE,0,0
1000,connected=+inf,-20.567,-20.567,2,0,FALSE,0,0
1000,connected=-inf,-20.567,-20.567,2,0,FALSE,0,0
1000,connected=nan,-20.567,-20.567,2,0,FALSE,0,0
1000,10,15,8,0,2,FALSE,2,0
1000,15,10,8,0,2,TRUE,0,0
1000,10,15,11,0,2,FALSE,2,0
1000,15,15,16,0,2,TRUE,0,0
1000,15,10,8,0,2,TRUE,0,0
1000,15,16,6,0,2,FALSE,2,0
1000,1,1,1,0,2,TRUE,0,0
1000,0,0,0,0,2,TRUE,0,0
1000,10,10,10,0,2,TRUE,0,0
1000,-10,-10,-10,0,2,TRUE,0,0
1000,20.567,20.567,20.567,0,2,TRUE,0,0
1000,-20.567,-20.567,-20.567,0,2,TRUE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,connected=+inf,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,connected=-inf,connected=-inf,connected=-inf,0,2,TRUE,2,0
1000,connected=nan,connected=nan,connected=nan,0,2,FALSE,2,0
1000,1,0,1,0,2,FALSE,0,0
1000,0,1,0,0,2,FALSE,2,0
1000,10,-10,10,0,2,FALSE,0,0
1000,-10,10,-10,0,2,FALSE,2,0
1000,20.567,-20.567,20.567,0,2,FALSE,0,0
1000,-20.567,20.567,-20.567,0,2,FALSE,2,0
1000,unconnected=5,10,unconnected=5,0,2,FALSE,2,0
1000,connected=+inf,10,connected=+inf,0,2,FALSE,2,0
1000,connected=-inf,10,connected=-inf,0,2,FALSE,2,0
1000,connected=nan,10,connected=nan,0,2,FALSE,2,0
1000,1,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,0,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,10,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,-10,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,20.567,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,-20.567,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,unconnected=5,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,connected=+inf,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,connected=-inf,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,connected=nan,unconnected=5,unconnected=5,0,2,FALSE,2,0
1000,1,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,0,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,10,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,-10,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,20.567,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,-20.567,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,unconnected=5,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,connected=+inf,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,connected=-inf,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,connected=nan,connected=+inf,connected=+inf,0,2,FALSE,2,0
1000,1,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,0,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,10,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,-10,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,20.567,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,-20.567,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,unconnected=5,connected=-inf,connected=-inf,0,2,FALSE,2,0
1000,connected=+inf,connected=-inf,connected=-inf,0,2,FALSE,2,0
1000,connected=-inf,connected=-inf,connected=-inf,0,2,TRUE,0,0
1000,connected=nan,connected=-inf,connected=-inf,0,2,FALSE,2,0
1000,1,connected=nan,connected=nan,0,2,FALSE,2,0
1000,0,connected=nan,connected=nan,0,2,FALSE,2,0
1000,10,connected=nan,connected=nan,0,2,FALSE,2,0
1000,-10,connected=nan,connected=nan,0,2,FALSE,2,0
1000,20.567,connected=nan,connected=nan,0,2,FALSE,2,0
1000,-20.567,connected=nan,connected=nan,0,2,FALSE,2,0
1000,unconnected=5,connected=nan,connected=nan,0,2,FALSE,2,0
1000,connected=+inf,connected=nan,connected=nan,0,2,FALSE,2,0
1000,connected=-inf,connected=nan,connected=nan,0,2,FALSE,2,0
1000,connected=nan,connected=nan,connected=nan,0,2,FALSE,2,0
1000,1,1,1,0,2,TRUE,0,0
1000,0,1,1,0,2,FALSE,2,0
1000,10,1,1,0,2,TRUE,0,0
1000,-10,1,1,0,2,FALSE,2,0
1000,20.567,1,1,0,2,TRUE,0,0
1000,-20.567,1,1,0,2,FALSE,2,0
1000,unconnected=5,1,1,0,2,FALSE,2,0
1000,connected=+inf,1,1,0,2,FALSE,2,0
1000,connected=-inf,1,1,0,2,FALSE,2,0
1000,connected=nan,1,1,0,2,FALSE,2,0
1000,1,0,0,0,2,TRUE,0,0
1000,0,0,0,0,2,TRUE,0,0
1000,10,0,0,0,2,TRUE,0,0
1000,-10,0,0,0,2,FALSE,2,0
1000,20.567,0,0,0,2,TRUE,0,0
1000,-20.567,0,0,0,2,FALSE,2,0
1000,unconnected=5,0,0,0,2,FALSE,2,0
1000,connected=+inf,0,0,0,2,FALSE,2,0
1000,connected=-inf,0,0,0,2,FALSE,2,0
1000,connected=nan,0,0,0,2,FALSE,2,0
1000,1,10,10,0,2,FALSE,2,0
1000,0,10,10,0,2,FALSE,2,0
1000,10,10,10,0,2,TRUE,0,0
1000,-10,10,10,0,2,FALSE,2,0
1000,20.567,10,10,0,2,TRUE,0,0
1000,-20.567,10,10,0,2,FALSE,2,0
1000,unconnected=5,10,10,0,2,FALSE,2,0
1000,connected=+inf,10,10,0,2,FALSE,2,0
1000,connected=-inf,10,10,0,2,FALSE,2,0
1000,connected=nan,10,10,0,2,FALSE,2,0
1000,1,-10,-10,0,2,TRUE,0,0
1000,0,-10,-10,0,2,TRUE,0,0
1000,10,-10,-10,0,2,TRUE,0,0
1000,-10,-10,-10,0,2,TRUE,0,0
1000,20.567,-10,-10,0,2,TRUE,0,0
1000,-20.567,-10,-10,0,2,FALSE,2,0
1000,unconnected=5,-10,-10,0,2,FALSE,2,0
1000,connected=+inf,-10,-10,0,2,FALSE,2,0
1000,connected=-inf,-10,-10,0,2,FALSE,2,0
1000,connected=nan,-10,-10,0,2,FALSE,2,0
1000,1,20.567,20.567,0,2,FALSE,2,0
1000,0,20.567,20.567,0,2,FALSE,2,0
1000,10,20.567,20.567,0,2,FALSE,2,0
1000,-10,20.567,20.567,0,2,FALSE,2,0
1000,20.567,20.567,20.567,0,2,TRUE,0,0
1000,-20.567,20.567,20.567,0,2,FALSE,2,0
1000,unconnected=5,20.567,20.567,0,2,FALSE,2,0
1000,connected=+inf,20.567,20.567,0,2,FALSE,2,0
1000,connected=-inf,20.567,20.567,0,2,FALSE,2,0
1000,connected=nan,20.567,20.567,0,2,FALSE,2,0
1000,1,-20.567,-20.567,0,2,TRUE,0,0
1000,0,-20.567,-20.567,0,2,TRUE,0,0
1000,10,-20.567,-20.567,0,2,TRUE,0,0
1000,-10,-20.567,-20.567,0,2,TRUE,0,0
1000,20.567,-20.567,-20.567,0,2,TRUE,0,0
1000,-20.567,-20.567,-20.567,0,2,TRUE,0,0
1000,unconnected=5,-20.567,-20.567,0,2,FALSE,2,0
1000,connected=+inf,-20.567,-20.567,0,2,FALSE,2,0
1000,connected=-inf,-20.567,-20.567,0,2,FALSE,2,0
1000,connected=nan,-20.567,-20.567,0,2,FALSE,2,0
1000,10,15,8,unconnected=5,unconnected=5,FALSE,0,0
1000,15,10,8,unconnected=5,unconnected=5,TRUE,0,0
1000,10,15,11,unconnected=5,unconnected=5,FALSE,0,0
1000,15,15,16,unconnected=5,unconnected=5,TRUE,0,0
1000,15,10,8,unconnected=5,unconnected=5,TRUE,0,0
1000,15,16,6,unconnected=5,unconnected=5,FALSE,0,0
1000,1,1,1,unconnected=5,unconnected=5,TRUE,0,0
1000,0,0,0,unconnected=5,unconnected=5,TRUE,0,0
1000,10,10,10,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,20.567,20.567,20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,connected=nan,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,1,0,1,unconnected=5,unconnected=5,FALSE,0,0
1000,0,1,0,unconnected=5,unconnected=5,FALSE,0,0
1000,10,-10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,-10,10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,-20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,-20.567,20.567,-20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,10,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,10,connected=-inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,10,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,-20.567,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,0,0
1000,1,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,0,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,10,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,-10,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,-20.567,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,connected=+inf,connected=+inf,unconnected=5,unconnected=5,FALSE,0,0
1000,1,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,0,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,10,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,20.567,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,unconnected=5,connected=-inf,connected=-inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,connected=-inf,connected=-inf,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,unconnected=5,unconnected=5,TRUE,0,0
1000,connected=nan,connected=-inf,connected=-inf,unconnected=5,unconnected=5,FALSE,0,0
1000,1,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,0,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,10,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,-10,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,-20.567,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,connected=nan,connected=nan,unconnected=5,unconnected=5,FALSE,0,0
1000,1,1,1,unconnected=5,unconnected=5,TRUE,0,0
1000,0,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,10,1,1,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,1,1,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,1,1,unconnected=5,unconnected=5,FALSE,0,0
1000,1,0,0,unconnected=5,unconnected=5,TRUE,0,0
1000,0,0,0,unconnected=5,unconnected=5,TRUE,0,0
1000,10,0,0,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,0,0,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,0,0,unconnected=5,unconnected=5,FALSE,0,0
1000,1,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,0,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,10,10,10,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,10,10,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,10,10,unconnected=5,unconnected=5,FALSE,0,0
1000,1,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,0,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,10,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,20.567,-10,-10,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,-10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,-10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,-10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,-10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,-10,-10,unconnected=5,unconnected=5,FALSE,0,0
1000,1,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,0,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,10,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,-10,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,20.567,20.567,20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,unconnected=5,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,20.567,20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,1,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,0,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,10,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,-10,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,20.567,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,-20.567,-20.567,-20.567,unconnected=5,unconnected=5,TRUE,0,0
1000,unconnected=5,-20.567,-20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=+inf,-20.567,-20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=-inf,-20.567,-20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,connected=nan,-20.567,-20.567,unconnected=5,unconnected=5,FALSE,0,0
1000,10,15,8,connected=nan,connected=nan,FALSE,0,0
1000,15,10,8,connected=nan,connected=nan,TRUE,0,0
1000,10,15,11,connected=nan,connected=nan,FALSE,0,0
1000,15,15,16,connected=nan,connected=nan,TRUE,0,0
1000,15,10,8,connected=nan,connected=nan,TRUE,0,0
1000,15,16,6,connected=nan,connected=nan,FALSE,0,0
1000,1,1,1,connected=nan,connected=nan,TRUE,0,0
1000,0,0,0,connected=nan,connected=nan,TRUE,0,0
1000,10,10,10,connected=nan,connected=nan,TRUE,0,0
1000,-10,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,20.567,20.567,20.567,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,1,0,1,connected=nan,connected=nan,FALSE,0,0
1000,0,1,0,connected=nan,connected=nan,FALSE,0,0
1000,10,-10,10,connected=nan,connected=nan,FALSE,0,0
1000,-10,10,-10,connected=nan,connected=nan,FALSE,0,0
1000,20.567,-20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,-20.567,20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,10,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,10,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,10,connected=-inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,10,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,1,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,0,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,10,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,-10,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,20.567,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,-20.567,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,unconnected=5,unconnected=5,connected=nan,connected=nan,FALSE,0,0
1000,1,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,0,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,10,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,-10,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,20.567,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,-20.567,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,connected=+inf,connected=+inf,connected=nan,connected=nan,FALSE,0,0
1000,1,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,0,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,10,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,-10,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,20.567,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,unconnected=5,connected=-inf,connected=-inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,connected=-inf,connected=-inf,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=nan,connected=nan,TRUE,0,0
1000,connected=nan,connected=-inf,connected=-inf,connected=nan,connected=nan,FALSE,0,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,10,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,-10,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,20.567,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,-20.567,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,0,0
1000,1,1,1,connected=nan,connected=nan,TRUE,0,0
1000,0,1,1,connected=nan,connected=nan,FALSE,0,0
1000,10,1,1,connected=nan,connected=nan,TRUE,0,0
1000,-10,1,1,connected=nan,connected=nan,FALSE,0,0
1000,20.567,1,1,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,1,1,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,1,1,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,1,1,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,1,1,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,1,1,connected=nan,connected=nan,FALSE,0,0
1000,1,0,0,connected=nan,connected=nan,TRUE,0,0
1000,0,0,0,connected=nan,connected=nan,TRUE,0,0
1000,10,0,0,connected=nan,connected=nan,TRUE,0,0
1000,-10,0,0,connected=nan,connected=nan,FALSE,0,0
1000,20.567,0,0,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,0,0,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,0,0,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,0,0,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,0,0,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,0,0,connected=nan,connected=nan,FALSE,0,0
1000,1,10,10,connected=nan,connected=nan,FALSE,0,0
1000,0,10,10,connected=nan,connected=nan,FALSE,0,0
1000,10,10,10,connected=nan,connected=nan,TRUE,0,0
1000,-10,10,10,connected=nan,connected=nan,FALSE,0,0
1000,20.567,10,10,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,10,10,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,10,10,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,10,10,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,10,10,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,10,10,connected=nan,connected=nan,FALSE,0,0
1000,1,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,0,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,10,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,-10,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,20.567,-10,-10,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,-10,-10,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=5,-10,-10,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,-10,-10,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,-10,-10,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,-10,-10,connected=nan,connected=nan,FALSE,0,0
1000,1,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,0,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,10,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,-10,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,20.567,20.567,20.567,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,unconnected=-5,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,20.567,20.567,connected=nan,connected=nan,FALSE,0,0
1000,1,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,0,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,10,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,-10,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,20.567,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,-20.567,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,0
1000,unconnected=-5,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=+inf,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=-inf,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,connected=nan,-20.567,-20.567,connected=nan,connected=nan,TRUE,0,1
1000,connected=nan,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,0
1000,1,-20.567,-20.567,connected=nan,connected=nan,FALSE,0,1