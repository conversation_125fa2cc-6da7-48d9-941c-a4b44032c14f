# Test data for Switch block
# Requirement ID: F1PLT-ADR-405; TestCase ID: F1PLT-ATC-194
#
# TestData format: Iteration Interval,input,offset,neg-Output0,neg-Ouput1,neg-Output2,neg-Output3,neg-Output4,neg-Output5,neg-Output6,neg-Output7,
#	ExpectedOutput0,ExpectedOutput1,ExpectedOutput2,ExpectedOutput3,ExpectedOutput4,ExpectedOutput5,ExpectedOutput6,ExpectedOutput7
#
# Note: Given numeric values will be directly set into the slots. To pass numeric values (by creating link) you should use connected/unconnected=2 syntax
#
#Iteration Interval,input,offset,neg-Output0,neg-Ouput1,neg-Output2,neg-Output3,neg-Output4,neg-Output5,neg-Output6,neg-Output7,ExpectedOutput0,ExpectedOutput1,ExpectedOutput2,ExpectedOutput3,ExpectedOutput4,ExpectedOutput5,ExpectedOutput6,ExpectedOutput7
1000,unconnected=2,0,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,connected=+inf,0,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,connected=NaN,0,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,256,255,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,connected=255,255,false,false,false,false,false,false,false,false,true,false,false,false,false,false,false,false
1000,-23,255,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,1,255,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,50,50,false,false,false,false,false,false,false,false,true,false,false,false,false,false,false,false
1000,51,50,false,false,false,false,false,false,false,false,false,true,false,false,false,false,false,false
1000,52,50,false,false,false,false,false,false,false,false,false,false,true,false,false,false,false,false
1000,53,50,false,false,false,false,false,false,false,false,false,false,false,true,false,false,false,false
1000,54,50,false,false,false,false,false,false,false,false,false,false,false,false,true,false,false,false
1000,55,50,false,false,false,false,false,false,false,false,false,false,false,false,false,true,false,false
1000,56,50,false,false,false,false,false,false,false,false,false,false,false,false,false,false,true,false
1000,57,50,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,true
1000,58,50,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false,false
1000,unconnected=2,0,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,connected=+inf,0,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,connected=NaN,0,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,260,255,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,-60,255,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,1,255,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,-1,255,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true
1000,70,70,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true
1000,71,70,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true
1000,72,70,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true
1000,73,70,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true
1000,74,70,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true
1000,75,70,true,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true
1000,76,70,true,true,true,true,true,true,true,true,true,true,true,true,true,true,false,true
1000,77,70,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,false
1000,78,70,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true