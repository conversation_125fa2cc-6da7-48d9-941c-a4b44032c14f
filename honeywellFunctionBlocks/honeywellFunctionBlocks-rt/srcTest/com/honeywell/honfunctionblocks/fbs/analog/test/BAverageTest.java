/*
 * Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.List;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BAverage;
import com.honeywell.honfunctionblocks.fbs.analog.BMinMaxAverageBlock;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Average block as per SDD rev26
 * Requirement ID: F1PLT-ADR-405 
 * TestCase ID: F1PLT-ATC-189
 * <AUTHOR> C.P.Sathish
 * @since Jan 9, 2018
 */
@NiagaraType

public class BAverageTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BAverageTest(2979906276)1.0$ @*/
/* Generated Thu Jan 04 12:09:08 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAverageTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  BAverage averageBlock;	
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  averageBlock = new BAverage();
  }
  
  @AfterClass
  public void tearDown() {
	  averageBlock = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[] getInputSlotNames() {
	  return new Object[]{"in1", "in2", "in3", "in4", "in5", "in6", "in7", "in8"};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[] getOutputSlotNames() {
	  return new Object[] {"OUTPUT"};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[] getExecOrderSlotName() {
	  return new Object[] {"ExecutionOrder", "toolVersion"};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[] getAllSlotNames() {
	  List<Object> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(getExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[] invalidSlotNames() {
	  return new Object[]{"In1", "In2", "In3", "In4", "In5", "In6", "In7", "In8", "out", "InvalidFlag", "Output", "outptu", "IgnoreInvalidInput", "TailOperation"};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(averageBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(averageBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "average.png");
	  BIcon actualFbIcon = averageBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  averageBlock.setIcon(expectedFbIcon);
	  actualFbIcon = averageBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider (name ="SettingValue")
  public Object[] testSettingInputValues() {
	  return new Object[] {32767, 2.2250738585072014E-308, 4.9e-324, 1.7976931348623157e+308, 0, Double.NEGATIVE_INFINITY, Double.POSITIVE_INFINITY};
  }

  @Test(dataProvider="SettingValue")
  public void testSettingInputValues(double snValue) {
	averageBlock.setIgnoreInvalidInput(true); Assert.assertEquals(averageBlock.getIgnoreInvalidInput(), true);
	averageBlock.setIn1(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn1().getValue(), snValue, 0.1);
	averageBlock.setIn2(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn2().getValue(), snValue, 0.1);
	averageBlock.setIn3(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn3().getValue(), snValue, 0.1);
	averageBlock.setIn4(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn4().getValue(), snValue, 0.1);
	averageBlock.setIn5(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn5().getValue(), snValue, 0.1);
	averageBlock.setIn6(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn6().getValue(), snValue, 0.1);
	averageBlock.setIn7(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn7().getValue(), snValue, 0.1);
	averageBlock.setIn8(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getIn8().getValue(), snValue, 0.1);
	averageBlock.setOUTPUT(new BHonStatusNumeric(snValue)); Assert.assertEquals(averageBlock.getOUTPUT().getValue(), snValue, 0.1);
  }
 
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Average_TestData.csv");
    
  }
  
  @Test(dataProvider="provideTestData")
  public void testAvgBlockWithTestData(List<String> inputs) throws BlockExecutionException {	  
	  BAverage tempAvgBlock = new BAverage();
	  BExecutionParams executionParams = new BExecutionParams();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
	  tempAvgBlock.setIgnoreInvalidInput(false);
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in1.getName(), inputs.get(1));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in2.getName(), inputs.get(2));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in3.getName(), inputs.get(3));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in4.getName(), inputs.get(4));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in5.getName(), inputs.get(5));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in6.getName(), inputs.get(6));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in7.getName(), inputs.get(7));
	  setupNumericSlot(tempAvgBlock, BMinMaxAverageBlock.in8.getName(), inputs.get(8));
		  
	  tempAvgBlock.executeHoneywellComponent(executionParams);
	  
	  Assert.assertEquals(tempAvgBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0d), 0.1);
  }
  
  
  public void setupNumericSlot(BAverage averageBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();			
			averageBlock.linkTo(nm1, nm1.getSlot("out"), averageBlock.getSlot(slotName));
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			return;
		}
		
		switch (slotName) {
		case "in1":
			averageBlock.setIn1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in2":
			averageBlock.setIn2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in3":
			averageBlock.setIn3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in4":
			averageBlock.setIn4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in5":
			averageBlock.setIn5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in6":
			averageBlock.setIn6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in7":
			averageBlock.setIn7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in8":
			averageBlock.setIn8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;

		default:
			break;
		}
	}
  
  //@Test(dependsOnMethods={"testSlotAvailability"}, groups={"testLinkRules"})
  public void testLinkRules() {
	  BAverage target = new BAverage();
	  BAverage src = new BAverage();
	  
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in1"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in2"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in3"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in4"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in5"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in6"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in7"));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot("in8"));
	  
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BMinMaxAverageBlock.OUTPUT.getName()),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()),target,target.getSlot(BMinMaxAverageBlock.ignoreInvalidInput.getName()));
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = averageBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BMinMaxAverageBlock.ignoreInvalidInput.getName());
	}
 
}
