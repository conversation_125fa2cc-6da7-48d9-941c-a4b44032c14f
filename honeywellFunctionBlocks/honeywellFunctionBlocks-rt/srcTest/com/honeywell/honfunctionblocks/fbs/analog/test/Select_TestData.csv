#Iteration Intervel,X,default,input0,input1,input2,input3,input4,input5,offset,output = input(x � offset),Sort,Expected output,,,
1000,0,0,-10,-20,-30,-40,-50,-60,0,-10,0,-10,,,
1000,connected=101,0,-10,-20,-30,-40,-50,-60,100,-20,1,-20,0,-20,0
1000,101,0,-10,-20,-30,-40,-50,-60,0,0,101,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,0,0,102,0,,,
1000,103,0,-10,-20,-30,-40,-50,-60,0,0,103,0,,,
1000,104,0,-10,-20,-30,-40,-50,-60,0,0,104,0,,,
1000,105,0,-10,-20,-30,-40,-50,-60,0,0,105,0,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,0,0,-100,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,0,0,10.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,0,0,-10.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,0,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,0,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,0,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,0,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,100,0,-100,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,100,-10,0,-10,,,
1000,101,0,-10,-20,-30,-40,-50,-60,100,-20,1,-20,,,
1000,102,0,-10,-20,-30,-40,-50,-60,100,-30,2,-30,,,
1000,103,0,-10,-20,-30,-40,-50,-60,100,-40,3,-40,,,
1000,104,0,-10,-20,-30,-40,-50,-60,100,-50,4,-50,,,
1000,105,0,-10,-20,-30,-40,-50,-60,100,-60,5,-60,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,100,0,-200,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,100,0,-89.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,100,0,-110.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,100,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,100,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,100,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,100,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,101,0,-101,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,101,0,-1,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,101,-10,0,-10,,,
1000,102,0,-10,-20,-30,-40,-50,-60,101,-20,1,-20,,,
1000,103,0,-10,-20,-30,-40,-50,-60,101,-30,2,-30,,,
1000,104,0,-10,-20,-30,-40,-50,-60,101,-40,3,-40,,,
1000,105,0,-10,-20,-30,-40,-50,-60,101,-50,4,-50,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,101,0,-201,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,101,0,-90.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,101,0,-111.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,101,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,101,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,101,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,101,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,102,0,-102,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,102,0,-2,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,102,0,-1,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,102,-10,0,-10,,,
1000,103,0,-10,-20,-30,-40,-50,-60,102,-20,1,-20,,,
1000,104,0,-10,-20,-30,-40,-50,-60,102,-30,2,-30,,,
1000,105,0,-10,-20,-30,-40,-50,-60,102,-40,3,-40,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,102,0,-202,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,102,0,-91.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,102,0,-112.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,102,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,102,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,102,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,102,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,103,0,-103,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,103,0,-3,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,103,0,-2,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,103,0,-1,0,,,
1000,103,0,-10,-20,-30,-40,-50,-60,103,-10,0,-10,,,
1000,104,0,-10,-20,-30,-40,-50,-60,103,-20,1,-20,,,
1000,105,0,-10,-20,-30,-40,-50,-60,103,-30,2,-30,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,103,0,-203,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,103,0,-92.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,103,0,-113.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,103,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,103,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,103,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,103,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,104,0,-104,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,104,0,-4,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,104,0,-3,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,104,0,-2,0,,,
1000,103,0,-10,-20,-30,-40,-50,-60,104,0,-1,0,,,
1000,104,0,-10,-20,-30,-40,-50,-60,104,-10,0,-10,,,
1000,105,0,-10,-20,-30,-40,-50,-60,104,-20,1,-20,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,104,0,-204,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,104,0,-93.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,104,0,-114.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,104,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,104,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,104,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,104,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,105,0,-105,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,105,0,-5,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,105,0,-4,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,105,0,-3,0,,,
1000,103,0,-10,-20,-30,-40,-50,-60,105,0,-2,0,,,
1000,104,0,-10,-20,-30,-40,-50,-60,105,0,-1,0,,,
1000,105,0,-10,-20,-30,-40,-50,-60,105,-10,0,-10,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,105,0,-205,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,105,0,-94.5,0,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,105,0,-115.5,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,105,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,105,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,105,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,105,0,#VALUE!,0,,,
1000,0,0,-10,-20,-30,-40,-50,-60,10.5,0,-10.5,0,,,
1000,100,0,-10,-20,-30,-40,-50,-60,10.5,0,89.5,0,,,
1000,101,0,-10,-20,-30,-40,-50,-60,10.5,0,90.5,0,,,
1000,102,0,-10,-20,-30,-40,-50,-60,10.5,0,91.5,0,,,
1000,103,0,-10,-20,-30,-40,-50,-60,10.5,0,92.5,0,,,
1000,104,0,-10,-20,-30,-40,-50,-60,10.5,0,93.5,0,,,
1000,105,0,-10,-20,-30,-40,-50,-60,10.5,0,94.5,0,,,
1000,-100,0,-10,-20,-30,-40,-50,-60,10.5,0,-110.5,0,,,
1000,10.5,0,-10,-20,-30,-40,-50,-60,10.5,-10,0,-10,,,
1000,-10.5,0,-10,-20,-30,-40,-50,-60,10.5,0,-21,0,,,
1000,unconnected=5,0,-10,-20,-30,-40,-50,-60,10.5,0,#VALUE!,0,,,
1000,connected=+inf,0,-10,-20,-30,-40,-50,-60,10.5,0,#VALUE!,0,,,
1000,connected=-inf,0,-10,-20,-30,-40,-50,-60,10.5,0,#VALUE!,0,,,
1000,connected=nan,0,-10,-20,-30,-40,-50,-60,10.5,0,#VALUE!,0,,,
1000,0,10,11,20,30,40,50,60,0,11,0,11,,,
1000,100,10,11,20,30,40,50,60,0,10,100,10,,,
1000,101,10,11,20,30,40,50,60,0,10,101,10,,,
1000,102,10,11,20,30,40,50,60,0,10,102,10,,,
1000,103,10,11,20,30,40,50,60,0,10,103,10,,,
1000,104,10,11,20,30,40,50,60,0,10,104,10,,,
1000,105,10,11,20,30,40,50,60,0,10,105,10,,,
1000,-100,10,11,20,30,40,50,60,0,10,-100,10,,,
1000,10.5,10,11,20,30,40,50,60,0,10,10.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,0,10,-10.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,0,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,0,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,0,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,0,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,100,10,-100,10,,,
1000,100,10,11,20,30,40,50,60,100,11,0,11,,,
1000,101,10,11,20,30,40,50,60,100,20,1,20,,,
1000,102,10,11,20,30,40,50,60,100,30,2,30,,,
1000,103,10,11,20,30,40,50,60,100,40,3,40,,,
1000,104,10,11,20,30,40,50,60,100,50,4,50,,,
1000,105,10,11,20,30,40,50,60,100,60,5,60,,,
1000,-100,10,11,20,30,40,50,60,100,10,-200,10,,,
1000,10.5,10,11,20,30,40,50,60,100,10,-89.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,100,10,-110.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,100,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,100,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,100,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,100,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,101,10,-101,10,,,
1000,100,10,11,20,30,40,50,60,101,10,-1,10,,,
1000,101,10,11,20,30,40,50,60,101,11,0,11,,,
1000,102,10,11,20,30,40,50,60,101,20,1,20,,,
1000,103,10,11,20,30,40,50,60,101,30,2,30,,,
1000,104,10,11,20,30,40,50,60,101,40,3,40,,,
1000,105,10,11,20,30,40,50,60,101,50,4,50,,,
1000,-100,10,11,20,30,40,50,60,101,10,-201,10,,,
1000,10.5,10,11,20,30,40,50,60,101,10,-90.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,101,10,-111.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,101,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,101,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,101,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,101,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,102,10,-102,10,,,
1000,100,10,11,20,30,40,50,60,102,10,-2,10,,,
1000,101,10,11,20,30,40,50,60,102,10,-1,10,,,
1000,102,10,11,20,30,40,50,60,102,11,0,11,,,
1000,103,10,11,20,30,40,50,60,102,20,1,20,,,
1000,104,10,11,20,30,40,50,60,102,30,2,30,,,
1000,105,10,11,20,30,40,50,60,102,40,3,40,,,
1000,-100,10,11,20,30,40,50,60,102,10,-202,10,,,
1000,10.5,10,11,20,30,40,50,60,102,10,-91.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,102,10,-112.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,102,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,102,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,102,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,102,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,103,10,-103,10,,,
1000,100,10,11,20,30,40,50,60,103,10,-3,10,,,
1000,101,10,11,20,30,40,50,60,103,10,-2,10,,,
1000,102,10,11,20,30,40,50,60,103,10,-1,10,,,
1000,103,10,11,20,30,40,50,60,103,11,0,11,,,
1000,104,10,11,20,30,40,50,60,103,20,1,20,,,
1000,105,10,11,20,30,40,50,60,103,30,2,30,,,
1000,-100,10,11,20,30,40,50,60,103,10,-203,10,,,
1000,10.5,10,11,20,30,40,50,60,103,10,-92.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,103,10,-113.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,103,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,103,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,103,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,103,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,104,10,-104,10,,,
1000,100,10,11,20,30,40,50,60,104,10,-4,10,,,
1000,101,10,11,20,30,40,50,60,104,10,-3,10,,,
1000,102,10,11,20,30,40,50,60,104,10,-2,10,,,
1000,103,10,11,20,30,40,50,60,104,10,-1,10,,,
1000,104,10,11,20,30,40,50,60,104,11,0,11,,,
1000,105,10,11,20,30,40,50,60,104,20,1,20,,,
1000,-100,10,11,20,30,40,50,60,104,10,-204,10,,,
1000,10.5,10,11,20,30,40,50,60,104,10,-93.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,104,10,-114.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,104,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,104,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,104,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,104,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,105,10,-105,10,,,
1000,100,10,11,20,30,40,50,60,105,10,-5,10,,,
1000,101,10,11,20,30,40,50,60,105,10,-4,10,,,
1000,102,10,11,20,30,40,50,60,105,10,-3,10,,,
1000,103,10,11,20,30,40,50,60,105,10,-2,10,,,
1000,104,10,11,20,30,40,50,60,105,10,-1,10,,,
1000,105,10,11,20,30,40,50,60,105,11,0,11,,,
1000,-100,10,11,20,30,40,50,60,105,10,-205,10,,,
1000,10.5,10,11,20,30,40,50,60,105,10,-94.5,10,,,
1000,-10.5,10,11,20,30,40,50,60,105,10,-115.5,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,105,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,105,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,105,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,105,10,#VALUE!,10,,,
1000,0,10,11,20,30,40,50,60,10.5,10,-10.5,10,,,
1000,100,10,11,20,30,40,50,60,10.5,10,89.5,10,,,
1000,101,10,11,20,30,40,50,60,10.5,10,90.5,10,,,
1000,102,10,11,20,30,40,50,60,10.5,10,91.5,10,,,
1000,103,10,11,20,30,40,50,60,10.5,10,92.5,10,,,
1000,104,10,11,20,30,40,50,60,10.5,10,93.5,10,,,
1000,105,10,11,20,30,40,50,60,10.5,10,94.5,10,,,
1000,-100,10,11,20,30,40,50,60,10.5,10,-110.5,10,,,
1000,10.5,10,11,20,30,40,50,60,10.5,11,0,11,,,
1000,-10.5,10,11,20,30,40,50,60,10.5,10,-21,10,,,
1000,unconnected=5,10,11,20,30,40,50,60,10.5,10,#VALUE!,10,,,
1000,connected=+inf,10,11,20,30,40,50,60,10.5,10,#VALUE!,10,,,
1000,connected=-inf,10,11,20,30,40,50,60,10.5,10,#VALUE!,10,,,
1000,connected=nan,10,11,20,30,40,50,60,10.5,10,#VALUE!,10,,,
1000,0,-10,0,0,0,0,0,0,0,0,0,0,,,
1000,100,-10,0,0,0,0,0,0,0,-10,100,-10,,,
1000,101,-10,0,0,0,0,0,0,0,-10,101,-10,,,
1000,102,-10,0,0,0,0,0,0,0,-10,102,-10,,,
1000,103,-10,0,0,0,0,0,0,0,-10,103,-10,,,
1000,104,-10,0,0,0,0,0,0,0,-10,104,-10,,,
1000,105,-10,0,0,0,0,0,0,0,-10,105,-10,,,
1000,-100,-10,0,0,0,0,0,0,0,-10,-100,-10,,,
1000,10.5,-10,0,0,0,0,0,0,0,-10,10.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,0,-10,-10.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,0,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,0,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,0,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,0,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,100,-10,-100,-10,,,
1000,100,-10,0,0,0,0,0,0,100,0,0,0,,,
1000,101,-10,0,0,0,0,0,0,100,0,1,0,,,
1000,102,-10,0,0,0,0,0,0,100,0,2,0,,,
1000,103,-10,0,0,0,0,0,0,100,0,3,0,,,
1000,104,-10,0,0,0,0,0,0,100,0,4,0,,,
1000,105,-10,0,0,0,0,0,0,100,0,5,0,,,
1000,-100,-10,0,0,0,0,0,0,100,-10,-200,-10,,,
1000,10.5,-10,0,0,0,0,0,0,100,-10,-89.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,100,-10,-110.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,100,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,100,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,100,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,100,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,101,-10,-101,-10,,,
1000,100,-10,0,0,0,0,0,0,101,-10,-1,-10,,,
1000,101,-10,0,0,0,0,0,0,101,0,0,0,,,
1000,102,-10,0,0,0,0,0,0,101,0,1,0,,,
1000,103,-10,0,0,0,0,0,0,101,0,2,0,,,
1000,104,-10,0,0,0,0,0,0,101,0,3,0,,,
1000,105,-10,0,0,0,0,0,0,101,0,4,0,,,
1000,-100,-10,0,0,0,0,0,0,101,-10,-201,-10,,,
1000,10.5,-10,0,0,0,0,0,0,101,-10,-90.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,101,-10,-111.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,101,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,101,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,101,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,101,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,102,-10,-102,-10,,,
1000,100,-10,0,0,0,0,0,0,102,-10,-2,-10,,,
1000,101,-10,0,0,0,0,0,0,102,-10,-1,-10,,,
1000,102,-10,0,0,0,0,0,0,102,0,0,0,,,
1000,103,-10,0,0,0,0,0,0,102,0,1,0,,,
1000,104,-10,0,0,0,0,0,0,102,0,2,0,,,
1000,105,-10,0,0,0,0,0,0,102,0,3,0,,,
1000,-100,-10,0,0,0,0,0,0,102,-10,-202,-10,,,
1000,10.5,-10,0,0,0,0,0,0,102,-10,-91.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,102,-10,-112.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,102,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,102,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,102,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,102,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,103,-10,-103,-10,,,
1000,100,-10,0,0,0,0,0,0,103,-10,-3,-10,,,
1000,101,-10,0,0,0,0,0,0,103,-10,-2,-10,,,
1000,102,-10,0,0,0,0,0,0,103,-10,-1,-10,,,
1000,103,-10,0,0,0,0,0,0,103,0,0,0,,,
1000,104,-10,0,0,0,0,0,0,103,0,1,0,,,
1000,105,-10,0,0,0,0,0,0,103,0,2,0,,,
1000,-100,-10,0,0,0,0,0,0,103,-10,-203,-10,,,
1000,10.5,-10,0,0,0,0,0,0,103,-10,-92.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,103,-10,-113.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,103,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,103,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,103,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,103,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,104,-10,-104,-10,,,
1000,100,-10,0,0,0,0,0,0,104,-10,-4,-10,,,
1000,101,-10,0,0,0,0,0,0,104,-10,-3,-10,,,
1000,102,-10,0,0,0,0,0,0,104,-10,-2,-10,,,
1000,103,-10,0,0,0,0,0,0,104,-10,-1,-10,,,
1000,104,-10,0,0,0,0,0,0,104,0,0,0,,,
1000,105,-10,0,0,0,0,0,0,104,0,1,0,,,
1000,-100,-10,0,0,0,0,0,0,104,-10,-204,-10,,,
1000,10.5,-10,0,0,0,0,0,0,104,-10,-93.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,104,-10,-114.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,104,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,104,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,104,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,104,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,105,-10,-105,-10,,,
1000,100,-10,0,0,0,0,0,0,105,-10,-5,-10,,,
1000,101,-10,0,0,0,0,0,0,105,-10,-4,-10,,,
1000,102,-10,0,0,0,0,0,0,105,-10,-3,-10,,,
1000,103,-10,0,0,0,0,0,0,105,-10,-2,-10,,,
1000,104,-10,0,0,0,0,0,0,105,-10,-1,-10,,,
1000,105,-10,0,0,0,0,0,0,105,0,0,0,,,
1000,-100,-10,0,0,0,0,0,0,105,-10,-205,-10,,,
1000,10.5,-10,0,0,0,0,0,0,105,-10,-94.5,-10,,,
1000,-10.5,-10,0,0,0,0,0,0,105,-10,-115.5,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,105,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,105,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,105,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,105,-10,#VALUE!,-10,,,
1000,0,-10,0,0,0,0,0,0,10.5,-10,-10.5,-10,,,
1000,100,-10,0,0,0,0,0,0,10.5,-10,89.5,-10,,,
1000,101,-10,0,0,0,0,0,0,10.5,-10,90.5,-10,,,
1000,102,-10,0,0,0,0,0,0,10.5,-10,91.5,-10,,,
1000,103,-10,0,0,0,0,0,0,10.5,-10,92.5,-10,,,
1000,104,-10,0,0,0,0,0,0,10.5,-10,93.5,-10,,,
1000,105,-10,0,0,0,0,0,0,10.5,-10,94.5,-10,,,
1000,-100,-10,0,0,0,0,0,0,10.5,-10,-110.5,-10,,,
1000,10.5,-10,0,0,0,0,0,0,10.5,0,0,0,,,
1000,-10.5,-10,0,0,0,0,0,0,10.5,-10,-21,-10,,,
1000,unconnected=5,-10,0,0,0,0,0,0,10.5,-10,#VALUE!,-10,,,
1000,connected=+inf,-10,0,0,0,0,0,0,10.5,-10,#VALUE!,-10,,,
1000,connected=-inf,-10,0,0,0,0,0,0,10.5,-10,#VALUE!,-10,,,
1000,connected=nan,-10,0,0,0,0,0,0,10.5,-10,#VALUE!,-10,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,11.5,0,11.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,100,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,101,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,102,10.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,103,10.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,104,10.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,105,10.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,-100,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,10.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,-10.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,0,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,-100,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,11.5,0,11.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,20.5,1,20.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,30.5,2,30.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,40.5,3,40.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,50.5,4,50.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,60.5,5,60.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,-200,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,-89.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,-110.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,100,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,-101,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,-1,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,11.5,0,11.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,20.5,1,20.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,30.5,2,30.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,40.5,3,40.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,50.5,4,50.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,-201,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,-90.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,-111.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,101,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-102,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-2,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-1,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,11.5,0,11.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,20.5,1,20.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,30.5,2,30.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,40.5,3,40.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-202,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-91.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,-112.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,102,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-103,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-3,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-2,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-1,10.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,11.5,0,11.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,20.5,1,20.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,30.5,2,30.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-203,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-92.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,-113.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,103,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-104,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-4,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-3,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-2,10.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-1,10.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,11.5,0,11.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,20.5,1,20.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-204,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-93.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,-114.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,104,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-105,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-5,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-4,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-3,10.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-2,10.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-1,10.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,11.5,0,11.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-205,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-94.5,10.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,-115.5,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,105,10.5,#VALUE!,10.5,,,
1000,0,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,-10.5,10.5,,,
1000,100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,89.5,10.5,,,
1000,101,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,90.5,10.5,,,
1000,102,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,91.5,10.5,,,
1000,103,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,92.5,10.5,,,
1000,104,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,93.5,10.5,,,
1000,105,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,94.5,10.5,,,
1000,-100,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,-110.5,10.5,,,
1000,10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,11.5,0,11.5,,,
1000,-10.5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,-21,10.5,,,
1000,unconnected=5,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,#VALUE!,10.5,,,
1000,connected=+inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,#VALUE!,10.5,,,
1000,connected=-inf,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,#VALUE!,10.5,,,
1000,connected=nan,10.5,11.5,20.5,30.5,40.5,50.5,60.5,10.5,10.5,#VALUE!,10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,0,+inf,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,100,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,101,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,102,-10.5,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,103,-10.5,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,104,-10.5,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,105,-10.5,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,-100,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,10.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,-10.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,-100,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,0,+inf,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,1,+inf,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,2,+inf,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,3,+inf,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,4,+inf,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,5,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,-200,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,-89.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,-110.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,-101,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,-1,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,0,+inf,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,1,+inf,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,2,+inf,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,3,+inf,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,4,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,-201,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,-90.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,-111.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-102,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-2,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-1,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,0,+inf,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,1,+inf,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,2,+inf,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,3,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-202,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-91.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,-112.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-103,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-3,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-2,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-1,-10.5,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,0,+inf,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,1,+inf,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,2,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-203,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-92.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,-113.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-104,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-4,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-3,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-2,-10.5,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-1,-10.5,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,0,+inf,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,1,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-204,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-93.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,-114.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-105,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-5,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-4,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-3,-10.5,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-2,-10.5,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-1,-10.5,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,0,+inf,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-205,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-94.5,-10.5,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,-115.5,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,-10.5,#VALUE!,-10.5,,,
1000,0,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,-10.5,-10.5,,,
1000,100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,89.5,-10.5,,,
1000,101,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,90.5,-10.5,,,
1000,102,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,91.5,-10.5,,,
1000,103,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,92.5,-10.5,,,
1000,104,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,93.5,-10.5,,,
1000,105,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,94.5,-10.5,,,
1000,-100,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,-110.5,-10.5,,,
1000,10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,0,+inf,,,
1000,-10.5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,-21,-10.5,,,
1000,unconnected=5,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,#VALUE!,-10.5,,,
1000,connected=+inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,#VALUE!,-10.5,,,
1000,connected=-inf,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,#VALUE!,-10.5,,,
1000,connected=nan,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,-10.5,#VALUE!,-10.5,,,
1000,0,unconnected=5,1,2,3,4,5,6,0,1,0,1,,,
1000,100,unconnected=5,1,2,3,4,5,6,0,unconnected=5,100,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,0,unconnected=5,101,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,0,unconnected=5,102,+inf,,,
1000,103,unconnected=5,1,2,3,4,5,6,0,unconnected=5,103,+inf,,,
1000,104,unconnected=5,1,2,3,4,5,6,0,unconnected=5,104,+inf,,,
1000,105,unconnected=5,1,2,3,4,5,6,0,unconnected=5,105,+inf,,,
1000,-100,unconnected=5,1,2,3,4,5,6,0,unconnected=5,-100,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,0,unconnected=5,10.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,0,unconnected=5,-10.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,0,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,100,unconnected=5,-100,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,100,1,0,1,,,
1000,101,unconnected=5,1,2,3,4,5,6,100,2,1,2,,,
1000,102,unconnected=5,1,2,3,4,5,6,100,3,2,3,,,
1000,103,unconnected=5,1,2,3,4,5,6,100,4,3,4,,,
1000,104,unconnected=5,1,2,3,4,5,6,100,5,4,5,,,
1000,105,unconnected=5,1,2,3,4,5,6,100,6,5,6,,,
1000,-100,unconnected=5,1,2,3,4,5,6,100,unconnected=5,-200,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,100,unconnected=5,-89.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,100,unconnected=5,-110.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,100,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,101,unconnected=5,-101,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,101,unconnected=5,-1,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,101,1,0,1,,,
1000,102,unconnected=5,1,2,3,4,5,6,101,2,1,2,,,
1000,103,unconnected=5,1,2,3,4,5,6,101,3,2,3,,,
1000,104,unconnected=5,1,2,3,4,5,6,101,4,3,4,,,
1000,105,unconnected=5,1,2,3,4,5,6,101,5,4,5,,,
1000,-100,unconnected=5,1,2,3,4,5,6,101,unconnected=5,-201,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,101,unconnected=5,-90.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,101,unconnected=5,-111.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,101,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-102,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-2,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-1,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,102,1,0,1,,,
1000,103,unconnected=5,1,2,3,4,5,6,102,2,1,2,,,
1000,104,unconnected=5,1,2,3,4,5,6,102,3,2,3,,,
1000,105,unconnected=5,1,2,3,4,5,6,102,4,3,4,,,
1000,-100,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-202,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-91.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,102,unconnected=5,-112.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,102,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-103,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-3,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-2,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-1,+inf,,,
1000,103,unconnected=5,1,2,3,4,5,6,103,1,0,1,,,
1000,104,unconnected=5,1,2,3,4,5,6,103,2,1,2,,,
1000,105,unconnected=5,1,2,3,4,5,6,103,3,2,3,,,
1000,-100,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-203,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-92.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,103,unconnected=5,-113.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,103,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-104,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-4,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-3,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-2,+inf,,,
1000,103,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-1,+inf,,,
1000,104,unconnected=5,1,2,3,4,5,6,104,1,0,1,,,
1000,105,unconnected=5,1,2,3,4,5,6,104,2,1,2,,,
1000,-100,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-204,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-93.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,104,unconnected=5,-114.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,104,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-105,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-5,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-4,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-3,+inf,,,
1000,103,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-2,+inf,,,
1000,104,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-1,+inf,,,
1000,105,unconnected=5,1,2,3,4,5,6,105,1,0,1,,,
1000,-100,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-205,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-94.5,+inf,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,105,unconnected=5,-115.5,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,105,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,-10.5,+inf,,,
1000,100,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,89.5,+inf,,,
1000,101,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,90.5,+inf,,,
1000,102,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,91.5,+inf,,,
1000,103,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,92.5,+inf,,,
1000,104,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,93.5,+inf,,,
1000,105,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,94.5,+inf,,,
1000,-100,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,-110.5,+inf,,,
1000,10.5,unconnected=5,1,2,3,4,5,6,10.5,1,0,1,,,
1000,-10.5,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,-21,+inf,,,
1000,unconnected=5,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,1,2,3,4,5,6,10.5,unconnected=5,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,0,1,0,1,,,
1000,100,connected=+inf,1,2,3,4,5,6,0,connected=+inf,100,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,0,connected=+inf,101,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,0,connected=+inf,102,+inf,,,
1000,103,connected=+inf,1,2,3,4,5,6,0,connected=+inf,103,+inf,,,
1000,104,connected=+inf,1,2,3,4,5,6,0,connected=+inf,104,+inf,,,
1000,105,connected=+inf,1,2,3,4,5,6,0,connected=+inf,105,+inf,,,
1000,-100,connected=+inf,1,2,3,4,5,6,0,connected=+inf,-100,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,0,connected=+inf,10.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,0,connected=+inf,-10.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,0,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,0,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,0,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,0,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,100,connected=+inf,-100,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,100,1,0,1,,,
1000,101,connected=+inf,1,2,3,4,5,6,100,2,1,2,,,
1000,102,connected=+inf,1,2,3,4,5,6,100,3,2,3,,,
1000,103,connected=+inf,1,2,3,4,5,6,100,4,3,4,,,
1000,104,connected=+inf,1,2,3,4,5,6,100,5,4,5,,,
1000,105,connected=+inf,1,2,3,4,5,6,100,6,5,6,,,
1000,-100,connected=+inf,1,2,3,4,5,6,100,connected=+inf,-200,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,100,connected=+inf,-89.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,100,connected=+inf,-110.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,100,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,100,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,100,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,100,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,101,connected=+inf,-101,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,101,connected=+inf,-1,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,101,1,0,1,,,
1000,102,connected=+inf,1,2,3,4,5,6,101,2,1,2,,,
1000,103,connected=+inf,1,2,3,4,5,6,101,3,2,3,,,
1000,104,connected=+inf,1,2,3,4,5,6,101,4,3,4,,,
1000,105,connected=+inf,1,2,3,4,5,6,101,5,4,5,,,
1000,-100,connected=+inf,1,2,3,4,5,6,101,connected=+inf,-201,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,101,connected=+inf,-90.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,101,connected=+inf,-111.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,101,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,101,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,101,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,101,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-102,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-2,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-1,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,102,1,0,1,,,
1000,103,connected=+inf,1,2,3,4,5,6,102,2,1,2,,,
1000,104,connected=+inf,1,2,3,4,5,6,102,3,2,3,,,
1000,105,connected=+inf,1,2,3,4,5,6,102,4,3,4,,,
1000,-100,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-202,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-91.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,102,connected=+inf,-112.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,102,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,102,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,102,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,102,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-103,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-3,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-2,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-1,+inf,,,
1000,103,connected=+inf,1,2,3,4,5,6,103,1,0,1,,,
1000,104,connected=+inf,1,2,3,4,5,6,103,2,1,2,,,
1000,105,connected=+inf,1,2,3,4,5,6,103,3,2,3,,,
1000,-100,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-203,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-92.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,103,connected=+inf,-113.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,103,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,103,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,103,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,103,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-104,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-4,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-3,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-2,+inf,,,
1000,103,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-1,+inf,,,
1000,104,connected=+inf,1,2,3,4,5,6,104,1,0,1,,,
1000,105,connected=+inf,1,2,3,4,5,6,104,2,1,2,,,
1000,-100,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-204,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-93.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,104,connected=+inf,-114.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,104,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,104,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,104,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,104,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-105,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-5,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-4,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-3,+inf,,,
1000,103,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-2,+inf,,,
1000,104,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-1,+inf,,,
1000,105,connected=+inf,1,2,3,4,5,6,105,1,0,1,,,
1000,-100,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-205,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-94.5,+inf,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,105,connected=+inf,-115.5,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,105,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,105,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,105,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,105,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,-10.5,+inf,,,
1000,100,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,89.5,+inf,,,
1000,101,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,90.5,+inf,,,
1000,102,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,91.5,+inf,,,
1000,103,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,92.5,+inf,,,
1000,104,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,93.5,+inf,,,
1000,105,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,94.5,+inf,,,
1000,-100,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,-110.5,+inf,,,
1000,10.5,connected=+inf,1,2,3,4,5,6,10.5,1,0,1,,,
1000,-10.5,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,-21,+inf,,,
1000,unconnected=5,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,#VALUE!,+inf,,,
1000,connected=+inf,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,#VALUE!,+inf,,,
1000,connected=-inf,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,#VALUE!,+inf,,,
1000,connected=nan,connected=+inf,1,2,3,4,5,6,10.5,connected=+inf,#VALUE!,+inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,0,1,0,1,,,
1000,100,connected=-inf,1,2,3,4,5,6,0,connected=-inf,100,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,0,connected=-inf,101,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,0,connected=-inf,102,-inf,,,
1000,103,connected=-inf,1,2,3,4,5,6,0,connected=-inf,103,-inf,,,
1000,104,connected=-inf,1,2,3,4,5,6,0,connected=-inf,104,-inf,,,
1000,105,connected=-inf,1,2,3,4,5,6,0,connected=-inf,105,-inf,,,
1000,-100,connected=-inf,1,2,3,4,5,6,0,connected=-inf,-100,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,0,connected=-inf,10.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,0,connected=-inf,-10.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,0,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,100,connected=-inf,-100,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,100,1,0,1,,,
1000,101,connected=-inf,1,2,3,4,5,6,100,2,1,2,,,
1000,102,connected=-inf,1,2,3,4,5,6,100,3,2,3,,,
1000,103,connected=-inf,1,2,3,4,5,6,100,4,3,4,,,
1000,104,connected=-inf,1,2,3,4,5,6,100,5,4,5,,,
1000,105,connected=-inf,1,2,3,4,5,6,100,6,5,6,,,
1000,-100,connected=-inf,1,2,3,4,5,6,100,connected=-inf,-200,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,100,connected=-inf,-89.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,100,connected=-inf,-110.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,100,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,101,connected=-inf,-101,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,101,connected=-inf,-1,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,101,1,0,1,,,
1000,102,connected=-inf,1,2,3,4,5,6,101,2,1,2,,,
1000,103,connected=-inf,1,2,3,4,5,6,101,3,2,3,,,
1000,104,connected=-inf,1,2,3,4,5,6,101,4,3,4,,,
1000,105,connected=-inf,1,2,3,4,5,6,101,5,4,5,,,
1000,-100,connected=-inf,1,2,3,4,5,6,101,connected=-inf,-201,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,101,connected=-inf,-90.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,101,connected=-inf,-111.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,101,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-102,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-2,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-1,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,102,1,0,1,,,
1000,103,connected=-inf,1,2,3,4,5,6,102,2,1,2,,,
1000,104,connected=-inf,1,2,3,4,5,6,102,3,2,3,,,
1000,105,connected=-inf,1,2,3,4,5,6,102,4,3,4,,,
1000,-100,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-202,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-91.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,102,connected=-inf,-112.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,102,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-103,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-3,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-2,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-1,-inf,,,
1000,103,connected=-inf,1,2,3,4,5,6,103,1,0,1,,,
1000,104,connected=-inf,1,2,3,4,5,6,103,2,1,2,,,
1000,105,connected=-inf,1,2,3,4,5,6,103,3,2,3,,,
1000,-100,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-203,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-92.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,103,connected=-inf,-113.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,103,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-104,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-4,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-3,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-2,-inf,,,
1000,103,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-1,-inf,,,
1000,104,connected=-inf,1,2,3,4,5,6,104,1,0,1,,,
1000,105,connected=-inf,1,2,3,4,5,6,104,2,1,2,,,
1000,-100,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-204,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-93.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,104,connected=-inf,-114.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,104,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-105,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-5,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-4,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-3,-inf,,,
1000,103,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-2,-inf,,,
1000,104,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-1,-inf,,,
1000,105,connected=-inf,1,2,3,4,5,6,105,1,0,1,,,
1000,-100,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-205,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-94.5,-inf,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,105,connected=-inf,-115.5,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,105,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,-10.5,-inf,,,
1000,100,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,89.5,-inf,,,
1000,101,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,90.5,-inf,,,
1000,102,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,91.5,-inf,,,
1000,103,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,92.5,-inf,,,
1000,104,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,93.5,-inf,,,
1000,105,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,94.5,-inf,,,
1000,-100,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,-110.5,-inf,,,
1000,10.5,connected=-inf,1,2,3,4,5,6,10.5,1,0,1,,,
1000,-10.5,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,-21,-inf,,,
1000,unconnected=5,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,1,2,3,4,5,6,10.5,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,0,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,100,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,101,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,102,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,103,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,104,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,105,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,-100,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,10.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,-10.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,-100,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,0,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,1,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,2,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,3,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,4,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,5,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,-200,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,-89.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,-110.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,100,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,-101,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,-1,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,0,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,1,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,2,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,3,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,4,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,-201,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,-90.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,-111.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,101,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-102,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-2,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-1,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,0,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,1,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,2,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,3,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-202,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-91.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,-112.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,102,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-103,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-3,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-2,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-1,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,0,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,1,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,2,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-203,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-92.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,-113.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,103,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-104,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-4,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-3,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-2,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-1,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,0,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,1,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-204,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-93.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,-114.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,104,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-105,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-5,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-4,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-3,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-2,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-1,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,0,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-205,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-94.5,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,-115.5,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,105,connected=-inf,#VALUE!,-inf,,,
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,-10.5,-inf,,,
1000,100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,89.5,-inf,,,
1000,101,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,90.5,-inf,,,
1000,102,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,91.5,-inf,,,
1000,103,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,92.5,-inf,,,
1000,104,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,93.5,-inf,,,
1000,105,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,94.5,-inf,,,
1000,-100,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,-110.5,-inf,,,
1000,10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,0,-inf,,,
1000,-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,-21,-inf,,,
1000,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,#VALUE!,-inf,,,
1000,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,10.5,connected=-inf,#VALUE!,-inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,0,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,100,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,101,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,102,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,103,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,104,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,105,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,-100,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,10.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,-10.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,0,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,-100,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,0,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,1,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,2,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,3,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,4,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,5,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,-200,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,-89.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,-110.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,100,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,-101,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,-1,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,0,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,1,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,2,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,3,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,4,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,-201,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,-90.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,-111.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,101,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-102,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-2,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-1,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,0,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,1,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,2,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,3,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-202,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-91.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,-112.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,102,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-103,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-3,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-2,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-1,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,0,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,1,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,2,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-203,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-92.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,-113.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,103,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-104,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-4,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-3,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-2,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-1,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,0,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,1,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-204,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-93.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,-114.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,104,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-105,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-5,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-4,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-3,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-2,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-1,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,0,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-205,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-94.5,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,-115.5,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,105,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,-10.5,+inf,,,
1000,100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,89.5,+inf,,,
1000,101,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,90.5,+inf,,,
1000,102,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,91.5,+inf,,,
1000,103,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,92.5,+inf,,,
1000,104,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,93.5,+inf,,,
1000,105,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,94.5,+inf,,,
1000,-100,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,-110.5,+inf,,,
1000,10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,0,+inf,,,
1000,-10.5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,-21,+inf,,,
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,10.5,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,connected=+inf,0,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,100,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,101,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,102,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,103,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,104,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,105,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,-100,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,10.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,-10.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,-100,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,0,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,1,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,2,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,3,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,4,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,connected=+inf,5,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,-200,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,-89.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,-110.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,100,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,-101,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,-1,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,connected=+inf,0,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,connected=+inf,1,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,connected=+inf,2,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,connected=+inf,3,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,connected=+inf,4,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,-201,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,-90.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,-111.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,101,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-102,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-2,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-1,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,connected=+inf,0,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,connected=+inf,1,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,connected=+inf,2,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,connected=+inf,3,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-202,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-91.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,-112.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,102,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-103,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-3,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-2,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-1,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,connected=+inf,0,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,connected=+inf,1,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,connected=+inf,2,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-203,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-92.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,-113.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,103,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-104,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-4,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-3,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-2,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-1,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,connected=+inf,0,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,connected=+inf,1,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-204,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-93.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,-114.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,104,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-105,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-5,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-4,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-3,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-2,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-1,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,connected=+inf,0,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-205,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-94.5,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,-115.5,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,105,unconnected=5,#VALUE!,+inf,,,
1000,0,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,-10.5,+inf,,,
1000,100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,89.5,+inf,,,
1000,101,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,90.5,+inf,,,
1000,102,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,91.5,+inf,,,
1000,103,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,92.5,+inf,,,
1000,104,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,93.5,+inf,,,
1000,105,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,94.5,+inf,,,
1000,-100,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,-110.5,+inf,,,
1000,10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,connected=+inf,0,+inf,,,
1000,-10.5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,-21,+inf,,,
1000,unconnected=5,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=-inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,#VALUE!,+inf,,,
1000,connected=nan,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,10.5,unconnected=5,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,0,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,100,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,101,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,102,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,103,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,104,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,105,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,-100,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,10.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,-10.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,0,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,-100,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,0,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,1,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,2,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,3,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,4,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,5,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,-200,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,-89.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,-110.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,100,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,-101,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,-1,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,0,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,1,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,2,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,3,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,4,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,-201,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,-90.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,-111.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,101,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-102,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-2,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-1,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,0,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,1,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,2,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,3,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-202,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-91.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,-112.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,102,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-103,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-3,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-2,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-1,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,0,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,1,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,2,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-203,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-92.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,-113.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,103,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-104,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-4,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-3,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-2,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-1,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,0,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,1,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-204,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-93.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,-114.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,104,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-105,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-5,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-4,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-3,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-2,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-1,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,0,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-205,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-94.5,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,-115.5,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,105,connected=nan,#VALUE!,+inf,,,
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,-10.5,+inf,,,
1000,100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,89.5,+inf,,,
1000,101,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,90.5,+inf,,,
1000,102,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,91.5,+inf,,,
1000,103,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,92.5,+inf,,,
1000,104,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,93.5,+inf,,,
1000,105,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,94.5,+inf,,,
1000,-100,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,-110.5,+inf,,,
1000,10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,0,+inf,,,
1000,-10.5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,-21,+inf,,,
1000,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,#VALUE!,+inf,,,
1000,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,#VALUE!,+inf,,,
1000,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,#VALUE!,+inf,,,
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,10.5,connected=nan,#VALUE!,+inf,,,
