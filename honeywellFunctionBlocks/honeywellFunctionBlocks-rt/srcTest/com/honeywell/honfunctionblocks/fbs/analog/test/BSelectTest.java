/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.analog.BSelect;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Select block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-188
 * <AUTHOR>
 *
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BSelectTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BSelectTest(2979906276)1.0$ @*/
/* Generated Mon Dec 11 17:29:13 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSelectTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  private BSelect selectBlock = null;
  private BExecutionParams executionParams = null;
  
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  selectBlock = new BSelect();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  selectBlock = null;
  }
  

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"defaultVal"}, {"input0"}, {"input1"}, {"input2"}, {"input3"},
		  {"input4"}, {"input5"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"offset"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"in1"}, {"out"}, {"IgnoreInvalidInput"}, {"Fire"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(selectBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(selectBlock.getSlot(slotName));
  }

  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "select.png");
	  BIcon actualFbIcon = selectBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  selectBlock.setIcon(expectedFbIcon);
	  actualFbIcon = selectBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingInputValues() {	  
	  verifySettingValue(32767);
	  verifySettingValue(2.2250738585072014E-308);
	  verifySettingValue(4.9e-324);
	  verifySettingValue(1.7976931348623157e+308);
	  verifySettingValue(Double.NEGATIVE_INFINITY);
	  verifySettingValue(Double.POSITIVE_INFINITY);
	  verifySettingValue(-34);
	  verifySettingValue(34);
	  verifySettingValue(255);
	  verifySettingValue(0);
	  
	  selectBlock.setOffset(0);
	  Assert.assertEquals(selectBlock.getOffset(),0, 0.1);
  }
  
  private void verifySettingValue(double snValue) {
	  selectBlock.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getX().getValue(), snValue, 0.1);
	  selectBlock.setDefaultVal(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getDefaultVal().getValue(), snValue, 0.1);
	  selectBlock.setInput0(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput0().getValue(), snValue, 0.1);
	  selectBlock.setInput1(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput1().getValue(), snValue, 0.1);
	  selectBlock.setInput2(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput2().getValue(), snValue, 0.1);
	  selectBlock.setInput3(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput3().getValue(), snValue, 0.1);
	  selectBlock.setInput4(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput4().getValue(), snValue, 0.1);
	  selectBlock.setInput5(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getInput5().getValue(), snValue, 0.1);
	  selectBlock.setOUTPUT(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(selectBlock.getOUTPUT().getValue(), snValue, 0.1);
  }
  
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Select_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testSelectBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BSelect selBlock = new BSelect();
	  setupNumericSlot(selBlock, BSelect.x.getName(), inputs.get(1));
	  setupNumericSlot(selBlock, BSelect.defaultVal.getName(), inputs.get(2));
	  setupNumericSlot(selBlock, BSelect.input0.getName(), inputs.get(3));
	  setupNumericSlot(selBlock, BSelect.input1.getName(), inputs.get(4));
	  setupNumericSlot(selBlock, BSelect.input2.getName(), inputs.get(5));
	  setupNumericSlot(selBlock, BSelect.input3.getName(), inputs.get(6));
	  setupNumericSlot(selBlock, BSelect.input4.getName(), inputs.get(7));
	  setupNumericSlot(selBlock, BSelect.input5.getName(), inputs.get(8));
	  selBlock.setOffset((int)TestDataHelper.getDouble(inputs.get(9), 0));
	  
	  selBlock.executeHoneywellComponent(executionParams);
	  
	  Assert.assertEquals(selBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(12), 0d), 0.1);
	  selBlock = null;
  }
  
  
  public void setupNumericSlot(BSelect selBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			selBlock.linkTo(nm1, nm1.getSlot("out"), selBlock.getSlot(slotName));
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			return;
		}
		
		switch (slotName) {
		case "x":
			selBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "defaultVal":
			selBlock.setDefaultVal(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input0":
			selBlock.setInput0(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input1":
			selBlock.setInput1(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input2":
			selBlock.setInput2(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input3":
			selBlock.setInput3(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input4":
			selBlock.setInput4(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "input5":
			selBlock.setInput5(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		default:
			break;
		}
	}
  
  //@Test(groups={"testLinkRules"})
  public void testLinkRules() {
	  BMaximum target = new BMaximum();
	  BSelect src = new BSelect();
	  
	  LinkCheck linkCheck = target.checkLink(src, src.getSlot("input0"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("input1"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("input2"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("input3"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("input4"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("input5"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("x"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());
	  linkCheck = target.checkLink(src, src.getSlot("defaultVal"), target.getSlot("in1"),null);
	  Assert.assertFalse(linkCheck.isValid());	  
	  linkCheck = target.checkLink(src, src.getSlot("OUTPUT"), target.getSlot("in1"),null);
	  Assert.assertTrue(linkCheck.isValid());
	  
	  BSelect tempSelect = new BSelect();
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BSelect.offset.getName()), tempSelect, tempSelect.getSlot(BSelect.offset.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BSelect.OUTPUT.getName()), tempSelect, tempSelect.getSlot(BSelect.offset.getName()));
	  LinkCheckUtil.checkValidLink(src, src.getSlot(BSelect.OUTPUT.getName()), tempSelect, tempSelect.getSlot(BSelect.input1.getName()));
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = selectBlock.getConfigPropertiesList();		
		Assert.assertEquals(configList.get(0).getName(), BSelect.offset.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = selectBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BSelect.x.getName(), BSelect.defaultVal.getName(), BSelect.input0.getName(), BSelect.input1.getName(), BSelect.input2.getName(),
				BSelect.input3.getName(), BSelect.input4.getName(), BSelect.input5.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = selectBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BSelect.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
}
