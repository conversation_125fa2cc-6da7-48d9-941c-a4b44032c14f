# Test data for HystereticRelay block
# Requirement ID: F1PLT-ADR-405; TestCase ID: F1PLT-ATC-193
#
# Sequence start format: start,<FYI: Testcase ignores this part. You can have this for your doc purposea>
# TestData format: Iteration Interval,in,onVal,offVal,minOn,minOff,OUTPUT,EnsureMinTime(0-NoWait,1=EnsureMinOn,2=EnsureMinOff),NegOUTPUT
# Sequence end format: end,<any text>
#
# Note: Given numeric values will be directly set into the slots. To pass numeric values (by creating link) you should use connected/unconnected syntax
#
start,sequence-1 (onVal>=offVal & no timer)
1000,10,15,8,0,0,FALSE,0
1000,15,15,8,0,0,TRUE,0
1000,16,15,8,0,0,TRUE,0
1000,9,15,8,0,0,TRUE,0
1000,8,15,8,0,0,TRUE,0
1000,7,15,8,0,0,FALSE,0
end,sequence-1
#
start,sequence-2 (offVal > onVal & no timer)
1000,10,8,15,0,0,TRUE,1
1000,8,8,15,0,0,FALSE,1
1000,7,8,15,0,0,FALSE,1
1000,15,8,15,0,0,FALSE,1
1000,16,8,15,0,0,TRUE,1
1000,8,8,15,0,0,FALSE,1
end,sequence-2
#
start,sequence-3 (onVal>=offVal & minOn timer)
1000,10,15,8,6,0,FALSE,0
1000,15,15,8,6,0,TRUE,0
1000,16,15,8,6,0,TRUE,0
1000,9,15,8,6,0,TRUE,0
1000,8,15,8,6,0,TRUE,0
1000,7,15,8,6,0,TRUE,0
1000,3,15,8,6,0,TRUE,0
1000,1,15,8,6,0,FALSE,0
1000,1,15,8,6,0,TRUE,1
end,sequence-3
#
start,sequence-4 (onVal>=offVal & minOff timer)
1000,15,15,8,0,0,FALSE,1
1000,7,15,8,0,6,TRUE,1
1000,15,15,8,0,6,TRUE,1
1000,16,15,8,0,6,TRUE,1
1000,17,15,8,0,6,TRUE,1
1000,18,15,8,0,6,TRUE,1
1000,19,15,8,0,6,TRUE,1
1000,15,15,8,0,6,FALSE,1
end,sequence-4
#
start,sequence-5
1000,7,8,15,0,0,TRUE,0
1000,16,8,15,0,0,FALSE,0
end,sequence-5