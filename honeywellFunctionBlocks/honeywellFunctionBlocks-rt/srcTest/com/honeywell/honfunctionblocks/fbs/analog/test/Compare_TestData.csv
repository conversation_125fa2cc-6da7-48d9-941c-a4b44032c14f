#Iteration ,in1,in2,onHyst,offHyst,operation,out-neg,Expected Output,Old Output,Equals,Less than,Greater than,onhyst,offhyst,Output Equal,Output less than,Output Greater than,Previous Output
1000,0,0,0,0,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,NULL,FALSE,FALSE,NULL
1000,0,0,0,0,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,1,1,20,20,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,TRUE
1000,10,10,15.5,15.5,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,TRUE
1000,-10,-10,connected=+inf,connected=+inf,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,-10.5,-10.5,unconnected=5,unconnected=5,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,-inf,-inf,0,0,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,connected=+inf,connected=+inf,20,20,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,Equals,0,TRUE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,FALSE
1000,10,10,15.5,15.5,Equals,0,TRUE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,TRUE
1000,-10,10,connected=+inf,connected=+inf,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,10.5,10,connected=nan,connected=nan,Equals,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,-inf,10,0,0,Equals,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,connected=+inf,10,20,20,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,1,1,20,20,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,TRUE
1000,10,10,15.5,15.5,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,TRUE
1000,-10,-10,connected=+inf,connected=+inf,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,-10.5,-10.5,unconnected=5,unconnected=5,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,-inf,-inf,0,0,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,connected=+inf,connected=+inf,20,20,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,Equals,0,TRUE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,FALSE
1000,10,10,15.5,15.5,Equals,0,TRUE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,TRUE
1000,-10,10,connected=+inf,connected=+inf,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,Equals,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,10.5,10,connected=nan,connected=nan,Equals,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,-inf,10,0,0,Equals,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,connected=+inf,10,20,20,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,Equals,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,100,1,20,20,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,20,20,TRUE,TRUE,FALSE,FALSE
1000,10,10,15.5,15.5,Equals,1,FALSE,TRUE,TRUE,FALSE,TRUE,15.5,15.5,FALSE,TRUE,FALSE,TRUE
1000,100,-10,connected=+inf,connected=+inf,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,-inf,-inf,0,0,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,connected=+inf,connected=+inf,20,20,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,Equals,1,FALSE,TRUE,TRUE,TRUE,FALSE,20,20,FALSE,FALSE,TRUE,TRUE
1000,598,10,15.5,15.5,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,15.5,15.5,TRUE,TRUE,FALSE,FALSE
1000,-10,10,connected=+inf,connected=+inf,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,10.5,10,connected=nan,connected=nan,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,-inf,10,0,0,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,connected=+inf,10,20,20,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,1,1,20,20,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,FALSE,TRUE,TRUE,FALSE
1000,10,10,15.5,15.5,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,FALSE,TRUE,TRUE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,-inf,-inf,0,0,Equals,1,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,connected=+inf,connected=+inf,20,20,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,Equals,1,FALSE,TRUE,TRUE,TRUE,FALSE,20,20,FALSE,FALSE,TRUE,TRUE
1000,10,10,15.5,15.5,Equals,1,FALSE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,FALSE,FALSE,TRUE,FALSE
1000,-10,10,connected=+inf,connected=+inf,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,-10.5,10,unconnected=5,unconnected=5,Equals,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,10.5,10,connected=nan,connected=nan,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,-inf,10,0,0,Equals,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,connected=+inf,10,20,20,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,Equals,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,-inf,-inf,15,20,Equals,0,TRUE,TRUE,TRUE,FALSE,FALSE,15,20,TRUE,FALSE,FALSE,FALSE
1000,25,20,2,40,Equals,0,TRUE,TRUE,TRUE,FALSE,TRUE,2,40,TRUE,FALSE,TRUE,TRUE
1000,18,20,2,3,Equals,0,TRUE,,TRUE,FALSE,TRUE,2,3,TRUE,FALSE,TRUE,TRUE
1000,18,20,2,3,Equals,1,FALSE,,TRUE,FALSE,TRUE,2,3,FALSE,TRUE,FALSE,TRUE
1000,13,15,1,0,Equals,0,FALSE,,FALSE,TRUE,FALSE,1,0,FALSE,TRUE,FALSE,FALSE
1000,18,20,2,5,Equals,0,TRUE,,TRUE,TRUE,FALSE,2,5,TRUE,TRUE,FALSE,FALSE
1000,13,15,1,0,Equals,1,TRUE,,FALSE,TRUE,FALSE,1,0,TRUE,FALSE,TRUE,TRUE
1000,18,20,2,5,Equals,1,FALSE,,TRUE,TRUE,FALSE,2,5,FALSE,FALSE,TRUE,TRUE
1000,0,0,0,0,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,1,1,20,20,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,FALSE
1000,10,10,15.5,15.5,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-inf,-inf,0,0,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,connected=+inf,connected=+inf,20,20,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,LessThan,0,TRUE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,TRUE
1000,10,10,15.5,15.5,LessThan,0,TRUE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,TRUE
1000,-10,10,connected=+inf,connected=+inf,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,10.5,10,connected=nan,connected=nan,LessThan,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,TRUE
1000,-inf,10,0,0,LessThan,0,TRUE,TRUE,FALSE,TRUE,TRUE,0,0,FALSE,TRUE,TRUE,FALSE
1000,connected=+inf,10,20,20,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,1,1,20,20,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,FALSE
1000,10,10,15.5,15.5,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-inf,-inf,0,0,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,connected=+inf,connected=+inf,20,20,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,LessThan,0,TRUE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,TRUE
1000,10,10,15.5,15.5,LessThan,0,TRUE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,TRUE
1000,-10,10,connected=+inf,connected=+inf,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,LessThan,0,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,TRUE
1000,10.5,10,connected=nan,connected=nan,LessThan,0,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,TRUE
1000,-inf,10,0,0,LessThan,0,TRUE,TRUE,FALSE,TRUE,TRUE,0,0,FALSE,TRUE,TRUE,FALSE
1000,connected=+inf,10,20,20,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,LessThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,100,1,20,20,LessThan,1,TRUE,TRUE,FALSE,FALSE,TRUE,20,20,TRUE,TRUE,FALSE,TRUE
1000,10,10,15.5,15.5,LessThan,1,TRUE,TRUE,TRUE,TRUE,TRUE,15.5,15.5,FALSE,FALSE,FALSE,TRUE
1000,100,-10,connected=+inf,connected=+inf,LessThan,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-inf,-inf,0,0,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,connected=+inf,connected=+inf,20,20,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,LessThan,1,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,FALSE,TRUE,TRUE,FALSE
1000,598,10,15.5,15.5,LessThan,1,TRUE,TRUE,FALSE,FALSE,TRUE,15.5,15.5,TRUE,TRUE,FALSE,TRUE
1000,-10,10,connected=+inf,connected=+inf,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,10.5,10,connected=nan,connected=nan,LessThan,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,FALSE
1000,-inf,10,0,0,LessThan,1,FALSE,TRUE,FALSE,TRUE,TRUE,0,0,TRUE,FALSE,FALSE,TRUE
1000,connected=+inf,10,20,20,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,1,1,20,20,LessThan,1,TRUE,TRUE,TRUE,TRUE,FALSE,20,20,FALSE,FALSE,TRUE,TRUE
1000,10,10,15.5,15.5,LessThan,1,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,FALSE,TRUE,TRUE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-10.5,-10.5,unconnected=5,unconnected=5,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-inf,-inf,0,0,LessThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,connected=+inf,connected=+inf,20,20,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,LessThan,1,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,FALSE,TRUE,TRUE,FALSE
1000,10,10,15.5,15.5,LessThan,1,TRUE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,FALSE,FALSE,TRUE,TRUE
1000,-10,10,connected=+inf,connected=+inf,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,-10.5,10,unconnected=5,unconnected=5,LessThan,1,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,10.5,10,connected=nan,connected=nan,LessThan,1,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,FALSE
1000,-inf,10,0,0,LessThan,1,FALSE,TRUE,FALSE,TRUE,TRUE,0,0,TRUE,FALSE,FALSE,TRUE
1000,connected=+inf,10,20,20,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,LessThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,-inf,-inf,15,20,LessThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15,20,TRUE,FALSE,FALSE,FALSE
1000,25,20,2,40,LessThan,0,FALSE,TRUE,TRUE,FALSE,TRUE,2,40,TRUE,FALSE,TRUE,FALSE
1000,18,20,2,3,LessThan,0,FALSE,,TRUE,FALSE,TRUE,2,3,TRUE,FALSE,TRUE,FALSE
1000,18,20,2,3,LessThan,1,FALSE,,TRUE,FALSE,TRUE,2,3,FALSE,TRUE,FALSE,FALSE
1000,13,15,1,0,LessThan,0,TRUE,,FALSE,TRUE,FALSE,1,0,FALSE,TRUE,FALSE,TRUE
1000,18,20,2,5,LessThan,0,TRUE,,TRUE,TRUE,FALSE,2,5,TRUE,TRUE,FALSE,TRUE
1000,13,15,1,0,LessThan,1,FALSE,,FALSE,TRUE,FALSE,1,0,TRUE,FALSE,TRUE,TRUE
1000,18,20,2,5,LessThan,1,FALSE,,TRUE,FALSE,FALSE,2,5,FALSE,TRUE,TRUE,FALSE
1000,0,0,0,0,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,TRUE
1000,1,1,20,20,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,FALSE
1000,10,10,15.5,15.5,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-inf,-inf,0,0,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,connected=+inf,connected=+inf,20,20,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,GreaterThan,0,FALSE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,FALSE
1000,10,10,15.5,15.5,GreaterThan,0,FALSE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,FALSE
1000,-10,10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,-10.5,10,unconnected=5,unconnected=5,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,10.5,10,connected=nan,connected=nan,GreaterThan,0,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,-inf,10,0,0,GreaterThan,0,FALSE,TRUE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,TRUE
1000,connected=+inf,10,20,20,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,1,1,20,20,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,20,20,TRUE,FALSE,FALSE,FALSE
1000,10,10,15.5,15.5,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,TRUE,FALSE,FALSE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-10.5,-10.5,unconnected=5,unconnected=5,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,-inf,-inf,0,0,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,0,0,TRUE,FALSE,FALSE,FALSE
1000,connected=+inf,connected=+inf,20,20,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,unconnected=5,15.5,15.5,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,1,10,20,20,GreaterThan,0,FALSE,TRUE,TRUE,TRUE,FALSE,20,20,TRUE,TRUE,FALSE,FALSE
1000,10,10,15.5,15.5,GreaterThan,0,FALSE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,TRUE,TRUE,FALSE,FALSE
1000,-10,10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,-10.5,10,unconnected=5,unconnected=5,GreaterThan,0,FALSE,TRUE,FALSE,TRUE,FALSE,0,0,FALSE,TRUE,FALSE,FALSE
1000,10.5,10,connected=nan,connected=nan,GreaterThan,0,TRUE,TRUE,FALSE,FALSE,TRUE,0,0,FALSE,FALSE,TRUE,FALSE
1000,-inf,10,0,0,GreaterThan,0,FALSE,TRUE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,TRUE
1000,connected=+inf,10,20,20,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,FALSE
1000,unconnected=5,10,15.5,15.5,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,GreaterThan,0,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,100,1,20,20,GreaterThan,1,FALSE,TRUE,FALSE,FALSE,TRUE,20,20,TRUE,TRUE,FALSE,TRUE
1000,10,10,15.5,15.5,GreaterThan,1,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,FALSE,TRUE,TRUE,FALSE
1000,100,-10,connected=+inf,connected=+inf,GreaterThan,1,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,-10.5,-10.5,unconnected=5,unconnected=5,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,10.5,10.5,connected=nan,connected=nan,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-inf,-inf,0,0,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,connected=+inf,connected=+inf,20,20,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,GreaterThan,1,TRUE,TRUE,TRUE,TRUE,TRUE,20,20,FALSE,FALSE,FALSE,TRUE
1000,598,10,15.5,15.5,GreaterThan,1,FALSE,TRUE,FALSE,FALSE,TRUE,15.5,15.5,TRUE,TRUE,FALSE,FALSE
1000,-10,10,connected=+inf,connected=+inf,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,-10.5,10,unconnected=5,unconnected=5,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,10.5,10,connected=nan,connected=nan,GreaterThan,1,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,-inf,10,0,0,GreaterThan,1,TRUE,TRUE,FALSE,FALSE,FALSE,0,0,TRUE,TRUE,TRUE,FALSE
1000,connected=+inf,10,20,20,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,0,0,0,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,FALSE
1000,1,1,20,20,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,TRUE,20,20,FALSE,TRUE,FALSE,TRUE
1000,10,10,15.5,15.5,GreaterThan,1,FALSE,TRUE,TRUE,FALSE,FALSE,15.5,15.5,FALSE,TRUE,TRUE,FALSE
1000,-10,-10,connected=+inf,connected=+inf,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-10.5,-10.5,unconnected=5,unconnected=5,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,10.5,10.5,connected=nan,connected=nan,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,-inf,-inf,0,0,GreaterThan,1,TRUE,TRUE,TRUE,FALSE,FALSE,0,0,FALSE,TRUE,TRUE,TRUE
1000,connected=+inf,connected=+inf,20,20,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,unconnected=5,15.5,15.5,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,connected=nan,connected=+inf,connected=+inf,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,0,10,0,0,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,FALSE
1000,1,10,20,20,GreaterThan,1,TRUE,TRUE,TRUE,TRUE,TRUE,20,20,FALSE,FALSE,FALSE,TRUE
1000,10,10,15.5,15.5,GreaterThan,1,FALSE,TRUE,TRUE,TRUE,FALSE,15.5,15.5,FALSE,FALSE,TRUE,FALSE
1000,-10,10,connected=+inf,connected=+inf,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,-10.5,10,unconnected=5,unconnected=5,GreaterThan,1,TRUE,TRUE,FALSE,TRUE,FALSE,0,0,TRUE,FALSE,TRUE,TRUE
1000,10.5,10,connected=nan,connected=nan,GreaterThan,1,FALSE,TRUE,FALSE,FALSE,TRUE,0,0,TRUE,TRUE,FALSE,TRUE
1000,-inf,10,0,0,GreaterThan,1,TRUE,TRUE,FALSE,FALSE,FALSE,0,0,TRUE,TRUE,TRUE,FALSE
1000,connected=+inf,10,20,20,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,20,20,FALSE,FALSE,FALSE,TRUE
1000,unconnected=5,10,15.5,15.5,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,15.5,15.5,FALSE,FALSE,FALSE,FALSE
1000,connected=nan,10,connected=+inf,connected=+inf,GreaterThan,1,FALSE,FALSE,FALSE,FALSE,FALSE,0,0,FALSE,FALSE,FALSE,FALSE
1000,-inf,-inf,15,20,GreaterThan,0,FALSE,TRUE,TRUE,FALSE,FALSE,15,20,TRUE,FALSE,FALSE,FALSE
1000,25,20,2,40,GreaterThan,0,TRUE,TRUE,TRUE,FALSE,TRUE,2,40,TRUE,FALSE,TRUE,FALSE
1000,18,20,2,3,GreaterThan,0,TRUE,,TRUE,FALSE,TRUE,2,3,TRUE,FALSE,TRUE,TRUE
1000,18,20,2,3,GreaterThan,1,TRUE,,TRUE,FALSE,TRUE,2,3,FALSE,TRUE,FALSE,TRUE
1000,13,15,1,0,GreaterThan,0,FALSE,,FALSE,TRUE,FALSE,1,0,FALSE,TRUE,FALSE,FALSE
1000,18,20,2,5,GreaterThan,0,FALSE,,TRUE,TRUE,FALSE,2,5,TRUE,TRUE,FALSE,FALSE
1000,13,15,1,0,GreaterThan,1,TRUE,,FALSE,TRUE,FALSE,1,0,TRUE,FALSE,TRUE,FALSE
1000,18,20,2,5,GreaterThan,1,TRUE,,TRUE,TRUE,TRUE,2,5,FALSE,FALSE,FALSE,TRUE
