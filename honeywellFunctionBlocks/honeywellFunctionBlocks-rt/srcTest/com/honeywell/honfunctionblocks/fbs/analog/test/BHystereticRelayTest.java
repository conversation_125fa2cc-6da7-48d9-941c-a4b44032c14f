/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BIcon;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.test.BTest.TestStationHandler;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BEncode;
import com.honeywell.honfunctionblocks.fbs.analog.BHystereticRelay;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Hysteretic block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-193
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 8, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BHystereticRelayTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BHystereticRelayTest(2979906276)1.0$ @*/
/* Generated Mon Dec 11 12:03:53 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHystereticRelayTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  hystRelayBlock = new BHystereticRelay();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  hystRelayBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"in"}, {"onVal"}, {"offVal"}, {"minOn"}, {"minOff"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"In"}, {"onValue"}, {"OnVal"}, {"OffVal"}, {"OffValue"}, {"MinOn"}, {"IgnoreInvalidInput"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(hystRelayBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(hystRelayBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "hysteretic_relay.png");
	  BIcon actualFbIcon = hystRelayBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  hystRelayBlock.setIcon(expectedFbIcon);
	  actualFbIcon = hystRelayBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInhystRelayBlock(double snValue) {
	  hystRelayBlock.setIn(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(hystRelayBlock.getIn().getValue(), snValue, 0.1);
	  
	  hystRelayBlock.setOnVal(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(hystRelayBlock.getOnVal().getValue(), snValue, 0.1);
	  
	  hystRelayBlock.setOffVal(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(hystRelayBlock.getOffVal().getValue(), snValue, 0.1);
	  
	  hystRelayBlock.setMinOn(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(hystRelayBlock.getMinOn().getValue(), snValue, 0.1);
	  
	  hystRelayBlock.setMinOff(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(hystRelayBlock.getMinOff().getValue(), snValue, 0.1);
	  
	  hystRelayBlock.setOUTPUT(new BNegatableStatusBoolean(true, BStatus.ok, false)); 
	  Assert.assertEquals(hystRelayBlock.getOUTPUT().getValue(), true);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/HystereticRelay_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testHystRelayBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BHystereticRelay hystRelayBlock = new BHystereticRelay();
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(hystRelayBlock, BHystereticRelay.in.getName(), inputs.get(1));
	  setupNumericSlot(hystRelayBlock, BHystereticRelay.onVal.getName(), inputs.get(2));
	  setupNumericSlot(hystRelayBlock, BHystereticRelay.offVal.getName(), inputs.get(3));
	  setupNumericSlot(hystRelayBlock, BHystereticRelay.minOn.getName(), inputs.get(4));
	  setupNumericSlot(hystRelayBlock, BHystereticRelay.minOff.getName(), inputs.get(5));
	  ((BNegatableStatusBoolean)hystRelayBlock.getOUTPUT()).setNegate(Integer.parseInt(inputs.get(8)) == 0 ? false : true);
	  int waitTime = 0;
	  switch (TestDataHelper.getInt(inputs.get(7), 0)) {
	  case 1:
		  waitTime = (int) hystRelayBlock.getMinOn().getValue();
		  break;

	  case 2:
		  waitTime = (int) hystRelayBlock.getMinOff().getValue();
		  break;
	  }

	  if(waitTime<=0) {
		  hystRelayBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(hystRelayBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(6)));
	  } else {
		  for (int i = 0; i < waitTime; i++) {
			  hystRelayBlock.executeHoneywellComponent(executionParams);
			  Assert.assertEquals(hystRelayBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(6)), "Failed in loop-"+i);
		  }
	  }
	  
	  hystRelayBlock = null;
  }
  
  @DataProvider(name="provideSequencedTestData")
  public Object[][] getSequencedTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/HystereticRelay_SequencedTestData.csv");
  }
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideSequencedTestData")
  public void testHystRelayBlockWithSequenceOfTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
	  BHystereticRelay tempHystRelayBlock = new BHystereticRelay();
	  
	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
		  List<String> inputs = iterator.next();

		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(tempHystRelayBlock, BHystereticRelay.in.getName(), inputs.get(1));
		  setupNumericSlot(tempHystRelayBlock, BHystereticRelay.onVal.getName(), inputs.get(2));
		  setupNumericSlot(tempHystRelayBlock, BHystereticRelay.offVal.getName(), inputs.get(3));
		  setupNumericSlot(tempHystRelayBlock, BHystereticRelay.minOn.getName(), inputs.get(4));
		  setupNumericSlot(tempHystRelayBlock, BHystereticRelay.minOff.getName(), inputs.get(5));
		  ((BNegatableStatusBoolean)tempHystRelayBlock.getOUTPUT()).setNegate(Integer.parseInt(inputs.get(7)) == 0 ? false : true);
		  if(seqNo==1)	tempHystRelayBlock.initHoneywellComponent(null);
		  tempHystRelayBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(tempHystRelayBlock.getOUTPUT().getValue(), TestDataHelper.getBoolean(inputs.get(6)),
				  "Failed at step #"+seqNo+" for input data "+inputs+"; ");
	  }
	  
	  tempHystRelayBlock = null;
  }
  
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = hystRelayBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BHystereticRelay.in.getName(), BHystereticRelay.onVal.getName(), BHystereticRelay.offVal.getName(), BHystereticRelay.minOn.getName(), BHystereticRelay.minOff.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = hystRelayBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BHystereticRelay.OUTPUT.getName() };
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/HystereticRelayFile.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BHystereticRelay hystereticRelay = (BHystereticRelay)folder.get("HystereticRelay");
			Assert.assertEquals(hystereticRelay.getOUTPUT().getType(), BNegatableStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}

  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param hystRelayBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BHystereticRelay hystRelayBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  hystRelayBlock.linkTo(nm1, nm1.getSlot("out"), hystRelayBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "in":
		  hystRelayBlock.setIn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "onVal":
		  hystRelayBlock.setOnVal(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "offVal":
		  hystRelayBlock.setOffVal(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "minOn":
		  hystRelayBlock.setMinOn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "minOff":
		  hystRelayBlock.setMinOff(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  //@Test(groups={"testLinkRules"})
  public void testLinkRules() {
	  BHystereticRelay tempBlock = new BHystereticRelay();
	  checkOutgoingLink(tempBlock, BHystereticRelay.in, false);   
	  checkOutgoingLink(tempBlock, BHystereticRelay.offVal, false);
	  checkOutgoingLink(tempBlock, BHystereticRelay.onVal, false);
	  checkOutgoingLink(tempBlock, BHystereticRelay.minOn, false);
	  checkOutgoingLink(tempBlock, BHystereticRelay.minOn, false);

	  //Make sure Output slot should NOT be a target slot
	  BNumericConst nm = new BNumericConst();
	  LinkCheck checkLink = hystRelayBlock.checkLink(nm, nm.getSlot("out"),
			  hystRelayBlock.getSlot(BHystereticRelay.OUTPUT.getName()), null);
	  Assert.assertFalse(checkLink.isValid());
  }

  private void checkOutgoingLink(BHystereticRelay tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = hystRelayBlock.checkLink(hystRelayBlock, hystRelayBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }

  BHystereticRelay hystRelayBlock;	
  BExecutionParams executionParams;
}
