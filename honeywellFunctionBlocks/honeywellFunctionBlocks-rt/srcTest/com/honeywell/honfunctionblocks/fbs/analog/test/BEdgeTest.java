/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BEdge;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Analog Latch as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-259
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 2, 2018
 */
@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BEdgeTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BEdgeTest(2979906276)1.0$ @*/
/* Generated Mon Dec 11 12:03:53 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEdgeTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  edgeBlock = new BEdge();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  edgeBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"x"}, {"offset"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"THIS"}, {"PREVIOUS"}, {"RISE"}, {"FALL"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X"}, {"Offset"}, {"this"}, {"previous"}, {"rise"}, {"fall"}, {"TailOperation"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(edgeBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(edgeBlock.getSlot(slotName));
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {Float.NEGATIVE_INFINITY}, {Float.MAX_VALUE}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInEdgeBlock(double snValue) {
	  edgeBlock.setX(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(edgeBlock.getX().getValue(), snValue, 0.1);
	  
	  edgeBlock.setOffset(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(edgeBlock.getOffset().getValue(), snValue, 0.1);
	  
	  edgeBlock.setTHIS(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(edgeBlock.getTHIS().getValue(), snValue, 0.1);
	  
	  edgeBlock.setPREVIOUS(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(edgeBlock.getPREVIOUS().getValue(), snValue, 0.1);
	  
	  edgeBlock.setRISE(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(edgeBlock.getRISE().getValue(), true);
	  
	  edgeBlock.setFALL(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(edgeBlock.getFALL().getValue(), true);
	  
	  edgeBlock.setRISE(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(edgeBlock.getRISE().getValue(), false);
	  
	  edgeBlock.setFALL(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(edgeBlock.getFALL().getValue(), false);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Edge_TestData.csv");
  }

  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"}, enabled=true)
  public void testEdgeBlockWithTestData(List<String> inputs) throws BlockExecutionException, BlockInitializationException {
	  BEdge edgeBlock = new BEdge();
	  edgeBlock.initHoneywellComponent(null);
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));

	  setupNumericSlot(edgeBlock, BEdge.x.getName(), inputs.get(1));
	  setupNumericSlot(edgeBlock, BEdge.offset.getName(), inputs.get(2));
	  setupNumericSlot(edgeBlock, BEdge.THIS.getName(), inputs.get(4));	

	  edgeBlock.executeHoneywellComponent(executionParams);
	  
	  Assert.assertEquals(edgeBlock.getTHIS().getValue(), TestDataHelper.getDouble(inputs.get(3), Double.POSITIVE_INFINITY), 0.1);
	  Assert.assertEquals(edgeBlock.getPREVIOUS().getValue(), TestDataHelper.getDouble(inputs.get(4), Double.POSITIVE_INFINITY), 0.1);
	  Assert.assertEquals(edgeBlock.getRISE().getValue(), TestDataHelper.getBoolean(inputs.get(5)));
	  Assert.assertEquals(edgeBlock.getFALL().getValue(), TestDataHelper.getBoolean(inputs.get(6)));

	  edgeBlock = null;
  }
    
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = edgeBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BEdge.x.getName(),BEdge.offset.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = edgeBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BEdge.FALL.getName(), BEdge.PREVIOUS.getName(), BEdge.RISE.getName(), BEdge.THIS.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param edgeBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BEdge edgeBlock, final String slotName, final String inputValue){
	  if (TestDataHelper.isConnected(inputValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = edgeBlock.getProperty(slotName).getType();		
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
				BConverter converter = null;
				if(edgeBlock.get("Link"+slotName)!=null) {
					edgeBlock.remove("Link"+slotName);
				}
				converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),edgeBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				edgeBlock.add("Link"+slotName,conversionLink );				
				conversionLink.activate();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			}else{
				edgeBlock.linkTo(nm1, nm1.getSlot("out"), edgeBlock.getSlot(slotName));
			}			
			
			return;
		}

	  switch (slotName) {
	  case "x":
		  edgeBlock.setX(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "offset":
		  edgeBlock.setOffset(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  
	  case "THIS":
		  edgeBlock.setTHIS(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;
	  }
  }
  
  //@Test(groups={"testLinkRules"})
  public void testLinkRules() {
	  BEdge tempBlock = new BEdge();
	  checkOutgoingLink(tempBlock, BEdge.x, false);   
	  checkOutgoingLink(tempBlock, BEdge.offset, false);
	  
	  //Make sure Output slot should NOT be a target slot
	  BNumericConst nm = new BNumericConst();
	  Assert.assertFalse(edgeBlock.checkLink(nm, nm.getSlot("out"), edgeBlock.getSlot(BEdge.THIS.getName()), null).isValid());
	  Assert.assertFalse(edgeBlock.checkLink(nm, nm.getSlot("out"), edgeBlock.getSlot(BEdge.PREVIOUS.getName()), null).isValid());
	  Assert.assertFalse(edgeBlock.checkLink(nm, nm.getSlot("out"), edgeBlock.getSlot(BEdge.RISE.getName()), null).isValid());
	  Assert.assertFalse(edgeBlock.checkLink(nm, nm.getSlot("out"), edgeBlock.getSlot(BEdge.FALL.getName()), null).isValid());
  }
  
  private void checkOutgoingLink(BEdge tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = edgeBlock.checkLink(edgeBlock, edgeBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }

  BEdge edgeBlock;	
  BExecutionParams executionParams;
}
