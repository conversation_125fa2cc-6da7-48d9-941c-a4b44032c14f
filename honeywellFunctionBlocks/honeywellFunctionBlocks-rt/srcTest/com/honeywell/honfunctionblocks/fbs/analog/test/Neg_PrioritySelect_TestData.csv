#Iteration Intervel,enable1,enable2,enable3,enable4,in1,in2,in3,in4,in1ASDefault,Expected Output,,Expected Output,Negenable1,Negenable2,Negenable3,Negenable4
1000,FALSE,FALSE,FALSE,FALSE,11.5,20,30,40,FALSE,11.5,11.5,+inf,0,0,0,0
1000,connected=1,connected=0,connected=0,connected=0,-11.5,20,30,40,FALSE,-11.5,-11.5,-11.5,0,0,0,0
1000,connected=10.5,FALSE,FALSE,FALSE,0,20,30,40,FALSE,0,0,0,0,0,0,0
1000,connected=-10.5,FALSE,FALSE,FALSE,unconnected=-5,20,30,40,FALSE,+inf,unconnected,+inf,0,0,0,0
1000,unconnected=5,FALSE,FALSE,FALSE,connected=+inf,20,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,FALSE,FALSE,FALSE,connected=-inf,20,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=-inf,FALSE,FALSE,FALSE,connected=nan,20,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=nan,FALSE,FALSE,FALSE,11.5,20,30,40,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,FALSE,TRUE,FALSE,FALSE,-11.5,20,30,40,TRUE,-11.5,-11.5,20,0,0,0,0
1000,TRUE,TRUE,FALSE,FALSE,0,20,30,40,TRUE,0,0,0,0,0,0,0
1000,connected=10.5,TRUE,FALSE,FALSE,unconnected=5,20,30,40,TRUE,+inf,unconnected,+inf,0,0,0,0
1000,connected=-10.5,TRUE,FALSE,FALSE,connected=+inf,20,30,40,TRUE,+inf,connected=+inf,+inf,0,0,0,0
1000,unconnected=5,TRUE,FALSE,FALSE,connected=-inf,20,30,40,TRUE,20,FALSE,20,0,0,0,0
1000,connected=+inf,TRUE,FALSE,FALSE,connected=nan,20,30,40,TRUE,20,FALSE,20,0,0,0,0
1000,connected=-inf,TRUE,FALSE,FALSE,10,11.5,30,40,FALSE,10,FALSE,10,0,0,0,0
1000,connected=nan,TRUE,FALSE,FALSE,10,-11.5,30,40,FALSE,-11.5,FALSE,-11.5,0,0,0,0
1000,FALSE,TRUE,TRUE,FALSE,10,0,30,40,FALSE,10,10,0,0,0,0,0
1000,TRUE,TRUE,TRUE,FALSE,10,unconnected=5,30,40,FALSE,10,10,10,0,0,0,0
1000,connected=10.5,TRUE,TRUE,FALSE,10,connected=+inf,30,40,FALSE,10,10,10,0,0,0,0
1000,connected=-10.5,TRUE,TRUE,FALSE,10,connected=-inf,30,40,FALSE,10,10,10,0,0,0,0
1000,unconnected=5,TRUE,TRUE,FALSE,10,connected=nan,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,TRUE,TRUE,FALSE,10,11.5,30,40,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,connected=-inf,TRUE,TRUE,FALSE,10,-11.5,30,40,TRUE,10,FALSE,10,0,0,0,0
1000,connected=nan,TRUE,TRUE,FALSE,10,0,30,40,TRUE,0,FALSE,0,0,0,0,0
1000,FALSE,TRUE,TRUE,TRUE,10,unconnected=5,30,40,TRUE,+inf,10,+inf,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,10,connected=+inf,30,40,TRUE,10,10,10,0,0,0,0
1000,connected=10.5,TRUE,TRUE,TRUE,10,connected=-inf,30,40,TRUE,10,10,10,0,0,0,0
1000,connected=-10.5,TRUE,TRUE,TRUE,10,connected=nan,30,40,TRUE,10,10,10,0,0,0,0
1000,unconnected=5,TRUE,TRUE,TRUE,10,20,11.5,40,FALSE,20,FALSE,20,0,0,0,0
1000,connected=+inf,TRUE,TRUE,TRUE,10,20,-11.5,40,FALSE,20,FALSE,20,0,0,0,0
1000,connected=-inf,TRUE,TRUE,TRUE,10,20,0,40,FALSE,10,FALSE,10,0,0,0,0
1000,connected=nan,TRUE,TRUE,TRUE,10,20,unconnected=5,40,FALSE,20,FALSE,20,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,10,20,connected=+inf,40,FALSE,10,10,+inf,0,0,0,0
1000,FALSE,TRUE,FALSE,FALSE,10,20,connected=-inf,40,FALSE,10,10,20,0,0,0,0
1000,FALSE,connected=10.5,FALSE,FALSE,10,20,connected=nan,40,FALSE,10,10,20,0,0,0,0
1000,FALSE,connected=-10.5,FALSE,FALSE,10,20,11.5,40,TRUE,10,10,20,0,0,0,0
1000,10,unconnected=5,FALSE,FALSE,10,20,-11.5,40,TRUE,10,FALSE,10,0,0,0,0
1000,FALSE,connected=+inf,FALSE,FALSE,10,20,0,40,TRUE,10,FALSE,10,0,0,0,0
1000,FALSE,connected=-inf,FALSE,FALSE,10,20,unconnected=5,40,TRUE,10,FALSE,20,0,0,0,0
1000,FALSE,connected=nan,FALSE,FALSE,10,20,connected=+inf,40,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,FALSE,FALSE,10,20,connected=-inf,40,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,FALSE,FALSE,10,20,connected=nan,40,TRUE,10,10,10,0,0,0,0
1000,TRUE,connected=10.5,FALSE,FALSE,10,20,30,11.5,FALSE,10,10,10,0,0,0,0
1000,TRUE,connected=-10.5,FALSE,FALSE,10,20,30,-11.5,FALSE,10,10,10,0,0,0,0
1000,TRUE,unconnected=5,FALSE,FALSE,10,20,30,0,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=+inf,FALSE,FALSE,10,20,30,unconnected=5,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=-inf,FALSE,FALSE,10,20,30,connected=+inf,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=nan,FALSE,FALSE,10,20,30,connected=-inf,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,TRUE,FALSE,10,20,30,connected=nan,FALSE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,FALSE,10,20,30,11.5,TRUE,10,10,10,0,0,0,0
1000,TRUE,connected=10.5,TRUE,FALSE,10,20,30,-11.5,TRUE,10,10,10,0,0,0,0
1000,TRUE,connected=-10.5,TRUE,FALSE,10,20,30,0,TRUE,10,10,10,0,0,0,0
1000,TRUE,unconnected=5,TRUE,FALSE,10,20,30,unconnected=5,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=+inf,TRUE,FALSE,10,20,30,connected=+inf,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=-inf,TRUE,FALSE,10,20,30,connected=-inf,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,connected=nan,TRUE,FALSE,10,20,30,connected=nan,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,TRUE,TRUE,11.5,11.5,11.5,11.5,FALSE,11.5,11.5,11.5,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,-11.5,-11.5,-11.5,-11.5,FALSE,-11.5,-11.5,-11.5,0,0,0,0
1000,TRUE,connected=10.5,TRUE,TRUE,0,0,0,0,FALSE,0,0,0,0,0,0,0
1000,TRUE,connected=-10.5,TRUE,TRUE,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,+inf,unconnected,+inf,0,0,0,0
1000,TRUE,unconnected=5,TRUE,TRUE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,TRUE,connected=+inf,TRUE,TRUE,connected=-inf,connected=-inf,connected=-inf,connected=-inf,FALSE,-inf,FALSE,-inf,0,0,0,0
1000,TRUE,connected=-inf,TRUE,TRUE,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,TRUE,connected=nan,TRUE,TRUE,11.5,11.5,11.5,11.5,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,-11.5,-11.5,-11.5,-11.5,TRUE,-11.5,-11.5,-11.5,0,0,0,0
1000,FALSE,FALSE,TRUE,FALSE,0,0,0,0,TRUE,0,0,0,0,0,0,0
1000,FALSE,FALSE,connected=10.5,FALSE,unconnected=5,unconnected=5,unconnected=5,unconnected=5,TRUE,+inf,unconnected,+inf,0,0,0,0
1000,FALSE,FALSE,connected=-10.5,FALSE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,TRUE,+inf,connected=+inf,+inf,0,0,0,0
1000,FALSE,FALSE,unconnected=5,FALSE,connected=-inf,connected=-inf,connected=-inf,connected=-inf,TRUE,-inf,FALSE,-inf,0,0,0,0
1000,FALSE,FALSE,connected=+inf,FALSE,connected=nan,connected=nan,connected=nan,connected=nan,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,connected=-inf,FALSE,11.5,20,30,40,FALSE,11.5,FALSE,30,0,0,0,0
1000,FALSE,FALSE,connected=nan,FALSE,-11.5,20,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,TRUE,FALSE,FALSE,FALSE,0,20,30,40,FALSE,0,0,0,0,0,0,0
1000,TRUE,FALSE,TRUE,FALSE,unconnected=5,20,30,40,FALSE,+inf,unconnected,+inf,0,0,0,0
1000,TRUE,FALSE,connected=10.5,FALSE,connected=+inf,20,30,40,FALSE,+inf,connected=+inf,+inf,0,0,0,0
1000,TRUE,FALSE,connected=-10.5,FALSE,connected=-inf,20,30,40,FALSE,-inf,connected=-inf,-inf,0,0,0,0
1000,TRUE,FALSE,unconnected=5,FALSE,connected=nan,20,30,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,TRUE,FALSE,connected=+inf,FALSE,11.5,20,30,40,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,TRUE,FALSE,connected=-inf,FALSE,-11.5,20,30,40,TRUE,-11.5,FALSE,-11.5,0,0,0,0
1000,TRUE,FALSE,connected=nan,FALSE,0,20,30,40,TRUE,0,FALSE,0,0,0,0,0
1000,TRUE,TRUE,FALSE,FALSE,unconnected=5,20,30,40,TRUE,+inf,unconnected,+inf,0,0,0,0
1000,TRUE,TRUE,TRUE,FALSE,connected=+inf,20,30,40,TRUE,+inf,connected=+inf,+inf,0,0,0,0
1000,TRUE,TRUE,connected=10.5,TRUE,connected=-inf,20,30,40,TRUE,-inf,connected=-inf,-inf,0,0,0,0
1000,TRUE,TRUE,connected=-10.5,TRUE,connected=nan,20,30,40,TRUE,+inf,connected=nan,+inf,0,0,0,0
1000,TRUE,TRUE,unconnected=5,TRUE,10,11.5,30,40,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=+inf,TRUE,10,-11.5,30,40,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=-inf,TRUE,10,0,30,40,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=nan,TRUE,10,unconnected=5,30,40,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,FALSE,TRUE,10,connected=+inf,30,40,FALSE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,10,connected=-inf,30,40,FALSE,10,10,10,0,0,0,0
1000,TRUE,TRUE,connected=10.5,TRUE,10,connected=nan,30,40,FALSE,10,10,10,0,0,0,0
1000,TRUE,TRUE,connected=-10.5,TRUE,10,11.5,30,40,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,unconnected=5,TRUE,10,-11.5,30,40,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=+inf,TRUE,10,0,30,40,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=-inf,TRUE,10,unconnected=5,30,40,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,connected=nan,TRUE,10,connected=+inf,30,40,TRUE,10,FALSE,10,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,10,connected=-inf,30,40,TRUE,10,10,10,0,0,0,0
1000,FALSE,FALSE,FALSE,TRUE,10,connected=nan,30,40,TRUE,10,10,40,0,0,0,0
1000,FALSE,FALSE,FALSE,connected=10.5,10,20,11.5,40,FALSE,10,10,40,0,0,0,0
1000,FALSE,FALSE,FALSE,connected=-10.5,10,20,-11.5,40,FALSE,10,10,40,0,0,0,0
1000,FALSE,FALSE,FALSE,unconnected=5,10,20,0,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,FALSE,connected=+inf,10,20,unconnected=5,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,FALSE,connected=-inf,10,20,connected=+inf,40,FALSE,10,FALSE,40,0,0,0,0
1000,FALSE,FALSE,FALSE,connected=nan,10,20,connected=-inf,40,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,TRUE,FALSE,10,20,connected=nan,40,FALSE,+inf,10,+inf,0,0,0,0
1000,FALSE,FALSE,TRUE,TRUE,10,20,11.5,40,TRUE,10,10,11.5,0,0,0,0
1000,FALSE,FALSE,TRUE,connected=10.5,10,20,-11.5,40,TRUE,10,10,-11.5,0,0,0,0
1000,FALSE,FALSE,TRUE,connected=-10.5,10,20,0,40,TRUE,10,10,0,0,0,0,0
1000,FALSE,FALSE,TRUE,unconnected=5,10,20,unconnected=5,40,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,TRUE,connected=+inf,10,20,connected=+inf,40,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,TRUE,connected=-inf,10,20,connected=-inf,40,TRUE,-inf,FALSE,-inf,0,0,0,0
1000,FALSE,FALSE,TRUE,connected=nan,10,20,connected=nan,40,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,TRUE,FALSE,TRUE,FALSE,10,20,30,11.5,FALSE,10,10,10,0,0,0,0
1000,TRUE,FALSE,TRUE,TRUE,10,20,30,-11.5,FALSE,10,10,10,0,0,0,0
1000,TRUE,FALSE,TRUE,connected=10.5,10,20,30,0,FALSE,10,10,10,0,0,0,0
1000,TRUE,FALSE,TRUE,connected=-10.5,10,20,30,unconnected=5,FALSE,10,10,10,0,0,0,0
1000,TRUE,FALSE,TRUE,unconnected=5,10,20,30,connected=+inf,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,TRUE,connected=+inf,10,20,30,connected=-inf,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,TRUE,connected=-inf,10,20,30,connected=nan,FALSE,10,FALSE,10,0,0,0,0
1000,TRUE,FALSE,TRUE,connected=nan,10,20,30,11.5,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,TRUE,FALSE,10,20,30,-11.5,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,10,20,30,0,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,connected=10.5,10,20,30,unconnected=5,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,connected=-10.5,10,20,30,connected=+inf,TRUE,10,10,10,0,0,0,0
1000,TRUE,TRUE,TRUE,unconnected=5,10,20,30,connected=-inf,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,TRUE,connected=+inf,10,20,30,connected=nan,TRUE,10,FALSE,10,0,0,0,0
1000,TRUE,TRUE,TRUE,connected=-inf,11.5,11.5,11.5,11.5,FALSE,11.5,FALSE,11.5,0,0,0,0
1000,TRUE,TRUE,TRUE,connected=nan,-11.5,-11.5,-11.5,-11.5,FALSE,-11.5,FALSE,-11.5,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,0,0,0,0,FALSE,0,0,+inf,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,unconnected=5,unconnected=5,unconnected=5,unconnected=5,FALSE,+inf,unconnected,+inf,0,0,0,0
1000,connected=10.5,connected=10.5,connected=10.5,connected=10.5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,FALSE,+inf,connected=+inf,+inf,0,0,0,0
1000,connected=-10.5,connected=-10.5,connected=-10.5,connected=-10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,FALSE,-inf,connected=-inf,-inf,0,0,0,0
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=-5,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,11.5,11.5,11.5,11.5,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,-11.5,-11.5,-11.5,-11.5,TRUE,-11.5,FALSE,-11.5,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,0,0,0,0,TRUE,0,FALSE,0,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,TRUE,+inf,unconnected,+inf,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,TRUE,+inf,connected=+inf,+inf,0,0,0,0
1000,connected=10.5,connected=10.5,connected=10.5,connected=10.5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,TRUE,-inf,connected=-inf,-inf,0,0,0,0
1000,connected=-10.5,connected=-10.5,connected=-10.5,connected=-10.5,connected=nan,connected=nan,connected=nan,connected=nan,TRUE,+inf,connected=nan,+inf,0,0,0,0
1000,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,11.5,11.5,11.5,11.5,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,-11.5,-11.5,-11.5,-11.5,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,0,0,0,0,FALSE,0,FALSE,0,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,FALSE,+inf,connected=+inf,+inf,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,connected=-inf,connected=-inf,connected=-inf,connected=-inf,FALSE,-inf,connected=-inf,-inf,0,0,0,0
1000,connected=10.5,connected=10.5,connected=10.5,connected=10.5,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,+inf,connected=nan,+inf,0,0,0,0
1000,connected=-10.5,connected=-10.5,connected=-10.5,connected=-10.5,11.5,11.5,11.5,11.5,TRUE,11.5,11.5,11.5,0,0,0,0
1000,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,-11.5,-11.5,-11.5,-11.5,TRUE,-11.5,FALSE,-11.5,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,0,0,0,0,TRUE,0,FALSE,0,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,FALSE,FALSE,FALSE,FALSE,connected=-inf,connected=-inf,connected=-inf,connected=-inf,TRUE,-inf,connected=-inf,-inf,0,0,0,0
1000,TRUE,TRUE,TRUE,TRUE,connected=nan,connected=nan,connected=nan,connected=nan,TRUE,+inf,connected=nan,+inf,0,0,0,0
1000,connected=10.5,connected=10.5,connected=10.5,connected=10.5,11.5,11.5,11.5,11.5,FALSE,11.5,11.5,11.5,0,0,0,0
1000,connected=-10.5,connected=-10.5,connected=-10.5,connected=-10.5,-11.5,-11.5,-11.5,-11.5,FALSE,-11.5,-11.5,-11.5,0,0,0,0
1000,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,0,0,0,0,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,connected=nan,connected=nan,connected=nan,connected=nan,FALSE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,11.5,11.5,11.5,11.5,TRUE,11.5,FALSE,11.5,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,-11.5,-11.5,-11.5,-11.5,TRUE,-11.5,FALSE,-11.5,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,0,0,0,0,TRUE,0,FALSE,0,0,0,0,0
1000,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,unconnected=-5,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,TRUE,-inf,FALSE,-inf,0,0,0,0
1000,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,TRUE,+inf,FALSE,+inf,0,0,0,0
1000,connected=1,connected=0,connected=0,connected=0,-11.5,20,30,40,FALSE,20,20,20,1,1,0,0
1000,connected=1,connected=0,connected=0,connected=0,-11.5,20,30,40,FALSE,20,20,20,1,1,0,0
1000,connected=1,connected=0,connected=0,connected=0,-11.5,20,30,40,FALSE,30,30,30,1,0,1,0
1000,connected=1,connected=0,connected=0,connected=0,-11.5,20,30,40,FALSE,40,40,40,1,0,0,1
1000,connected=1,connected=1,connected=1,connected=0,-11.5,20,30,40,FALSE,40,40,40,1,1,1,1