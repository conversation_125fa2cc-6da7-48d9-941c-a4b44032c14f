# Test data for Maximum block
# The test data should follow the order given below
# IterationInterval(sec),IgnoreInvalidInput,in1,in2,in3,in4,in5,in6,in7,in8,ExpectedOutput
1,FALSE,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,+inf
1,TRUE,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,+inf
1,TRUE,45.67,-360.78,-490.65,-490,125,136,64,89,136
1,FALSE,45.67,-360.78,490.65,-490,125,136,64,89,490.65
1,FALSE,45,-360,490,64,125,136,unconnected=500,unconnected=500,490
1,TRUE,45,-360,490,64,125,136,unconnected=500,unconnected=500,490
1,TRUE,29,150,connected=+inf,connected=+inf,connected=+inf,connected=+inf,75,115,150
1,FALSE,29,150,connected=+inf,connected=+inf,connected=+inf,connected=+inf,75,115,+inf
1,FALSE,9,90,-90,unconnected=500,unconnected=500,unconnected=500,unconnected=500,unconnected=500,90
1,FALSE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,+inf
1,TRUE,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,+inf
1,TRUE,+inf,650,-760,-1.11,3456,-6539,-67,-inf,3456
1,FALSE,+inf,650,-760,-1.11,3456,-6539,-67,-inf,+inf
1,TRUE,45,-360,490,64,125,136,25,+inf,490
1,FALSE,45,-360,-490,64,125,136,25,+inf,+inf