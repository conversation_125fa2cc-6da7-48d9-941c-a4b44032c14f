/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BStation;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BPrioritySelect;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Priority Select as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-192
 * <AUTHOR>
 * @since Dec 13, 2017
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BPrioritySelectTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BPrioritySelectTest(2979906276)1.0$ @*/
/* Generated Tue Dec 12 15:30:19 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPrioritySelectTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  private BPrioritySelect prioritySelectBlock = null;
  private BExecutionParams executionParams = null;
  
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  prioritySelectBlock = new BPrioritySelect();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  prioritySelectBlock = null;
  }
  

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"enable1"}, {"enable2"}, {"enable3"}, {"enable4"}, {"in1"}, {"in2"},
		  {"in3"}, {"in4"}};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"In1AsDefault"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"In1"}, {"Output"}, {"IgnoreInvalidInput"}, {"In1Asdefault"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(prioritySelectBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(prioritySelectBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "priority_select.png");
	  BIcon actualFbIcon = prioritySelectBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  prioritySelectBlock.setIcon(expectedFbIcon);
	  actualFbIcon = prioritySelectBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingInputValues() {
	  verifySettingValue(32767);
	  verifySettingValue(2.2250738585072014E-308);
	  verifySettingValue(4.9e-324);
	  verifySettingValue(1.7976931348623157e+308);
	  verifySettingValue(Double.NEGATIVE_INFINITY);
	  verifySettingValue(Double.POSITIVE_INFINITY);
	  verifySettingValue(Double.NaN);
	  verifySettingValue(-34);
	  verifySettingValue(34);
	  verifySettingValue(255);
	  verifySettingValue(0);
	  
	  prioritySelectBlock.setIn1AsDefault(true);
	  Assert.assertEquals(prioritySelectBlock.getIn1AsDefault(),true);
	  prioritySelectBlock.setIn1AsDefault(false);
	  Assert.assertEquals(prioritySelectBlock.getIn1AsDefault(),false);
	  
	  prioritySelectBlock.setEnable1(new BNegatableFiniteStatusBoolean(false, BStatus.ok,false));
	  ((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable1()).setNegate(true);
	  Assert.assertEquals(prioritySelectBlock.getEnable1().getValue(),false);
	  prioritySelectBlock.setEnable1(new BNegatableFiniteStatusBoolean(true, BStatus.ok,false));
	  Assert.assertEquals(prioritySelectBlock.getEnable1().getValue(),true);
	  Assert.assertEquals(((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable1()).getNegate(), true);
	  
	  prioritySelectBlock.setEnable2(new BNegatableFiniteStatusBoolean(false, BStatus.ok,false));
	  ((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable2()).setNegate(false);
	  Assert.assertEquals(prioritySelectBlock.getEnable2().getValue(),false);
	  prioritySelectBlock.setEnable2(new BNegatableFiniteStatusBoolean(true, BStatus.ok,false));
	  Assert.assertEquals(prioritySelectBlock.getEnable2().getValue(),true);
	  Assert.assertEquals(((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable2()).getNegate(), false);
	  
	  prioritySelectBlock.setEnable3(new BNegatableFiniteStatusBoolean(false, BStatus.ok,false));
	  ((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable3()).setNegate(true);
	  Assert.assertEquals(prioritySelectBlock.getEnable3().getValue(),false);
	  prioritySelectBlock.setEnable3(new BNegatableFiniteStatusBoolean(true, BStatus.ok,false));
	  Assert.assertEquals(prioritySelectBlock.getEnable3().getValue(),true);
	  Assert.assertEquals(((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable3()).getNegate(), true);
	  
	  prioritySelectBlock.setEnable4(new BNegatableFiniteStatusBoolean(false, BStatus.ok,false));
	  ((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable4()).setNegate(false);
	  Assert.assertEquals(prioritySelectBlock.getEnable4().getValue(),false);
	  prioritySelectBlock.setEnable4(new BNegatableFiniteStatusBoolean(true, BStatus.ok,false));
	  Assert.assertEquals(prioritySelectBlock.getEnable4().getValue(),true);
	  Assert.assertEquals(((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable4()).getNegate(), false);
  }
  
  private void verifySettingValue(double snValue) {
	  prioritySelectBlock.setIn1(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(prioritySelectBlock.getIn1().getValue()));
	  else
		  Assert.assertEquals(prioritySelectBlock.getIn1().getValue(), snValue, 0.1);	  
	  
	  prioritySelectBlock.setIn2(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(prioritySelectBlock.getIn1().getValue()));
	  else
		  Assert.assertEquals(prioritySelectBlock.getIn2().getValue(), snValue, 0.1);
	  
	  
	  prioritySelectBlock.setIn3(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(prioritySelectBlock.getIn1().getValue()));
	  else
		  Assert.assertEquals(prioritySelectBlock.getIn3().getValue(), snValue, 0.1);
	  
	  prioritySelectBlock.setIn4(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(prioritySelectBlock.getIn1().getValue()));
	  else
		  Assert.assertEquals(prioritySelectBlock.getIn4().getValue(), snValue, 0.1);
	  
	  prioritySelectBlock.setOUTPUT(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(prioritySelectBlock.getIn1().getValue()));
	  else
		  Assert.assertEquals(prioritySelectBlock.getOUTPUT().getValue(), snValue, 0.1);
  }
  
  
  
  @DataProvider(name="provideTestData")
	public Object[][] getTesData() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/PrioritySelect_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}

		Object[][] objArray = new Object[validInputs.size()][];
		for (int i = 0; i < validInputs.size(); i++) {
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		}

		return objArray;
	}
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testSelectBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BPrioritySelect prioritySelBlock = new BPrioritySelect();
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable1.getName(), inputs.get(1));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable2.getName(), inputs.get(2));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable3.getName(), inputs.get(3));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable4.getName(), inputs.get(4));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in1.getName(), inputs.get(5));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in2.getName(), inputs.get(6));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in3.getName(), inputs.get(7));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in4.getName(), inputs.get(8));
	  prioritySelBlock.setIn1AsDefault(TestDataHelper.getBoolean(inputs.get(9)));
	  
	  prioritySelBlock.executeHoneywellComponent(executionParams);
	  
	  Assert.assertEquals(prioritySelBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(12), 0d), 0.1);
	  prioritySelBlock = null;
  }
  
  @DataProvider(name="provideTestData1")
 	public Object[][] getTesData1() {
 		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Neg_PrioritySelect_TestData.csv").get();
 		CsvReader readValidInputs;
 		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
 		try {
 			readValidInputs = new CsvReader(file.getInputStream());
 			List<String> rec;
 			while ((rec = readValidInputs.read()) != null) {
 				validInputs.add(rec);
 			}
 			readValidInputs.close();
 		} catch (IOException e) {
 			validInputs = null;
 		}

 		Object[][] objArray = new Object[validInputs.size()][];
 		for (int i = 0; i < validInputs.size(); i++) {
 			objArray[i] = new Object[1];
 			objArray[i][0] = validInputs.get(i);
 		}

 		return objArray;
 	}
  
  @Test(dataProvider="provideTestData1", dependsOnMethods={"testSlotAvailability"})
  public void testSelectBlockWithTestData1(List<String> inputs) throws BlockExecutionException {
	  BPrioritySelect prioritySelBlock = new BPrioritySelect();
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable1.getName(), inputs.get(1));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable2.getName(), inputs.get(2));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable3.getName(), inputs.get(3));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.enable4.getName(), inputs.get(4));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in1.getName(), inputs.get(5));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in2.getName(), inputs.get(6));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in3.getName(), inputs.get(7));
	  setupNumericSlot(prioritySelBlock, BPrioritySelect.in4.getName(), inputs.get(8));
	  prioritySelBlock.setIn1AsDefault(TestDataHelper.getBoolean(inputs.get(9)));
	  
	  boolean negateEnable1 = (inputs.get(13).trim()).equals("0") ? false : true;
	  ((BNegatableFiniteStatusBoolean)prioritySelBlock.getEnable1()).setNegate(negateEnable1);
	  boolean negateEnable2 = (inputs.get(14).trim()).equals("0") ? false : true;
	  ((BNegatableFiniteStatusBoolean)prioritySelBlock.getEnable2()).setNegate(negateEnable2);
	  boolean negateEnable3 = (inputs.get(15).trim()).equals("0") ? false : true;
	  ((BNegatableFiniteStatusBoolean)prioritySelBlock.getEnable3()).setNegate(negateEnable3);
	  boolean negateEnable4 = (inputs.get(16).trim()).equals("0") ? false : true;
	  ((BNegatableFiniteStatusBoolean)prioritySelBlock.getEnable4()).setNegate(negateEnable4);
	  
	  prioritySelBlock.executeHoneywellComponent(executionParams);
	  
	  Assert.assertEquals(prioritySelBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(12), 0d), 0.1);
	  prioritySelBlock = null;
  }
  
  
  public void setupNumericSlot(BPrioritySelect prioritySelBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = prioritySelBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),prioritySelBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				prioritySelBlock.add("Link"+slotName,conversionLink );			
				conversionLink.activate();
			}else{
				prioritySelBlock.linkTo(nm1, nm1.getSlot("out"), prioritySelBlock.getSlot(slotName));
			}			
			
			return;
		}
		
		switch (slotName) {
		case "enable1":
			prioritySelBlock.setEnable1(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue,false));
			break;
			
		case "enable2":
			prioritySelBlock.setEnable2(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue,false));
			break;
			
		case "enable3":
			prioritySelBlock.setEnable3(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue,false));
			break;
			
		case "enable4":
			prioritySelBlock.setEnable4(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue,false));
			break;
			
		case "in1":
			prioritySelBlock.setIn1(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "in2":
			prioritySelBlock.setIn2(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "in3":
			prioritySelBlock.setIn3(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		case "in4":
			prioritySelBlock.setIn4(TestDataHelper.getHonStatusNumeric(inputValue, Double.POSITIVE_INFINITY));
			break;
			
		default:
			break;
		}
	}
  
  
  @Test
  public void testSettingNullStatus() {
	  prioritySelectBlock.setEnable1(new BNegatableFiniteStatusBoolean(true,BStatus.ok,false));
	  ((BNegatableFiniteStatusBoolean)prioritySelectBlock.getEnable1()).setNegate(false);
	  prioritySelectBlock.setEnable2(new BNegatableFiniteStatusBoolean(false,BStatus.ok,false));
	  prioritySelectBlock.setEnable3(new BNegatableFiniteStatusBoolean(false,BStatus.ok,false));
	  prioritySelectBlock.setEnable4(new BNegatableFiniteStatusBoolean(false,BStatus.ok,false));
	  prioritySelectBlock.setIn1(new BHonStatusNumeric(-11.5));
	  prioritySelectBlock.getIn1().setStatus(BStatus.nullStatus);
	  prioritySelectBlock.setIn2(new BHonStatusNumeric(20));
	  prioritySelectBlock.setIn3(new BHonStatusNumeric(30));
	  prioritySelectBlock.setIn4(new BHonStatusNumeric(40));
	  prioritySelectBlock.setIn1AsDefault(false);
	  
	  try {
		prioritySelectBlock.executeHoneywellComponent(executionParams);
	} catch (BlockExecutionException e) {
		e.printStackTrace();
	}
	  
	Assert.assertEquals(prioritySelectBlock.getOUTPUT().getValue(), Double.POSITIVE_INFINITY,0.1);	  
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = prioritySelectBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BPrioritySelect.In1AsDefault.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = prioritySelectBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BPrioritySelect.enable1.getName(), BPrioritySelect.enable2.getName(), BPrioritySelect.enable3.getName(), BPrioritySelect.enable4.getName(), BPrioritySelect.in1.getName(),
				BPrioritySelect.in2.getName(), BPrioritySelect.in3.getName(), BPrioritySelect.in4.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = prioritySelectBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BPrioritySelect.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/PrioritySelectFile.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BPrioritySelect prioritySelect = (BPrioritySelect)folder.get("PrioritySelect");
			Assert.assertEquals(prioritySelect.getEnable1().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(prioritySelect.getEnable2().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(prioritySelect.getEnable3().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(prioritySelect.getEnable4().getType(), BNegatableFiniteStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}
  
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BPrioritySelect target = new BPrioritySelect();
	  BPrioritySelect src = new BPrioritySelect();	  
	  LinkCheckUtil.checkInvalidLink(src,src.getSlot(BPrioritySelect.enable1.getName()), target, target.getSlot(BPrioritySelect.enable1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.enable2.getName()), target, target.getSlot(BPrioritySelect.enable1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.enable3.getName()), target, target.getSlot(BPrioritySelect.enable1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.enable4.getName()), target, target.getSlot(BPrioritySelect.enable1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.in1.getName()), target, target.getSlot(BPrioritySelect.in1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.in2.getName()), target, target.getSlot(BPrioritySelect.in1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.in3.getName()), target, target.getSlot(BPrioritySelect.in1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.in4.getName()), target, target.getSlot(BPrioritySelect.in1.getName()));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPrioritySelect.In1AsDefault.getName()), target, target.getSlot(BPrioritySelect.In1AsDefault.getName())); 
	}


}
