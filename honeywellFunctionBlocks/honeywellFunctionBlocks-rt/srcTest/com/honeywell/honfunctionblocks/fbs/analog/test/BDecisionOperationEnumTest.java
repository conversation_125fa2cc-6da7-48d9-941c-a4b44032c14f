/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.analog.BDecisionOperationEnum;

/**
 * Test suite to test DecisionOperationEnum as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-260
 * <AUTHOR> - RSH.<PERSON>nan
 * @since Jan 17, 2018
 */

@NiagaraType
public class BDecisionOperationEnumTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BDecisionOperationEnumTest(**********)1.0$ @*/
/* Generated Wed Jan 17 16:15:25 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDecisionOperationEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { Integer.valueOf(BDecisionOperationEnum.EQUALS), BDecisionOperationEnum.Equals },
				{ Integer.valueOf(BDecisionOperationEnum.GREATER_THAN), BDecisionOperationEnum.GreaterThan },
				{ Integer.valueOf(BDecisionOperationEnum.GREATER_THAN_OR_EQUAL_TO), BDecisionOperationEnum.GreaterThanOrEqualTo },
				{ Integer.valueOf(BDecisionOperationEnum.LESS_THAN), BDecisionOperationEnum.LessThan },
				{ Integer.valueOf(BDecisionOperationEnum.LESS_THAN_OR_EQUAL_TO), BDecisionOperationEnum.LessThanOrEqualTo },
				{ Integer.valueOf(BDecisionOperationEnum.ANY_EQUAL), BDecisionOperationEnum.AnyEqual },
				{ Integer.valueOf(BDecisionOperationEnum.IN_ANY_RANGE), BDecisionOperationEnum.InAnyRange }};
	}

	@Test(dataProvider = "enumOrdinal")
	public void testOperationByMakeOrdinal(Integer ordinal, BDecisionOperationEnum oe) {
		Assert.assertEquals(BDecisionOperationEnum.make(ordinal.intValue()), oe);
	}
	
	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { BDecisionOperationEnum.Equals.getTag(), BDecisionOperationEnum.Equals },
				{ BDecisionOperationEnum.GreaterThan.getTag(), BDecisionOperationEnum.GreaterThan },
				{ BDecisionOperationEnum.GreaterThanOrEqualTo.getTag(), BDecisionOperationEnum.GreaterThanOrEqualTo },
				{ BDecisionOperationEnum.LessThan.getTag(), BDecisionOperationEnum.LessThan },
				{ BDecisionOperationEnum.LessThanOrEqualTo.getTag(), BDecisionOperationEnum.LessThanOrEqualTo },
				{ BDecisionOperationEnum.AnyEqual.getTag(), BDecisionOperationEnum.AnyEqual },
				{ BDecisionOperationEnum.InAnyRange.getTag(), BDecisionOperationEnum.InAnyRange }};
	}
	
	@Test(dataProvider = "enumTag")
	public void testOperationByMakeTag(String tag, BDecisionOperationEnum oe) {
		Assert.assertEquals(BDecisionOperationEnum.make(tag), oe);
	}
}
