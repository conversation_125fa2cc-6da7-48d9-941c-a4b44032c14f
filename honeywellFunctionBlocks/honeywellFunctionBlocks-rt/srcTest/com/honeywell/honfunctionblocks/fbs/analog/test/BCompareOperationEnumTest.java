/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.analog.BCompareOperationEnum;

/**
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON>
 * @since May 16, 2018
 */
@NiagaraType
public class BCompareOperationEnumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BCompareOperationEnumTest(**********)1.0$ @*/
/* Generated Wed May 16 06:16:25 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCompareOperationEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { 0, BCompareOperationEnum.Equals }, { 1, BCompareOperationEnum.LessThan },{ 2, BCompareOperationEnum.GreaterThan } };
	}

	@Test(dataProvider = "enumOrdinal")
	public void testCompareOperationByMakeOrdinal(int ordinal, BCompareOperationEnum toEnum) {
		Assert.assertEquals(BCompareOperationEnum.make(ordinal), toEnum);
	}

	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { "Equals", BCompareOperationEnum.Equals }, { "LessThan", BCompareOperationEnum.LessThan } ,{ "GreaterThan", BCompareOperationEnum.GreaterThan }};
	}

	@Test(dataProvider = "enumTag")
	public void testCompareOperationByMakeTag(String tag, BCompareOperationEnum adEEnum) {
		Assert.assertEquals(BCompareOperationEnum.make(tag), adEEnum);
	}
  
}
