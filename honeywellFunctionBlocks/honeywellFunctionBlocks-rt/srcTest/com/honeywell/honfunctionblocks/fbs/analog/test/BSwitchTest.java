/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BSwitch;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Switch block as per SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-194
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Dec 15, 2017
 */
@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BSwitchTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BSwitchTest(2979906276)1.0$ @*/
/* Generated Mon Dec 11 12:03:53 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSwitchTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  switchBlock = new BSwitch();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  switchBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"input"}, {"offset"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT0"}, {"OUTPUT1"}, {"OUTPUT2"}, {"OUTPUT3"}, {"OUTPUT4"}, {"OUTPUT5"}, {"OUTPUT6"}, {"OUTPUT7"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"Input"}, {"In"}, {"Offset"}, {"OFFSET"}, {"output"}, {"IgnoreInvalidInput"}, {"TailOperation"}, {"Output"}, {"OUTPUT"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(switchBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(switchBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "switch.png");
	  BIcon actualFbIcon = switchBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  switchBlock.setIcon(expectedFbIcon);
	  actualFbIcon = switchBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-1}, {0}, {255}, {256}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInSwitchRelayBlock(double snValue) {
	  switchBlock.setInput(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(switchBlock.getInput().getValue(), snValue, 0.1);
	  
	  switchBlock.setOffset((int) snValue);
	  Assert.assertEquals(switchBlock.getOffset(), snValue, 0.1);
	  
	  switchBlock.setOUTPUT0(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT0().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT0().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT0().getNegate(), false);
	  
	  switchBlock.setOUTPUT1(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT1().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT1().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT1().getNegate(), false);
	  
	  switchBlock.setOUTPUT2(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT2().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT2().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT2().getNegate(), false);
	  
	  switchBlock.setOUTPUT3(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT3().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT3().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT3().getNegate(), false);

	  switchBlock.setOUTPUT4(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT4().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT4().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT4().getNegate(), false);

	  switchBlock.setOUTPUT5(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT5().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT5().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT5().getNegate(), false);

	  switchBlock.setOUTPUT6(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT6().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT6().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT6().getNegate(), false);

	  switchBlock.setOUTPUT7(new BNegatableStatusBoolean(false, BStatus.ok, false));
	  switchBlock.getOUTPUT7().setNegate(false);
	  Assert.assertEquals(switchBlock.getOUTPUT7().getValue(), false);
	  Assert.assertEquals(switchBlock.getOUTPUT7().getNegate(), false);
	  
	  //Test with true value for slot and negate
	  switchBlock.setOUTPUT0(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT0().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT0().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT0().getNegate(), true);
	  
	  switchBlock.setOUTPUT1(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT1().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT1().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT1().getNegate(), true);
	  
	  switchBlock.setOUTPUT2(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT2().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT2().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT2().getNegate(), true);
	  
	  switchBlock.setOUTPUT3(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT3().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT3().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT3().getNegate(), true);

	  switchBlock.setOUTPUT4(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT4().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT4().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT4().getNegate(), true);

	  switchBlock.setOUTPUT5(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT5().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT5().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT5().getNegate(), true);

	  switchBlock.setOUTPUT6(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT6().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT6().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT6().getNegate(), true);

	  switchBlock.setOUTPUT7(new BNegatableStatusBoolean(true, BStatus.ok, true));
	  switchBlock.getOUTPUT7().setNegate(true);
	  Assert.assertEquals(switchBlock.getOUTPUT7().getValue(), true);
	  Assert.assertEquals(switchBlock.getOUTPUT7().getNegate(), true);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Switch_TestData.csv");
  }

  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testSwitchBlockWithTestData(List<String> inputs) throws BlockExecutionException, BlockInitializationException {
	  BSwitch switchBlock = new BSwitch();
	  switchBlock.initHoneywellComponent(null);
	  
	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setupNumericSlot(switchBlock, BSwitch.input.getName(), inputs.get(1));
	  setupNumericSlot(switchBlock, BSwitch.offset.getName(), inputs.get(2));
	  switchBlock.getOUTPUT0().setNegate(TestDataHelper.getBoolean(inputs.get(3)));
	  switchBlock.getOUTPUT1().setNegate(TestDataHelper.getBoolean(inputs.get(4)));
	  switchBlock.getOUTPUT2().setNegate(TestDataHelper.getBoolean(inputs.get(5)));
	  switchBlock.getOUTPUT3().setNegate(TestDataHelper.getBoolean(inputs.get(6)));
	  switchBlock.getOUTPUT4().setNegate(TestDataHelper.getBoolean(inputs.get(7)));
	  switchBlock.getOUTPUT5().setNegate(TestDataHelper.getBoolean(inputs.get(8)));
	  switchBlock.getOUTPUT6().setNegate(TestDataHelper.getBoolean(inputs.get(9)));
	  switchBlock.getOUTPUT7().setNegate(TestDataHelper.getBoolean(inputs.get(10)));

	  switchBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(switchBlock.getOUTPUT0().getValue(), TestDataHelper.getBoolean(inputs.get(11)));
	  Assert.assertEquals(switchBlock.getOUTPUT1().getValue(), TestDataHelper.getBoolean(inputs.get(12)));
	  Assert.assertEquals(switchBlock.getOUTPUT2().getValue(), TestDataHelper.getBoolean(inputs.get(13)));
	  Assert.assertEquals(switchBlock.getOUTPUT3().getValue(), TestDataHelper.getBoolean(inputs.get(14)));
	  Assert.assertEquals(switchBlock.getOUTPUT4().getValue(), TestDataHelper.getBoolean(inputs.get(15)));
	  Assert.assertEquals(switchBlock.getOUTPUT5().getValue(), TestDataHelper.getBoolean(inputs.get(16)));
	  Assert.assertEquals(switchBlock.getOUTPUT6().getValue(), TestDataHelper.getBoolean(inputs.get(17)));
	  Assert.assertEquals(switchBlock.getOUTPUT7().getValue(), TestDataHelper.getBoolean(inputs.get(18)));
	  
	  switchBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param switchBlock
   * @param slotName
   * @param inputValue
   */
  public void setupNumericSlot(BSwitch switchBlock, final String slotName, final String inputValue){
	  if(TestDataHelper.isConnected(inputValue)){
		  BNumericConst nm1 = new BNumericConst();
		  switchBlock.linkTo(nm1, nm1.getSlot("out"), switchBlock.getSlot(slotName));
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  return;
	  }

	  switch (slotName) {
	  case "input":
		  switchBlock.setInput(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
		  break;

	  case "offset":
		  switchBlock.setOffset((int) TestDataHelper.getDouble(inputValue, 0d));
		  break;
	  }
  }
  
  //@Test(groups={"testLinkRules"})
  public void testLinkRules() {
	  BSwitch tempBlock = new BSwitch();
	  checkOutgoingLink(tempBlock, BSwitch.input, false);   
	  checkOutgoingLink(tempBlock, BSwitch.offset, false);
	  
	  checkIncomingLink(BSwitch.offset, false);
	  //Make sure Output slot should NOT be a target slot
	  checkIncomingLink(BSwitch.OUTPUT0, false);
	  checkIncomingLink(BSwitch.OUTPUT1, false);
	  checkIncomingLink(BSwitch.OUTPUT2, false);
	  checkIncomingLink(BSwitch.OUTPUT3, false);
	  checkIncomingLink(BSwitch.OUTPUT4, false);
	  checkIncomingLink(BSwitch.OUTPUT5, false);
	  checkIncomingLink(BSwitch.OUTPUT6, false);
	  checkIncomingLink(BSwitch.OUTPUT7, false);
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = switchBlock.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BSwitch.offset.getName());
	}

	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = switchBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BSwitch.input.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = switchBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BSwitch.OUTPUT0.getName(), BSwitch.OUTPUT1.getName(), BSwitch.OUTPUT2.getName(), BSwitch.OUTPUT3.getName(), BSwitch.OUTPUT4.getName(), BSwitch.OUTPUT5.getName(), BSwitch.OUTPUT6.getName(),
				BSwitch.OUTPUT7.getName() };
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}

  private void checkIncomingLink(Property prop, boolean isLinkValid) {
	  BNumericConst nm = new BNumericConst();
		LinkCheck checkLink = switchBlock.checkLink(nm, nm.getSlot("out"), switchBlock.getSlot(BSwitch.OUTPUT0.getName()), null);
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }
  
  private void checkOutgoingLink(BSwitch tempBlock, Property prop, boolean isLinkValid) {
	  LinkCheck checkLink = switchBlock.checkLink(switchBlock, switchBlock.getSlot(prop.getName()), tempBlock.getSlot(prop.getName()), null);	   
	  Assert.assertEquals(checkLink.isValid(), isLinkValid);
  }

  BSwitch switchBlock;	
  BExecutionParams executionParams;
}
