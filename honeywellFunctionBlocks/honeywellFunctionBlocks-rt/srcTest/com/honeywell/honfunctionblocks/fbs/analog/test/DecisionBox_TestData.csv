#Iteration Intervel,Y/N input ,Hysteresis_input,Value 1 ,Value 2 ,Value 3,Value 4,Value 5,Operation,NumValues,Y Output,N Output
1000,1,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,1,0
1000,1,0,1,83,4,34,-8,1,2,0,1
1000,connected=1,99,4,4,4,5,6,1,2,1,0
1000,1,67868,33,21,22,23,24,1,2,0,1
1000,1,5654,76,39,40,41,42,1,2,0,1
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,1,2,1,0
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,1,2,0,1
1000,1,0.07,78.56,0,10.5,10,connected=nan,1,7,0,1
1000,1,unconnected=5,-6577,111,112,113,114,1,2,0,1
1000,1,99999,100,129,130,131,132,1,2,0,1
1000,1,connected=+inf,88,5,10,10,15.5,1,2,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,1,2,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,1,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,1,0
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,1,0
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,1,0
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,1,0
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,1
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,1,2,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,1,2,1,0
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,1,2,1,0
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,1,2,1,0
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,1
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,1,0
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,1
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,1,0
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,1,0
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,1,0
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,1,0
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,1
1000,1,10,unconnected=5,10,10,10,10,1,2,0,1
1000,1,10,connected=nan,10,10,10,10,1,2,0,1
1000,1,10,connected=-inf,10,10,10,10,1,2,0,1
1000,1,10,connected=+inf,10,10,10,10,1,2,0,1
1000,1,10,10,10,10,10,10,1,2,1,0
1000,1,10,-10,10,10,10,10,1,2,0,1
1000,1,10,0,10,10,10,10,1,2,0,1
1000,1,10,65535,10,10,10,10,1,2,0,1
1000,1,10,0.001,10,10,10,10,1,2,0,1
1000,1,10,-0.001,10,10,10,10,1,2,0,1
1000,1,-10,unconnected=5,-10,-10,-10,-10,1,2,0,1
1000,1,-10,connected=nan,-10,-10,-10,-10,1,2,0,1
1000,1,-10,connected=-inf,-10,-10,-10,-10,1,2,0,1
1000,1,-10,connected=+inf,-10,-10,-10,-10,1,2,0,1
1000,1,-10,10,-10,-10,-10,-10,1,2,0,1
1000,1,-10,-10,-10,-10,-10,-10,1,2,1,0
1000,1,-10,0,-10,-10,-10,-10,1,2,0,1
1000,1,-10,65535,-10,-10,-10,-10,1,2,0,1
1000,1,-10,0.001,-10,-10,-10,-10,1,2,0,1
1000,1,-10,-0.001,-10,-10,-10,-10,1,2,0,1
1000,1,0,unconnected=5,0,0,0,0,1,2,1,0
1000,1,0,connected=nan,0,0,0,0,1,2,1,0
1000,1,0,connected=-inf,0,0,0,0,1,2,0,1
1000,1,0,connected=+inf,0,0,0,0,1,2,1,0
1000,1,0,10,0,0,0,0,1,2,0,1
1000,1,0,-10,0,0,0,0,1,2,0,1
1000,1,0,0,0,0,0,0,1,2,1,0
1000,1,0,65535,0,0,0,0,1,2,0,1
1000,1,0,0.001,0,0,0,0,1,2,0,1
1000,1,0,-0.001,0,0,0,0,1,2,0,1
1000,1,65535,unconnected=5,65535,65535,65535,65535,1,2,0,1
1000,1,65535,connected=nan,65535,65535,65535,65535,1,2,0,1
1000,1,65535,connected=-inf,65535,65535,65535,65535,1,2,0,1
1000,1,65535,connected=+inf,65535,65535,65535,65535,1,2,0,1
1000,1,65535,10,65535,65535,65535,65535,1,2,0,1
1000,1,65535,-10,65535,65535,65535,65535,1,2,0,1
1000,1,65535,0,65535,65535,65535,65535,1,2,0,1
1000,1,65535,65535,65535,65535,65535,65535,1,2,1,0
1000,1,65535,0.001,65535,65535,65535,65535,1,2,0,1
1000,1,65535,-0.001,65535,65535,65535,65535,1,2,0,1
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,10,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,-10,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,0,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,65535,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,1,2,1,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,1,2,0,1
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,1,2,0,1
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,1,2,1,0
1000,1,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,0,1,83,4,34,-8,2,2,0,1
1000,1,99,4,4,4,5,6,2,2,0,0
1000,1,67868,33,21,22,23,24,2,2,1,0
1000,1,5654,76,39,40,41,42,2,2,1,0
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,2,2,0,1
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,2,2,1,0
1000,1,0.07,78.56,0,10.5,10,connected=nan,2,2,1,0
1000,1,unconnected=5,-6577,111,112,113,114,2,2,0,1
1000,1,99999,100,129,130,131,132,2,2,0,0
1000,1,connected=+inf,88,5,10,10,15.5,2,2,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,0,1
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,1,0
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,1,0
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,1,0
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,1
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,2,2,1,0
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,2,2,1,0
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,2,2,1,0
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,1
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,0,1
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,1,0
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,1,0
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,1,0
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,1,0
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,1
1000,1,10,unconnected=5,10,10,10,10,2,2,0,1
1000,1,10,connected=nan,10,10,10,10,2,2,0,1
1000,1,10,connected=-inf,10,10,10,10,2,2,0,1
1000,1,10,connected=+inf,10,10,10,10,2,2,0,1
1000,1,10,10,10,10,10,10,2,2,0,0
1000,1,10,-10,10,10,10,10,2,2,0,1
1000,1,10,0,10,10,10,10,2,2,0,1
1000,1,10,65535,10,10,10,10,2,2,1,0
1000,1,10,0.001,10,10,10,10,2,2,0,0
1000,1,10,-0.001,10,10,10,10,2,2,0,1
1000,1,-10,unconnected=5,-10,-10,-10,-10,2,2,1,0
1000,1,-10,connected=nan,-10,-10,-10,-10,2,2,1,0
1000,1,-10,connected=-inf,-10,-10,-10,-10,2,2,0,1
1000,1,-10,connected=+inf,-10,-10,-10,-10,2,2,1,0
1000,1,-10,10,-10,-10,-10,-10,2,2,1,0
1000,1,-10,-10,-10,-10,-10,-10,2,2,0,1
1000,1,-10,0,-10,-10,-10,-10,2,2,1,0
1000,1,-10,65535,-10,-10,-10,-10,2,2,1,0
1000,1,-10,0.001,-10,-10,-10,-10,2,2,1,0
1000,1,-10,-0.001,-10,-10,-10,-10,2,2,1,0
1000,1,0,unconnected=5,0,0,0,0,2,2,0,1
1000,1,0,connected=nan,0,0,0,0,2,2,0,1
1000,1,0,connected=-inf,0,0,0,0,2,2,0,1
1000,1,0,connected=+inf,0,0,0,0,2,2,0,1
1000,1,0,10,0,0,0,0,2,2,1,0
1000,1,0,-10,0,0,0,0,2,2,0,1
1000,1,0,0,0,0,0,0,2,2,0,1
1000,1,0,65535,0,0,0,0,2,2,1,0
1000,1,0,0.001,0,0,0,0,2,2,1,0
1000,1,0,-0.001,0,0,0,0,2,2,0,1
1000,1,65535,unconnected=5,65535,65535,65535,65535,2,2,0,1
1000,1,65535,connected=nan,65535,65535,65535,65535,2,2,0,1
1000,1,65535,connected=-inf,65535,65535,65535,65535,2,2,0,1
1000,1,65535,connected=+inf,65535,65535,65535,65535,2,2,0,1
1000,1,65535,10,65535,65535,65535,65535,2,2,0,0
1000,1,65535,-10,65535,65535,65535,65535,2,2,0,1
1000,1,65535,0,65535,65535,65535,65535,2,2,0,1
1000,1,65535,65535,65535,65535,65535,65535,2,2,0,0
1000,1,65535,0.001,65535,65535,65535,65535,2,2,0,0
1000,1,65535,-0.001,65535,65535,65535,65535,2,2,0,1
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,10,0.001,0.001,0.001,0.001,2,2,1,0
1000,1,0.001,-10,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,0,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,0.001,65535,0.001,0.001,0.001,0.001,2,2,1,0
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,2,2,0,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,2,2,0,1
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,2,2,0,1
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,2,2,0,1
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,2,2,1,0
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,2,2,0,1
1000,1,0,1,83,4,34,-8,3,2,0,1
1000,1,99,4,4,4,5,6,3,2,1,0
1000,1,67868,33,21,22,23,24,3,2,1,0
1000,1,5654,76,39,40,41,42,3,2,1,0
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,3,2,1,0
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,3,2,1,0
1000,1,0.07,78.56,0,10.5,10,connected=nan,3,2,1,0
1000,1,unconnected=5,-6577,111,112,113,114,3,2,0,1
1000,1,99999,100,129,130,131,132,3,2,0,0
1000,1,connected=+inf,88,5,10,10,15.5,3,2,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,0,1
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,0,1
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,1,0
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,0,1
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,3,2,0,1
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,3,2,0,1
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,3,2,1,0
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,3,2,0,1
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,1,0
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,0,1
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,0,1
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,1,0
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,0,1
1000,1,10,unconnected=5,10,10,10,10,3,2,0,0
1000,1,10,connected=nan,10,10,10,10,3,2,0,0
1000,1,10,connected=-inf,10,10,10,10,3,2,0,1
1000,1,10,connected=+inf,10,10,10,10,3,2,0,0
1000,1,10,10,10,10,10,10,3,2,1,0
1000,1,10,-10,10,10,10,10,3,2,0,1
1000,1,10,0,10,10,10,10,3,2,0,0
1000,1,10,65535,10,10,10,10,3,2,1,0
1000,1,10,0.001,10,10,10,10,3,2,0,0
1000,1,10,-0.001,10,10,10,10,3,2,0,1
1000,1,-10,unconnected=5,-10,-10,-10,-10,3,2,1,0
1000,1,-10,connected=nan,-10,-10,-10,-10,3,2,1,0
1000,1,-10,connected=-inf,-10,-10,-10,-10,3,2,0,1
1000,1,-10,connected=+inf,-10,-10,-10,-10,3,2,1,0
1000,1,-10,10,-10,-10,-10,-10,3,2,1,0
1000,1,-10,-10,-10,-10,-10,-10,3,2,1,0
1000,1,-10,0,-10,-10,-10,-10,3,2,1,0
1000,1,-10,65535,-10,-10,-10,-10,3,2,1,0
1000,1,-10,0.001,-10,-10,-10,-10,3,2,1,0
1000,1,-10,-0.001,-10,-10,-10,-10,3,2,1,0
1000,1,0,unconnected=5,0,0,0,0,3,2,1,0
1000,1,0,connected=nan,0,0,0,0,3,2,1,0
1000,1,0,connected=-inf,0,0,0,0,3,2,0,1
1000,1,0,connected=+inf,0,0,0,0,3,2,1,0
1000,1,0,10,0,0,0,0,3,2,1,0
1000,1,0,-10,0,0,0,0,3,2,0,1
1000,1,0,0,0,0,0,0,3,2,1,0
1000,1,0,65535,0,0,0,0,3,2,1,0
1000,1,0,0.001,0,0,0,0,3,2,1,0
1000,1,0,-0.001,0,0,0,0,3,2,0,1
1000,1,65535,unconnected=5,65535,65535,65535,65535,3,2,0,0
1000,1,65535,connected=nan,65535,65535,65535,65535,3,2,0,0
1000,1,65535,connected=-inf,65535,65535,65535,65535,3,2,0,1
1000,1,65535,connected=+inf,65535,65535,65535,65535,3,2,0,0
1000,1,65535,10,65535,65535,65535,65535,3,2,0,0
1000,1,65535,-10,65535,65535,65535,65535,3,2,0,1
1000,1,65535,0,65535,65535,65535,65535,3,2,0,0
1000,1,65535,65535,65535,65535,65535,65535,3,2,1,0
1000,1,65535,0.001,65535,65535,65535,65535,3,2,0,0
1000,1,65535,-0.001,65535,65535,65535,65535,3,2,0,1
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,3,2,0,0
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,3,2,0,0
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,3,2,0,1
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,3,2,0,0
1000,1,0.001,10,0.001,0.001,0.001,0.001,3,2,1,0
1000,1,0.001,-10,0.001,0.001,0.001,0.001,3,2,0,1
1000,1,0.001,0,0.001,0.001,0.001,0.001,3,2,0,0
1000,1,0.001,65535,0.001,0.001,0.001,0.001,3,2,1,0
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,3,2,1,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,3,2,0,1
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,3,2,0,1
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,3,2,0,1
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,3,2,1,0
1000,1,0,1,83,4,34,-8,4,2,1,0
1000,1,99,4,4,4,5,6,4,2,0,0
1000,1,67868,33,21,22,23,24,4,2,0,0
1000,1,5654,76,39,40,41,42,4,2,0,0
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,4,2,0,1
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,4,2,0,1
1000,1,0.07,78.56,0,10.5,10,connected=nan,4,2,0,1
1000,1,unconnected=5,-6577,111,112,113,114,4,2,1,0
1000,1,99999,100,129,130,131,132,4,2,1,0
1000,1,connected=+inf,88,5,10,10,15.5,4,2,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,1,0
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,1,0
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,1
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,1,0
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,4,2,1,0
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,4,2,1,0
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,1
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,4,2,1,0
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,1
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,1,0
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,1,0
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,1
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,1,0
1000,1,10,unconnected=5,10,10,10,10,4,2,1,0
1000,1,10,connected=nan,10,10,10,10,4,2,1,0
1000,1,10,connected=-inf,10,10,10,10,4,2,1,0
1000,1,10,connected=+inf,10,10,10,10,4,2,1,0
1000,1,10,10,10,10,10,10,4,2,0,0
1000,1,10,-10,10,10,10,10,4,2,1,0
1000,1,10,0,10,10,10,10,4,2,1,0
1000,1,10,65535,10,10,10,10,4,2,0,1
1000,1,10,0.001,10,10,10,10,4,2,1,0
1000,1,10,-0.001,10,10,10,10,4,2,1,0
1000,1,-10,unconnected=5,-10,-10,-10,-10,4,2,0,1
1000,1,-10,connected=nan,-10,-10,-10,-10,4,2,0,1
1000,1,-10,connected=-inf,-10,-10,-10,-10,4,2,1,0
1000,1,-10,connected=+inf,-10,-10,-10,-10,4,2,0,1
1000,1,-10,10,-10,-10,-10,-10,4,2,0,1
1000,1,-10,-10,-10,-10,-10,-10,4,2,0,1
1000,1,-10,0,-10,-10,-10,-10,4,2,0,1
1000,1,-10,65535,-10,-10,-10,-10,4,2,0,1
1000,1,-10,0.001,-10,-10,-10,-10,4,2,0,1
1000,1,-10,-0.001,-10,-10,-10,-10,4,2,0,1
1000,1,0,unconnected=5,0,0,0,0,4,2,0,1
1000,1,0,connected=nan,0,0,0,0,4,2,0,1
1000,1,0,connected=-inf,0,0,0,0,4,2,1,0
1000,1,0,connected=+inf,0,0,0,0,4,2,0,1
1000,1,0,10,0,0,0,0,4,2,0,1
1000,1,0,-10,0,0,0,0,4,2,1,0
1000,1,0,0,0,0,0,0,4,2,0,1
1000,1,0,65535,0,0,0,0,4,2,0,1
1000,1,0,0.001,0,0,0,0,4,2,0,1
1000,1,0,-0.001,0,0,0,0,4,2,1,0
1000,1,65535,unconnected=5,65535,65535,65535,65535,4,2,1,0
1000,1,65535,connected=nan,65535,65535,65535,65535,4,2,1,0
1000,1,65535,connected=-inf,65535,65535,65535,65535,4,2,1,0
1000,1,65535,connected=+inf,65535,65535,65535,65535,4,2,1,0
1000,1,65535,10,65535,65535,65535,65535,4,2,1,0
1000,1,65535,-10,65535,65535,65535,65535,4,2,1,0
1000,1,65535,0,65535,65535,65535,65535,4,2,1,0
1000,1,65535,65535,65535,65535,65535,65535,4,2,0,0
1000,1,65535,0.001,65535,65535,65535,65535,4,2,1,0
1000,1,65535,-0.001,65535,65535,65535,65535,4,2,1,0
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,10,0.001,0.001,0.001,0.001,4,2,0,1
1000,1,0.001,-10,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,0,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,0.001,65535,0.001,0.001,0.001,0.001,4,2,0,1
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,4,2,0,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,4,2,1,0
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,4,2,1,0
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,4,2,1,0
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,4,2,0,1
1000,1,0,1,83,4,34,-8,5,2,1,0
1000,1,99,4,4,4,5,6,5,2,1,0
1000,1,67868,33,21,22,23,24,5,2,0,0
1000,1,5654,76,39,40,41,42,5,2,0,0
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,5,2,1,0
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,5,2,0,1
1000,1,0.07,78.56,0,10.5,10,connected=nan,5,2,0,1
1000,1,unconnected=5,-6577,111,112,113,114,5,2,1,0
1000,1,99999,100,129,130,131,132,5,2,1,0
1000,1,connected=+inf,88,5,10,10,15.5,5,2,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,1,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,0,1
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,0,1
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,0,1
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,1,0
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,5,2,0,1
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,5,2,0,1
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,5,2,0,1
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,5,2,1,0
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,1,0
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,1
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,0,1
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,0,1
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,0,1
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,1,0
1000,1,10,unconnected=5,10,10,10,10,5,2,1,0
1000,1,10,connected=nan,10,10,10,10,5,2,1,0
1000,1,10,connected=-inf,10,10,10,10,5,2,1,0
1000,1,10,connected=+inf,10,10,10,10,5,2,1,0
1000,1,10,10,10,10,10,10,5,2,1,0
1000,1,10,-10,10,10,10,10,5,2,1,0
1000,1,10,0,10,10,10,10,5,2,1,0
1000,1,10,65535,10,10,10,10,5,2,0,1
1000,1,10,0.001,10,10,10,10,5,2,1,0
1000,1,10,-0.001,10,10,10,10,5,2,1,0
1000,1,-10,unconnected=5,-10,-10,-10,-10,5,2,0,1
1000,1,-10,connected=nan,-10,-10,-10,-10,5,2,0,1
1000,1,-10,connected=-inf,-10,-10,-10,-10,5,2,1,0
1000,1,-10,connected=+inf,-10,-10,-10,-10,5,2,0,1
1000,1,-10,10,-10,-10,-10,-10,5,2,0,1
1000,1,-10,-10,-10,-10,-10,-10,5,2,1,0
1000,1,-10,0,-10,-10,-10,-10,5,2,0,1
1000,1,-10,65535,-10,-10,-10,-10,5,2,0,1
1000,1,-10,0.001,-10,-10,-10,-10,5,2,0,1
1000,1,-10,-0.001,-10,-10,-10,-10,5,2,0,1
1000,1,0,unconnected=5,0,0,0,0,5,2,1,0
1000,1,0,connected=nan,0,0,0,0,5,2,1,0
1000,1,0,connected=-inf,0,0,0,0,5,2,1,0
1000,1,0,connected=+inf,0,0,0,0,5,2,1,0
1000,1,0,10,0,0,0,0,5,2,0,1
1000,1,0,-10,0,0,0,0,5,2,1,0
1000,1,0,0,0,0,0,0,5,2,1,0
1000,1,0,65535,0,0,0,0,5,2,0,1
1000,1,0,0.001,0,0,0,0,5,2,0,1
1000,1,0,-0.001,0,0,0,0,5,2,1,0
1000,1,65535,unconnected=5,65535,65535,65535,65535,5,2,1,0
1000,1,65535,connected=nan,65535,65535,65535,65535,5,2,1,0
1000,1,65535,connected=-inf,65535,65535,65535,65535,5,2,1,0
1000,1,65535,connected=+inf,65535,65535,65535,65535,5,2,1,0
1000,1,65535,10,65535,65535,65535,65535,5,2,1,0
1000,1,65535,-10,65535,65535,65535,65535,5,2,1,0
1000,1,65535,0,65535,65535,65535,65535,5,2,1,0
1000,1,65535,65535,65535,65535,65535,65535,5,2,1,0
1000,1,65535,0.001,65535,65535,65535,65535,5,2,1,0
1000,1,65535,-0.001,65535,65535,65535,65535,5,2,1,0
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,10,0.001,0.001,0.001,0.001,5,2,0,1
1000,1,0.001,-10,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,0,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,65535,0.001,0.001,0.001,0.001,5,2,0,1
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,5,2,1,0
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,5,2,1,0
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,5,2,1,0
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,5,2,0,1
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,5,2,1,0
1000,1,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,1,0
1000,1,0,1,83,4,34,-8,6,5,0,1
1000,1,99,4,4,4,5,6,6,5,1,0
1000,1,67868,33,21,22,23,24,6,5,0,1
1000,1,5654,76,39,40,41,42,6,5,0,1
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,6,5,1,0
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,6,5,0,1
1000,1,0.07,78.56,0,10.5,10,connected=nan,6,5,0,1
1000,1,unconnected=5,-6577,111,112,113,114,6,5,0,1
1000,1,99999,100,129,130,131,132,6,5,0,1
1000,1,connected=+inf,88,5,10,10,15.5,6,5,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,6,5,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,1,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,1,0
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,1,0
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,1,0
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,1,0
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,1
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,6,5,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,6,5,1,0
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,6,5,1,0
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,6,5,1,0
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,1
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,1,0
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,1
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,1,0
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,1,0
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,1,0
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,1,0
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,1
1000,1,10,unconnected=5,10,10,10,10,6,5,0,1
1000,1,10,connected=nan,10,10,10,10,6,5,0,1
1000,1,10,connected=-inf,10,10,10,10,6,5,0,1
1000,1,10,connected=+inf,10,10,10,10,6,5,0,1
1000,1,10,10,10,10,10,10,6,5,1,0
1000,1,10,-10,10,10,10,10,6,5,0,1
1000,1,10,0,10,10,10,10,6,5,0,1
1000,1,10,65535,10,10,10,10,6,5,0,1
1000,1,10,0.001,10,10,10,10,6,5,0,1
1000,1,10,-0.001,10,10,10,10,6,5,0,1
1000,1,-10,unconnected=5,-10,-10,-10,-10,6,5,0,1
1000,1,-10,connected=nan,-10,-10,-10,-10,6,5,0,1
1000,1,-10,connected=-inf,-10,-10,-10,-10,6,5,0,1
1000,1,-10,connected=+inf,-10,-10,-10,-10,6,5,0,1
1000,1,-10,10,-10,-10,-10,-10,6,5,0,1
1000,1,-10,-10,-10,-10,-10,-10,6,5,1,0
1000,1,-10,0,-10,-10,-10,-10,6,5,0,1
1000,1,-10,65535,-10,-10,-10,-10,6,5,0,1
1000,1,-10,0.001,-10,-10,-10,-10,6,5,0,1
1000,1,-10,-0.001,-10,-10,-10,-10,6,5,0,1
1000,1,0,unconnected=5,0,0,0,0,6,5,1,0
1000,1,0,connected=nan,0,0,0,0,6,5,1,0
1000,1,0,connected=-inf,0,0,0,0,6,5,0,1
1000,1,0,connected=+inf,0,0,0,0,6,5,1,0
1000,1,0,10,0,0,0,0,6,5,0,1
1000,1,0,-10,0,0,0,0,6,5,0,1
1000,1,0,0,0,0,0,0,6,5,1,0
1000,1,0,65535,0,0,0,0,6,5,0,1
1000,1,0,0.001,0,0,0,0,6,5,0,1
1000,1,0,-0.001,0,0,0,0,6,5,0,1
1000,1,65535,unconnected=5,65535,65535,65535,65535,6,5,0,1
1000,1,65535,connected=nan,65535,65535,65535,65535,6,5,0,1
1000,1,65535,connected=-inf,65535,65535,65535,65535,6,5,0,1
1000,1,65535,connected=+inf,65535,65535,65535,65535,6,5,0,1
1000,1,65535,10,65535,65535,65535,65535,6,5,0,1
1000,1,65535,-10,65535,65535,65535,65535,6,5,0,1
1000,1,65535,0,65535,65535,65535,65535,6,5,0,1
1000,1,65535,65535,65535,65535,65535,65535,6,5,1,0
1000,1,65535,0.001,65535,65535,65535,65535,6,5,0,1
1000,1,65535,-0.001,65535,65535,65535,65535,6,5,0,1
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,10,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,-10,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,0,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,65535,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,6,5,1,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,6,5,0,1
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,6,5,0,1
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,6,5,1,0
1000,1,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,1,0
1000,1,0,1,83,4,34,-8,7,5,0,1
1000,1,99,4,4,4,5,6,7,5,1,0
1000,1,67868,33,21,22,23,24,7,5,0,1
1000,1,5654,76,39,40,41,42,7,5,0,1
1000,1,-23,unconnected=5,unconnected=5,-10,10,connected=+inf,7,5,0,1
1000,1,-0.07,21,connected=nan,-10.5,10,unconnected=5,7,5,0,1
1000,1,0.07,78.56,0,10.5,10,connected=nan,7,5,0,1
1000,1,unconnected=5,-6577,111,112,113,114,7,5,0,1
1000,1,99999,100,129,130,131,132,7,5,0,1
1000,1,connected=+inf,88,5,10,10,15.5,7,5,0,1
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,7,5,1,0
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,1,0
1000,1,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,1,0
1000,1,unconnected=5,connected=nan,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,1,0
1000,1,unconnected=5,connected=-inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,unconnected=5,connected=+inf,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,1,0
1000,1,unconnected=5,10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,unconnected=5,-10,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,unconnected=5,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,1,0
1000,1,unconnected=5,65535,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,unconnected=5,0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,unconnected=5,-0.001,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,1
1000,1,connected=nan,unconnected=5,connected=nan,connected=nan,connected=nan,connected=nan,7,5,1,0
1000,1,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,7,5,1,0
1000,1,connected=nan,connected=-inf,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=nan,connected=+inf,connected=nan,connected=nan,connected=nan,connected=nan,7,5,1,0
1000,1,connected=nan,10,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=nan,-10,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=nan,0,connected=nan,connected=nan,connected=nan,connected=nan,7,5,1,0
1000,1,connected=nan,65535,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=nan,0.001,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=nan,-0.001,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,1
1000,1,connected=-inf,unconnected=5,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,connected=nan,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,1,0
1000,1,connected=-inf,connected=+inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,-10,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,65535,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=-inf,-0.001,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,1
1000,1,connected=+inf,unconnected=5,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,1,0
1000,1,connected=+inf,connected=nan,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,1,0
1000,1,connected=+inf,connected=-inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,1,0
1000,1,connected=+inf,10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,connected=+inf,-10,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,connected=+inf,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,1,0
1000,1,connected=+inf,65535,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,connected=+inf,0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,connected=+inf,-0.001,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,1
1000,1,10,unconnected=5,10,10,10,10,7,5,0,1
1000,1,10,connected=nan,10,10,10,10,7,5,0,1
1000,1,10,connected=-inf,10,10,10,10,7,5,0,1
1000,1,10,connected=+inf,10,10,10,10,7,5,0,1
1000,1,10,10,10,10,10,10,7,5,1,0
1000,1,10,-10,10,10,10,10,7,5,0,1
1000,1,10,0,10,10,10,10,7,5,0,1
1000,1,10,65535,10,10,10,10,7,5,0,1
1000,1,10,0.001,10,10,10,10,7,5,0,1
1000,1,10,-0.001,10,10,10,10,7,5,0,1
1000,1,-10,unconnected=5,-10,-10,-10,-10,7,5,0,1
1000,1,-10,connected=nan,-10,-10,-10,-10,7,5,0,1
1000,1,-10,connected=-inf,-10,-10,-10,-10,7,5,0,1
1000,1,-10,connected=+inf,-10,-10,-10,-10,7,5,0,1
1000,1,-10,10,-10,-10,-10,-10,7,5,0,1
1000,1,-10,-10,-10,-10,-10,-10,7,5,1,0
1000,1,-10,0,-10,-10,-10,-10,7,5,0,1
1000,1,-10,65535,-10,-10,-10,-10,7,5,0,1
1000,1,-10,0.001,-10,-10,-10,-10,7,5,0,1
1000,1,-10,-0.001,-10,-10,-10,-10,7,5,0,1
1000,1,0,unconnected=5,0,0,0,0,7,5,1,0
1000,1,0,connected=nan,0,0,0,0,7,5,1,0
1000,1,0,connected=-inf,0,0,0,0,7,5,0,1
1000,1,0,connected=+inf,0,0,0,0,7,5,1,0
1000,1,0,10,0,0,0,0,7,5,0,1
1000,1,0,-10,0,0,0,0,7,5,0,1
1000,1,0,0,0,0,0,0,7,5,1,0
1000,1,0,65535,0,0,0,0,7,5,0,1
1000,1,0,0.001,0,0,0,0,7,5,0,1
1000,1,0,-0.001,0,0,0,0,7,5,0,1
1000,1,65535,unconnected=5,65535,65535,65535,65535,7,5,0,1
1000,1,65535,connected=nan,65535,65535,65535,65535,7,5,0,1
1000,1,65535,connected=-inf,65535,65535,65535,65535,7,5,0,1
1000,1,65535,connected=+inf,65535,65535,65535,65535,7,5,0,1
1000,1,65535,10,65535,65535,65535,65535,7,5,0,1
1000,1,65535,-10,65535,65535,65535,65535,7,5,0,1
1000,1,65535,0,65535,65535,65535,65535,7,5,0,1
1000,1,65535,65535,65535,65535,65535,65535,7,5,1,0
1000,1,65535,0.001,65535,65535,65535,65535,7,5,0,1
1000,1,65535,-0.001,65535,65535,65535,65535,7,5,0,1
1000,1,0.001,unconnected=5,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,connected=nan,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,connected=-inf,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,connected=+inf,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,10,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,-10,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,0,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,65535,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,0.001,0.001,0.001,0.001,0.001,0.001,7,5,1,0
1000,1,0.001,-0.001,0.001,0.001,0.001,0.001,7,5,0,1
1000,1,-0.001,unconnected=5,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,connected=nan,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,connected=-inf,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,connected=+inf,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,10,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,-10,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,0,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,65535,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,0.001,-0.001,-0.001,-0.001,-0.001,7,5,0,1
1000,1,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,7,5,1,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,1,2,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,1,2,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,1,2,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,1,2,0,0
1000,0,10,10,10,10,10,10,1,2,0,0
1000,0,-10,-10,-10,-10,-10,-10,1,2,0,0
1000,0,0,0,0,0,0,0,1,2,0,0
1000,0,65535,65535,65535,65535,65535,65535,1,2,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,1,2,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,1,2,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,2,2,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,2,2,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,2,2,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,2,2,0,0
1000,0,10,10,10,10,10,10,2,2,0,0
1000,0,-10,-10,-10,-10,-10,-10,2,2,0,0
1000,0,0,0,0,0,0,0,2,2,0,0
1000,0,65535,65535,65535,65535,65535,65535,2,2,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,2,2,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,2,2,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,3,2,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,3,2,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,3,2,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,3,2,0,0
1000,0,10,10,10,10,10,10,3,2,0,0
1000,0,-10,-10,-10,-10,-10,-10,3,2,0,0
1000,0,0,0,0,0,0,0,3,2,0,0
1000,0,65535,65535,65535,65535,65535,65535,3,2,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,3,2,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,3,2,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,4,2,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,4,2,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,4,2,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,4,2,0,0
1000,0,10,10,10,10,10,10,4,2,0,0
1000,0,-10,-10,-10,-10,-10,-10,4,2,0,0
1000,0,0,0,0,0,0,0,4,2,0,0
1000,0,65535,65535,65535,65535,65535,65535,4,2,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,4,2,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,4,2,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,5,2,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,5,2,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,5,2,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,5,2,0,0
1000,0,10,10,10,10,10,10,5,2,0,0
1000,0,-10,-10,-10,-10,-10,-10,5,2,0,0
1000,0,0,0,0,0,0,0,5,2,0,0
1000,0,65535,65535,65535,65535,65535,65535,5,2,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,5,2,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,5,2,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,6,5,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,6,5,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,6,5,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,6,5,0,0
1000,0,10,10,10,10,10,10,6,5,0,0
1000,0,-10,-10,-10,-10,-10,-10,6,5,0,0
1000,0,0,0,0,0,0,0,6,5,0,0
1000,0,65535,65535,65535,65535,65535,65535,6,5,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,6,5,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,6,5,0,0
1000,0,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,7,5,0,0
1000,0,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,connected=nan,7,5,0,0
1000,0,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,connected=-inf,7,5,0,0
1000,0,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,connected=+inf,7,5,0,0
1000,0,10,10,10,10,10,10,7,5,0,0
1000,0,-10,-10,-10,-10,-10,-10,7,5,0,0
1000,0,0,0,0,0,0,0,7,5,0,0
1000,0,65535,65535,65535,65535,65535,65535,7,5,0,0
1000,0,0.001,0.001,0.001,0.001,0.001,0.001,7,5,0,0
1000,0,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,7,5,0,0
1000,unconnected=5,-0.001,-0.001,-0.001,-0.001,-0.001,-0.001,7,5,0,0
1000,unconnected=5,0,1,83,4,34,-8,1,2,0,0