/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.analog.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BEncode;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Encode block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-188
 * <AUTHOR>
 *
 */
@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BEncodeTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.analog.test.BEncodeTest(2979906276)1.0$ @*/
/* Generated Thu Dec 07 12:44:24 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEncodeTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  private BEncode encodeBlock = null;
  private BExecutionParams executionParams = null;
  
  @BeforeClass(alwaysRun=true)
  public void setUp() {	
	  encodeBlock = new BEncode();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  encodeBlock = null;
  }
  

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"input"}, {"disable"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"OUTPUT"}, {"FIRE"}};
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[][] createConfigSlotNames() {
	  return new Object[][] {{"in1"}, {"in2"}, {"in3"}, {"in4"}, {"in5"}, {"in6"}, {"in7"}, {"in8"}, {"in9"},
			{"out1"}, {"out2"}, {"out3"}, {"out4"}, {"out5"}, {"out6"}, {"out7"}, {"out8"}, {"out9"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"X1"}, {"x1"}, {"In1"}, {"out"}, {"IgnoreInvalidInput"}, {"Fire"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(encodeBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(encodeBlock.getSlot(slotName));
  }

  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "select.png");
	  BIcon actualFbIcon = encodeBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  encodeBlock.setIcon(expectedFbIcon);
	  actualFbIcon = encodeBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingInputValues() {
	verifySettingValue(0);
	verifySettingValue(255.0);
	verifySettingValue(34.0);
	
	
	encodeBlock.setDisable(new BNegatableFiniteStatusBoolean(true, BStatus.ok, false));
	Assert.assertEquals(encodeBlock.getDisable().getValue(), true);
	encodeBlock.setDisable(new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));	
	Assert.assertEquals(encodeBlock.getDisable().getValue(), false);
	encodeBlock.setFIRE(new BNegatableStatusBoolean(true, BStatus.ok, false));
	Assert.assertEquals(encodeBlock.getFIRE().getValue(), true);
	encodeBlock.setFIRE(new BNegatableStatusBoolean(false, BStatus.ok, false));
	Assert.assertEquals(encodeBlock.getFIRE().getValue(), false);
  }
  
  private void verifySettingValue(double snValue) {
	  encodeBlock.setInput(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getInput().getValue(), snValue, 0.1);
	  encodeBlock.setIn1(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn1().getValue(), snValue, 0.1);
	  encodeBlock.setIn2(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn2().getValue(), snValue, 0.1);
	  encodeBlock.setIn3(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn3().getValue(), snValue, 0.1);
	  encodeBlock.setIn4(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn4().getValue(), snValue, 0.1);
	  encodeBlock.setIn5(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn5().getValue(), snValue, 0.1);
	  encodeBlock.setIn6(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn6().getValue(), snValue, 0.1);
	  encodeBlock.setIn7(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn7().getValue(), snValue, 0.1);
	  encodeBlock.setIn8(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn8().getValue(), snValue, 0.1);
	  encodeBlock.setIn9(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getIn9().getValue(), snValue, 0.1);
	  
	  encodeBlock.setOut1(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut1().getValue(), snValue, 0.1);
	  encodeBlock.setOut2(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut2().getValue(), snValue, 0.1);
	  encodeBlock.setOut3(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut3().getValue(), snValue, 0.1);
	  encodeBlock.setOut4(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut4().getValue(), snValue, 0.1);
	  encodeBlock.setOut5(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut5().getValue(), snValue, 0.1);
	  encodeBlock.setOut6(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut6().getValue(), snValue, 0.1);
	  encodeBlock.setOut7(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut7().getValue(), snValue, 0.1);
	  encodeBlock.setOut8(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut8().getValue(), snValue, 0.1);
	  encodeBlock.setOut9(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOut9().getValue(), snValue, 0.1);
	  
	  encodeBlock.setOUTPUT(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(encodeBlock.getOUTPUT().getValue(), snValue, 0.1);
}
  
  @Test
  public void testSettingNullStatus() {
	  BEncode encBlock = new BEncode();
	  encBlock.setInput(new BHonStatusNumeric(0));	  
	  encBlock.setDisable(new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));
	  encBlock.setIn1(new BHonStatusNumeric(35));
	  encBlock.getIn1().setStatus(BStatus.nullStatus);
	  encBlock.setIn2(new BHonStatusNumeric(1));
	  encBlock.setIn3(new BHonStatusNumeric(2));
	  encBlock.setIn4(new BHonStatusNumeric(3));
	  encBlock.setIn5(new BHonStatusNumeric(4));
	  encBlock.setIn6(new BHonStatusNumeric(5));
	  encBlock.setIn7(new BHonStatusNumeric(6));
	  encBlock.setIn8(new BHonStatusNumeric(7));
	  encBlock.setIn9(new BHonStatusNumeric(8));
	  encBlock.setOut1(new BHonStatusNumeric(9));
	  encBlock.setOut2(new BHonStatusNumeric(10));
	  encBlock.setOut3(new BHonStatusNumeric(11));
	  encBlock.setOut4(new BHonStatusNumeric(12));
	  encBlock.setOut5(new BHonStatusNumeric(13));
	  encBlock.setOut6(new BHonStatusNumeric(14));
	  encBlock.setOut7(new BHonStatusNumeric(15));
	  encBlock.setOut8(new BHonStatusNumeric(16));
	  encBlock.setOut9(new BHonStatusNumeric(17));
	  
	  encBlock.executeBlock(executionParams);
	  Assert.assertEquals(encBlock.getOUTPUT().getValue(), 9, 0.1);
	  Assert.assertEquals(encBlock.getFIRE().getValue(), true);
	  //Negate TEST
	  ((BNegatableStatusBoolean)encBlock.getFIRE()).setNegate(true);
	  encBlock.executeBlock(executionParams);
	  Assert.assertEquals(encBlock.getFIRE().getValue(), false);
  }
  
  
  
  @DataProvider(name="provideTestData")
 	public Object[][] getTesData() {
 		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/Neg_Encode_TestData.csv").get();
 		CsvReader readValidInputs;
 		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
 		try {
 			readValidInputs = new CsvReader(file.getInputStream());
 			List<String> rec;
 			while ((rec = readValidInputs.read()) != null) {
 				validInputs.add(rec);
 			}
 			readValidInputs.close();
 		} catch (IOException e) {
 			validInputs = null;
 		}

 		Object[][] objArray = new Object[validInputs.size()][];
 		for (int i = 0; i < validInputs.size(); i++) {
 			objArray[i] = new Object[1];
 			objArray[i][0] = validInputs.get(i);
 		}

 		return objArray;
 	}
  
   
   @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
   public void testencBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	   BEncode encBlock = new BEncode(); 	 
	   executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	   setupNumericSlot(encBlock, BEncode.input.getName(), inputs.get(1));
	   setupNumericSlot(encBlock, BEncode.disable.getName(), inputs.get(2));
	   setupNumericSlot(encBlock, BEncode.in1.getName(), inputs.get(3));
	   setupNumericSlot(encBlock, BEncode.in2.getName(), inputs.get(4));
	   setupNumericSlot(encBlock, BEncode.in3.getName(), inputs.get(5));
	   setupNumericSlot(encBlock, BEncode.in4.getName(), inputs.get(6));
	   setupNumericSlot(encBlock, BEncode.in5.getName(), inputs.get(7));
	   setupNumericSlot(encBlock, BEncode.in6.getName(), inputs.get(8));
	   setupNumericSlot(encBlock, BEncode.in7.getName(), inputs.get(9));
	   setupNumericSlot(encBlock, BEncode.in8.getName(), inputs.get(10));
	   setupNumericSlot(encBlock, BEncode.in9.getName(), inputs.get(11));
	   
	   setupNumericSlot(encBlock, BEncode.out1.getName(), inputs.get(12));
	   setupNumericSlot(encBlock, BEncode.out2.getName(), inputs.get(13));
	   setupNumericSlot(encBlock, BEncode.out3.getName(), inputs.get(14));
	   setupNumericSlot(encBlock, BEncode.out4.getName(), inputs.get(15));
	   setupNumericSlot(encBlock, BEncode.out5.getName(), inputs.get(16));
	   setupNumericSlot(encBlock, BEncode.out6.getName(), inputs.get(17));
	   setupNumericSlot(encBlock, BEncode.out7.getName(), inputs.get(18));
	   setupNumericSlot(encBlock, BEncode.out8.getName(), inputs.get(19));
	   setupNumericSlot(encBlock, BEncode.out9.getName(), inputs.get(20));
	   
	   boolean negateDisable = (inputs.get(23).trim()).equals("0") ? false : true;
	   ((BNegatableFiniteStatusBoolean)encBlock.getDisable()).setNegate(negateDisable);
	   boolean negateFIRE = (inputs.get(24).trim()).equals("0") ? false : true;
	   ((BNegatableStatusBoolean)encBlock.getFIRE()).setNegate(negateFIRE);
	   encBlock.executeHoneywellComponent(executionParams);
	   
	   Assert.assertEquals(encBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(21), 0d), 0.1);
	   Assert.assertEquals(encBlock.getFIRE().getValue(), TestDataHelper.getBoolean(inputs.get(22)));
	   encBlock = null;
   }
   
   
   public void setupNumericSlot(BEncode encBlock, final String slotName, final String inputValue){

		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = encBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),encBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				encBlock.add("Link?",conversionLink );				
				conversionLink.activate();
			}else{
				encBlock.linkTo(nm1, nm1.getSlot("out"), encBlock.getSlot(slotName));
			}			
			
			return;
		}

		
		switch (slotName) {
		case "input":
			encBlock.setInput(TestDataHelper.getHonStatusNumeric(inputValue, 255d));
			break;
			
		case "disable":
			encBlock.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			break;
			
		case "in1":
			encBlock.setIn1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in2":
			encBlock.setIn2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in3":
			encBlock.setIn3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in4":
			encBlock.setIn4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in5":
			encBlock.setIn5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in6":
			encBlock.setIn6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in7":
			encBlock.setIn7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in8":
			encBlock.setIn8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "in9":
			encBlock.setIn9(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out1":
			encBlock.setOut1(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out2":
			encBlock.setOut2(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out3":
			encBlock.setOut3(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out4":
			encBlock.setOut4(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out5":
			encBlock.setOut5(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out6":
			encBlock.setOut6(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out7":
			encBlock.setOut7(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out8":
			encBlock.setOut8(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "out9":
			encBlock.setOut9(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		default:
			break;
		}
	}
   
   
   //@Test(groups={"testLinkRules"})
   public void testLinkRules() {
	   BComponent source = new BComponent();
	   source.add("honStatusNumeric", new BHonStatusNumeric(23));
	   source.add("honStatusBoolean", new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));
	   checkInvalidLink(source, "in1");   
	   checkInvalidLink(source, "in2");
	   checkInvalidLink(source, "in3");
	   checkInvalidLink(source, "in4");
	   checkInvalidLink(source, "in5");
	   checkInvalidLink(source, "in6");
	   checkInvalidLink(source, "in7");
	   checkInvalidLink(source, "in8");
	   checkInvalidLink(source, "in9");
	   checkInvalidLink(source, "out1");
	   checkInvalidLink(source, "out2");
	   checkInvalidLink(source, "out3");
	   checkInvalidLink(source, "out4");
	   checkInvalidLink(source, "out5");
	   checkInvalidLink(source, "out6");
	   checkInvalidLink(source, "out7");
	   checkInvalidLink(source, "out8");
	   checkInvalidLink(source, "out9");
	   
	   checkValidLink(source, "input");
	   
	   LinkCheck checkLink = encodeBlock.checkLink(source, source.getSlot("honStatusBoolean"), encodeBlock.getSlot("disable"), null);
	   Assert.assertTrue(checkLink.isValid());
   }
   
   @Test
	public void testConfigProperties() {
		List<Property> configList = encodeBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BEncode.in1.getName(), BEncode.in2.getName(), BEncode.in3.getName(), BEncode.in4.getName(), BEncode.in5.getName(), BEncode.in6.getName(), BEncode.in7.getName(), BEncode.in8.getName(), BEncode.in9.getName(),
				BEncode.out1.getName(), BEncode.out2.getName(), BEncode.out3.getName(), BEncode.out4.getName(), BEncode.out5.getName(), BEncode.out6.getName(), BEncode.out7.getName(), BEncode.out8.getName(), BEncode.out9.getName(), };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
   
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = encodeBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BEncode.input.getName(), BEncode.disable.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = encodeBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BEncode.FIRE.getName(), BEncode.OUTPUT.getName() };
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/analog/test/EncodeUpgradeFile.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BEncode encode = (BEncode)folder.get("Encode");
			Assert.assertEquals(encode.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(encode.getFIRE().getType(), BNegatableStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}

   private void checkInvalidLink(BComponent source, String slotName) {
	   LinkCheck checkLink = encodeBlock.checkLink(source, source.getSlot("honStatusNumeric"), encodeBlock.getSlot(slotName), null);
	   Assert.assertFalse(checkLink.isValid());
   }

   private void checkValidLink(BComponent source, String slotName) {
	   LinkCheck checkLink = encodeBlock.checkLink(source, source.getSlot("honStatusNumeric"), encodeBlock.getSlot(slotName), null);
	   Assert.assertTrue(checkLink.isValid());
   }
}

