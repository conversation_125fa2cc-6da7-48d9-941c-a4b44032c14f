/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 * Testing OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON>vanya B.
 * @since Feb 12, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BScheduledStateEnumTest extends BTestNg {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BScheduledStateEnumTest(**********)1.0$ @*/
/* Generated Mon Feb 12 10:55:30 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BScheduledStateEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
	  return new Object[][] {{Integer.valueOf(BScheduledStateEnum.OCCUPIED),BScheduledStateEnum.Occupied},
		  {Integer.valueOf(BScheduledStateEnum.UNOCCUPIED),BScheduledStateEnum.Unoccupied},
		  {Integer.valueOf(BScheduledStateEnum.STANDBY),BScheduledStateEnum.Standby},
		  {Integer.valueOf(BScheduledStateEnum.NULL),BScheduledStateEnum.Null}};
	}

	@Test(dataProvider = "enumOrdinal")
	public void testOperationByMakeOrdinal(Integer ordinal, BScheduledStateEnum oe) {
		Assert.assertEquals(BScheduledStateEnum.make(ordinal.intValue()), oe);
	}
	
	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		 return new Object[][] {{BScheduledStateEnum.Occupied.getTag(),BScheduledStateEnum.Occupied},
			  {BScheduledStateEnum.Unoccupied.getTag(),BScheduledStateEnum.Unoccupied},
			  {BScheduledStateEnum.Standby.getTag(),BScheduledStateEnum.Standby},
			  {BScheduledStateEnum.Null.getTag(),BScheduledStateEnum.Null}};
	}
	
	@Test(dataProvider = "enumTag")
	public void testOperationByMakeTag(String tag, BScheduledStateEnum oe) {
		Assert.assertEquals(BScheduledStateEnum.make(tag), oe);
	}
}
