/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;

import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancyArbitrator;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;

/**
 * <AUTHOR> - RSH.<PERSON>
 * @since Feb 2, 2018
 */
public class OccupancyArbitratorOutputGenerator {
	private final int OCC_OCC=0;
	private final int OCC_UNOCC=1;
	private final int OCC_BYPASS=2;
	private final int OCC_STANDBY=3;
	private final int OCC_NUL=255;
	
	private final int OA_NET_WINS=0;
	
	private final int OA_CONF_ROOM = 0;
	private final int OA_CLEAN_CREW = 1;
	private final int OA_TENANT = 2;

	int schedCurrentState, wmOverride, networkManOcc, occSensorState;
	int netLastInWins, occSensorOper;
	int effOccCurrentState, manOverrideState=OCC_NUL;
	
	//previous outputs
	int prevNetworkManOcc = OCC_NUL;
	int prevWMOverride = OCC_NUL;
	
	int prevLoopNetworkManOcc = OCC_NUL;
	int prevLoopWMOverride = OCC_NUL;
	int prevLoopManOverrideState = 0;
	
	BOccupancyArbitrator occArb = null;
	
	/**
	 * 
	 */
	public OccupancyArbitratorOutputGenerator() {
		// TODO Auto-generated constructor stub
	}
	
	/**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param occArbTempBlock
	   * @param slotName
	   * @param inputValue
	   */
	  private void setSlotValue(int slotName, String slotValue) {
		  double value;
		  if (TestDataHelper.isConnected(slotValue)) {
			  value = TestDataHelper.getDouble(slotValue, 255d);
			  if(Double.isInfinite(value) && value>0)
					value= 255;
			  else if(Double.isNaN(value))
					value=255;
			  else if(value<0)
				  value=0;
		  } else if(TestDataHelper.isUnconnected(slotValue)) {
			  value=255;
		  }else	  
			  value=TestDataHelper.getDouble(slotValue, 0d);

		  switch(slotName) {//"netLastInWins", "occSensorOper"
		  case 0:
			  schedCurrentState = (int)value;
			  break;

		  case 1:
			  wmOverride = (int)value;
			  break;

		  case 2:
			  networkManOcc= (int)value;
			  break;

		  case 3:
			  occSensorState = (int)value;
			  break;

		  case 4:
			  netLastInWins = (int) value;
			  break;

		  case 5:
			  occSensorOper = (int) value;
			  break;

		  default:
			  break;
		  }
	  }
	
	  private void intializeValues(String[] inputs) {
		  setSlotValue(0, inputs[1]);
		  setSlotValue(1, inputs[2]);
		  setSlotValue(2, inputs[3]);
		  setSlotValue(3, inputs[4]);
		  setSlotValue(4, inputs[5]);
		  setSlotValue(5, inputs[6]);
		  
		  prevLoopNetworkManOcc = prevNetworkManOcc;
		  prevLoopWMOverride = prevWMOverride;
		  prevLoopManOverrideState = manOverrideState;
		  
	  }
	
	public void calculateOutput(String[] inputList) {
		intializeValues(inputList);
		
		//Insure all inputs are legal values
		// networkManOcc = Occ, Unocc, Byp, Stdby, Null
		// wmOverride    = Occ, Unocc, Byp, Stdby, Null
		// schedCurrentState = Occ, Unocc, Stdby, Null
		// occSensorState    = Occ, Unocc, Null
		if (networkManOcc > OCC_STANDBY )
			networkManOcc = OCC_NUL;
		if (wmOverride > OCC_STANDBY)
			wmOverride = OCC_NUL;
		if (schedCurrentState > OCC_STANDBY || schedCurrentState == OCC_BYPASS)
			schedCurrentState = OCC_NUL;
		if (occSensorState > OCC_UNOCC)
			occSensorState = OCC_NUL;
		
		// Calculate the manual override
		// Check the user selected override priority
		if( this.netLastInWins ==  OA_NET_WINS)
		{
			//Network Priority
			// Use the network override if it's active.
			// Otherwise use the wall module override.
			if (networkManOcc == OCC_NUL)
				manOverrideState = wmOverride;
			else
				manOverrideState = networkManOcc;
		}
		else
		{
			//Last In Wins
			// If the etwork input changed state, update man override.
			//  then check if the wall module override changed state.
			if (networkManOcc != prevNetworkManOcc)
			{
				prevNetworkManOcc = networkManOcc;
				manOverrideState = networkManOcc;
			}
			else if (wmOverride != prevWMOverride)
			{
				prevWMOverride = wmOverride;
				manOverrideState = wmOverride;
			}
		}
		
		// Combine the manual override, schedule, and occupancy sensor per the SDS.
		// Manual override has priority
		switch (manOverrideState)
		{
		// If Manual override is commanding the state, then set the Eff Occ according to the SDS table.
		case OCC_OCC:
		case OCC_STANDBY:
		case OCC_UNOCC:
			effOccCurrentState = manOverrideState;
			break;

			// If Manual override is commanding bypass, then there are three cases that
			//  set the state to bypass.  The rest of the cases report occupied.
		case OCC_BYPASS:
			if ( schedCurrentState == OCC_STANDBY ||
			schedCurrentState == OCC_UNOCC ||
			(schedCurrentState == OCC_NUL && occSensorState == OCC_UNOCC))
				effOccCurrentState = OCC_BYPASS;
			else
				effOccCurrentState = OCC_OCC;
			break;

		case OCC_NUL:
		default:
			//No Manual override command, so check schedule.
			//If schedule is standby, set Eff Occ to match.
			// else look at Occ Sensor and follow SDS.
			if (schedCurrentState == OCC_STANDBY)
				effOccCurrentState = OCC_STANDBY;
			else if (schedCurrentState == OCC_OCC)
			{
				if (occSensorState == OCC_UNOCC)
					effOccCurrentState = OCC_STANDBY;
				else
					effOccCurrentState = OCC_OCC;
			}
			else if (schedCurrentState == OCC_UNOCC)
			{
				if (occSensorState == OCC_OCC)
				{
					// We're here because there is no manual override, the schedule is unoccupied,
					//  and the occ sensor says there is someone in the space.
					switch (occSensorOper)
					{
					case OA_CONF_ROOM:                  //schedule has priority, keep space unocc.
						effOccCurrentState = OCC_UNOCC;
						break;

					case OA_CLEAN_CREW:                 //make it comfortable for the cleaning crew.
						effOccCurrentState = OCC_STANDBY;
						break;

					case OA_TENANT:                     //The tenant is there, so go occupied.
					default:
						effOccCurrentState = OCC_OCC;
						break;
					}
				}
				// We're here because there is no manual override, the schedule is unoccupied,
				//  and the occ sensor says there is no one in the space.
				else
					effOccCurrentState = OCC_UNOCC;
			}
			// We're here because there is no manual override and the schedule is null.
			// Give priority to the occ sensor
			else if (occSensorState == OCC_UNOCC)
				effOccCurrentState = OCC_UNOCC;
			else
				effOccCurrentState = OCC_OCC;
			break;
		}
		
	}
	
	public void printOutput(String[] inputList) {
		System.out.println(inputList[0]+"," + inputList[1] + "," + inputList[2] + "," + inputList[3] + ","
				+ inputList[4] + "," + inputList[5] + "," + inputList[6] + "," + effOccCurrentState + ","
				+ manOverrideState+ "," + prevLoopNetworkManOcc+ "," + prevLoopWMOverride+ "," + prevLoopManOverrideState );
	}
	
	public static ArrayList<String[]> readTestDataFromCSVFile(File completeFile) {
		BufferedReader br = null;
		String line = "";
		String cvsSplitBy = ",";
		ArrayList<String[]> testData = new ArrayList<>();
		try {
			br = new BufferedReader(new FileReader(completeFile));
			while ((line = br.readLine()) != null) {
				String[] data = line.split(cvsSplitBy);
				testData.add(data);
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return testData;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
//		ArrayList<String[]> testDataFromCSVFile = readTestDataFromCSVFile("C:\\Work\\Git\\f1softwaretool\\programmingTool\\programmingTool-rt\\srcTest\\com\\honeywell\\programmingtool\\fbs\\zonecontrol\\test\\OccupancyArbitrator_InputData.csv");
		File testDataFile = TestDataHelper.getTestDataFileReference(OccupancyArbitratorOutputGenerator.class, "OccupancyArbitrator_InputData.csv");
		ArrayList<String[]> testDataFromCSVFile = readTestDataFromCSVFile(testDataFile);
		
		OccupancyArbitratorOutputGenerator occArb = new OccupancyArbitratorOutputGenerator();
		occArb.prevNetworkManOcc=255;
		occArb.prevWMOverride=255;
		occArb.manOverrideState=255;
		for (int i=1; i<testDataFromCSVFile.size();i++) {
			String[] data = testDataFromCSVFile.get(i);
			
			occArb.calculateOutput(data);
			occArb.printOutput(data);
		}
	}

}
