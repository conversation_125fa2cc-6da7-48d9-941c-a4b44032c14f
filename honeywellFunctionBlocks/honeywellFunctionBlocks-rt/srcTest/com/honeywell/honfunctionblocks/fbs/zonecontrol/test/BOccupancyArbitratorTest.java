/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BIcon;
import javax.baja.sys.BRelTime;
import javax.baja.sys.BStation;
import javax.baja.sys.BValue;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;
import javax.baja.util.BFolder;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusOccupancyStateEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFBOverrideProperties;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BNetworkLastInWinsEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancyArbitrator;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorOperationEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 1, 2018
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})
public class BOccupancyArbitratorTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BOccupancyArbitratorTest(2979906276)1.0$ @*/
/* Generated Fri Feb 02 13:57:44 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOccupancyArbitratorTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass
	public void setUp() throws Exception {
		occArbBlock = new BOccupancyArbitrator();
		executionParams = new BExecutionParams();
		stationHandler = BTest.createTestStation();
		stationHandler.startStation();
		station = stationHandler.getStation();

		stationHandler.startStation();
	}
  
  @AfterClass
	public void tearDown() {
		occArbBlock = null;
		executionParams = null;
		occArbBlockOverride = null;
		fbContainer = null;
		stationHandler.stopStation();
		stationHandler.releaseStation();

		station = null;

		stationHandler = null;
	}

  @DataProvider(name="provideInSlotNames")
  public Object[] createInputSlotNames() {
	  return new Object[]{"scheduleCurrentState", "WMOverride", "NetworkManOcc", "OccSensorState"};	  
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[] createConfigSlotNames() {
	  return new Object[] {"netLastInWins", "occSensorOper"};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[] createOutputSlotNames() {
	  return new Object[] {"EFF_OCC_CURRENT_STATE", "MANUAL_OVERRIDE_STATE"};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[] createExecOrderSlotName() {
	  return new Object[] {"ExecutionOrder", "toolVersion"};
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[] createAllSlotNames() {
	  List<Object> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[] invalidSlotNames() {
	  return new Object[]{"ScheduleCurrentState", "wmOverride", "networkManOcc", "occSensorState", "EffOccCurrentState", "ManualOverrideState"};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(occArbBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(occArbBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "occupancy_arbitrator.png");
	  BIcon actualFbIcon = occArbBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  occArbBlock.setIcon(expectedFbIcon);
	  actualFbIcon = occArbBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
    
  @DataProvider(name = "scheduleCurrentStateEnumValues")
  public Object[][] getDataToTestEnumOrdinalForScheduleStateEnum(){
	  return new Object[][] {{Integer.valueOf(BScheduledStateEnum.OCCUPIED),BScheduledStateEnum.Occupied},
		  {Integer.valueOf(BScheduledStateEnum.UNOCCUPIED),BScheduledStateEnum.Unoccupied},
		  {Integer.valueOf(BScheduledStateEnum.STANDBY),BScheduledStateEnum.Standby},
		  {Integer.valueOf(BScheduledStateEnum.NULL),BScheduledStateEnum.Null}};
  }
  
  

  @Test(dataProvider="scheduleCurrentStateEnumValues")
  public void testSettingValueInScheduleCurrentState(int ordinal,BScheduledStateEnum schedStateEnum) {
	  occArbBlock.setScheduleCurrentState(new BStatusOccupancyStateEnum(BScheduledStateEnum.make(ordinal)));
	  Assert.assertEquals(BScheduledStateEnum.make(occArbBlock.getScheduleCurrentState().getValue().getOrdinal()), schedStateEnum);
  }
  
  
  @DataProvider(name = "OccupancyEnumValues")
  public Object[][] getDataToTestEnumOrdinalForOccupancyEnum(){
	  return new Object[][] {{Integer.valueOf(BOccupancyEnum.OCCUPIED),BOccupancyEnum.Occupied},
		  {Integer.valueOf(BOccupancyEnum.UNOCCUPIED),BOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BOccupancyEnum.STANDBY),BOccupancyEnum.Standby},
		  {Integer.valueOf(BOccupancyEnum.BYPASS),BOccupancyEnum.Bypass},
		  {Integer.valueOf(BOccupancyEnum.NULL),BOccupancyEnum.Null}};
  }

  @Test(dataProvider="OccupancyEnumValues")
  public void testSettingValueInOccupancyEnum(int ordinal,BOccupancyEnum occEnum) {
	  occArbBlock.setWMOverride(new BStatusOccupancyStateEnum(BOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancyEnum.make(occArbBlock.getWMOverride().getValue().getOrdinal()), occEnum);
	  
	  occArbBlock.setNetworkManOcc(new BStatusOccupancyStateEnum(BOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancyEnum.make(occArbBlock.getNetworkManOcc().getValue().getOrdinal()), occEnum);
	  
	  occArbBlock.setMANUAL_OVERRIDE_STATE(new BStatusOccupancyStateEnum(BOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancyEnum.make(occArbBlock.getMANUAL_OVERRIDE_STATE().getValue().getOrdinal()), occEnum);
  }
  
  
  @DataProvider(name = "OccupancySensorStateEnum")
  public Object[][] getDataToTestEnumOrdinalForOccupancySensorStateEnum(){
	  return new Object[][] {{Integer.valueOf(BOccupancyEnum.OCCUPIED),BOccupancySensorStateEnum.Occupied},
		  {Integer.valueOf(BOccupancyEnum.UNOCCUPIED),BOccupancySensorStateEnum.Unoccupied},
		  {Integer.valueOf(BOccupancyEnum.NULL),BOccupancySensorStateEnum.Null}};
  }

  @Test(dataProvider="OccupancySensorStateEnum")
  public void testSettingValueInOccupancySensorStateEnum(int ordinal,BOccupancySensorStateEnum occEnum) {
	  occArbBlock.setOccSensorState(new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancySensorStateEnum.make(occArbBlock.getOccSensorState().getValue().getOrdinal()), occEnum);
  }  
  
  @DataProvider(name = "EffecctiveOccupancyStateEnumValues")
  public Object[][] getDataToTestEnumOrdinalForEffOccupancyStateEnum(){
	  return new Object[][] {{Integer.valueOf(BEffectiveOccupancyEnum.OCCUPIED),BEffectiveOccupancyEnum.Occupied},
		  {Integer.valueOf(BEffectiveOccupancyEnum.UNOCCUPIED),BEffectiveOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BEffectiveOccupancyEnum.STANDBY),BEffectiveOccupancyEnum.Standby},
		  {Integer.valueOf(BEffectiveOccupancyEnum.BYPASS),BEffectiveOccupancyEnum.Bypass}};
  }
  
  

  @Test(dataProvider="EffecctiveOccupancyStateEnumValues")
  public void testSettingValueInEffOccupancyState(int ordinal,BEffectiveOccupancyEnum effEnum) {
	  occArbBlock.setEFF_OCC_CURRENT_STATE(new BStatusOccupancyStateEnum(BEffectiveOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BEffectiveOccupancyEnum.make(occArbBlock.getEFF_OCC_CURRENT_STATE().getValue().getOrdinal()), effEnum);
  }
  
  

  
  @DataProvider(name="provideSequencedTestData")
  public Object[][] getSequencedTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/OccupancyArbitrator_TestData.csv");
  }
  
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideSequencedTestData", dependsOnMethods={"testSlotAvailability"})
  public void testOccupancyArbitratorBlockWithSeqTestData(List<List<String>> inputSequence) throws BlockExecutionException {
	  BOccupancyArbitrator occArbTempBlock = new BOccupancyArbitrator();
	  try {
		  occArbTempBlock.initHoneywellComponent(null);
	  } catch (BlockInitializationException e) {
		  e.printStackTrace();
	  }

	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
		  List<String> inputs = iterator.next();

		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));

		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.scheduleCurrentState.getName(), inputs.get(1));
		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.WMOverride.getName(), inputs.get(2));
		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.NetworkManOcc.getName(), inputs.get(3));
		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.OccSensorState.getName(), inputs.get(4));
		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.netLastInWins.getName(), inputs.get(5));
		  setSlotValue(occArbTempBlock, BOccupancyArbitrator.occSensorOper.getName(), inputs.get(6));

		  try {
			  occArbTempBlock.executeHoneywellComponent(executionParams);
		  }catch (Exception e) {
			  System.out.println(" number --> " + seqNo);
		  }
			Assert.assertEquals(occArbTempBlock.getEFF_OCC_CURRENT_STATE().getValue().getOrdinal(),
					TestDataHelper.getInt(inputs.get(7), 0),
					"Failed for EFF OCC at step #" + seqNo + " for input data " + inputs + "; ");
			Assert.assertEquals(occArbTempBlock.getMANUAL_OVERRIDE_STATE().getValue().getOrdinal(),
					TestDataHelper.getInt(inputs.get(8), 0),
					"Failed for MANUAL_OVERRIDE_STATE  step #" + seqNo + " for input data " + inputs + "; ");
	  }

	  occArbTempBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param occArbTempBlock
   * @param slotName
   * @param inputValue
   */
  private void setSlotValue(BOccupancyArbitrator occArbTempBlock, String slotName, String slotValue) {
	  if (TestDataHelper.isConnected(slotValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(slotValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = occArbTempBlock.getProperty(slotName).getType();			
			BConverter converter;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusOccupancyStateEnum.TYPE)) {				
				converter = new BStatusNumericToStatusOccupancyStateEnumConverter();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),occArbTempBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				if(occArbTempBlock.get("Link"+slotName)!=null) {
					occArbTempBlock.remove("Link"+slotName);
				}
				
				occArbTempBlock.add("Link"+slotName,conversionLink );		
				conversionLink.activate();
			}else{
				occArbTempBlock.linkTo(nm1, nm1.getSlot("out"), occArbTempBlock.getSlot(slotName));
			}			
			
		  return;
	  }
	  

	  double dvalue = (int)TestDataHelper.getDouble(slotValue,255);
	  int value = (int)dvalue;
	  BStatusOccupancyStateEnum occEnum; 

	  switch(slotName) {//"netLastInWins", "occSensorOper"
	  case "scheduleCurrentState":		  
		  occEnum = new BStatusOccupancyStateEnum(BScheduledStateEnum.make(value));
		  occArbTempBlock.setScheduleCurrentState(occEnum);	
		  
		  if(TestDataHelper.isUnconnected(slotValue))
			  occEnum.setStatus(BStatus.nullStatus);
		  break;

	  case "WMOverride":
		  occEnum = new BStatusOccupancyStateEnum(BOccupancyEnum.make(value));
		  occArbTempBlock.setWMOverride(occEnum);
		  
		  if(TestDataHelper.isUnconnected(slotValue))
			  occEnum.setStatus(BStatus.nullStatus);
		  break;

	  case "NetworkManOcc":
		  occEnum = new BStatusOccupancyStateEnum(BOccupancyEnum.make(value));
		  occArbTempBlock.setNetworkManOcc(occEnum);
		  
		  if(TestDataHelper.isUnconnected(slotValue))
			  occEnum.setStatus(BStatus.nullStatus);
		  break;

	  case "OccSensorState":
		  occEnum = new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.make(value));
		  occArbTempBlock.setOccSensorState(occEnum);
		  
		  if(TestDataHelper.isUnconnected(slotValue))
			  occEnum.setStatus(BStatus.nullStatus);
		  break;

	  case "netLastInWins":
		  occArbTempBlock.setNetLastInWins(BNetworkLastInWinsEnum.make(TestDataHelper.getInt(slotValue, 0)));
		  break;

	  case "occSensorOper":
		  occArbTempBlock.setOccSensorOper(BOccupancySensorOperationEnum.make(TestDataHelper.getInt(slotValue, 0)));
		  break;

	  default:
		  break;
	  }
  }
  
  @Test
  public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
	  BOccupancyArbitrator occArbTempBlock = new BOccupancyArbitrator();

	  executionParams.setIterationInterval(1000);
	  
	  occArbTempBlock.setScheduleCurrentState(new BStatusOccupancyStateEnum(BScheduledStateEnum.make(BScheduledStateEnum.OCCUPIED)));
	  occArbTempBlock.setWMOverride(new BStatusOccupancyStateEnum(BOccupancyEnum.make(BOccupancyEnum.OCCUPIED)));
	  occArbTempBlock.setNetworkManOcc(new BStatusOccupancyStateEnum(BOccupancyEnum.make(BOccupancyEnum.OCCUPIED)));
	  occArbTempBlock.setOccSensorState(new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.make(BOccupancySensorStateEnum.OCCUPIED)));
	  occArbTempBlock.setNetLastInWins(BNetworkLastInWinsEnum.DEFAULT);
	  occArbTempBlock.setOccSensorOper(BOccupancySensorOperationEnum.DEFAULT);

	  for (int i = 0; i < 5000; i++) {
		  occArbTempBlock.executeHoneywellComponent(executionParams);
	  }

	  BogFileUtil bogUtil = new BogFileUtil();
	  try {
	  	int valBeforeShutDown = occArbTempBlock.getMANUAL_OVERRIDE_STATE().getValue().getOrdinal();
		  File bogFile = bogUtil.saveComponentToBogFile("OccupancyArbitrator", occArbTempBlock);
		  BOccupancyArbitrator occArbSaved = (BOccupancyArbitrator) bogUtil.getComponentFromBogFile(bogFile);

		  Assert.assertEquals(occArbSaved.getEFF_OCC_CURRENT_STATE().getValue().getOrdinal(), BEffectiveOccupancyEnum.DEFAULT.getOrdinal(), "Failed to verify EFF_OCC_CURRENT_STATE");
		  Assert.assertEquals(occArbSaved.getMANUAL_OVERRIDE_STATE().getValue().getOrdinal(), valBeforeShutDown, "Failed to verify MANUAL_OVERRIDE_STATE");
	  } catch (IOException e) {
		  e.printStackTrace();
	  }
  }
  
  //@Test(dataProvider="provideInSlotNames")
  public void testLinkRulesForInSlot(String slotName) {
	  BOccupancyArbitrator tempBlock = new BOccupancyArbitrator();
	  LinkCheck checkLink = tempBlock.checkLink(occArbBlock, occArbBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
	  Assert.assertEquals(checkLink.isValid(), false);
  }

  //@Test(dataProvider="provideConfigSlotNames")
  public void testLinkRulesForConfigSlots(String slotName) {
	  BOccupancyArbitrator tempBlock = new BOccupancyArbitrator();
	  LinkCheck checkLink = tempBlock.checkLink(occArbBlock, occArbBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
	  Assert.assertEquals(checkLink.isValid(), false);
  }

  //@Test(dataProvider="provideOutputSlotNames")
  public void testLinkRulesForOutSlots(String slotName) {
	  BOccupancyArbitrator nm = new BOccupancyArbitrator();
	  LinkCheck checkLink = occArbBlock.checkLink(nm, nm.getSlot(slotName), occArbBlock.getSlot(slotName), null);
	  Assert.assertFalse(checkLink.isValid());
  }
  
	@Test
	public void testConfigProperties() {
		List<Property> configList = occArbBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BOccupancyArbitrator.netLastInWins.getName(), BOccupancyArbitrator.occSensorOper.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = occArbBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BOccupancyArbitrator.scheduleCurrentState.getName(), BOccupancyArbitrator.WMOverride.getName(),BOccupancyArbitrator.NetworkManOcc.getName(),BOccupancyArbitrator.OccSensorState.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = occArbBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BOccupancyArbitrator.EFF_OCC_CURRENT_STATE.getName(),BOccupancyArbitrator.MANUAL_OVERRIDE_STATE.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	
	@Test
	public void testOverrideAutoAction() {
		if (null == station.get("FB")) {
			fbContainer = new BFolder();
			station.add("FB", fbContainer);
			occArbBlockOverride = new BOccupancyArbitrator();
			fbContainer.add("OCC", occArbBlockOverride);
		}
		BFBOverrideProperties fbOverrideProperty = new BFBOverrideProperties();
		fbOverrideProperty.setOverrideDuration(BRelTime.makeMinutes(1));
		List<Property> properties = ((BFunctionBlock) occArbBlockOverride).getOutputPropertiesList();

		for (int i = 0; i < properties.size(); i++) {

			BValue outPutSlotValue = occArbBlockOverride.get(properties.get(i).getName());
			if (outPutSlotValue instanceof BStatusEnum) {
				BEnum f1SlotEnum = ((BStatusEnum) outPutSlotValue).getEnum();
				fbOverrideProperty.add(properties.get(i).getName(), f1SlotEnum.newCopy());

			} else if (outPutSlotValue instanceof BStatusBoolean) {
				boolean isTrueValueSet = ((BStatusBoolean) outPutSlotValue).getValue();
				fbOverrideProperty.add(properties.get(i).getName(), BBoolean.make(isTrueValueSet));

			} else if (outPutSlotValue instanceof BStatusNumeric) {
				fbOverrideProperty.add(properties.get(i).getName(),
						BDouble.make(((BStatusNumeric) outPutSlotValue).getValue()));

			} 
		}

		occArbBlockOverride.doOverride(fbOverrideProperty);
		Assert.assertNotEquals(occArbBlockOverride.getOverrideExpiration(), BAbsTime.NULL);
		
		occArbBlockOverride.doAuto();
		Assert.assertEquals(occArbBlockOverride.getOverrideExpiration(), BAbsTime.NULL);

		if (null != station.get("FB"))
			station.remove((BFolder) station.get("FB"));

	}
  
	private BOccupancyArbitrator occArbBlock;
	private BExecutionParams executionParams;

	private BStation station = null;
	private TestStationHandler stationHandler = null;
	private BFolder fbContainer = null;
	private BOccupancyArbitrator occArbBlockOverride = null;
  
}
