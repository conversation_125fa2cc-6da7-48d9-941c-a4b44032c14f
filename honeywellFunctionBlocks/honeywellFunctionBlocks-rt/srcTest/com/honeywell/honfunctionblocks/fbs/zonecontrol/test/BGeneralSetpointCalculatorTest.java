/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToGenSetpointCalcEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BStatusGenSetpointCalcEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BGeneralSetpointCalculator;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of General Setpoint Calculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-262
 * <AUTHOR> - Lavanya B.
 * @since Feb 13, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BGeneralSetpointCalculatorTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BGeneralSetpointCalculatorTest(2979906276)1.0$ @*/
/* Generated Tue Feb 13 10:50:53 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BGeneralSetpointCalculatorTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  @BeforeClass
  public void setUp() {	
	  genSPCalcBlock = new BGeneralSetpointCalculator();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  genSPCalcBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[] createInputSlotNames() {
	  return new Object[]{"effOccuCurrentState", "ResetInput", "Reset0Pct", "Reset100Pct", "ResetAmount", "OccupiedSetpoint",
			  "StandbySetpoint", "UnoccupiedSetpoint"};
  }  

  @DataProvider(name="provideOutputSlotNames")
  public Object[] createOutputSlotNames() {
	  return new Object[] {"EFF_SETPT"};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[] createExecOrderSlotName() {
	  return new Object[] {"ExecutionOrder", "toolVersion"};
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[] createAllSlotNames() {
	  List<Object> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[] invalidSlotNames() {
	  return new Object[]{"EffOccuCurrentState", "resetInput", "reset0Pct", "reset100Pct", "resetAmount", "occupiedSetpoint",
			  "unoccupiedSetpoint","standbySetpoint"};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(genSPCalcBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(genSPCalcBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "general_setpoint_calculator.png");
	  BIcon actualFbIcon = genSPCalcBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  genSPCalcBlock.setIcon(expectedFbIcon);
	  actualFbIcon = genSPCalcBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @Test
  public void testSettingslotValues() {
	  verifySettingValue(32767);
	  verifySettingValue(2.2250738585072014E-308);
	  verifySettingValue(4.9e-324);
	  verifySettingValue(1.7976931348623157e+308);
	  verifySettingValue(Double.NEGATIVE_INFINITY);
	  verifySettingValue(Double.POSITIVE_INFINITY);
	  verifySettingValue(Double.NaN);
	  verifySettingValue(-34);
	  verifySettingValue(34);
	  verifySettingValue(255);
	  verifySettingValue(0);
  }
  
  private void verifySettingValue(double snValue) {
	  genSPCalcBlock.setResetInput(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getResetInput().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getResetInput().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setReset0Pct(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getReset0Pct().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getReset0Pct().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setReset100Pct(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getReset100Pct().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getReset100Pct().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setResetAmount(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getResetAmount().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getResetAmount().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setOccupiedSetpoint(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getOccupiedSetpoint().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getOccupiedSetpoint().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setUnoccupiedSetpoint(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getUnoccupiedSetpoint().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getUnoccupiedSetpoint().getValue(), snValue, 0.1);	  
	  
	  genSPCalcBlock.setStandbySetpoint(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getStandbySetpoint().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getStandbySetpoint().getValue(), snValue, 0.1);
	  
	  genSPCalcBlock.setEFF_SETPT(new BHonStatusNumeric(snValue));
	  if(Double.isNaN(snValue))
		  Assert.assertTrue(Double.isNaN(genSPCalcBlock.getEFF_SETPT().getValue()));
	  else
		  Assert.assertEquals(genSPCalcBlock.getEFF_SETPT().getValue(), snValue, 0.1);
  }
  
  @DataProvider(name = "OccupancyEnumValues")
  public Object[][] getDataToTestEnumOrdinalForOccupancyEnum(){
	  return new Object[][] {{Integer.valueOf(BOccupancyEnum.OCCUPIED),BOccupancyEnum.Occupied},
		  {Integer.valueOf(BOccupancyEnum.UNOCCUPIED),BOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BOccupancyEnum.STANDBY),BOccupancyEnum.Standby},
		  {Integer.valueOf(BOccupancyEnum.BYPASS),BOccupancyEnum.Bypass},
		  {Integer.valueOf(BOccupancyEnum.NULL),BOccupancyEnum.Null}};
  }

  @Test(dataProvider="OccupancyEnumValues")
  public void testSettingValueInOccupancyEnum(int ordinal,BOccupancyEnum occEnum) {
	  genSPCalcBlock.setEffOccuCurrentState(new BStatusGenSetpointCalcEnum(BOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancyEnum.make(genSPCalcBlock.getEffOccuCurrentState().getValue().getOrdinal()), occEnum);
  }  

  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
	  return TestDataHelper.getTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/GSC_TestData.csv");
  }
  
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testGenSetptCalcBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BGeneralSetpointCalculator gscBlock = new BGeneralSetpointCalculator();
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.effOccuCurrentState.getName(), inputs.get(1));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.ResetInput.getName(), inputs.get(2));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.Reset0Pct.getName(), inputs.get(3));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.Reset100Pct.getName(), inputs.get(4));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.ResetAmount.getName(), inputs.get(5));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.OccupiedSetpoint.getName(), inputs.get(6));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.StandbySetpoint.getName(), inputs.get(7));
	  setSlotValue(gscBlock, BGeneralSetpointCalculator.UnoccupiedSetpoint.getName(), inputs.get(8));	  
	  gscBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(gscBlock.getEFF_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(10),0d));	  
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param gscBlock
   * @param slotName
   * @param slotValue
   */
  private void setSlotValue(BGeneralSetpointCalculator gscBlock, String slotName, String slotValue) {
	  if (TestDataHelper.isConnected(slotValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(slotValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = gscBlock.getProperty(slotName).getType();			
			BConverter converter;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusGenSetpointCalcEnum.TYPE)) {				
				converter = new BStatusNumericToGenSetpointCalcEnumConverter();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),gscBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				if(gscBlock.get("Link"+slotName)!=null) {
					gscBlock.remove("Link"+slotName);
				}
				
				gscBlock.add("Link"+slotName,conversionLink );
				conversionLink.activate();
			}else{
				gscBlock.linkTo(nm1, nm1.getSlot("out"), gscBlock.getSlot(slotName));
			}			
		  return;
	  }

	  BStatusGenSetpointCalcEnum genSpEnum;

	  switch(slotName) {
	  case "effOccuCurrentState":
		  double dvalue = (int)TestDataHelper.getDouble(slotValue,255);
		  int value = (int)dvalue;
		  try {
		  	BOccupancyEnum.make(value);
	  }catch(Exception e) {
		  value = 0;
	  }		  	
		  genSpEnum = new BStatusGenSetpointCalcEnum(BOccupancyEnum.make(value));
		  gscBlock.setEffOccuCurrentState(genSpEnum);			  
		  if(TestDataHelper.isUnconnected(slotValue))
			  genSpEnum.setStatus(BStatus.nullStatus);
		  break;

	  case "ResetInput":
		  gscBlock.setResetInput(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;

	  case "Reset0Pct":
		  gscBlock.setReset0Pct(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;

	  case "Reset100Pct":
		  gscBlock.setReset100Pct(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;

	  case "ResetAmount":
		  gscBlock.setResetAmount(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;

	  case "OccupiedSetpoint":
		  gscBlock.setOccupiedSetpoint(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;
		  
	  case "StandbySetpoint":
		  gscBlock.setStandbySetpoint(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;
		  
	  case "UnoccupiedSetpoint":
		  gscBlock.setUnoccupiedSetpoint(TestDataHelper.getHonStatusNumeric(slotValue, Double.POSITIVE_INFINITY));
		  break;
		  
		  

	  default:
		  break;
	  }
  }
  
  //@Test(dataProvider="provideInSlotNames")
  public void testLinkRulesForInSlot(String slotName) {
	  BGeneralSetpointCalculator tempBlock = new BGeneralSetpointCalculator();
	  LinkCheck checkLink = tempBlock.checkLink(genSPCalcBlock, genSPCalcBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
	  Assert.assertEquals(checkLink.isValid(), false);
  }

  //@Test(dataProvider="provideOutputSlotNames")
  public void testLinkRulesForOutSlots(String slotName) {
	  BGeneralSetpointCalculator nm = new BGeneralSetpointCalculator();
	  LinkCheck checkLink = genSPCalcBlock.checkLink(nm, nm.getSlot(slotName), genSPCalcBlock.getSlot(slotName), null);
	  Assert.assertFalse(checkLink.isValid());
  }
  
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = genSPCalcBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BGeneralSetpointCalculator.EFF_SETPT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  private BGeneralSetpointCalculator genSPCalcBlock;
  private BExecutionParams executionParams;
}
