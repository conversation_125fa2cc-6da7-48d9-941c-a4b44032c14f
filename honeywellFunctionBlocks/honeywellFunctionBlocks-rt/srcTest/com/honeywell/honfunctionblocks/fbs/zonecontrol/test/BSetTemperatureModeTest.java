/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusSetTemperatureModeEnumConveter;
import com.honeywell.honfunctionblocks.converters.StatusSetTemperatureModeEnumsConverter;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetTemperatureMode;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BBehaviorTypeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BControlTypeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BEffTempModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BStatusSetTemperatureModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of SetTemperatureMode TestCase of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 8, 2018
 */
@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BSetTemperatureModeTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BSetTemperatureModeTest(2979906276)1.0$ @*/
/* Generated Fri Feb 09 13:11:03 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSetTemperatureModeTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	  @BeforeClass
	  public void setUp() {
		  setTempModeBlock = new BSetTemperatureMode();
		  setTempModeBlock1 = new BSetTemperatureMode();
		  executionParams = new BExecutionParams();
	  }
	  
	  @AfterClass
	  public void tearDown() {
		  setTempModeBlock = null;
		  setTempModeBlock1 = null;
		  executionParams = null;
	  }
	  
	  @DataProvider(name="provideInSlotNames")
	  public Object[][] createInputSlotNames() {
		  return new Object[][]{{"sysSwitch"}, {"cmdMode"}, {"supplyTemp"}, {"spaceTemp"}, {"effHeatSP"}, {"effCoolSP"}, {"allowAutoChange"}};
	  }
	  
	  @DataProvider(name="provideConfigSlotNames")
	  public Object[][] createConfigSlotNames() {
		  return new Object[][] {{"controlType"},{"behaviorType"}};
	  }
	  
	  @DataProvider(name="provideOutputSlotNames")
	  public Object[][] createOutputSlotNames() {
		  return new Object[][] {{"EFF_SETPT"}, {"EFF_TEMP_MODE"}};
	  }
	  
	  @DataProvider(name="provideMiscSlotNames")
	  public Object[][] createExecOrderSlotName() {
		  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	  }
	  
	  @DataProvider(name="provideAllSlotNames")
	  public Object[][] createAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  }
	  
	  @DataProvider(name = "provideInvalidSlotNames")
	  public Object[][] invalidSlotNames() {
		  return new Object[][]{{"In"}, {"onValue"}, {"OnVal"}, {"OffVal"}, {"OffValue"}, {"MinOn"}, {"InvalidFlag"}, {"TailOperation"}};
	  }
	  
	  @Test(dataProvider="provideInvalidSlotNames")
	  public void testInvalidSlots(String slotName){
		  Assert.assertNull(setTempModeBlock.getSlot(slotName));
	  }  
	  
	  @Test(dataProvider="provideAllSlotNames")
	  public void testSlotAvailability(String slotName) {
		  Assert.assertNotNull(setTempModeBlock.getSlot(slotName));
	  }
	  
	  @Test(groups={"testIconSlot"})
	  public void testIconSlot(){
		  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "set_temperature_mode.png");
		  BIcon actualFbIcon = setTempModeBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);

		  //check if new icon can be set on AIA to update modified state
		  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		  setTempModeBlock.setIcon(expectedFbIcon);
		  actualFbIcon = setTempModeBlock.getIcon();
		  Assert.assertEquals(expectedFbIcon, actualFbIcon);
	  }
	  
	  @DataProvider(name = "provideSampleValues")
	  public Object[][] sampleValues() {
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	  }

	  @Test(dataProvider="provideSampleValues")
	  public void testSettingValueInsetTempModeBlock(double snValue) {
		  setTempModeBlock.setSysSwitch(new BStatusSetTemperatureModeEnum(BSystemSwitchEnum.Ss_Cool));
		  Assert.assertEquals(BSystemSwitchEnum.make(setTempModeBlock.getSysSwitch().getValue().getTag()), BSystemSwitchEnum.Ss_Cool);
		  
		  setTempModeBlock.setCmdMode(new BStatusSetTemperatureModeEnum(BCommandModeEnum.Cmd_Cool_Mode));
		  Assert.assertEquals(BCommandModeEnum.make(setTempModeBlock.getCmdMode().getValue().getTag()), BCommandModeEnum.Cmd_Cool_Mode);
		  
		  setTempModeBlock.setSupplyTemp(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(setTempModeBlock.getSupplyTemp().getValue(), snValue, 0.1);
		  
		  setTempModeBlock.setSpaceTemp(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(setTempModeBlock.getSpaceTemp().getValue(), snValue, 0.1);
		  
		  setTempModeBlock.setEffHeatSP(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(setTempModeBlock.getEffHeatSP().getValue(), snValue, 0.1);
		  
		  setTempModeBlock.setEffCoolSP(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(setTempModeBlock.getEffCoolSP().getValue(), snValue, 0.1);
		  
		  setTempModeBlock.setAllowAutoChange(new BHonStatusBoolean());
		  Assert.assertEquals(setTempModeBlock.getAllowAutoChange().getValue(), false);
		  
		  setTempModeBlock.setEFF_SETPT(new BHonStatusNumeric(snValue));
		  Assert.assertEquals(setTempModeBlock.getEFF_SETPT().getValue(), snValue, 0.1);
		  
		  setTempModeBlock.setEFF_TEMP_MODE(new BStatusSetTemperatureModeEnum(BEffTempModeEnum.Cool_Mode));
		  Assert.assertEquals(BEffTempModeEnum.make(setTempModeBlock.getEFF_TEMP_MODE().getValue().getTag()), BEffTempModeEnum.Cool_Mode);
		  
		  setTempModeBlock.setControlType(BControlTypeEnum.Cvahu);
		  Assert.assertEquals(BControlTypeEnum.make(setTempModeBlock.getControlType().getTag()), BControlTypeEnum.Cvahu);
		  
	  }
	  
	  @DataProvider(name="provideTestData")
	  public Object[][] getTesData() {
		  return TestDataHelper.getTestDataInTestNGFormat(
				  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/SetTemperatureMode_TestData.csv");
	  }
	  
	  @DataProvider(name="provideTestDataEnhanced")
	  public Object[][] getTesDataEnhanced() {
		  return TestDataHelper.getTestDataInTestNGFormat(
				  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/New_SetTemperatureMode_TestData.csv");
	  }
	  
	  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	  public void testSetTemperatureModeBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  //BSetTemperatureMode setTempModeBlock = new BSetTemperatureMode();
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.sysSwitch.getName(), inputs.get(1));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.cmdMode.getName(), inputs.get(2));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.supplyTemp.getName(), inputs.get(3));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.spaceTemp.getName(), inputs.get(4));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.effHeatSP.getName(), inputs.get(5));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.effCoolSP.getName(), inputs.get(6));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.allowAutoChange.getName(), inputs.get(7));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.controlType.getName(), inputs.get(8));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.behaviorType.getName(), "0");
		 
		  setTempModeBlock1.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(setTempModeBlock1.getEFF_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), 0.1, "Failed to verify TempSetPt for inputData: "+inputs);	  
		  Assert.assertEquals(setTempModeBlock1.getEFF_TEMP_MODE().getValue().getOrdinal(), TestDataHelper.getDouble(inputs.get(10), 0.0d), 0.1, "Failed to verify TempMode for inputData: "+inputs);
	  }
	  
	  @Test(dataProvider="provideTestDataEnhanced", dependsOnMethods={"testSlotAvailability"})
	  public void testSetTemperatureModeBlockWithTestDataEnhanced(List<String> inputs) throws BlockExecutionException {
		  //BSetTemperatureMode setTempModeBlock = new BSetTemperatureMode();
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.sysSwitch.getName(), inputs.get(1));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.cmdMode.getName(), inputs.get(2));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.supplyTemp.getName(), inputs.get(3));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.spaceTemp.getName(), inputs.get(4));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.effHeatSP.getName(), inputs.get(5));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.effCoolSP.getName(), inputs.get(6));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.allowAutoChange.getName(), inputs.get(7));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.controlType.getName(), inputs.get(8));
		  setupNumericSlot(setTempModeBlock1, BSetTemperatureMode.behaviorType.getName(), "1");
		  
		  setTempModeBlock1.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(setTempModeBlock1.getEFF_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), 0.1, "Failed to verify TempSetPt for inputData: "+inputs);	  
		  Assert.assertEquals(setTempModeBlock1.getEFF_TEMP_MODE().getValue().getOrdinal(), TestDataHelper.getDouble(inputs.get(10), 0.0d), 0.1, "Failed to verify TempMode for inputData: "+inputs);
	  }
	  
	  /**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param setTempModeBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BSetTemperatureMode setTempModeBlock, final String slotName, final String inputValue){
			if(TestDataHelper.isConnected(inputValue)){
				BNumericConst nm1 = new BNumericConst();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				Type srcType = nm1.getOut().getType();
				Type targetType = setTempModeBlock.getProperty(slotName).getType();			
				BConverter converter = null;
				if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
                     converter = new BStatusNumericToHonStatusNumeric();
                     BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),setTempModeBlock.getSlot(slotName),converter);
                     conversionLink.setEnabled(true);
                     setTempModeBlock.add("Link?",conversionLink );                    
                     conversionLink.activate();
                     nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				} else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
					converter = new BStatusNumericToFiniteStatusBoolean();
					BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),setTempModeBlock.getSlot(slotName),converter);
					conversionLink.setEnabled(true);
					setTempModeBlock.add("Link?", conversionLink);				
					conversionLink.activate();
				}else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusSetTemperatureModeEnum.TYPE)) {				
					converter = new BStatusNumericToStatusSetTemperatureModeEnumConveter();
					BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),setTempModeBlock.getSlot(slotName),converter);
					conversionLink.setEnabled(true);
					if(setTempModeBlock.get("Link"+slotName)!=null) {
						setTempModeBlock.remove("Link"+slotName);
					}
					setTempModeBlock.add("Link"+slotName,conversionLink );		
					conversionLink.activate();
				} else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusBoolean.TYPE)) {				
					converter = new BStatusNumericToStatusBoolean();
					BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),setTempModeBlock.getSlot(slotName),converter);
					conversionLink.setEnabled(true);
					setTempModeBlock.add("Link?", conversionLink);				
					conversionLink.activate();
				} else{
					setTempModeBlock.linkTo(nm1, nm1.getSlot("out"), setTempModeBlock.getSlot(slotName));
				}			
				return;
			}

		  switch (slotName) {
			  case "sysSwitch":
					if(TestDataHelper.isUnconnected(inputValue)){
						setTempModeBlock.setSysSwitch(new BStatusSetTemperatureModeEnum(StatusSetTemperatureModeEnumsConverter.calculateForSystemSwitchEnum(BSystemSwitchEnum.SS_OTHERS), BStatus.nullStatus));
					}else
					setTempModeBlock.setSysSwitch(new BStatusSetTemperatureModeEnum(StatusSetTemperatureModeEnumsConverter.calculateForSystemSwitchEnum((int)TestDataHelper.getDouble(inputValue, 0))));
				  break;
			  case "cmdMode":
					if(TestDataHelper.isUnconnected(inputValue)){
						setTempModeBlock.setCmdMode(new BStatusSetTemperatureModeEnum(StatusSetTemperatureModeEnumsConverter.calculateForCommandModeEnum(BCommandModeEnum.CMD_AUTO_MODE), BStatus.nullStatus));
					}else
					setTempModeBlock.setCmdMode(new BStatusSetTemperatureModeEnum(StatusSetTemperatureModeEnumsConverter.calculateForCommandModeEnum((int)TestDataHelper.getDouble(inputValue, 0))));
				  break;
			  case "supplyTemp":
				  setTempModeBlock.setSupplyTemp(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
			  case "spaceTemp":
				  setTempModeBlock.setSpaceTemp(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
			  case "effHeatSP":
				  setTempModeBlock.setEffHeatSP(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
			  case "effCoolSP":
				  setTempModeBlock.setEffCoolSP(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
			  case "allowAutoChange":
				  setTempModeBlock.setAllowAutoChange(TestDataHelper.getHonStatusBoolean(inputValue));
				  break;
			  case "controlType":
				  setTempModeBlock.setControlType(BControlTypeEnum.make(TestDataHelper.getInt(inputValue, 0)));
				  break;
			  case "behaviorType":
				  setTempModeBlock.setBehaviorType(BBehaviorTypeEnum.make(TestDataHelper.getInt(inputValue, 0)));
				  break;
		  }
	  }

		@Test
		public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
			BSetTemperatureMode setTempMode = new BSetTemperatureMode();
			executionParams.setIterationInterval(1000);
			ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
					"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/SetTemperatureMode_TestData.csv");
			for (List<String> inputs : readTestDataFromCSVFile) {
				  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.sysSwitch.getName(), inputs.get(1));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.cmdMode.getName(), inputs.get(2));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.supplyTemp.getName(), inputs.get(3));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.spaceTemp.getName(), inputs.get(4));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.effHeatSP.getName(), inputs.get(5));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.effCoolSP.getName(), inputs.get(6));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.allowAutoChange.getName(), inputs.get(7));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.controlType.getName(), inputs.get(8));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.behaviorType.getName(), "0");
				  setTempMode.executeHoneywellComponent(executionParams);
				  if(inputs.get(11).equals("376")) {
					  break;
				  }
				  Assert.assertEquals(setTempMode.getEFF_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), 0.1, "Failed to verify TempSetPt for inputData: "+inputs);	  
				  Assert.assertEquals(setTempMode.getEFF_TEMP_MODE().getValue().getOrdinal(), TestDataHelper.getDouble(inputs.get(10), 0.0d), 0.1, "Failed to verify TempMode for inputData: "+inputs);
			}

			BogFileUtil bogUtil = new BogFileUtil();
			try {
				File bogFile = bogUtil.saveComponentToBogFile("SetTemeratureMode", setTempMode);
				BSetTemperatureMode setTempModeSaved = (BSetTemperatureMode) bogUtil.getComponentFromBogFile(bogFile);
				
				Assert.assertEquals(setTempModeSaved.getEFF_TEMP_MODE().getValue().getOrdinal(), 255, 0.1, "Failed to verify Set Temperature Mode");
				setTempModeSaved.initHoneywellComponent(null);
				Assert.assertEquals(setTempModeSaved.getEFF_TEMP_MODE().getValue().getOrdinal(), 255, 0.1, "After init, failed to verify Count");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		
		@Test
		public void testDeviceRestartScenarioEnhanced() throws BlockExecutionException, BlockInitializationException {
			BSetTemperatureMode setTempMode = new BSetTemperatureMode();
			executionParams.setIterationInterval(1000);
			ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
					"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/New_SetTemperatureMode_TestData.csv");
			for (List<String> inputs : readTestDataFromCSVFile) {
				  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.sysSwitch.getName(), inputs.get(1));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.cmdMode.getName(), inputs.get(2));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.supplyTemp.getName(), inputs.get(3));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.spaceTemp.getName(), inputs.get(4));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.effHeatSP.getName(), inputs.get(5));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.effCoolSP.getName(), inputs.get(6));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.allowAutoChange.getName(), inputs.get(7));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.controlType.getName(), inputs.get(8));
				  setupNumericSlot(setTempMode, BSetTemperatureMode.behaviorType.getName(), "1");
				  setTempMode.executeHoneywellComponent(executionParams);
				  if(inputs.get(11).equals("376")) {
					  break;
				  }
				  Assert.assertEquals(setTempMode.getEFF_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), 0.1, "Failed to verify TempSetPt for inputData: "+inputs);	  
				  Assert.assertEquals(setTempMode.getEFF_TEMP_MODE().getValue().getOrdinal(), TestDataHelper.getDouble(inputs.get(10), 0.0d), 0.1, "Failed to verify TempMode for inputData: "+inputs);
			}

			BogFileUtil bogUtil = new BogFileUtil();
			try {
				File bogFile = bogUtil.saveComponentToBogFile("SetTemeratureMode", setTempMode);
				BSetTemperatureMode setTempModeSaved = (BSetTemperatureMode) bogUtil.getComponentFromBogFile(bogFile);
				
				Assert.assertEquals(setTempModeSaved.getEFF_TEMP_MODE().getValue().getOrdinal(), 255, 0.1, "Failed to verify Set Temperature Mode");
				setTempModeSaved.initHoneywellComponent(null);
				Assert.assertEquals(setTempModeSaved.getEFF_TEMP_MODE().getValue().getOrdinal(), 255, 0.1, "After init, failed to verify Count");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}


		//@Test
		public void testLinkRules() {
			BSetTemperatureMode setTempModeBlock = new BSetTemperatureMode();
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.sysSwitch, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.cmdMode, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.supplyTemp, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.spaceTemp, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.effHeatSP, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.effCoolSP, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.allowAutoChange, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.controlType, false);
			checkOutgoingLink(setTempModeBlock, BSetTemperatureMode.behaviorType, false);
		}

		//@Test
		public void testLinkRules1() {
			BSetTemperatureMode target = new BSetTemperatureMode();
			BMaximum source = new BMaximum();		
			LinkCheck linkCheck;
			
			try {
			linkCheck = source.checkLink(target, target.getSlot("sysSwitch"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			} catch (Exception e) {
				Assert.assertTrue(true);
			}

			try {
			linkCheck = source.checkLink(target, target.getSlot("cmdMode"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			} catch (Exception e) {
				Assert.assertTrue(true);
			}
			
			linkCheck = source.checkLink(target, target.getSlot("supplyTemp"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			
			linkCheck = source.checkLink(target, target.getSlot("spaceTemp"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			
			linkCheck = source.checkLink(target, target.getSlot("effHeatSP"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			
			linkCheck = source.checkLink(target, target.getSlot("effCoolSP"), source.getSlot("in1"), null);
			Assert.assertFalse(linkCheck.isValid());
			
			try {
			linkCheck = source.checkLink(target, target.getSlot("allowAutoChange"), source.getSlot("in1"), null);
			} catch (Exception e) {
				Assert.assertTrue(true);
			}
			
			try {
				linkCheck = source.checkLink(target, target.getSlot("controlType"), source.getSlot("in1"), null);
			} catch (Exception e) {
				Assert.assertTrue(true);
			}
			try {
				linkCheck = source.checkLink(target, target.getSlot("behaviorType"), source.getSlot("in1"), null);
			} catch (Exception e) {
				Assert.assertTrue(true);
			}
			
			BComponent c = new BComponent();
			c.add("out1", BControlTypeEnum.Cvahu);
			linkCheck = target.checkLink(c, c.getSlot("out1"), target.getSlot("controlType"), null);
			Assert.assertFalse(linkCheck.isValid());
			
			try {		
			linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("sysSwitch"), null);
			Assert.assertTrue(linkCheck.isValid());
			} catch (Exception e) {
				Assert.assertTrue(true);
			}
			
			linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("supplyTemp"), null);
			Assert.assertTrue(linkCheck.isValid());

			linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("EFF_SETPT"), null);
			Assert.assertFalse(linkCheck.isValid());

			linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("EFF_TEMP_MODE"), null);
			Assert.assertFalse(linkCheck.isValid());
}
		
		@Test
		public void testConfigProperties() {
			List<Property> configList = setTempModeBlock.getConfigPropertiesList();		
			Assert.assertEquals(configList.get(0).getName(), BSetTemperatureMode.controlType.getName());
			Assert.assertEquals(configList.get(1).getName(), BSetTemperatureMode.behaviorType.getName());
		}
		
		private void checkOutgoingLink(BSetTemperatureMode block, Property prop, boolean isLinkValid) {
			LinkCheck checkLink = setTempModeBlock.checkLink(setTempModeBlock, setTempModeBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
			Assert.assertEquals(checkLink.isValid(), isLinkValid);
		}
		
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = setTempModeBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = { BSetTemperatureMode.EFF_SETPT.getName(), BSetTemperatureMode.EFF_TEMP_MODE.getName() };
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
		
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = setTempModeBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BSetTemperatureMode.sysSwitch.getName(), BSetTemperatureMode.cmdMode.getName(), BSetTemperatureMode.supplyTemp.getName(), BSetTemperatureMode.spaceTemp.getName(), BSetTemperatureMode.effHeatSP.getName(),
				BSetTemperatureMode.effCoolSP.getName(), BSetTemperatureMode.allowAutoChange.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	  private BSetTemperatureMode setTempModeBlock;
	  private BSetTemperatureMode setTempModeBlock1;
	  private BExecutionParams executionParams;


}
