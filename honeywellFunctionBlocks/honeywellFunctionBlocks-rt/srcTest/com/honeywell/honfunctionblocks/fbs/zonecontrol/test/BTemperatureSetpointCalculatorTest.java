/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToTempSetptCalcEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetpointTypeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BStatusTempSetpointCalcEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BTemperatureSetpointCalculator;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing TemperatureSetpointCalculator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-265
 * 
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Feb 9, 2018
 */
@NiagaraType
@SuppressWarnings({"squid:S1845", "squid:S1213", "squid:S2387", "squid:MaximumInheritanceDepth"})
public class BTemperatureSetpointCalculatorTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BTemperatureSetpointCalculatorTest(2979906276)1.0$ @*/
/* Generated Fri Feb 02 13:57:44 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTemperatureSetpointCalculatorTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass
  public void setUp() {	
	  tspcBlock = new BTemperatureSetpointCalculator();
	  executionParams = new BExecutionParams();
  }
  
  @AfterClass
  public void tearDown() {
	  tspcBlock = null;
	  executionParams = null;
  }

  @DataProvider(name="provideInSlotNames")
  public Object[] createInputSlotNames() {
	  return new Object[]{"EffOccuCurrentState", "ScheduleNextState", "ScheduleTUNCOS", "Setpoint", "HeatRampRate", "CoolRampRate", "ManualOverrideState", 
			  "occupiedCool", "standbyCool", "unoccupiedCool", "occupiedHeat", "standbyHeat", "unoccupiedHeat"};	  
  }
  
  @DataProvider(name="provideConfigSlotNames")
  public Object[] createConfigSlotNames() {
	  return new Object[] {"SetpointType"};
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[] createOutputSlotNames() {
	  return new Object[] {"EFF_HEAT_SETPT", "EFF_COOL_SETPT"};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[] createExecOrderSlotName() {
	  return new Object[] {"ExecutionOrder", "toolVersion"};
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[] createAllSlotNames() {
	  List<Object> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[] invalidSlotNames() {
	  return new Object[]{"EffectiveOccupiedCurrentState", "setpoint"};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(tspcBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(tspcBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "temperature_setpoint_calculator.png");
	  BIcon actualFbIcon = tspcBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  tspcBlock.setIcon(expectedFbIcon);
	  actualFbIcon = tspcBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
    
  @DataProvider(name = "effOccuCurrentStateEnumValues")
  public Object[][] getDataToTestEnumOrdinalForEffOccuCurrentState(){
	  return new Object[][] {{Integer.valueOf(BEffectiveOccupancyEnum.OCCUPIED),BEffectiveOccupancyEnum.Occupied},
		  {Integer.valueOf(BEffectiveOccupancyEnum.UNOCCUPIED),BEffectiveOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BEffectiveOccupancyEnum.STANDBY),BEffectiveOccupancyEnum.Standby},
		  {Integer.valueOf(BEffectiveOccupancyEnum.BYPASS),BEffectiveOccupancyEnum.Bypass}};
  }

  @Test(dataProvider="effOccuCurrentStateEnumValues")
  public void testSettingValueInEffOccuCurrentState(int ordinal, BEffectiveOccupancyEnum effOccuCurrentStateEnum) {
	  tspcBlock.setEffOccuCurrentState(new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BEffectiveOccupancyEnum.make(tspcBlock.getEffOccuCurrentState().getValue().getOrdinal()), effOccuCurrentStateEnum);
  }
  
  @DataProvider(name = "schedNextStateEnumValues")
  public Object[][] getDataToTestEnumOrdinalForSchedNextState(){
	  return new Object[][] {{Integer.valueOf(BScheduledStateEnum.OCCUPIED),BScheduledStateEnum.Occupied},
		  {Integer.valueOf(BScheduledStateEnum.UNOCCUPIED),BScheduledStateEnum.Unoccupied},
		  {Integer.valueOf(BScheduledStateEnum.STANDBY),BScheduledStateEnum.Standby},
		  {Integer.valueOf(BScheduledStateEnum.NULL),BScheduledStateEnum.Null}};
  }

  @Test(dataProvider="schedNextStateEnumValues")
  public void testSettingValueInSchedNextState(int ordinal, BScheduledStateEnum schedStateEnum) {
	  tspcBlock.setScheduleNextState(new BStatusTempSetpointCalcEnum(BScheduledStateEnum.make(ordinal)));
	  Assert.assertEquals(BScheduledStateEnum.make(tspcBlock.getScheduleNextState().getValue().getOrdinal()), schedStateEnum);
  }
  
  @DataProvider(name = "manOverrideStateEnumValues")
  public Object[][] getDataToTestEnumOrdinalForManOverrideState(){
	  return new Object[][] {{Integer.valueOf(BOccupancyEnum.OCCUPIED),BOccupancyEnum.Occupied},
		  {Integer.valueOf(BOccupancyEnum.UNOCCUPIED),BOccupancyEnum.Unoccupied},
		  {Integer.valueOf(BOccupancyEnum.STANDBY),BOccupancyEnum.Standby},
		  {Integer.valueOf(BOccupancyEnum.BYPASS),BOccupancyEnum.Bypass},
		  {Integer.valueOf(BOccupancyEnum.NULL),BOccupancyEnum.Null}};
  }

  @Test(dataProvider="manOverrideStateEnumValues")
  public void testSettingValueInManOverrideState(int ordinal, BOccupancyEnum occEnum) {
	  tspcBlock.setManualOverrideState(new BStatusTempSetpointCalcEnum(BOccupancyEnum.make(ordinal)));
	  Assert.assertEquals(BOccupancyEnum.make(tspcBlock.getManualOverrideState().getValue().getOrdinal()), occEnum);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[] sampleValues() {
	  return new Object[] {-23, 0, 60, 32767, 32768, 40000, Double.POSITIVE_INFINITY, Double.NEGATIVE_INFINITY, Double.NaN};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInTSPCBlock(double snValue) {
	  tspcBlock.setScheduleTUNCOS(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getScheduleTUNCOS().getValue(), snValue, 0.1);
	  
	  tspcBlock.setSetpoint(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getSetpoint().getValue(), snValue, 0.1);
	  
	  tspcBlock.setHeatRampRate(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getHeatRampRate().getValue(), snValue, 0.1);
	  
	  tspcBlock.setCoolRampRate(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getCoolRampRate().getValue(), snValue, 0.1);
	  
	  tspcBlock.setOccupiedCool(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getOccupiedCool().getValue(), snValue, 0.1);
	  
	  tspcBlock.setStandbyCool(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getStandbyCool().getValue(), snValue, 0.1);
	  
	  tspcBlock.setUnoccupiedCool(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getUnoccupiedCool().getValue(), snValue, 0.1);
	  
	  tspcBlock.setOccupiedHeat(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getOccupiedHeat().getValue(), snValue, 0.1);
	  
	  tspcBlock.setStandbyHeat(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getStandbyHeat().getValue(), snValue, 0.1);
	  
	  tspcBlock.setUnoccupiedHeat(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getUnoccupiedHeat().getValue(), snValue, 0.1);
	  
	  tspcBlock.setSetpointType(BSetpointTypeEnum.Global);
	  Assert.assertEquals(tspcBlock.getSetpointType(), BSetpointTypeEnum.Global);
	  
	  tspcBlock.setSetpointType(BSetpointTypeEnum.Custom);
	  Assert.assertEquals(tspcBlock.getSetpointType(), BSetpointTypeEnum.Custom);
	  
	  tspcBlock.setEFF_HEAT_SETPT(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getEFF_HEAT_SETPT().getValue(), snValue, 0.1);
	  
	  tspcBlock.setEFF_COOL_SETPT(new BHonStatusNumeric(snValue)); 
	  Assert.assertEquals(tspcBlock.getEFF_COOL_SETPT().getValue(), snValue, 0.1);
  }
  
  @DataProvider(name="provideTestData")
  public Object[][] getTesData() {
		return TestDataHelper.getTestDataInTestNGFormat(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/zonecontrol/test/TemperatureSetpointCalculator_TestData.csv");
  }
  
  @Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
  public void testTSPCBlockWithTestData(List<String> inputs) throws BlockExecutionException {
	  BTemperatureSetpointCalculator tspcTempBlock = new BTemperatureSetpointCalculator();

	  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.EffOccuCurrentState.getName(), inputs.get(1));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.ScheduleNextState.getName(), inputs.get(2));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.ScheduleTUNCOS.getName(), inputs.get(3));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.Setpoint.getName(), inputs.get(4));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.HeatRampRate.getName(), inputs.get(5));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.CoolRampRate.getName(), inputs.get(6));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.ManualOverrideState.getName(), inputs.get(7));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.occupiedCool.getName(), inputs.get(8));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.standbyCool.getName(), inputs.get(9));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.unoccupiedCool.getName(), inputs.get(10));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.occupiedHeat.getName(), inputs.get(11));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.standbyHeat.getName(), inputs.get(12));
	  setSlotValue(tspcTempBlock, BTemperatureSetpointCalculator.unoccupiedHeat.getName(), inputs.get(13));

	  tspcTempBlock.executeHoneywellComponent(executionParams);
	  Assert.assertEquals(tspcTempBlock.getEFF_HEAT_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(14), 0), "Failed for EFF_HEAT_SETPT with inputData: "+inputs);
	  Assert.assertEquals(tspcTempBlock.getEFF_COOL_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(15), 0), "Failed for EFF_COOL_SETPT with inputData: "+inputs);

	  tspcTempBlock = null;
  }
  
  /**
   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
   * @param tspcTempBlock
   * @param slotName
   * @param inputValue
   */
  private void setSlotValue(BTemperatureSetpointCalculator tspcTempBlock, String slotName, String inputValue) {
	  if (TestDataHelper.isConnected(inputValue)) {
		  BNumericConst nm1 = new BNumericConst();
		  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
		  Type srcType = nm1.getOut().getType();
		  Type targetType = tspcTempBlock.getProperty(slotName).getType();			
		  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusTempSetpointCalcEnum.TYPE)) {				
			  BConverter converter = new BStatusNumericToTempSetptCalcEnumConverter();
			  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),tspcTempBlock.getSlot(slotName),converter);
			  conversionLink.setEnabled(true);

			  tspcTempBlock.add("Link?", conversionLink );		
			  conversionLink.activate();
		  }else{
			  tspcTempBlock.linkTo(nm1, nm1.getSlot("out"), tspcTempBlock.getSlot(slotName));
		  }			

		  return;
	  }
	  
	  switch(slotName) {
	  case "EffOccuCurrentState":		  
		  if(TestDataHelper.isUnconnected(inputValue)) {
				tspcTempBlock.setEffOccuCurrentState(new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.nullStatus));
		  } else {
			  tspcTempBlock.setEffOccuCurrentState(new BStatusTempSetpointCalcEnum(
					  BEffectiveOccupancyEnum.make((int) TestDataHelper.getDouble(inputValue, 0)), BStatus.ok));
		  }
		  break;

	  case "ScheduleNextState":
		  if(TestDataHelper.isUnconnected(inputValue)) {
				tspcTempBlock.setScheduleNextState(new BStatusTempSetpointCalcEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus));
		  } else {
			  tspcTempBlock.setScheduleNextState(new BStatusTempSetpointCalcEnum(
					  BScheduledStateEnum.make((int) TestDataHelper.getDouble(inputValue, 0)), BStatus.ok));
		  }
		  break;

	  case "ScheduleTUNCOS":
		  tspcTempBlock.setScheduleTUNCOS(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;

	  case "Setpoint":
		  tspcTempBlock.setSetpoint(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "HeatRampRate":
		  tspcTempBlock.setHeatRampRate(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "CoolRampRate":
		  tspcTempBlock.setCoolRampRate(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "ManualOverrideState":
		  if(TestDataHelper.isUnconnected(inputValue)) {
				tspcTempBlock.setManualOverrideState(new BStatusTempSetpointCalcEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus));
		  } else {
			  tspcTempBlock.setManualOverrideState(new BStatusTempSetpointCalcEnum(
					  BOccupancyEnum.make((int) TestDataHelper.getDouble(inputValue, 0)), BStatus.ok));
		  }
		  break;
		  
	  case "occupiedCool":
		  tspcTempBlock.setOccupiedCool(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "standbyCool":
		  tspcTempBlock.setStandbyCool(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "unoccupiedCool":
		  tspcTempBlock.setUnoccupiedCool(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "occupiedHeat":
		  tspcTempBlock.setOccupiedHeat(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "standbyHeat":
		  tspcTempBlock.setStandbyHeat(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  case "unoccupiedHeat":
		  tspcTempBlock.setUnoccupiedHeat(TestDataHelper.getHonStatusNumeric(inputValue, 0));
		  break;
		  
	  default:
		  break;
	  }
  }
  
  @Test
  public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
	  BTemperatureSetpointCalculator tspcTempBlock = new BTemperatureSetpointCalculator();

	  executionParams.setIterationInterval(1000);
	  tspcTempBlock.setEffOccuCurrentState(new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.make(0)));
	  tspcTempBlock.setScheduleNextState(new BStatusTempSetpointCalcEnum(BScheduledStateEnum.make(0)));
	  tspcTempBlock.setScheduleTUNCOS(new BHonStatusNumeric(65));
	  tspcTempBlock.setSetpoint(new BHonStatusNumeric(70));
	  tspcTempBlock.setHeatRampRate(new BHonStatusNumeric(68));
	  tspcTempBlock.setCoolRampRate(new BHonStatusNumeric(68));
	  tspcTempBlock.setManualOverrideState(new BStatusTempSetpointCalcEnum(BOccupancyEnum.make(255)));
	  tspcTempBlock.setOccupiedCool(new BHonStatusNumeric(75));
	  tspcTempBlock.setStandbyCool(new BHonStatusNumeric(0));
	  tspcTempBlock.setUnoccupiedCool(new BHonStatusNumeric(68));
	  tspcTempBlock.setOccupiedHeat(new BHonStatusNumeric(68));
	  tspcTempBlock.setStandbyHeat(new BHonStatusNumeric(68));
	  tspcTempBlock.setUnoccupiedHeat(new BHonStatusNumeric(0));
	  tspcTempBlock.setSetpointType(BSetpointTypeEnum.Custom);
	  
	  for(int i=0; i<5000; i++) {
		  tspcTempBlock.executeHoneywellComponent(executionParams);
	  }
	  
	  Assert.assertEquals(tspcTempBlock.getEFF_HEAT_SETPT().getValue(), 66.5, 0.1, "Failed for EFF_HEAT_SETPT");
	  Assert.assertEquals(tspcTempBlock.getEFF_COOL_SETPT().getValue(), 73.5, 0.1, "Failed for EFF_COOL_SETPT");
	  
	  BogFileUtil bogUtil = new BogFileUtil();
	  try {
		  File bogFile = bogUtil.saveComponentToBogFile("TemperatureSetpointCalculator", tspcTempBlock);
		  BTemperatureSetpointCalculator tspcSaved = (BTemperatureSetpointCalculator) bogUtil.getComponentFromBogFile(bogFile);

		  Assert.assertEquals(tspcSaved.getEFF_HEAT_SETPT().getValue(), Float.POSITIVE_INFINITY, 0.1, "Failed to verify EFF_HEAT_SETPT after device restart");
		  Assert.assertEquals(tspcSaved.getEFF_COOL_SETPT().getValue(), Float.POSITIVE_INFINITY, 0.1, "Failed to verify EFF_COOL_SETPT after device restart");
	  } catch (IOException e) {
		  e.printStackTrace();
	  }
  }
  
  //@Test(dataProvider="provideInSlotNames")
  public void testLinkRulesForInSlot(String slotName) {
	  BTemperatureSetpointCalculator tempBlock = new BTemperatureSetpointCalculator();
	  LinkCheck checkLink = tempBlock.checkLink(tspcBlock, tspcBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
	  Assert.assertEquals(checkLink.isValid(), false);
  }

  //@Test(dataProvider="provideConfigSlotNames")
  public void testLinkRulesForConfigSlots(String slotName) {
	  BTemperatureSetpointCalculator tempBlock = new BTemperatureSetpointCalculator();
	  LinkCheck checkLink = tempBlock.checkLink(tspcBlock, tspcBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
	  Assert.assertEquals(checkLink.isValid(), false);
  }

  //@Test(dataProvider="provideOutputSlotNames")
  public void testLinkRulesForOutSlots(String slotName) {
	  BTemperatureSetpointCalculator nm = new BTemperatureSetpointCalculator();
	  LinkCheck checkLink = tspcBlock.checkLink(nm, nm.getSlot(slotName), tspcBlock.getSlot(slotName), null);
	  Assert.assertFalse(checkLink.isValid());
  }
  
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = tspcBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BTemperatureSetpointCalculator.EffOccuCurrentState.getName(), BTemperatureSetpointCalculator.ScheduleNextState.getName(), BTemperatureSetpointCalculator.ScheduleTUNCOS.getName(),
				BTemperatureSetpointCalculator.Setpoint.getName(), BTemperatureSetpointCalculator.HeatRampRate.getName(), BTemperatureSetpointCalculator.CoolRampRate.getName(), BTemperatureSetpointCalculator.ManualOverrideState.getName(),
				BTemperatureSetpointCalculator.occupiedCool.getName(), BTemperatureSetpointCalculator.occupiedHeat.getName(), BTemperatureSetpointCalculator.standbyCool.getName(), BTemperatureSetpointCalculator.standbyHeat.getName(),
				BTemperatureSetpointCalculator.unoccupiedCool.getName(), BTemperatureSetpointCalculator.unoccupiedHeat.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = tspcBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BTemperatureSetpointCalculator.EFF_COOL_SETPT.getName(),BTemperatureSetpointCalculator.EFF_HEAT_SETPT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
  
  private BTemperatureSetpointCalculator tspcBlock;
  private BExecutionParams executionParams;
  
}
