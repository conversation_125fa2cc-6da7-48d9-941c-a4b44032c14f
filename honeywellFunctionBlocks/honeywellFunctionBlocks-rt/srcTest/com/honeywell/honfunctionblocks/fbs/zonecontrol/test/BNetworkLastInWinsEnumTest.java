/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.zonecontrol.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.zonecontrol.BNetworkLastInWinsEnum;

/**
 * Testing OccupancyArbitrator block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - <PERSON><PERSON><PERSON> B.
 * @since Feb 12, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BNetworkLastInWinsEnumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.zonecontrol.test.BNetworkLastInWinsEnumTest(**********)1.0$ @*/
/* Generated Mon Feb 12 10:40:14 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNetworkLastInWinsEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
	  return new Object[][] {{Integer.valueOf(BNetworkLastInWinsEnum.LAST_IN_WINS),BNetworkLastInWinsEnum.LastInWins},
		  {Integer.valueOf(BNetworkLastInWinsEnum.NETWORK_WINS),BNetworkLastInWinsEnum.NetworkWins}};
	}

	@Test(dataProvider = "enumOrdinal")
	public void testOperationByMakeOrdinal(Integer ordinal, BNetworkLastInWinsEnum oe) {
		Assert.assertEquals(BNetworkLastInWinsEnum.make(ordinal.intValue()), oe);
	}
	
	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		 return new Object[][] {{BNetworkLastInWinsEnum.LastInWins.getTag(),BNetworkLastInWinsEnum.LastInWins},
			  {BNetworkLastInWinsEnum.NetworkWins.getTag(),BNetworkLastInWinsEnum.NetworkWins}};
	}
	
	@Test(dataProvider = "enumTag")
	public void testOperationByMakeTag(String tag, BNetworkLastInWinsEnum oe) {
		Assert.assertEquals(BNetworkLastInWinsEnum.make(tag), oe);
	}
}
