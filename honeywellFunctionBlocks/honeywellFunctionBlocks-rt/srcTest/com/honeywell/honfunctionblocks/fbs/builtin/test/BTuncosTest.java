/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.builtin.test;

import java.util.Arrays;
import java.util.List;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BIcon;
import javax.baja.sys.BRelTime;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.builtin.BTuncos;

/**
 * BTuncos FB test
 * 
 * <AUTHOR> - <PERSON>
 * @since 05-Sep-2025
 */
@NiagaraType
public class BTuncosTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.builtin.test.BTuncosTest(**********)1.0$ @*/
/* Generated Fri Sep 05 10:19:22 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTuncosTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	private BTuncos tuncosBlock;
	private BExecutionParams executionParams;
	
	@BeforeClass(alwaysRun = true)
	public void setUp() {
		tuncosBlock = new BTuncos();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass(alwaysRun = true)
	public void tearDown() {
		tuncosBlock = null;
		executionParams = null;
	}
	
	@DataProvider(name = "provideInSlotNames")
	public Object[][] createInputSlotNames() {
		return new Object[][] { { "nextTime" } };
	}
	
	@DataProvider(name = "provideOutputSlotNames")
	public Object[][] createOutputSlotNames() {
		return new Object[][] { { "TUNCOS" } };
	}
	
	@DataProvider(name = "provideMiscSlotNames")
	public Object[][] createExecOrderSlotName() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" }, { "icon" } };
	}
	
	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	}
	
	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "In" }, { "onValue" }, { "OnVal" }, { "OffVal" }, { "OffValue" }, { "MinOn" }, { "invalidFlag" }, { "TailOperation" }, { "IgnoreInvalidInput" } };
	}
	
	@Test(dataProvider = "provideInvalidSlotNames")
	public void testInvalidSlots(String slotName) {
		Assert.assertNull(tuncosBlock.getSlot(slotName));
	}
	
	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		Assert.assertNotNull(tuncosBlock.getSlot(slotName));
	}
	
	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "tuncos.png");
		BIcon actualFbIcon = tuncosBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	
		// check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		tuncosBlock.setIcon(expectedFbIcon);
		actualFbIcon = tuncosBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	@Test
	public void testValueUpdate() {
		BAbsTime currentTime = BAbsTime.now();
		BRelTime timeToAdd = BRelTime.makeHours(5);
		BAbsTime time1 = currentTime.add(timeToAdd);
		
		tuncosBlock.setNextTime(time1);
		Assert.assertEquals(time1, tuncosBlock.getNextTime(), "Next time not equal");
		
		tuncosBlock.executeBlock(executionParams);
		Assert.assertEquals((int) tuncosBlock.getTUNCOS().getValue(), timeToAdd.getMinutes(), 1, "TUNCOS not correct");
		
		currentTime = BAbsTime.now();
		timeToAdd = BRelTime.makeHours(200);
		time1 = currentTime.add(timeToAdd);
		
		tuncosBlock.setNextTime(time1);
		Assert.assertEquals(time1, tuncosBlock.getNextTime(), "Next time not equal");
		
		tuncosBlock.executeBlock(executionParams);
		Assert.assertEquals((int) tuncosBlock.getTUNCOS().getValue(), 11520, 1, "TUNCOS not correct");
		
		currentTime = BAbsTime.now();
		timeToAdd = BRelTime.makeHours(1);
		time1 = currentTime.subtract(timeToAdd);
		
		tuncosBlock.setNextTime(time1);
		Assert.assertEquals(time1, tuncosBlock.getNextTime(), "Next time not equal");
		
		tuncosBlock.executeBlock(executionParams);
		Assert.assertEquals((int) tuncosBlock.getTUNCOS().getValue(), 0, "TUNCOS not correct");
	}
  
}
