/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BStation;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.enums.BPidOutputRangeEnum;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.control.BDirectReverseSignOfTREnum;
import com.honeywell.honfunctionblocks.fbs.control.BPid;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of PID block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: F1PLT-ATC-186
 * <AUTHOR> - RSH.Lakshminarayanan
 *
 */

@NiagaraType
public class BPidTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BPidTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPidTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	BPid pidBlock;
	
	@BeforeClass(alwaysRun=true)
	public void setUp() throws Exception{
		pidBlock = new BPid();
		pidBlock.started();
	}
	
	@DataProvider(name = "provideInSlotNames")
	public Object[][] inputSlotNames() {
		return new Object[][]{{"sensor"}, {"setPt"}, {"tr"}, {"intgTime"}, {"dervTime"}, {"deadBand"}, {"dbDelay"}, {"disable"}};
	}
	
	@DataProvider(name = "provideOutSlotNames")
	public Object[][] outputSlotNames() {
		return new Object[][]{{"OUTPUT"}};
	}
	
	@DataProvider(name = "provideConfigSlotNames")
	public Object[][] configSlotNames() {
		return new Object[][]{{"revAct"}, {"bias"}};
	}
	
	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "Sensor" }, { "SetPt" }, { "SetPoint" }, { "IntgTime" }, { "IntegralTime" },
				{ "derivTime" }, { "DerivativeTime" }, { "Disable" }, { "TR" }, { "output" }, { "RevAct" },
				{ "ReverseAct" }, { "Bias" }, { "BIAS" }, { "DeadBand" }, { "deadband" } };
	}
	
	@DataProvider(name="provideAllSlotNames")
	public Object[][] allValidSlotNames(){
		List<Object[]> result = Lists.newArrayList();
		result.addAll(Arrays.asList(inputSlotNames()));
		result.addAll(Arrays.asList(outputSlotNames()));
		result.addAll(Arrays.asList(configSlotNames()));
		return result.toArray(new Object[result.size()][]);
	}
	
	@Test(dataProvider="provideAllSlotNames")
	public void testSlotAvailability(String slotName){
		Assert.assertNotNull(pidBlock.getSlot(slotName));
	}
	
	@Test(dataProvider="provideInvalidSlotNames")
	public void testInvalidSlots(String slotName){
		Assert.assertNull(pidBlock.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "pid.png");
		BIcon actualFbIcon = pidBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		pidBlock.setIcon(expectedFbIcon);
		actualFbIcon = pidBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	  @DataProvider(name = "provideSampleValues")
	  public Object[][] sampleValues() {
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {Float.NEGATIVE_INFINITY}, {Float.MAX_VALUE}};
	  }

	  @Test(dataProvider="provideSampleValues")
	  public void testSettingValues(double snValue) {
		  pidBlock.setSensor(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getSensor().getValue(), snValue, 0.1);
		  
		  pidBlock.setSetPt(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getSetPt().getValue(), snValue, 0.1);
		  
		  pidBlock.setTr(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getTr().getValue(), snValue, 0.1);
		  
		  pidBlock.setIntgTime(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getIntgTime().getValue(), snValue, 0.1);
		  
		  pidBlock.setDervTime(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getDervTime().getValue(), snValue, 0.1);
		  
		  pidBlock.setDeadBand(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getDeadBand().getValue(), snValue, 0.1);
		  
		  pidBlock.setDbDelay(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getDbDelay().getValue(), snValue, 0.1);
		  
		  pidBlock.setOUTPUT(new BHonStatusNumeric(snValue, BStatus.ok));
		  Assert.assertEquals(pidBlock.getOUTPUT().getValue(), snValue, 0.1);
		  
		  pidBlock.setBias(5);
		  Assert.assertEquals(pidBlock.getBias(), 5, 0.1);
	  }
	  
	  @DataProvider(name="provideTestData_0to100")
		public Object[][] getTestData_0_to_100(){
			BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Pid_TestData_0to100.csv").get();
			CsvReader readValidInputs;
			ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
			try {
				readValidInputs = new CsvReader(file.getInputStream());
				List<String> rec;
				while ((rec = readValidInputs.read()) != null) {
					validInputs.add(rec);
				}
				readValidInputs.close();
			} catch (IOException e) {
				validInputs = null;
			}
			
			Object[][] objArray = new Object[validInputs.size()][];
			for(int i=0;i< validInputs.size();i++){
				objArray[i] = new Object[1];
				objArray[i][0] = validInputs.get(i);
			} 
			
			return objArray;
		}
	  
	  @DataProvider(name="provideTestData_0to100_Negate")
		public Object[][] getNegateTestData_0_to_100(){
			BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Neg_Pid_TestData_0to100.csv").get();
			CsvReader readValidInputs;
			ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
			try {
				readValidInputs = new CsvReader(file.getInputStream());
				List<String> rec;
				while ((rec = readValidInputs.read()) != null) {
					validInputs.add(rec);
				}
				readValidInputs.close();
			} catch (IOException e) {
				validInputs = null;
			}
			
			Object[][] objArray = new Object[validInputs.size()][];
			for(int i=0;i< validInputs.size();i++){
				objArray[i] = new Object[1];
				objArray[i][0] = validInputs.get(i);
			} 
			
			return objArray;
		}

	
	@DataProvider(name="provideTestData_-200to200")
	public Object[][] getTestData(){
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Pid_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		
		Object[][] objArray = new Object[validInputs.size()][];
		for(int i=0;i< validInputs.size();i++){
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		} 
		
		return objArray;
	}
	
	@DataProvider(name="provideTestData_-200to200_Negate")
	public Object[][] getTestData_Negate(){
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Neg_Pid_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		
		Object[][] objArray = new Object[validInputs.size()][];
		for(int i=0;i< validInputs.size();i++){
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		} 
		
		return objArray;
	}
	
	/**
	 * Testing the block execution with dataProvider which reads data from CSV
	 * file and passes each row as List<String> The advantage in this is that it
	 * can generate a readable report
	 * 
	 * @param inputs
	 * @throws Exception 
	 */
	@Test(dataProvider="provideTestData_-200to200", dependsOnMethods={"testSlotAvailability"})
	public void testPidLogicWithTestData(List<String> inputs) throws Exception{
		BPid pidBlock = new BPid();
		// Setting the Output Range as DEFAULT
		pidBlock.setOutputRange(BPidOutputRangeEnum.DEFAULT);
		double dbDelayInSec = 0f;
		BExecutionParams executionParams = new BExecutionParams();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
		setupNumericSlot(pidBlock, BPid.sensor.getName(), inputs.get(1));
		setupNumericSlot(pidBlock, BPid.setPt.getName(), inputs.get(2));
		setupNumericSlot(pidBlock, BPid.disable.getName(), inputs.get(3));
		setupNumericSlot(pidBlock, BPid.tr.getName(), inputs.get(4));
		setupNumericSlot(pidBlock, BPid.intgTime.getName(), inputs.get(5));
		setupNumericSlot(pidBlock, BPid.dervTime.getName(), inputs.get(6));
		setupNumericSlot(pidBlock, BPid.deadBand.getName(), inputs.get(7));
		setupNumericSlot(pidBlock, BPid.dbDelay.getName(), inputs.get(8));
		pidBlock.setRevAct(BDirectReverseSignOfTREnum.make(TestDataHelper.getInt(inputs.get(9), 0)));
		setupNumericSlot(pidBlock, BPid.bias.getName(), inputs.get(10));
		
		dbDelayInSec = TestDataHelper.getDouble(inputs.get(8), 0);
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0;i<dbDelayInSec;i++) 
				pidBlock.executeHoneywellComponent(executionParams);
		}
		
		pidBlock.executeHoneywellComponent(executionParams);
		
		Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
		
		
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0; i<10; i++) {
				Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
			}
		}		
		
		pidBlock = null;
	}
	
	@Test(dataProvider="provideTestData_-200to200_Negate", dependsOnMethods={"testSlotAvailability"})
	public void testPidLogicWithTestData_Negate(List<String> inputs) throws Exception{
		BPid pidBlock = new BPid();
		// Setting the Output Range as DEFAULT
		pidBlock.setOutputRange(BPidOutputRangeEnum.DEFAULT);
		double dbDelayInSec = 0f;
		BExecutionParams executionParams = new BExecutionParams();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
		setupNumericSlot(pidBlock, BPid.sensor.getName(), inputs.get(1));
		setupNumericSlot(pidBlock, BPid.setPt.getName(), inputs.get(2));
		setupNumericSlot(pidBlock, BPid.disable.getName(), inputs.get(3));
		boolean negate = TestDataHelper.getBoolean(inputs.get(12));
		((BNegatableFiniteStatusBoolean)pidBlock.getDisable()).setNegate(negate);
		setupNumericSlot(pidBlock, BPid.tr.getName(), inputs.get(4));
		setupNumericSlot(pidBlock, BPid.intgTime.getName(), inputs.get(5));
		setupNumericSlot(pidBlock, BPid.dervTime.getName(), inputs.get(6));
		setupNumericSlot(pidBlock, BPid.deadBand.getName(), inputs.get(7));
		setupNumericSlot(pidBlock, BPid.dbDelay.getName(), inputs.get(8));
		pidBlock.setRevAct(BDirectReverseSignOfTREnum.make(TestDataHelper.getInt(inputs.get(9), 0)));
		setupNumericSlot(pidBlock, BPid.bias.getName(), inputs.get(10));
		
		dbDelayInSec = TestDataHelper.getDouble(inputs.get(8), 0);
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0;i<dbDelayInSec;i++) 
				pidBlock.executeHoneywellComponent(executionParams);
		}
		
				pidBlock.executeHoneywellComponent(executionParams);
		
		Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
		
		
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0; i<10; i++) {
				Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
			}
		}		
		
		pidBlock = null;
	}
	
	
	
	@Test(dataProvider="provideTestData_0to100", dependsOnMethods={"testSlotAvailability"})
	public void testPidLogicWithTestData_0to100(List<String> inputs) throws Exception{
		BPid pidBlock = new BPid();
		// Setting the Output Range as 0 to 100
		pidBlock.setOutputRange(BPidOutputRangeEnum.pidOutputRange0to100);
		double dbDelayInSec = 0f;
		BExecutionParams executionParams = new BExecutionParams();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
		setupNumericSlot(pidBlock, BPid.sensor.getName(), inputs.get(1));
		setupNumericSlot(pidBlock, BPid.setPt.getName(), inputs.get(2));
		setupNumericSlot(pidBlock, BPid.disable.getName(), inputs.get(3));
		setupNumericSlot(pidBlock, BPid.tr.getName(), inputs.get(4));
		setupNumericSlot(pidBlock, BPid.intgTime.getName(), inputs.get(5));
		setupNumericSlot(pidBlock, BPid.dervTime.getName(), inputs.get(6));
		setupNumericSlot(pidBlock, BPid.deadBand.getName(), inputs.get(7));
		setupNumericSlot(pidBlock, BPid.dbDelay.getName(), inputs.get(8));
		pidBlock.setRevAct(BDirectReverseSignOfTREnum.make(TestDataHelper.getInt(inputs.get(9), 0)));
		setupNumericSlot(pidBlock, BPid.bias.getName(), inputs.get(10));
		
		dbDelayInSec = TestDataHelper.getDouble(inputs.get(8), 0);
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0;i<dbDelayInSec;i++) 
				pidBlock.executeHoneywellComponent(executionParams);
		}
		
		pidBlock.executeHoneywellComponent(executionParams);
		
		Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
		
		
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0; i<10; i++) {
				Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
			}
		}		
		
		pidBlock = null;
	}
	
	@Test(dataProvider="provideTestData_0to100_Negate", dependsOnMethods={"testSlotAvailability"})
	public void testPidLogicWithTestData_0to100_Negate(List<String> inputs) throws Exception{
		BPid pidBlock = new BPid();
    	// Setting the Output Range as 0 to 100
		pidBlock.setOutputRange(BPidOutputRangeEnum.pidOutputRange0to100);
		double dbDelayInSec = 0f;
		BExecutionParams executionParams = new BExecutionParams();
		executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
		setupNumericSlot(pidBlock, BPid.sensor.getName(), inputs.get(1));
		setupNumericSlot(pidBlock, BPid.setPt.getName(), inputs.get(2));
		setupNumericSlot(pidBlock, BPid.disable.getName(), inputs.get(3));
		boolean negate = TestDataHelper.getBoolean(inputs.get(12));
		((BNegatableFiniteStatusBoolean)pidBlock.getDisable()).setNegate(negate);
		setupNumericSlot(pidBlock, BPid.tr.getName(), inputs.get(4));
		setupNumericSlot(pidBlock, BPid.intgTime.getName(), inputs.get(5));
		setupNumericSlot(pidBlock, BPid.dervTime.getName(), inputs.get(6));
		setupNumericSlot(pidBlock, BPid.deadBand.getName(), inputs.get(7));
		setupNumericSlot(pidBlock, BPid.dbDelay.getName(), inputs.get(8));
		pidBlock.setRevAct(BDirectReverseSignOfTREnum.make(TestDataHelper.getInt(inputs.get(9), 0)));
		setupNumericSlot(pidBlock, BPid.bias.getName(), inputs.get(10));
		
		dbDelayInSec = TestDataHelper.getDouble(inputs.get(8), 0);
		
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0;i<dbDelayInSec;i++) 
				pidBlock.executeHoneywellComponent(executionParams);
		}
			pidBlock.executeHoneywellComponent(executionParams);
		
		Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
		
		
		if(Double.isFinite(dbDelayInSec) && dbDelayInSec > 0) {
			for(int i=0; i<10; i++) {
				Assert.assertEquals(pidBlock.getOUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1);
			}
		}		
		
		pidBlock = null;
	}
	
	
	public void setupNumericSlot(BPid pidBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = pidBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),pidBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				pidBlock.add("Link",conversionLink );				
				conversionLink.activate();
			}else{
				pidBlock.linkTo(nm1, nm1.getSlot("out"), pidBlock.getSlot(slotName));
			}			
			
			return;
		}
		
		switch (slotName) {
		case "sensor":
			pidBlock.setSensor(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "setPt":
			pidBlock.setSetPt(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "disable":
			pidBlock.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			break;
			
		case "tr":
			pidBlock.setTr(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "intgTime":
			pidBlock.setIntgTime(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "dervTime":
			pidBlock.setDervTime(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "deadBand":
			pidBlock.setDeadBand(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "dbDelay":
			pidBlock.setDbDelay(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "bias":
			pidBlock.setBias(TestDataHelper.getInt(inputValue, 0));
			break;

		default:
			break;
		}
	}
	
	@AfterClass
	public void tearDown(){
		pidBlock=null;
	}
	
	
	 //@Test(dependsOnMethods={"testSlotAvailability"}, groups={"testLinkRules"})
	  public void testLinkCreation() throws Exception {
		  BPid target = new BPid();
		  BPid src = new BPid();
		
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.sensor.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.setPt.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.tr.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.intgTime.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.dervTime.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.deadBand.getName()));
		  LinkCheckUtil.checkValidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.dbDelay.getName()));
		  
		  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.revAct.getName()));
		  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPid.OUTPUT.getName()),target,target.getSlot(BPid.bias.getName()));
		  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPid.revAct.getName()),target,target.getSlot(BPid.revAct.getName()));
		  LinkCheckUtil.checkInvalidLink(src, src.getSlot(BPid.bias.getName()),target,target.getSlot(BPid.bias.getName()));
	  }
	 
	@Test
	public void testConfigProperties() {
		List<Property> configList = pidBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BPid.revAct.getName(), BPid.bias.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = pidBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BPid.sensor.getName(), BPid.setPt.getName(),BPid.tr.getName(), BPid.intgTime.getName(),
				BPid.dervTime.getName(), BPid.deadBand.getName(),BPid.dbDelay.getName(), BPid.disable.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = pidBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BPid.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}


	@Test
	public void testupgradesenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Pid_Station_bog/config/file.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BPid pId  = (BPid )folder.get("PID");
			Assert.assertEquals(pId.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
			}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}

}
