/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.control.BHVACOverrideEnum;

/**
 * Implementation of FlowControl block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: F1PLT-ATC-252
 * <AUTHOR> - <PERSON><PERSON><PERSON>.
 * @since Jan 18, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BHVACOverrideEnumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BHVACOverrideEnumTest(**********)1.0$ @*/
/* Generated Thu Jan 18 11:43:56 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHVACOverrideEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name ="enumOrdinal")
  public Object[][] getDataToTestEnumOrdinal(){
	  return new Object[][] { {Integer.valueOf(BHVACOverrideEnum.HVO_OFF),BHVACOverrideEnum.Hvo_OFF},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_POSITION),BHVACOverrideEnum.Hvo_POSITION},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_FLOW_VALUE),BHVACOverrideEnum.Hvo_FLOW_VALUE},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_FLOW_PERCENT),BHVACOverrideEnum.Hvo_FLOW_PERCENT},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_OPEN),BHVACOverrideEnum.Hvo_OPEN},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_CLOSE),BHVACOverrideEnum.Hvo_CLOSE},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_MINIMUM),BHVACOverrideEnum.Hvo_MINIMUM},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_MAXIMUM),BHVACOverrideEnum.Hvo_MAXIMUM},
		  {Integer.valueOf(BHVACOverrideEnum.HVO_NUL),BHVACOverrideEnum.Hvo_NUL}};
  }
  
  @Test(dataProvider="enumOrdinal")
  public void testOperationByMakeOrdinal(Integer ordinal, BHVACOverrideEnum hOvEnum) {
	  Assert.assertEquals(BHVACOverrideEnum.make(ordinal),hOvEnum);	  
  }
  
  
  @DataProvider(name ="enumTag")
  public Object[][] getDataToTestEnumTag(){
	  return new Object[][] { {BHVACOverrideEnum.Hvo_OFF.getTag(),BHVACOverrideEnum.Hvo_OFF},
		  {BHVACOverrideEnum.Hvo_POSITION.getTag(),BHVACOverrideEnum.Hvo_POSITION},
		  {BHVACOverrideEnum.Hvo_FLOW_VALUE.getTag(),BHVACOverrideEnum.Hvo_FLOW_VALUE},
		  {BHVACOverrideEnum.Hvo_FLOW_PERCENT.getTag(),BHVACOverrideEnum.Hvo_FLOW_PERCENT},
		  {BHVACOverrideEnum.Hvo_OPEN.getTag(),BHVACOverrideEnum.Hvo_OPEN},
		  {BHVACOverrideEnum.Hvo_CLOSE.getTag(),BHVACOverrideEnum.Hvo_CLOSE},
		  {BHVACOverrideEnum.Hvo_MINIMUM.getTag(),BHVACOverrideEnum.Hvo_MINIMUM},
		  {BHVACOverrideEnum.Hvo_MAXIMUM.getTag(),BHVACOverrideEnum.Hvo_MAXIMUM},
		  {BHVACOverrideEnum.Hvo_NUL.getTag(),BHVACOverrideEnum.Hvo_NUL}};
  }
  
  @Test(dataProvider="enumTag")
  public void testOperationByMakeTag(String tag, BHVACOverrideEnum fcuEnum) {
	  Assert.assertEquals(BHVACOverrideEnum.make(tag),fcuEnum);
  } 

}
