#Boundary condition check data,,,,,,,,,,,,,,
#Sec, in, StgsActive, antic, cphMultOut, maxStgs, disable, overrideOff, minOn, minOff, intstgOn, intstgOff, antAuth, hyst, cph
0,1.014,0,0,0.00796115,6,connected=+inf,0,10,10,10,10,100,0,6
1,1.014,0,0,0.00796115,6,unconnected=5,0,10,10,10,10,100,0,6
2,1.014,0,0,0.00796115,6,connected=nan,0,10,10,10,10,100,0,6
3,1.014,0,0,0.00796115,6,connected=1,connected=0,10,10,10,10,100,0,6
4,connected=+inf,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
5,unconnected=5,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
6,connected=nan,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
7,1.014,0,0,0.00796115,connected=+inf,0,0,10,10,10,10,100,0,6
8,1.014,0,0,0.00796115,unconnected=5,0,0,10,10,10,10,100,0,6
9,1.014,0,0,0.00796115,connected=nan,0,0,10,10,10,10,100,0,6
10,1.014,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
11,1.014,0,0,0.00796115,6,0,connected=+inf,10,10,10,10,100,0,6
12,1.014,0,0,0.00796115,6,0,unconnected=5,10,10,10,10,100,0,6
13,1.014,0,0,0.00796115,6,0,connected=nan,10,10,10,10,100,0,6
14,1.014,0,0,0.00796115,6,0,0,connected=+inf,10,10,10,100,0,6
15,1.014,0,0,0.00796115,6,0,0,unconnected=5,10,10,10,100,0,6
16,1.014,0,0,0.00796115,6,0,0,connected=nan,10,10,10,100,0,6
17,1.014,1,0,0.00796115,6,0,0,10,connected=+inf,10,10,100,0,6
18,1.014,1,0,0.00796115,6,0,0,10,unconnected=5,10,10,100,0,6
19,1.014,1,0,0.00796115,6,0,0,10,connected=nan,10,10,100,0,6
20,1.014,2,0,0.00796115,6,0,0,10,10,connected=+inf,10,100,0,6
21,1.014,3,0,0.00796115,6,0,0,10,10,unconnected=5,10,100,0,6
22,1.014,4,0,0.00796115,6,0,0,10,10,connected=nan,10,100,0,6
23,1.014,3,0,0.00796115,3,0,0,10,10,10,connected=+inf,100,0,6
24,1.014,3,0,0.00796115,3,0,0,10,10,10,unconnected=5,100,0,6
25,1.014,3,0,0.00796115,3,0,0,10,10,10,connected=nan,100,0,6
26,1.014,0,0,0.00796115,6,0,0,10,10,10,70,10,-1,60
27,1.014,0,0,0.00796115,6,0,0,10,10,10,70,10,10,60
28,1.014,0,0,0.00796115,6,0,0,10,10,10,70,10,210,60
29,1.014,0,0,0.00796115,6,0,0,10,10,10,70,100,0,60
30,1.014,0,0,0.00796115,10,0,0,10,10,10,70,100,10,60
31,1.014,0,0,0.00796115,10,0,0,10,10,10,70,100,11,60
32,1.014,0,0,0.00796115,6,0,0,10,10,10,70,100,0,60
33,1.014,0,0,0.00796115,6,0,0,10,10,10,70,100,0,60
34,1.014,0,0,0.00796115,6,0,0,10,10,10,-1,100,0,60
35,1.014,0,0,0.00796115,6,0,0,10,10,10,0.01,100,0,60
36,1.014,0,0,0.00796115,6,0,0,10,10,10,10,100,0,70
37,1.014,0,0,0.00796115,6,0,0,10,10,10,10,100,0,-1
38,1.023,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
39,1.022,0,0,0.00796115,6,0,0,10,10,10,10,-1,0,6
40,1.022,0,0,0.00796115,6,0,0,10,10,10,10,0,0,6
41,1.022,0,0,0.00796115,6,0,0,10,10,10,10,210,0,6
42,1.022,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
43,1.011,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
44,0.991,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
45,0.961,0,0,0.00796115,6,0,0,10,10,10,10,100,0,6
64800,0.923,1,0,0.00796115,6,0,0,10,10,10,10,100,0,6
47,0.876,1,0,0.00796115,6,0,0,10,10,10,10,100,0,6
48,0.876,1,0,0.00796115,6,0,0,10,10,10,10,-1,0,60
49,0.876,1,0,0.00796115,6,0,0,10,10,10,10,50,0,60
50,0.822,1,0,0.00796115,6,0,0,10,10,10,10,100,0,6
51,0.822,0,0,0.00796115,0,0,0,10,10,10,10,100,0,6
52,0.822,0,0,0.00796115,10,0,0,10,10,10,10,100,-1,6
53,0.822,0,0,0.00796115,10,0,0,10,10,10,10,100,0,6
54,0.822,0,0,0.00796115,10,0,0,10,10,10,10,100,10,6
55,0.822,0,0,0.00796115,10,0,0,10,10,10,10,100,11,6
56,0.822,0,0,0.00796115,10,0,0,10,10,10,10,100,10,6
57,0.822,0,0,0.00796115,10,unconnected=5,unconnected=5,10,10,10,10,100,9,6
