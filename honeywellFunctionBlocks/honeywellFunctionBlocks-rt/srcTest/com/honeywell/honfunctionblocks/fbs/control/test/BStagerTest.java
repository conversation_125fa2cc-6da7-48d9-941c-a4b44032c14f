/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.control.BStager;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of Stager block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-195
 * <AUTHOR> - Suresh Khatri
 * @since Dec 27, 2017
 */

@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BStagerTest extends BTestNg{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BStagerTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStagerTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		stager = getStager();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass
	public void tearDown() {
		stager = null;
		executionParams = null;
	}
	
	private Object[][] getInputSlotNames() {
		return new Object[][]{{"disable"}, {"overrideOff"}, {"in"}, {"maxStgs"}, {"minOn"}, {"minOff"}, {"intstgOn"}, {"intstgOff"}};
	}
	
	private Object[][] getOutputSlotNames() {
		return new Object[][]{{"STAGES_ACTIVE"}};
	}
	
	private Object[][] getConfigurationSlotNames() {
		return new Object[][]{{"hyst"}};
	}
	
	private Object[][] getExecOrderSlotNames() {
		return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	}
	
	@DataProvider(name="allSlotNames")
	public Object[][] getAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getConfigurationSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  
	}
	
	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(stager.getSlot(slotName));
	}
	
	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"Invalid1"}, {"Invalid2"}};
	}
	
	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(stager.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "stager_thermostat_cycler.png");
		BIcon actualFbIcon = stager.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		stager.setIcon(expectedFbIcon);
		actualFbIcon = stager.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		return new Object[][] {{0}, {100}};
	}
	
	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		stager.setIn(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getIn().getValue(), value, 0.1);
		
		stager.setMaxStgs(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getMaxStgs().getValue(), value, 0.1);

		stager.setMinOn(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getMinOn().getValue(), value, 0.1);

		stager.setMinOff(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getMinOff().getValue(), value, 0.1);

		stager.setIntstgOn(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getIntstgOn().getValue(), value, 0.1);

		stager.setIntstgOff(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getIntstgOff().getValue(), value, 0.1);

		stager.setSTAGES_ACTIVE(new BHonStatusNumeric(value));
		Assert.assertEquals(stager.getSTAGES_ACTIVE().getValue(), value, 0.1);

		stager.setHyst((int) value);
		Assert.assertEquals(stager.getHyst(), value, 0.1);
	}
	
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BStager stagerB = getStager();
		checkOutgoingLink(stagerB, BStager.disable, false);
		checkOutgoingLink(stagerB, BStager.overrideOff, false);
		checkOutgoingLink(stagerB, BStager.in, false);
		checkOutgoingLink(stagerB, BStager.maxStgs, false);
		checkOutgoingLink(stagerB, BStager.minOff, false);
		checkOutgoingLink(stagerB, BStager.minOn, false);
		checkOutgoingLink(stagerB, BStager.intstgOff, false);
		checkOutgoingLink(stagerB, BStager.intstgOn, false);
		checkOutgoingLink(stagerB, BStager.hyst, false);
	}

	private void checkOutgoingLink(BStager block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = stager.checkLink(stager, stager.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	@Test
	public void testStagerBlockWithNegateTestData() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_data_with_negate_on_overrideOff.csv");
		
		BStager stagerTemp = getStager();
		executionParams.setIterationInterval(1000);
		for (List<String> inputSequence : readTestDataFromCSVFile) {
			setupInputSlot(stagerTemp, BStager.in.getName(), inputSequence.get(1));
			setupInputSlot(stagerTemp, BStager.maxStgs.getName(), inputSequence.get(5));
			setupInputSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(6));
			setupNegateSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(15));
			setupInputSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(7));
			setupNegateSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(16));
			setupInputSlot(stagerTemp, BStager.minOn.getName(), inputSequence.get(8));
			setupInputSlot(stagerTemp, BStager.minOff.getName(), inputSequence.get(9));
			setupInputSlot(stagerTemp, BStager.intstgOn.getName(), inputSequence.get(10));
			setupInputSlot(stagerTemp, BStager.intstgOff.getName(), inputSequence.get(11));
			
			setupInputSlot(stagerTemp, BStager.hyst.getName(), inputSequence.get(13));
			if(inputSequence.get(0).equalsIgnoreCase("0")) {
				stagerTemp.initHoneywellComponent(null);
			}

			stagerTemp.executeHoneywellComponent(executionParams);
			Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(2), 0.0d), Arrays.toString(inputSequence.toArray()));
		}
	}
	
	
	@Test
	public void TestStagerBlockWithSequenceTestData1() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_TestData1.csv");
		
		BStager stagerTemp = getStager();
		executionParams.setIterationInterval(1000);
		for (List<String> inputSequence : readTestDataFromCSVFile) {
			setupInputSlot(stagerTemp, BStager.in.getName(), inputSequence.get(1));
			setupInputSlot(stagerTemp, BStager.maxStgs.getName(), inputSequence.get(5));
			setupInputSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(6));
			setupInputSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(7));
			setupInputSlot(stagerTemp, BStager.minOn.getName(), inputSequence.get(8));
			setupInputSlot(stagerTemp, BStager.minOff.getName(), inputSequence.get(9));
			setupInputSlot(stagerTemp, BStager.intstgOn.getName(), inputSequence.get(10));
			setupInputSlot(stagerTemp, BStager.intstgOff.getName(), inputSequence.get(11));
			
			setupInputSlot(stagerTemp, BStager.hyst.getName(), inputSequence.get(13));
			if(inputSequence.get(0).equalsIgnoreCase("0")) {
				stagerTemp.initHoneywellComponent(null);
			}
			stagerTemp.executeHoneywellComponent(executionParams);
			Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(2), 0.0d), Arrays.toString(inputSequence.toArray()));
		}
	}
	
	@Test
	public void TestStagerBlockWithSequenceTestData2() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_TestData2.csv");
		
		BStager stagerTemp = getStager();
		executionParams.setIterationInterval(1000);
		for (List<String> inputSequence : readTestDataFromCSVFile) {
			setupInputSlot(stagerTemp, BStager.in.getName(), inputSequence.get(1));
			setupInputSlot(stagerTemp, BStager.maxStgs.getName(), inputSequence.get(5));
			setupInputSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(6));
			setupInputSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(7));
			setupInputSlot(stagerTemp, BStager.minOn.getName(), inputSequence.get(8));
			setupInputSlot(stagerTemp, BStager.minOff.getName(), inputSequence.get(9));
			setupInputSlot(stagerTemp, BStager.intstgOn.getName(), inputSequence.get(10));
			setupInputSlot(stagerTemp, BStager.intstgOff.getName(), inputSequence.get(11));
			
			setupInputSlot(stagerTemp, BStager.hyst.getName(), inputSequence.get(13));
			if(inputSequence.get(0).equalsIgnoreCase("0")) {
				stagerTemp.initHoneywellComponent(null);
			}
			stagerTemp.executeHoneywellComponent(executionParams);
			Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(2), 0.0d), Arrays.toString(inputSequence.toArray()));
		}
	}
	
	@DataProvider(name = "stagerTestData")
	public Object[][] getStagerTestData() throws FileNotFoundException{
	    	return TestDataHelper.getSequencedTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_SequencedTestData.csv");
	}

	@Test(dataProvider = "stagerTestData")
	public void testStagerBlockWithSequenceTestData(List<List<String>> inputSequenceList) throws BlockExecutionException, BlockInitializationException {
		
		BStager stagerTemp = getStager();
		for (Iterator<List<String>> iterator = inputSequenceList.iterator(); iterator.hasNext();) {
			List<String> inputSequence = iterator.next();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputSequence.get(0), 1000));
			setupInputSlot(stagerTemp, BStager.in.getName(), inputSequence.get(1));
			setupInputSlot(stagerTemp, BStager.maxStgs.getName(), inputSequence.get(2));
			setupInputSlot(stagerTemp, BStager.minOn.getName(), inputSequence.get(3));
			setupInputSlot(stagerTemp, BStager.minOff.getName(), inputSequence.get(4));
			setupInputSlot(stagerTemp, BStager.intstgOn.getName(), inputSequence.get(5));
			setupInputSlot(stagerTemp, BStager.intstgOff.getName(), inputSequence.get(6));
			setupInputSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(7));
			setupInputSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(8));
			setupInputSlot(stagerTemp, BStager.hyst.getName(), inputSequence.get(9));
			int timeout = TestDataHelper.getInt(inputSequence.get(51), 0);
			stagerTemp.initHoneywellComponent(null);
			for (int i = 1; i <= timeout; i++) {
				stagerTemp.executeHoneywellComponent(executionParams);
				if(i == TestDataHelper.getInt(inputSequence.get(12), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(11), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(14), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(13), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(16), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(15), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(18), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(17), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(20), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(19), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(22), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(21), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(24), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(23), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(26), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(25), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(28), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(27), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(30), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(29), 0), inputSequence.toString());
				}
			}
		}
	}
	
	@DataProvider(name = "stagerWithNegateTestData")
	public Object[][] getStagerWithNegateTestData() throws FileNotFoundException{
	    	return TestDataHelper.getSequencedTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_data_with_negate_on_Slots.csv");
	}

	@Test(dataProvider = "stagerWithNegateTestData")
	public void testStagerBlockWithNegateSequenceTestData(List<List<String>> inputSequenceList) throws BlockExecutionException, BlockInitializationException {
		
		BStager stagerTemp = getStager();
		for (Iterator<List<String>> iterator = inputSequenceList.iterator(); iterator.hasNext();) {
			List<String> inputSequence = iterator.next();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputSequence.get(0), 1000));
			setupInputSlot(stagerTemp, BStager.in.getName(), inputSequence.get(1));
			setupInputSlot(stagerTemp, BStager.maxStgs.getName(), inputSequence.get(2));
			setupInputSlot(stagerTemp, BStager.minOn.getName(), inputSequence.get(3));
			setupInputSlot(stagerTemp, BStager.minOff.getName(), inputSequence.get(4));
			setupInputSlot(stagerTemp, BStager.intstgOn.getName(), inputSequence.get(5));
			setupInputSlot(stagerTemp, BStager.intstgOff.getName(), inputSequence.get(6));
			setupInputSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(7));
			setupNegateSlot(stagerTemp, BStager.overrideOff.getName(), inputSequence.get(52));
			setupInputSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(8));
			setupNegateSlot(stagerTemp, BStager.disable.getName(), inputSequence.get(53));
			setupInputSlot(stagerTemp, BStager.hyst.getName(), inputSequence.get(9));
			int timeout = TestDataHelper.getInt(inputSequence.get(51), 0);
			stagerTemp.initHoneywellComponent(null);
			for (int i = 1; i <= timeout; i++) {
				stagerTemp.executeHoneywellComponent(executionParams);
				if(i == TestDataHelper.getInt(inputSequence.get(12), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(11), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(14), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(13), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(16), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(15), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(18), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(17), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(20), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(19), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(22), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(21), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(24), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(23), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(26), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(25), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(28), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(27), 0), inputSequence.toString());
				}
				if(i == TestDataHelper.getInt(inputSequence.get(30), 0)) {
					Assert.assertEquals(stagerTemp.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputSequence.get(29), 0), inputSequence.toString());
				}
			}
		}
	}
	
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = stager.getConfigPropertiesList();		
		Assert.assertEquals(configList.get(0).getName(), BStager.hyst.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = stager.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BStager.disable.getName(), BStager.overrideOff.getName(),BStager.in.getName(), BStager.maxStgs.getName(),
				BStager.minOn.getName(), BStager.minOff.getName(),BStager.intstgOn.getName(),BStager.intstgOff.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = stager.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BStager.STAGES_ACTIVE.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testupgradesenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Stager_Config_Bog/config/file.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BStager stager = (BStager)folder.get("Stager");
			Assert.assertEquals(stager.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(stager.getOverrideOff().getType(), BNegatableFiniteStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}
	private void setupNegateSlot(BStager stager, final String slotname, final String negateString) {
		boolean negate = false;
		if(negateString != null) {
			negate = TestDataHelper.getBoolean(negateString);
			BNegatableFiniteStatusBoolean nsb = (BNegatableFiniteStatusBoolean) stager.get(slotname);
			nsb.setNegate(negate); 
		}
	}
	
	private void setupInputSlot(BStager stager, final String slotName, final String inputValue) {
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = stager.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
	                     converter = new BStatusNumericToHonStatusNumeric();
	                     BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),stager.getSlot(slotName),converter);
	                     conversionLink.setEnabled(true);
	                     stager.add("Link?",conversionLink );                    
	                     conversionLink.activate();
	                     nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
	                } else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),stager.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				stager.add("Link?", conversionLink);				
				conversionLink.activate();
			}else{
				stager.linkTo(nm1, nm1.getSlot("out"), stager.getSlot(slotName));
			}			
			return;
		}
		switch (slotName) {
		case "in":
			stager.setIn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "maxStgs":
			stager.setMaxStgs(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "minOn":
			stager.setMinOn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "minOff":
			stager.setMinOff(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "intstgOn":
			stager.setIntstgOn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "intstgOff":
			stager.setIntstgOff(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		case "overrideOff":
			stager.setOverrideOff(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			break;
		case "disable":
			stager.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			break;
		case "hyst":
			stager.setHyst(Integer.parseInt(inputValue));
			break;
		}
	}
	
	private BStager getStager() {
		BStager stager = new BStager();
		try {
			stager.initHoneywellComponent(null);
		} catch (BlockInitializationException e) {
			e.printStackTrace();
		}
		return stager;
	}
	
	private BStager stager;
	private BExecutionParams executionParams;
}
