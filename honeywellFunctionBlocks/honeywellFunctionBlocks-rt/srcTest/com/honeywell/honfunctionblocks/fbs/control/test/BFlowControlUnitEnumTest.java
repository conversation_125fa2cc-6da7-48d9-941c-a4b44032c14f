/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.control.BFlowControlUnitEnum;

/**
 * Implementation of FlowControl block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: F1PLT-ATC-252
 * <AUTHOR> - <PERSON><PERSON><PERSON> B
 * @since Jan 18, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BFlowControlUnitEnumTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BFlowControlUnitEnumTest(**********)1.0$ @*/
/* Generated Thu Jan 18 11:33:19 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowControlUnitEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @DataProvider(name ="enumOrdinal")
  public Object[][] getDataToTestEnumOrdinal(){
	  return new Object[][] { {Integer.valueOf(BFlowControlUnitEnum.CFM_FT),BFlowControlUnitEnum.CfmFt},
		  {Integer.valueOf(BFlowControlUnitEnum.LPS_MTR),BFlowControlUnitEnum.LpsMtr},
		  {Integer.valueOf(BFlowControlUnitEnum.CMH_MTR),BFlowControlUnitEnum.CmhMtr}};
  }
  
  @Test(dataProvider="enumOrdinal")
  public void testOperationByMakeOrdinal(Integer ordinal, BFlowControlUnitEnum fcuEnum) {
	  Assert.assertEquals(BFlowControlUnitEnum.make(ordinal),fcuEnum);	  
  }
  
  
  @DataProvider(name ="enumTag")
  public Object[][] getDataToTestEnumTag(){
	  return new Object[][] { {BFlowControlUnitEnum.CfmFt.getTag(),BFlowControlUnitEnum.CfmFt},
		  {BFlowControlUnitEnum.LpsMtr.getTag(),BFlowControlUnitEnum.LpsMtr},
		  {BFlowControlUnitEnum.CmhMtr.getTag(),BFlowControlUnitEnum.CmhMtr}};
  }
  
  @Test(dataProvider="enumTag")
  public void testOperationByMakeTag(String tag, BFlowControlUnitEnum fcuEnum) {
	  Assert.assertEquals(BFlowControlUnitEnum.make(tag),fcuEnum);
  } 
}
