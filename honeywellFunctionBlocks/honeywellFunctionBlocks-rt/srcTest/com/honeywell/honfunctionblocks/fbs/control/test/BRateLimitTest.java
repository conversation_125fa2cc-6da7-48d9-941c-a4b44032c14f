/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BLink;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.control.BRateLimit;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Requirement ID: F1PLT-ADR-405 - Test case ID: F1PLT-ATC-251
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since Dec 28, 2017
 */

@NiagaraType
public class BRateLimitTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BRateLimitTest(2979906276)1.0$ @*/
/* Generated Thu Dec 28 18:43:38 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRateLimitTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  	private BRateLimit rateLimit;
  	
  	@Override
	@BeforeClass(alwaysRun=true)
	public void setup() throws Exception {
  		rateLimit = new BRateLimit();
  		
  		//Initialize rate limit with the initial data
  		ArrayList<List<String>> inputData = readTestData();
  		List<String> inputs = inputData.get(0);
		setSlotValue(rateLimit, BRateLimit.in.getName(), inputs.get(1));
		setSlotValue(rateLimit, BRateLimit.disable.getName(), inputs.get(2));
		setSlotValue(rateLimit, BRateLimit.startVal.getName(), inputs.get(3));
		setSlotValue(rateLimit, BRateLimit.upRate.getName(), inputs.get(4));
		setSlotValue(rateLimit, BRateLimit.downRate.getName(), inputs.get(5));
		setSlotValue(rateLimit, BRateLimit.startInterval.getName(), inputs.get(6));
  		
  		rateLimit.initHoneywellComponent(null);
	}
  	
	@DataProvider(name="provideValidSlotNames")
	public Object[] allValidSlotNames(){
		return new Object[]{"in", "disable", "startInterval", "startVal", "upRate", "downRate", "OUTPUT"};
	}
	
	@Test(groups={"rl"}, dataProvider="provideValidSlotNames")
	public void testSlotAvailability(String slotName){
		Assert.assertNotNull(rateLimit.getSlot(slotName));
	}
	
	@DataProvider(name = "provideInvalidSlotNames")
	public Object[] invalidSlotNames() {
		return new Object[]{"In", "Disable", "StartInterval", "StartVal", "UpRate", "DownRate", "output",
							   "IN", "DISABLE", "STARTINTERVAL", "STARTVAL", "UPRATE", "DOWNRATE", "out"};
	}
	
	@Test(dataProvider="provideInvalidSlotNames")
	public void testInvalidSlots(String slotName){
		Assert.assertNull(rateLimit.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "rateLimit.png");
		BIcon actualFbIcon = rateLimit.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		rateLimit.setIcon(expectedFbIcon);
		actualFbIcon = rateLimit.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
  
	private ArrayList<List<String>> readTestData() {
        String testDataFilePath = "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Neg_RateLimit_TestData.csv";
		return TestDataHelper.readTestDataFromCSVFile(testDataFilePath);
	}

	/*
	 * Since TestNG DataProvider does not guarantee the test data order,
	 * this test itself manages the test data to maintain the test sequence.   
	 */
	@Test(groups={"testRateLimit"})
	public void testRateLimit() throws Exception{
		//clear all the links added by the previous test run
		BLink [] links = rateLimit.getLinks();
		for (int i = 0; i < links.length; i++) {
			rateLimit.remove(links[i]);
		}
		
		StringBuffer failureData = new StringBuffer();
		
		int successCount = 1;
		ArrayList<List<String>> inputData = readTestData();
		for (int i = 1; i < inputData.size(); i++) {
			List<String> inputs = inputData.get(i);
			
			BExecutionParams executionParams = new BExecutionParams();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
			
			setSlotValue(rateLimit, BRateLimit.in.getName(), inputs.get(1));
			setSlotValue(rateLimit, BRateLimit.disable.getName(), inputs.get(2));
			setSlotValue(rateLimit, BRateLimit.startVal.getName(), inputs.get(3));
			setSlotValue(rateLimit, BRateLimit.upRate.getName(), inputs.get(4));
			setSlotValue(rateLimit, BRateLimit.downRate.getName(), inputs.get(5));
			setSlotValue(rateLimit, BRateLimit.startInterval.getName(), inputs.get(6));
			String negate = inputs.get(9) != null ? inputs.get(9).trim() : "0";
			((BNegatableFiniteStatusBoolean)rateLimit.getDisable()).setNegate(false);
			if(negate.equals("1")) {
				((BNegatableFiniteStatusBoolean)rateLimit.getDisable()).setNegate(true);
			}
			int seq = Integer.valueOf(inputs.get(8));
			rateLimit.executeBlock(executionParams);
			
			
			if(rateLimit.getOUTPUT().getValue() == TestDataHelper.getDouble(inputs.get(7), 0d)) {
				successCount++;
			} else {
				failureData.append("Test failed for test data '"+ seq +"' - expected ["+ TestDataHelper.getDouble(inputs.get(7), 0d) +
						"] actual ["+ rateLimit.getOUTPUT().getValue() +"] \n");
			}
		}
		
		if(successCount == inputData.size()) {
			Assert.assertTrue(true);
		} else {
			String failureMessage = "\nTotal test data - ["+ inputData.size() +"]"
					+ "\nTotal test data passed ["+ successCount +"]"
					+ "\nTotal test data failed ["+ (inputData.size() - successCount)  +"]"
					+ "\n\nFailure summary"
					+ "\n"+failureData.toString();
			Assert.fail(failureMessage);
		}
	}
	
	@Test
	public void testInitComponentWithNagativeData() throws Exception {
		BRateLimit rateLmt = new BRateLimit();
  		
  		//Initialize rate limit with the initial data
  		ArrayList<List<String>> inputData = readTestData();
  		List<String> inputs = inputData.get(0);
		setSlotValue(rateLmt, BRateLimit.in.getName(), inputs.get(1));
		setSlotValue(rateLmt, BRateLimit.disable.getName(), inputs.get(2));
		setSlotValue(rateLmt, BRateLimit.startVal.getName(), String.valueOf(Float.MAX_VALUE));
		setSlotValue(rateLmt, BRateLimit.upRate.getName(), inputs.get(4));
		setSlotValue(rateLmt, BRateLimit.downRate.getName(), inputs.get(5));
		setSlotValue(rateLmt, BRateLimit.startInterval.getName(), String.valueOf(Float.MAX_VALUE));
  		
		rateLmt.initHoneywellComponent(null);
		Assert.assertEquals(0, rateLmt.getStartTimer());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = rateLimit.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BRateLimit.in.getName(), BRateLimit.disable.getName(),BRateLimit.startVal.getName(), BRateLimit.upRate.getName(),
				BRateLimit.downRate.getName(), BRateLimit.startInterval.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = rateLimit.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BRateLimit.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	private void setSlotValue(BRateLimit rateLimit, String slotName, String slotValue) {
		if (TestDataHelper.isConnected(slotValue)) {
			BConverter converter = null;
			BNumericConst nc = new BNumericConst();
			nc.getOut().setValue(TestDataHelper.getDouble(slotValue, 0d));
			Type srcType = nc.getOut().getType();
			Type targetType = rateLimit.getProperty(slotName).getType();

			if (srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nc, nc.getSlot("out"), rateLimit.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				rateLimit.add("Link?", conversionLink);
				conversionLink.activate();
			} else {
				converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nc, nc.getSlot("out"), rateLimit.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				rateLimit.add("Link?", conversionLink);
				conversionLink.activate();
			}
			
			return;
		}
		
		switch(slotName) {
			case "in":
				rateLimit.setIn(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "disable":
				rateLimit.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(slotValue, false));
				break;
				
			case "startVal":
				rateLimit.setStartVal(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "upRate":
				rateLimit.setUpRate(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "downRate":
				rateLimit.setDownRate(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "startInterval":
				rateLimit.setStartInterval(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
		
			default:
				break;
		}
	}
	
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BRateLimit source = new BRateLimit();
		BMaximum target = new BMaximum();		
		
		//checking is any of the RateLimit source slot can be linked to another functional block
		LinkCheck linkCheck = target.checkLink(source, source.getSlot("in"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		try {
			linkCheck = target.checkLink(source, source.getSlot("disable"), target.getSlot("in1"), null);
		} catch (Exception e) {
			//There will not be any link type available to link from FiniteStatusBoolean to StatusNumeric
			Assert.assertTrue(true);
		}
		
		linkCheck = target.checkLink(source, source.getSlot("startVal"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("upRate"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("downRate"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("startInterval"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		//checking if link to OUT slot is blocked
		linkCheck = source.checkLink(target, target.getSlot("OUTPUT"), source.getSlot("OUTPUT"), null);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/RateLimitFile.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BRateLimit rateLimit = (BRateLimit)folder.get("RateLimit");
			Assert.assertEquals(rateLimit.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}
	
	@AfterClass
	public void tearDown(){
		rateLimit = null;
	}
}
