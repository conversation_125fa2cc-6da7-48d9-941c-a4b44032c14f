# Test case ID: F1PLT-ATC-251
#Iteration Interval,in,disable,startVal,upRate,downRate,startInterval,OUT,sequence,negdisable
1000,connected=65,0,50,1,1,10,50,1,0
1000,65,0,50,1,1,10,51,2,0
1000,65,0,50,1,1,10,52,3,0
1000,65,0,50,1,1,10,53,4,0
1000,65,0,50,1,1,10,54,5,0
1000,65,0,50,1,1,10,55,6,0
1000,65,0,50,1,1,10,56,7,0
1000,65,0,50,1,1,10,57,8,0
1000,65,0,50,1,1,10,58,9,0
1000,65,0,50,1,1,10,59,10,0
1000,65,0,50,1,1,10,60,11,0
1000,65,0,70,1,1,10,65,12,0
1000,65,1,60,1,1,10,60,13,0
1000,62,1,60,1,1,10,60,14,0
1000,55,0,60,1,1,10,59,15,0
1000,55,0,60,1,1,10,58,16,0
1000,55,0,60,1,1,10,57,17,0
1000,55,0,60,1,1,10,56,18,0
1000,55,0,60,1,1,10,55,19,0
1000,55,0,65,1,1,10,55,20,0
1000,75,0,50,1,1,0,56,21,0
1000,75,0,50,1,1,0,57,22,0
1000,75,0,50,1,1,0,58,23,0
1000,75,0,50,1,1,0,59,24,0
1000,75,0,50,1,1,0,60,25,0
1000,75,0,50,1,1,0,61,26,0
1000,75,0,50,1,1,0,62,27,0
1000,75,0,50,1,1,0,63,28,0
1000,75,0,50,1,1,0,64,29,0
1000,75,0,50,1,1,0,65,30,0
1000,75,0,50,1,1,0,66,31,0
1000,75,0,50,1,1,0,67,32,0
1000,75,0,50,1,1,0,68,33,0
1000,75,0,50,1,1,0,69,34,0
1000,75,0,50,1,1,10,75,35,0
1000,75,0,50,1,1,10,75,36,0
1000,75,0,70,1,1,10,75,37,0
1000,75,0,70,1,1,10,75,38,0
1000,75,0,60,1,1,10,75,39,0
1000,75,0,60,1,1,10,75,40,0
1000,70,0,60,1,1,10,70,41,0
1000,70,0,60,1,1,10,70,42,0
1000,70,0,60,1,1,10,70,43,0
1000,70,0,60,1,1,10,70,44,0
1000,75,0,60,1,1,10,75,45,0
1000,connected=-inf,0,connected=-inf,1,1,10,-inf,46,0
1000,unconnected=5,0,unconnected=5,1,1,10,0,47,0
1000,connected=nan,0,connected=nan,1,1,10,+inf,48,0
1000,connected=+inf,0,connected=+inf,1,1,10,+inf,49,0
1000,55,0,60,1,1,10,55,50,0
1000,55,1,50,1,1,10,50,51,0
1000,80,1,50,1,1,10,50,52,0
1000,connected=-inf,1,50,1,1,10,50,53,0
1000,unconnected=5,1,50,1,1,10,50,54,0
1000,connected=nan,1,50,1,1,10,50,55,0
1000,connected=+inf,1,50,1,1,10,50,56,0
1000,55,1,50,1,1,10,50,57,0
1000,80,1,50,1,1,10,50,58,0
1000,connected=-inf,1,connected=-inf,1,1,10,-inf,59,0
1000,unconnected=5,1,unconnected=5,1,1,10,0,60,0
1000,connected=nan,1,connected=nan,1,1,10,+inf,61,0
1000,connected=+inf,1,connected=+inf,1,1,10,+inf,62,0
1000,65,1,50,1,1,10,50,63,0
1000,65,0,50,1,1,unconnected=5,51,64,0
1000,65,0,50,1,1,connected=nan,52,65,0
1000,65,0,50,1,1,connected=+inf,53,66,0
1000,65,0,50,1,1,connected=-inf,54,67,0
1000,65,0,50,1,1,unconnected=5,55,68,0
1000,65,0,50,1,1,connected=nan,56,69,0
1000,65,0,50,1,1,connected=+inf,57,70,0
1000,65,0,50,1,1,connected=-inf,58,71,0
1000,65,0,50,1,1,unconnected=5,59,72,0
1000,65,0,50,1,1,connected=nan,60,73,0
1000,65,0,70,1,1,connected=+inf,61,74,0
1000,65,0,70,1,1,connected=+inf,62,75,0
1000,65,0,70,1,1,connected=+inf,63,76,0
1000,65,0,70,1,1,connected=+inf,64,77,0
1000,65,0,70,1,1,connected=+inf,65,78,0
1000,65,1,60,1,1,connected=-inf,60,79,0
1000,62,1,60,1,1,unconnected=5,60,80,0
1000,55,0,60,1,1,connected=nan,59,81,0
1000,55,0,60,1,1,connected=+inf,58,82,0
1000,55,0,60,1,1,connected=-inf,57,83,0
1000,55,0,60,1,1,unconnected=5,56,84,0
1000,55,0,60,1,1,connected=nan,55,85,0
1000,55,0,65,1,1,connected=+inf,55,86,0
1000,75,0,50,1,1,connected=-inf,56,87,0
1000,75,0,50,1,1,connected=-inf,57,88,0
1000,75,0,50,1,1,unconnected=5,58,89,0
1000,75,0,50,1,1,connected=nan,59,90,0
1000,75,0,50,1,1,connected=+inf,60,91,0
1000,75,0,50,1,1,connected=-inf,61,92,0
1000,75,0,50,1,1,unconnected=5,62,93,0
1000,75,0,50,1,1,connected=nan,63,94,0
1000,75,0,50,1,1,connected=+inf,64,95,0
1000,75,0,50,1,1,connected=-inf,65,96,0
1000,75,0,50,1,1,unconnected=5,66,97,0
1000,75,0,50,1,1,connected=nan,67,98,0
1000,75,0,50,1,1,connected=+inf,68,99,0
1000,75,0,50,1,1,connected=-inf,69,100,0
1000,75,0,50,1,1,unconnected=5,70,101,0
1000,75,0,50,1,1,connected=nan,71,102,0
1000,75,0,50,1,1,connected=+inf,72,103,0
1000,75,0,50,1,1,connected=-inf,73,104,0
1000,75,0,50,1,1,unconnected=5,74,105,0
1000,75,0,70,1,1,connected=nan,75,106,0
1000,75,0,70,1,1,connected=+inf,75,107,0
1000,75,0,60,1,1,connected=-inf,75,108,0
1000,75,0,60,1,1,unconnected=5,75,109,0
1000,70,0,60,1,1,connected=nan,74,110,0
1000,70,0,60,1,1,connected=+inf,73,111,0
1000,70,0,60,1,1,connected=-inf,72,112,0
1000,70,0,60,1,1,unconnected=5,71,113,0
1000,70,0,60,1,1,connected=nan,70,114,0
1000,connected=-inf,0,connected=-inf,1,1,10,-inf,115,0
1000,unconnected=5,0,unconnected=5,1,1,connected=-inf,-inf,116,0
1000,connected=nan,0,connected=nan,1,1,10,+inf,117,0
1000,connected=+inf,0,connected=+inf,1,1,10,+inf,118,0
1000,55,0,60,1,1,10,55,119,0
1000,55,1,50,1,1,connected=-inf,50,120,0
1000,80,1,50,1,1,unconnected=5,50,121,0
1000,connected=-inf,1,50,1,1,connected=nan,50,122,0
1000,unconnected=5,1,50,1,1,connected=+inf,50,123,0
1000,connected=nan,1,50,1,1,connected=-inf,50,124,0
1000,connected=+inf,1,50,1,1,unconnected=5,50,125,0
1000,55,1,50,1,1,connected=nan,50,126,0
1000,80,1,50,1,1,connected=+inf,50,127,0
1000,connected=-inf,1,connected=-inf,1,1,connected=-inf,-inf,128,0
1000,unconnected=5,1,unconnected=5,1,1,unconnected=5,0,129,0
1000,connected=nan,1,connected=nan,1,1,connected=nan,+inf,130,0
1000,connected=+inf,1,connected=+inf,1,1,connected=+inf,+inf,131,0
1000,55,1,50,1,1,connected=nan,50,132,0
1000,65,connected=-inf,50,1,1,10,50,133,0
1000,65,unconnected=5,50,1,1,10,51,134,0
1000,65,connected=nan,50,1,1,10,52,135,0
1000,65,connected=+inf,50,1,1,10,53,136,0
1000,65,connected=-inf,50,1,1,10,50,137,0
1000,65,unconnected=5,50,1,1,10,51,138,0
1000,65,connected=nan,50,1,1,10,52,139,0
1000,65,connected=+inf,50,1,1,10,53,140,0
1000,65,connected=-inf,50,1,1,10,50,141,0
1000,65,unconnected=5,50,1,1,10,51,142,0
1000,65,connected=nan,50,1,1,10,52,143,0
1000,65,connected=+inf,70,1,1,10,53,144,0
1000,65,1,60,1,1,10,60,145,0
1000,62,1,60,1,1,10,60,146,0
1000,55,connected=-inf,60,1,1,10,60,147,0
1000,55,unconnected=5,60,1,1,10,59,148,0
1000,55,connected=nan,60,1,1,10,58,149,0
1000,55,connected=+inf,60,1,1,10,57,150,0
1000,55,connected=-inf,60,1,1,10,60,151,0
1000,55,unconnected=5,65,1,1,10,59,152,0
1000,55,connected=nan,50,1,1,10,58,153,0
1000,55,connected=+inf,50,1,1,10,57,154,0
1000,75,connected=-inf,58,1,1,0,58,155,0
1000,75,unconnected=5,50,1,1,0,59,156,0
1000,75,unconnected=5,50,1,1,0,60,157,0
1000,75,unconnected=5,50,1,1,0,61,158,0
1000,75,unconnected=5,50,1,1,0,62,159,0
1000,75,unconnected=5,50,1,1,0,63,160,0
1000,75,unconnected=5,50,1,1,0,64,161,0
1000,75,unconnected=5,50,1,1,0,65,162,0
1000,75,unconnected=5,50,1,1,0,66,163,0
1000,75,unconnected=5,50,1,1,0,67,164,0
1000,75,unconnected=5,50,1,1,0,68,165,0
1000,75,unconnected=5,50,1,1,0,69,166,0
1000,75,connected=nan,50,1,1,0,70,167,0
1000,75,connected=+inf,50,1,1,0,71,168,0
1000,75,connected=-inf,70,1,1,0,70,169,0
1000,75,unconnected=5,70,1,1,0,71,170,0
1000,75,connected=nan,60,1,1,0,72,171,0
1000,75,connected=+inf,60,1,1,0,73,172,0
1000,75,connected=+inf,60,1,1,0,74,173,0
1000,75,connected=+inf,60,1,1,0,75,174,0
1000,70,connected=-inf,60,1,1,10,60,175,0
1000,70,unconnected=5,60,1,1,10,61,176,0
1000,70,connected=nan,60,1,1,10,62,177,0
1000,70,connected=+inf,60,1,1,10,63,178,0
1000,75,connected=-inf,60,1,1,10,60,179,0
1000,connected=-inf,unconnected=5,connected=-inf,0,0,10,60,180,0
1000,unconnected=5,connected=nan,unconnected=5,1,1,10,59,181,0
1000,connected=nan,connected=+inf,connected=nan,1,1,10,60,182,0
1000,connected=+inf,connected=+inf,connected=+inf,1,1,10,61,183,0
1000,55,connected=+inf,60,1,1,10,60,184,0
1000,55,1,50,1,1,10,50,185,0
1000,80,1,50,1,1,10,50,186,0
1000,connected=-inf,1,50,1,1,10,50,187,0
1000,unconnected=5,1,50,1,1,10,50,188,0
1000,connected=nan,1,50,1,1,10,50,189,0
1000,connected=+inf,1,50,1,1,10,50,190,0
1000,55,1,50,1,1,10,50,191,0
1000,80,1,50,1,1,10,50,192,0
1000,connected=-inf,1,connected=-inf,1,1,10,-inf,193,0
1000,unconnected=5,1,unconnected=5,1,1,10,0,194,0
1000,connected=nan,1,connected=nan,1,1,10,+inf,195,0
1000,connected=+inf,1,connected=+inf,1,1,10,+inf,196,0
1000,65,connected=-inf,50,1,1,connected=-inf,50,197,0
1000,50,unconnected=5,50,1,1,unconnected=5,50,198,0
1000,50,connected=nan,50,1,1,connected=nan,50,199,0
1000,50,connected=+inf,50,1,1,connected=+inf,50,200,0
1000,50,connected=-inf,50,connected=-inf,connected=-inf,connected=-inf,50,201,0
1000,50,unconnected=5,50,unconnected=5,unconnected=5,unconnected=5,50,202,0
1000,65,connected=nan,50,connected=nan,connected=nan,connected=nan,50,203,0
1000,65,connected=+inf,50,connected=+inf,connected=+inf,connected=+inf,50,204,0
1000,50,1,50,connected=+inf,connected=+inf,connected=+inf,50,205,0
1000,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,unconnected=5,50,206,0
1000,55.5,1,50.5,1,1,0,50.5,207,0
1000,60.5,0,50.5,1,1,0,51.5,208,0
1000,60.5,0,50.5,1,1,0,52.5,209,0
1000,60.5,0,50.5,1,1,0,53.5,210,0
1000,60.5,0,50.5,1,1,0,54.5,211,0
1000,60.5,0,50.5,1,1,0,55.5,212,0
1000,60.5,0,50.5,1,1,0,56.5,213,0
1000,60.5,0,50.5,1,1,0,57.5,214,0
1000,60.5,0,50.5,1,1,0,58.5,215,0
1000,60.5,0,50.5,1,1,0,59.5,216,0
1000,60.5,0,50.5,1,1,0,60.5,217,0
1000,60.5,0,50.5,1,1,5.5,60.5,218,0
1000,60.5,1,58.5,0.5,0.5,10.5,58.5,219,0
1000,60.5,0,58.5,0.5,0.5,10.5,59,220,0
1000,60.5,0,58.5,0.5,0.5,10.5,59.5,221,0
1000,60.5,0,58.5,0.5,0.5,10.5,60,222,0
1000,60.5,0,58.5,0.5,0.5,10.5,60.5,223,0
1000,55.5,1,58.5,0.5,0.5,0,58.5,224,0
1000,55.5,0,58.5,0.5,0.5,0,58,225,0
1000,55.5,0,58.5,0.5,0.5,0,57.5,226,0
1000,55.5,0,58.5,0.5,0.5,0,57,227,0
1000,55.5,0,58.5,0.5,0.5,0,56.5,228,0
1000,55.5,0,58.5,0.5,0.5,0,56,229,0
1000,55.5,0,58.5,0.5,0.5,0,55.5,230,0
1000,55.5,0.5,58.5,0.5,0.5,0,58.5,231,0
1000,55.5,0.5,58.5,0.5,0.5,0,58.0,232,1
1000,connected=65,0,50,1,1,10,65,233,0
1000,connected=65,0,50,1,1,10,50,234,1
1000,65,1,50,1,1,10,51,237,1
1000,65,1,50,1,1,10,52,238,1
1000,65,1,50,1,1,10,53,239,1
1000,65,1,50,1,1,10,54,240,1
1000,65,1,50,1,1,10,55,241,1
1000,65,1,50,1,1,10,56,242,1
1000,65,1,50,1,1,10,57,243,1
1000,65,1,50,1,1,10,58,244,1
1000,65,1,50,1,1,10,59,245,1
1000,65,1,50,1,1,10,60,246,1
1000,65,1,70,1,1,10,65,247,1
1000,65,1,60,1,1,10,65,248,1
1000,62,1,60,1,1,10,62,249,1
1000,55,1,60,1,1,10,55,250,1
1000,55,1,60,1,1,10,55,251,1
1000,55,1,60,1,1,10,55,252,1
1000,55,1,60,1,1,10,55,253,1
1000,55,1,60,1,1,10,55,254,1
1000,55,1,65,1,1,10,55,255,1
1000,75,1,50,1,1,0,56,256,1
1000,75,1,50,1,1,0,57,257,1
1000,75,1,50,1,1,0,58,258,1
1000,75,1,50,1,1,0,59,259,1
1000,75,1,50,1,1,0,60,260,1
1000,75,1,50,1,1,0,61,261,1
1000,75,1,50,1,1,0,62,262,1
1000,75,1,50,1,1,0,63,263,1
1000,75,1,50,1,1,0,64,264,1
1000,75,1,50,1,1,0,65,265,1
1000,75,1,50,1,1,0,66,266,1
1000,75,1,50,1,1,0,67,267,1
1000,75,1,50,1,1,0,68,268,1
1000,75,1,50,1,1,0,69,269,1
1000,75,1,50,1,1,10,75,270,1
1000,75,1,50,1,1,10,75,271,1
1000,75,1,70,1,1,10,75,272,1
1000,75,1,70,1,1,10,75,273,1
1000,75,1,60,1,1,10,75,274,1
1000,75,1,60,1,1,10,75,275,1
1000,70,1,60,1,1,10,70,276,1
1000,70,1,60,1,1,10,70,277,1
1000,70,1,60,1,1,10,70,278,1
1000,70,1,60,1,1,10,70,279,1
1000,75,1,60,1,1,10,75,280,1
1000,connected=-inf,1,connected=-inf,1,1,10,-inf,280,1
1000,unconnected=5,1,unconnected=5,1,1,10,0,281,1