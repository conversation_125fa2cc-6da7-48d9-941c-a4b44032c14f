/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BLink;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.constants.UnitConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.enums.BAiaDirectionEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.control.BAia;
import com.honeywell.honfunctionblocks.utils.test.CsvReader;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Requirement ID: F1PLT-ADR-405 - Test case ID: F1PLT-ATC-253
 * 
 * <AUTHOR> - Ravi Bharathi .K
 * @since Jan 7, 2018
 */

@NiagaraType
public class BAiaTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BAiaTest(2979906276)1.0$ @*/
/* Generated Sun Jan 07 15:56:16 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAiaTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  	private BAia aia;
  	private static final int ITERATION_INTERVAL = 1000;
  	
  	@Override
	@BeforeClass(alwaysRun=true)
	public void setup() throws Exception {
  		aia = new BAia();
  		aia.initHoneywellComponent(null);
  	}
  	
  	@DataProvider(name="provideValidSlotNames")
	public Object[] allValidSlotNames(){
		return new Object[]{"sensor", "setPt", "disable", "tr", "deadband", "maxAOChange", "dervGain", "minAOChange", "OUTPUT"};
	}
	
	@Test(groups={"aiaSlotTest"}, dataProvider="provideValidSlotNames")
	public void testSlotAvailability(String slotName){
		Assert.assertNotNull(aia.getSlot(slotName));
	}
	
	@DataProvider(name = "provideInvalidSlotNames")
	public Object[] invalidSlotNames() {
		return new Object[] { "SENSOR", "SETPT", "DISABLE", "TR", "DEADBAND", "MAXAOCHANGE", "DERVGAIN", "MINAOCHANGE", "output", 
				"Sensor", "Setpt", "Disable", "Tr", "Deadband", "Maxaochange", "Dervgain", "Minaochange", "Output" };
	}
	
	@Test(groups={"aiaSlotTest"}, dataProvider="provideInvalidSlotNames")
	public void testInvalidSlots(String slotName){
		Assert.assertNull(aia.getSlot(slotName));
	}
	
	@Test(groups={"aiaSlotTest"})
	public void testIcoSlot(){
		BAia aia = new BAia();
		
		//check if correct icon is used for AIA
		BIcon icon = BIcon.make(ResourceConstants.ICON_DIR + "aia.png");
		BIcon aiaIcon = aia.getIcon();
		Assert.assertEquals(icon, aiaIcon);
		
		//check if new icon can be set on AIA to update modified state
		icon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		aia.setIcon(icon);
		aiaIcon = aia.getIcon();
		Assert.assertEquals(icon, aiaIcon);
		
		//testing setOUTPUT() api of AIA block
		aia.setOUTPUT(new BHonStatusNumeric(0));
	}

	/*
	 * This method will test AIA for all possible values for each slot.  This method treats AIA as a black box,
	 * compute the expected output and compare the expected output with the value returned by AIA
	 */
	@Test(groups={"aiaTestAll"}, enabled=false)
	public void testSlotValuesForBoundaryRange() throws BlockInitializationException {
		long counter = 1;
		StringBuffer failureData = new StringBuffer();
		long successCount = 1;
		
		String[] sensor = { "connected=-inf", "-10", "0", "80", "connected=+inf", "unconnected", "nan" };
		String[] setPt = { "connected=-inf", "-5", "0", "75", "connected=+inf", "unconnected", "nan" };
		String[] disable = { "connected=-inf", "-0.001", "0", "0.001", "connected=+inf", "unconnected", "nan" };
		String[] tr = { "connected=-inf", "-5", "0", "5", "connected=+inf", "unconnected", "nan" };
		String[] deadband = { "connected=-inf", "-2.5", "0", "2", "connected=+inf", "unconnected", "nan" };
		String[] maxAOChange = { "connected=-inf", "-2", "0", "2", "connected=+inf", "unconnected", "nan" };
		String[] dervGain = { "connected=-inf", "-1", "0", "1", "connected=+inf", "unconnected", "nan" };
		String[] minAOChange = { "connected=-inf", "-1", "0", "1", "connected=+inf", "unconnected", "nan" };
		String[] revAct = {"false", "true"};
		
		System.out.println("started AIA testing for all possible scenarios");
		long startTime = BAbsTime.now().getMillis();
		for (int i = 0; i < sensor.length; i++) { // sensor
			for (int j = 0; j < setPt.length; j++) { // setpt
				for (int k = 0; k < disable.length; k++) { // disable
					for (int l = 0; l < tr.length; l++) { // tr
						for (int m = 0; m < deadband.length; m++) { // deadband
							for (int n = 0; n < maxAOChange.length; n++) { // maxAOChange
								for (int o = 0; o < dervGain.length; o++) { // dervGain
									for (int p = 0; p < minAOChange.length; p++) { // minAOChange
										for (int q = 0; q < revAct.length; q++) { //revAct
											BAia aia = new BAia();
											setSlotValue(aia, BAia.sensor.getName(), sensor[i]);
											setSlotValue(aia, BAia.setPt.getName(), setPt[j]);
											setSlotValue(aia, BAia.disable.getName(), disable[k]);
											setSlotValue(aia, BAia.tr.getName(), tr[l]);
											setSlotValue(aia, BAia.deadband.getName(), deadband[m]);
											setSlotValue(aia, BAia.maxAOChange.getName(), maxAOChange[n]);
											setSlotValue(aia, BAia.dervGain.getName(), dervGain[o]);
											setSlotValue(aia, BAia.minAOChange.getName(), minAOChange[p]);
											setSlotValue(aia, BAia.revAct.getName(), revAct[q]);
											
											aia.initHoneywellComponent(null);
											calculateExpectedOutput(aia);
											double expectedOut = aia.getOUTPUT().getValue();
											
											BAia aia1 = new BAia();
											setSlotValue(aia1, BAia.sensor.getName(), sensor[i]);
											setSlotValue(aia1, BAia.setPt.getName(), setPt[j]);
											setSlotValue(aia1, BAia.disable.getName(), disable[k]);
											setSlotValue(aia1, BAia.tr.getName(), tr[l]);
											setSlotValue(aia1, BAia.deadband.getName(), deadband[m]);
											setSlotValue(aia1, BAia.maxAOChange.getName(), maxAOChange[n]);
											setSlotValue(aia1, BAia.dervGain.getName(), dervGain[o]);
											setSlotValue(aia1, BAia.minAOChange.getName(), minAOChange[p]);
											setSlotValue(aia1, BAia.revAct.getName(), revAct[q]);
											
											BExecutionParams executionParams = new BExecutionParams();
											executionParams.setIterationInterval(1000);
											aia1.initHoneywellComponent(null);
											aia1.executeBlock(executionParams);
											double actualOut = aia1.getOUTPUT().getValue();
											
											if(actualOut == expectedOut) {
												successCount++;
											} else {
												String testData = "SENSOR-["+ sensor[i] +"], SETPT-["+ setPt[j] +"], DISABLE-["+ disable[k] +"], "
														+ "TR-["+ tr[l] +"], DEADBAND-["+ deadband[m] +"], " + "MAXAOCHANGE-["+ maxAOChange[n] +"], "
														+ "DERVGAIN-["+ dervGain[o] +"], MINAOCHANGE-["+ minAOChange[p] +"], REVACT-["+ revAct[q] +"]";
												failureData.append("Test failed for test data '"+ counter +"' data - "+ testData +" - "
														+ "expected ["+ expectedOut + "] actual ["+ actualOut +"] \n");
											}
											counter++;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		long endTime = BAbsTime.now().getMillis();
		long totalTime = endTime - startTime;
		String time = "completed AIA testing for all possible scenarios, total time taken in millisecond'"+ (totalTime) +"'";
		
		if(successCount == counter) {
			Assert.assertTrue(true);
		} else {
			String failureMessage = "\n Time summary '" + time + ""  
					+ "\nTotal test data - ["+ counter +"]"
					+ "\nTotal test data passed ["+ successCount +"]"
					+ "\nTotal test data failed ["+ (counter - successCount)  +"]"
					+ "\n\nFailure summary"
					+ "\n"+failureData.toString();
			Assert.fail(failureMessage);
		}

		System.out.println(time);
	}
	
	private void calculateExpectedOutput(BAia aia) {
		double out = aia.getOUTPUT().getValue();

		// AIA functional will be disabled and output will be set to "0" for the following condition
		// disable = TRUE; sensor = not-configured or invalid; setPt = not-configured or invalid; tr
		// = not-configured or invalid
		boolean disable = false;
		if(aia.isConfigured(aia.getProperty("disable"))) {
			disable = aia.getDisable().getBoolean();
		}
		
		if (disable || !aia.isSlotValueValid(aia.getProperty("sensor")) || !aia.isSlotValueValid(aia.getProperty("setPt")) 
				|| !aia.isSlotValueValid(aia.getProperty("tr"))) {
			aia.setOUTPUT(new BHonStatusNumeric(0));

		} else {
			double sensor = aia.getSensor().getValue();
			double setPt = aia.getSetPt().getValue();
			double tr = aia.getTr().getValue();
			
			double maxAOChange = 1.1;
			if(aia.isSlotValueValid(aia.getProperty("maxAOChange"))) {
				maxAOChange = aia.getMaxAOChange().getValue();
			}
			maxAOChange *= ITERATION_INTERVAL / UnitConstants.THOUSAND_MILLI_SECOND;
			
			double deadband = 0;
			if(aia.isSlotValueValid(aia.getProperty("deadband"))) {
				deadband = aia.getDeadband().getValue();
			}
			if(deadband < 0 || deadband >= tr)
				deadband = 0;
			
			double dervGain = 0;;
			if(aia.isSlotValueValid(aia.getProperty("dervGain"))) {
				dervGain = aia.getDervGain().getValue();
			}
			
			double minAOChange = 0;
			if(aia.isSlotValueValid(aia.getProperty("minAOChange"))) {
				minAOChange = aia.getMinAOChange().getValue();
			}
			
			boolean revAct = aia.getRevAct().getOrdinal() == BAiaDirectionEnum.DIRECT_ACTING ? false : true;
			double propErr = sensor - setPt;
			if(revAct)
				propErr = -propErr;
			
			out += calculateGains(aia, dervGain, propErr, tr, deadband, maxAOChange, minAOChange);
			
			
			//limiting the actual out to be between 0 and 100
			if (out < 0)
				out = 0;
			else if (out > 100)
				out = 100;
		}

		aia.setOUTPUT(new BHonStatusNumeric(out));
	}

	/*
	 * OUTPUT CALCULATION
	 */
	private double calculateGains(BAia aia, final double dervGain, final double propErr, final double tr, final double deadband, 
			final double maxAOChange, final double minAOChange) {
		double gains = 0;
		double error = propErr;
		
		//using derivative gain only if configured
		if(dervGain > 0) {
			double deltaError = propErr - aia.getOldError();
			aia.setOldError(propErr);
			
			if(deltaError > 1)
				deltaError = 1;
			else if(deltaError < -1)
				deltaError = -1;
			
			error = propErr + dervGain*deltaError;
		}
		
		int errorDirection = 1;
		if(error < 0) {
			errorDirection = -1;
			error = -error;
		}
		
		if(error > deadband && ((tr - deadband) != 0)) {
			if(error >= tr) {
				gains = maxAOChange * errorDirection;
			} else {
				double aoDelta = maxAOChange - minAOChange;
				double v = (error - deadband) / (tr - deadband);
				gains = errorDirection * (aoDelta * Math.pow(v, 3) + minAOChange);
			}
		}
		
		return gains;
	}
	
	private void setSlotValue(BAia aia, String slotName, String slotValue) {
		if (TestDataHelper.isConnected(slotValue)) {
			BConverter converter = null;
			BNumericConst nc = new BNumericConst();
			nc.getOut().setValue(TestDataHelper.getDouble(slotValue, 0d));
			Type srcType = nc.getOut().getType();
			Type targetType = aia.getProperty(slotName).getType();

			if (srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nc, nc.getSlot("out"), aia.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				aia.add("Link?", conversionLink);
				conversionLink.activate();
			} else {
				converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nc, nc.getSlot("out"), aia.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				aia.add("Link?", conversionLink);
				conversionLink.activate();
			}
			
			return;
		}
		
		switch(slotName) {
			case "sensor":
				aia.setSensor(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "setPt":
				aia.setSetPt(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "disable":
				aia.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(slotValue,false));
				break;
				
			case "tr":
				aia.setTr(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "deadband":
				aia.setDeadband(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "maxAOChange":
				aia.setMaxAOChange(TestDataHelper.getHonStatusNumeric(slotValue, 1.1));
				break;
				
			case "dervGain":
				aia.setDervGain(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;
				
			case "minAOChange":
				aia.setMinAOChange(TestDataHelper.getHonStatusNumeric(slotValue, 0d));
				break;	
				
			case "revAct":
				boolean direction = TestDataHelper.getBoolean(slotValue); 
				aia.setRevAct(direction ? BAiaDirectionEnum.reverseActing : BAiaDirectionEnum.directActing);
				break;
		
			default:
				break;
		}
	}
	
	/*
	 * For sequential test cases
	 */
	@DataProvider(name="provideDataForAiaTest")
	public Object [][] provideDataForAiaTest() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Aia_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		
		Object[][] objArray = new Object[validInputs.size()][];
		for(int i=0;i< validInputs.size();i++){
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		} 
		
		return objArray;
	}
	
	@DataProvider(name="provideDataForAiaTest_Negate")
	public Object [][] provideDataForAiaTest_Negate() {
		BIFile file = (BIFile) BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Neg_Aia_TestData.csv").get();
		CsvReader readValidInputs;
		ArrayList<List<String>> validInputs = new ArrayList<List<String>>();
		try {
			readValidInputs = new CsvReader(file.getInputStream());
			List<String> rec;
			while ((rec = readValidInputs.read()) != null) {
				validInputs.add(rec);
			}
			readValidInputs.close();
		} catch (IOException e) {
			validInputs = null;
		}
		
		Object[][] objArray = new Object[validInputs.size()][];
		for(int i=0;i< validInputs.size();i++){
			objArray[i] = new Object[1];
			objArray[i][0] = validInputs.get(i);
		} 
		
		return objArray;
	}
	
	private ArrayList<List<String>> readTestData() {
        String testDataFilePath = "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Aia_TestData.csv";
		return TestDataHelper.readTestDataFromCSVFile(testDataFilePath);
	}
	
	private ArrayList<List<String>> readTestData_Negate() {
        String testDataFilePath = "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Neg_Aia_TestData.csv";
		return TestDataHelper.readTestDataFromCSVFile(testDataFilePath);
	}
	
	@Test(groups={"aiaTest"})
	public void testAia() throws BlockExecutionException, BlockInitializationException{
		StringBuffer failureData = new StringBuffer();
		int successCount = 0;
		ArrayList<List<String>> inputData = readTestData();
		for (int i = 0; i < inputData.size(); i++) {
			//clear all the links added by the previous test run
			BLink [] links = aia.getLinks();
			for (int j = 0; j < links.length; j++) {
				aia.remove(links[j]);
			}
			
			List<String> inputs = inputData.get(i);
			
			BExecutionParams executionParams = new BExecutionParams();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
			
			double expectedOut = TestDataHelper.getDouble(inputs.get(10), 0);
			BigDecimal bigDecimal = BigDecimal.valueOf(expectedOut).setScale(2, RoundingMode.HALF_UP);
			expectedOut = bigDecimal.doubleValue();
			
			
			populateValueToAiaBlock(inputs, aia);
			aia.executeHoneywellComponent(executionParams);
			double actualOut = aia.getOUTPUT().getValue();
			bigDecimal = BigDecimal.valueOf(actualOut).setScale(2, RoundingMode.HALF_UP);
			actualOut = bigDecimal.doubleValue();
			
			int seq = Integer.valueOf(inputs.get(13));
			if(expectedOut == actualOut) {
				successCount++;
			} else {
				failureData.append("Test failed for test data '"+ seq +"' - expected ["+ expectedOut + "] actual ["+ actualOut +"] \n");
			}
		}
		
		if(successCount == inputData.size()) {
			Assert.assertTrue(true);
		} else {
			String failureMessage = "\nTotal test data - ["+ inputData.size() +"]"
					+ "\nTotal test data passed ["+ successCount +"]"
					+ "\nTotal test data failed ["+ (inputData.size() - successCount)  +"]"
					+ "\n\nFailure summary"
					+ "\n"+failureData.toString();
			Assert.fail(failureMessage);
		}
	}
	
	
	@Test(groups={"aiaTest_Negate"})
	public void testAia_Negate() throws BlockExecutionException, BlockInitializationException{
		StringBuffer failureData = new StringBuffer();
		int successCount = 0;
		ArrayList<List<String>> inputData = readTestData_Negate();
		for (int i = 0; i < inputData.size(); i++) {
			//clear all the links added by the previous test run
			BLink [] links = aia.getLinks();
			for (int j = 0; j < links.length; j++) {
				aia.remove(links[j]);
			}
			
			List<String> inputs = inputData.get(i);
			
			BExecutionParams executionParams = new BExecutionParams();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
			
			double expectedOut = TestDataHelper.getDouble(inputs.get(10), 0);
			BigDecimal bigDecimal = BigDecimal.valueOf(expectedOut).setScale(2, RoundingMode.HALF_UP);
			expectedOut = bigDecimal.doubleValue();
			
			
			populateValueToAiaBlock_Negate(inputs, aia);
			aia.executeHoneywellComponent(executionParams);
			double actualOut = aia.getOUTPUT().getValue();
			bigDecimal = BigDecimal.valueOf(actualOut).setScale(2, RoundingMode.HALF_UP);
			actualOut = bigDecimal.doubleValue();
			
			int seq = Integer.valueOf(inputs.get(13));
			if(expectedOut == actualOut) {
				successCount++;
			} else {
				failureData.append("Test failed for test data '"+ seq +"' - expected ["+ expectedOut + "] actual ["+ actualOut +"] \n");
			}
		}
		
		if(successCount == inputData.size()) {
			Assert.assertTrue(true);
		} else {
			String failureMessage = "\nTotal test data - ["+ inputData.size() +"]"
					+ "\nTotal test data passed ["+ successCount +"]"
					+ "\nTotal test data failed ["+ (inputData.size() - successCount)  +"]"
					+ "\n\nFailure summary"
					+ "\n"+failureData.toString();
			Assert.fail(failureMessage);
		}
	}

	private void populateValueToAiaBlock(List<String> inputs, BAia aia) {
		setSlotValue(aia, BAia.sensor.getName(), inputs.get(1));
		setSlotValue(aia, BAia.setPt.getName(), inputs.get(2));
		setSlotValue(aia, BAia.disable.getName(), inputs.get(3));
		setSlotValue(aia, BAia.tr.getName(), inputs.get(4));
		setSlotValue(aia, BAia.deadband.getName(), inputs.get(5));
		setSlotValue(aia, BAia.maxAOChange.getName(), inputs.get(6));
		setSlotValue(aia, BAia.dervGain.getName(), inputs.get(7));
		setSlotValue(aia, BAia.minAOChange.getName(), inputs.get(8));
		setSlotValue(aia, BAia.revAct.getName(), inputs.get(9));
	}
	
	private void populateValueToAiaBlock_Negate(List<String> inputs, BAia aia) {
		setSlotValue(aia, BAia.sensor.getName(), inputs.get(1));
		setSlotValue(aia, BAia.setPt.getName(), inputs.get(2));
		setSlotValue(aia, BAia.disable.getName(), inputs.get(3));
		boolean negate = TestDataHelper.getBoolean(inputs.get(14));
		((BNegatableFiniteStatusBoolean)aia.getDisable()).setNegate(negate);
		setSlotValue(aia, BAia.tr.getName(), inputs.get(4));
		setSlotValue(aia, BAia.deadband.getName(), inputs.get(5));
		setSlotValue(aia, BAia.maxAOChange.getName(), inputs.get(6));
		setSlotValue(aia, BAia.dervGain.getName(), inputs.get(7));
		setSlotValue(aia, BAia.minAOChange.getName(), inputs.get(8));
		setSlotValue(aia, BAia.revAct.getName(), inputs.get(9));
	}
	
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BAia source = new BAia();
		BMaximum target = new BMaximum();		
		
		//checking is any of the Aia source slot can be linked to another functional block
		LinkCheck linkCheck = target.checkLink(source, source.getSlot("sensor"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("setPt"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		try {
			linkCheck = target.checkLink(source, source.getSlot("disable"), target.getSlot("in1"), null);
		} catch (Exception e) {
			//There will not be any link type available to link from FiniteStatusBoolean to HonStatusNumeric
			Assert.assertTrue(true);
		}
		
		linkCheck = target.checkLink(source, source.getSlot("tr"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("deadband"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("maxAOChange"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("dervGain"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("minAOChange"), target.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		try {
			linkCheck = target.checkLink(source, source.getSlot("revAct"), target.getSlot("in1"), null);
		} catch (Exception e) {
			//There will not be any link type available to link from BAiaDirectionEnum to HonStatusNumeric
			Assert.assertTrue(true);
		}
		
		//checking if link to Aia:revAct is blocked
		BComponent c = new BComponent();
		c.add("out", BAiaDirectionEnum.directActing);
		linkCheck = source.checkLink(c, c.getSlot("out"), source.getSlot("revAct"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		
		//checking if Aia:sesnor slot can be linked
		linkCheck = source.checkLink(target, target.getSlot("OUTPUT"), source.getSlot("sensor"), null);
		Assert.assertTrue(linkCheck.isValid());
		
		//checking if link to OUT slot is blocked
		linkCheck = source.checkLink(target, target.getSlot("OUTPUT"), source.getSlot("OUTPUT"), null);
		Assert.assertFalse(linkCheck.isValid());
		
	}
	
	@Test()
	public void testConfigProperties() {
		List<Property> configList = aia.getConfigPropertiesList();
		Assert.assertEquals(configList.get(0).getName(), BAia.revAct.getName());
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = aia.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BAia.sensor.getName(), BAia.setPt.getName(),BAia.disable.getName(), BAia.tr.getName(),
				BAia.maxAOChange.getName(), BAia.deadband.getName(),BAia.dervGain.getName(),BAia.minAOChange.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = aia.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BAia.OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Aia_Config_bog/config/file.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BAia Aia = (BAia)folder.get("AIA");
			Assert.assertEquals(Aia.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}
	
	
	@AfterClass
	public void tearDown(){
		aia = null;
	}
	
}
