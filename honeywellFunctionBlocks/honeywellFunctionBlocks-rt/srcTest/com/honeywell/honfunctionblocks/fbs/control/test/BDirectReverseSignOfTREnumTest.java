/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.fbs.control.BDirectReverseSignOfTREnum;



/**
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON>
 * @since May 15, 2018
 */
@NiagaraType
public class BDirectReverseSignOfTREnumTest extends BTestNg  {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BDirectReverseSignOfTREnumTest(**********)1.0$ @*/
/* Generated Tue May 15 16:35:31 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDirectReverseSignOfTREnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { 0, BDirectReverseSignOfTREnum.DirectActing }, { 1, BDirectReverseSignOfTREnum.ReverseActing }, { 2, BDirectReverseSignOfTREnum.SignOfTR } };
	}

	@Test(dataProvider = "enumOrdinal")
	public void testDirectReverseSignOfTRByMakeOrdinal(int ordinal, BDirectReverseSignOfTREnum toEnum) {
		Assert.assertEquals(BDirectReverseSignOfTREnum.make(ordinal), toEnum);
	}

	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { "DirectActing", BDirectReverseSignOfTREnum.DirectActing }, { "ReverseActing", BDirectReverseSignOfTREnum.ReverseActing }, { "SignOfTR", BDirectReverseSignOfTREnum.SignOfTR } };
	}

	@Test(dataProvider = "enumTag")
	public void testDirectReverseSignOfTRByMakeTag(String tag, BDirectReverseSignOfTREnum adEEnum) {
		Assert.assertEquals(BDirectReverseSignOfTREnum.make(tag), adEEnum);
	}

}
