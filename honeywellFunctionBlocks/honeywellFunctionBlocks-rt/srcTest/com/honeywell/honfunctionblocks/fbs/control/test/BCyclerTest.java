/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BInteger;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.analog.BMaximum;
import com.honeywell.honfunctionblocks.fbs.control.BCycler;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation Cycler block test cases as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TODO
 * <AUTHOR> - Suresh Khatri
 * @since Feb 9, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BCyclerTest  extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BCyclerTest(2979906276)1.0$ @*/
/* Generated Mon Feb 05 20:03:18 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCyclerTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() throws BlockInitializationException {
		cyclerBlock = new BCycler();
		cyclerBlock.initHoneywellComponent(null);
		executionParams = new BExecutionParams();
		executionParams.setIterationInterval(1000);
		executionParamsMAXONOFFTIME = new BExecutionParams();
		executionParamsMAXONOFFTIME.setIterationInterval(64800);
		cyclerBlock1 = new BCycler();
		cyclerBlock3 = new BCycler();
		cyclerBlock7 = new BCycler();
		cyclerBlock8 = new BCycler();
		cyclerBlock9 = new BCycler();
		cyclerBlock10 = new BCycler();
		cyclerBlock11 = new BCycler();
		
	}

	@AfterClass(alwaysRun=true)
	public void tearDown() {
		cyclerBlock = null;
		cyclerBlock1 = null;
 		cyclerBlock3 = null;
		cyclerBlock7 = null;
		cyclerBlock8 = null;
		cyclerBlock9 = null;
		cyclerBlock10 = null;
		cyclerBlock11 = null;
		
		
		executionParams = null;
	}

	@DataProvider(name = "provideInSlotNames")
	public Object[][] createInputSlotNames() {
		return new Object[][] { { "disable" }, { "overrideOff" }, { "in" }, { "maxStgs" } , { "minOn" } , { "minOff" } , { "intstgOn" } , { "intstgOff" } };
	}

	@DataProvider(name = "provideConfigSlotNames")
	public Object[][] createConfigSlotNames() {
		return new Object[][] { { "cph" }, { "hyst" }, { "anticipatorAuthority" } };
	}

	@DataProvider(name = "provideOutputSlotNames")
	public Object[][] createOutputSlotNames() {
		return new Object[][] { { "STAGES_ACTIVE" } };
	}

	@DataProvider(name = "provideMiscSlotNames")
	public Object[][] createExecOrderSlotName() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createConfigSlotNames()));
		slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	}

	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] { { "InvalidFlag" }, { "TailOperation" } };
	}

	@Test(dataProvider = "provideInvalidSlotNames")
	public void testInvalidSlots(String slotName) {
		Assert.assertNull(cyclerBlock.getSlot(slotName));
	}

	@Test(dataProvider = "provideAllSlotNames")
	public void testSlotAvailability(String slotName) {
		Assert.assertNotNull(cyclerBlock.getSlot(slotName));
	}

	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "stager_thermostat_cycler.png");
		BIcon actualFbIcon = cyclerBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		cyclerBlock.setIcon(expectedFbIcon);
		actualFbIcon = cyclerBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "provideSampleValues")
	public Object[][] sampleValues() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }, { 2.2250738585072014E-308 }, { -1.7976931348623157e+308 }, { Double.NEGATIVE_INFINITY }, { Double.POSITIVE_INFINITY }, { Double.NaN } };
	}

	@Test(dataProvider = "provideSampleValues")
	public void testSettingValueInCyclerBlock(double snValue) {
		cyclerBlock.setIn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIn().getValue(), snValue, 0.1);

		cyclerBlock.setIn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIn().getValue(), snValue, 0.1);

		cyclerBlock.setMaxStgs(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMaxStgs().getValue(), snValue, 0.1);

		cyclerBlock.setMinOn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMinOn().getValue(), snValue, 0.1);

		cyclerBlock.setMinOff(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMinOff().getValue(), snValue, 0.1);

		cyclerBlock.setIntstgOn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIntstgOn().getValue(), snValue, 0.1);

		cyclerBlock.setIntstgOff(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIntstgOff().getValue(), snValue, 0.1);

		cyclerBlock.setSTAGES_ACTIVE(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getSTAGES_ACTIVE().getValue(), snValue, 0.1);
	}

	@DataProvider(name = "provideSampleValues1")
	public Object[][] sampleValues1() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }};
	}

	@Test(dataProvider = "provideSampleValues1")
	public void testSettingValueInCyclerBlock1(double snValue) {
		cyclerBlock.setCph((int) snValue);
		Assert.assertEquals(cyclerBlock.getCph(), snValue, 0.1);

		cyclerBlock.setHyst((int) snValue);
		Assert.assertEquals(cyclerBlock.getHyst(), snValue, 0.1);

		cyclerBlock.setAnticipatorAuthority((int) snValue);
		Assert.assertEquals(cyclerBlock.getAnticipatorAuthority(), snValue, 0.1);
	}
	
	@Test
	public void testCyclerBlockWithTestData() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_data_with_negate_on_overrideOff.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {

			setupNumericSlot(cyclerBlock1, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock1, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock1, BCycler.disable.getName(), inputs.get(6));
			setupNegateSlot(cyclerBlock1, BCycler.disable.getName(), inputs.get(15));
			setupNumericSlot(cyclerBlock1, BCycler.overrideOff.getName(), inputs.get(7));
			setupNegateSlot(cyclerBlock1, BCycler.overrideOff.getName(), inputs.get(16));

			setupNumericSlot(cyclerBlock1, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock1, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock1, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock1, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock1, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock1, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock1, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock1.initHoneywellComponent(null);
			}
			if(TestDataHelper.getDouble(inputs.get(0), 0d) > 100000) {
				executionParams.setIterationInterval((int)TestDataHelper.getDouble(inputs.get(0), 0d));
			}else {
				executionParams.setIterationInterval(1000);	
			}
			cyclerBlock1.executeHoneywellComponent(executionParams);
						
			Assert.assertEquals(cyclerBlock1.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}
	

	@Test
	public void testCyclerBlockWithTestData3() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData3.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock3, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock3, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock3, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock3, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock3, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock3, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock3, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock3, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock3, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock3, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock3, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock3.initHoneywellComponent(null);
			}
			if(TestDataHelper.getDouble(inputs.get(0), 0d) > 100000) {
				executionParams.setIterationInterval((int)TestDataHelper.getDouble(inputs.get(0), 0d));
			}else {
				executionParams.setIterationInterval(1000);	
			}
			cyclerBlock3.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock3.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}

	@Test
	public void testCyclerBlockWithTestData7() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData7.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock7, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock7, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock7, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock7, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock7, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock7, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock7, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock7, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock7, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock7, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock7, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock7.initHoneywellComponent(null);
			}
			cyclerBlock7.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock7.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}

	@Test
	public void testCyclerBlockWithTestData8() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData8.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock8, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock8, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock8, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock8, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock8, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock8, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock8, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock8, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock8, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock8, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock8, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock8.initHoneywellComponent(null);
			}
			cyclerBlock8.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock8.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}

	@Test
	public void testCyclerBlockWithTestData9() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData9.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock9, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock9, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock9, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock9, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock9, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock9, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock9, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock9, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock9, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock9, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock9, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock9.initHoneywellComponent(null);
			}
			cyclerBlock9.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock9.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}
	
	@Test
	public void testCyclerBlockWithTestData10() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData10.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock10, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock10, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock10, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock10, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock10, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock10, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock10, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock10, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock10, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock10, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock10, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock10.initHoneywellComponent(null);
			}
			cyclerBlock10.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock10.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}
	
	@Test
	public void testCyclerBlockWithTestData11() throws BlockExecutionException, BlockInitializationException {
		ArrayList<List<String>> readTestDataFromCSVFile = TestDataHelper.readTestDataFromCSVFile(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_TestData11.csv");
		for (List<String> inputs : readTestDataFromCSVFile) {
			setupNumericSlot(cyclerBlock11, BCycler.in.getName(), inputs.get(1));

			setupNumericSlot(cyclerBlock11, BCycler.maxStgs.getName(), inputs.get(5));
			setupNumericSlot(cyclerBlock11, BCycler.disable.getName(), inputs.get(6));
			setupNumericSlot(cyclerBlock11, BCycler.overrideOff.getName(), inputs.get(7));

			setupNumericSlot(cyclerBlock11, BCycler.minOn.getName(), inputs.get(8));
			setupNumericSlot(cyclerBlock11, BCycler.minOff.getName(), inputs.get(9));
			setupNumericSlot(cyclerBlock11, BCycler.intstgOn.getName(), inputs.get(10));
			setupNumericSlot(cyclerBlock11, BCycler.intstgOff.getName(), inputs.get(11));
			setupNumericSlot(cyclerBlock11, BCycler.anticipatorAuthority.getName(), inputs.get(12));
			setupNumericSlot(cyclerBlock11, BCycler.hyst.getName(), inputs.get(13));
			setupNumericSlot(cyclerBlock11, BCycler.cph.getName(), inputs.get(14));
			if(inputs.get(0).equalsIgnoreCase("0")) {
				cyclerBlock11.initHoneywellComponent(null);
			}
			cyclerBlock11.executeHoneywellComponent(executionParams);
			Assert.assertEquals(cyclerBlock11.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(inputs.get(2), 0.0d), Arrays.toString(inputs.toArray()));
		}
	}

	public void setupNumericSlot(BCycler cyclerBlock, final String slotName, final String inputValue) {
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = cyclerBlock.getProperty(slotName).getType();			
			BConverter converter = null;
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
	                     converter = new BStatusNumericToHonStatusNumeric();
	                     BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),cyclerBlock.getSlot(slotName),converter);
	                     conversionLink.setEnabled(true);
	                     cyclerBlock.add("Link?",conversionLink );                    
	                     conversionLink.activate();
	                     nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
	                } else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				converter = new BStatusNumericToFiniteStatusBoolean();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),cyclerBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				cyclerBlock.add("Link?", conversionLink);				
				conversionLink.activate();
			}else{
				cyclerBlock.linkTo(nm1, nm1.getSlot("out"), cyclerBlock.getSlot(slotName));
			}			
			return;
		}
		switch (slotName) {
			case "in":
				cyclerBlock.setIn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "maxStgs":
				cyclerBlock.setMaxStgs(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "minOn":
				cyclerBlock.setMinOn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "minOff":
				cyclerBlock.setMinOff(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "intstgOn":
				cyclerBlock.setIntstgOn(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "intstgOff":
				cyclerBlock.setIntstgOff(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;
			case "overrideOff":
				cyclerBlock.setOverrideOff(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
				break;
			case "disable":
				cyclerBlock.setDisable(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
				break;
			case "cph":
				cyclerBlock.setCph(TestDataHelper.getInt(inputValue, 0));
				break;
			case "hyst":
				cyclerBlock.setHyst(TestDataHelper.getDouble(inputValue,0.0d));
				break;
			case "anticipatorAuthority":
				cyclerBlock.setAnticipatorAuthority(TestDataHelper.getInt(inputValue, 0));
				break;
		}
	}
	
	@Test
	public void testupgradesenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/Cycler_Config_Bog/config/file.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BCycler cycler  = (BCycler) folder.get("Cycler");
			Assert.assertEquals(cycler.getDisable().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(cycler.getOverrideOff().getType(), BNegatableFiniteStatusBoolean.TYPE);
			}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}

	//@Test
	public void testLinkRules() {
		BCycler cyclerBlock = new BCycler();
		checkOutgoingLink(cyclerBlock, BCycler.in, false);
		checkOutgoingLink(cyclerBlock, BCycler.maxStgs, false);
		checkOutgoingLink(cyclerBlock, BCycler.minOn, false);
		checkOutgoingLink(cyclerBlock, BCycler.minOff, false);
		checkOutgoingLink(cyclerBlock, BCycler.intstgOn, false);
		checkOutgoingLink(cyclerBlock, BCycler.intstgOff, false);
		checkOutgoingLink(cyclerBlock, BCycler.cph, false);
		checkOutgoingLink(cyclerBlock, BCycler.hyst, false);
		checkOutgoingLink(cyclerBlock, BCycler.anticipatorAuthority, false);
		checkOutgoingLink(cyclerBlock, BCycler.overrideOff, false);
		checkOutgoingLink(cyclerBlock, BCycler.disable, false);
	}

	//@Test
	public void testLinkRules1() {
		BCycler target = new BCycler();
		BMaximum source = new BMaximum();		
		
		LinkCheck linkCheck = source.checkLink(target, target.getSlot("in"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("maxStgs"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("minOn"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("minOff"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("intstgOn"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		linkCheck = source.checkLink(target, target.getSlot("intstgOff"), source.getSlot("in1"), null);
		Assert.assertFalse(linkCheck.isValid());
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("overrideOff"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("disable"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("cph"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("hyst"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		try {
			linkCheck = source.checkLink(target, target.getSlot("anticipatorAuthority"), source.getSlot("in1"), null);
		} catch (Exception e) {
			Assert.assertTrue(true);
		}
		
		BComponent c = new BComponent();
		c.add("out1", BInteger.make(1));
		linkCheck = target.checkLink(c, c.getSlot("out1"), target.getSlot("cph"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		c = new BComponent();
		c.add("out2", BInteger.make(1));
		linkCheck = target.checkLink(c, c.getSlot("out2"), target.getSlot("hyst"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		c = new BComponent();
		c.add("out3", BInteger.make(1));
		linkCheck = target.checkLink(c, c.getSlot("out3"), target.getSlot("anticipatorAuthority"), null);
		Assert.assertFalse(linkCheck.isValid());
				
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("in"), null);
		Assert.assertTrue(linkCheck.isValid());
		
		linkCheck = target.checkLink(source, source.getSlot("OUTPUT"), target.getSlot("STAGES_ACTIVE"), null);
		Assert.assertFalse(linkCheck.isValid());
	}
	
	@DataProvider(name = "provideSampleValues3")
	public Object[][] sampleValues3() {
		return new Object[][] {{ -20 }, { 0 }, {20 }};
	}

	@Test(dataProvider = "provideSampleValues3")
	public void testMaxONOFFTimerInCyclerBlock(double snValue) throws BlockExecutionException {
		
		cyclerBlock.executeHoneywellComponent(executionParamsMAXONOFFTIME);
		
		cyclerBlock.setIn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIn().getValue(), snValue, 0.1);

		cyclerBlock.setIn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIn().getValue(), snValue, 0.1);

		cyclerBlock.setMaxStgs(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMaxStgs().getValue(), snValue, 0.1);

		cyclerBlock.setMinOn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMinOn().getValue(), snValue, 0.1);

		cyclerBlock.setMinOff(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getMinOff().getValue(), snValue, 0.1);

		cyclerBlock.setIntstgOn(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIntstgOn().getValue(), snValue, 0.1);

		cyclerBlock.setIntstgOff(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getIntstgOff().getValue(), snValue, 0.1);

		cyclerBlock.setSTAGES_ACTIVE(new BHonStatusNumeric(snValue));
		Assert.assertEquals(cyclerBlock.getSTAGES_ACTIVE().getValue(), snValue, 0.1);
		
		
		//Assert.assertEquals(cyclerBlock.getSTAGES_ACTIVE().getValue(), TestDataHelper.getDouble(Double.toString(1.0), 0.0d));
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = cyclerBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BCycler.cph.getName(), BCycler.hyst.getName(), BCycler.anticipatorAuthority.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = cyclerBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BCycler.disable.getName(), BCycler.overrideOff.getName(), BCycler.in.getName(), BCycler.maxStgs.getName(), BCycler.minOn.getName(), BCycler.minOff.getName(), BCycler.intstgOn.getName(), BCycler.intstgOff.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}

	public void setupNegateSlot(BCycler cyclerBlock, final String slotName, final String negateString) {
		boolean negate = false;
		if(negateString != null) {
			negate =  TestDataHelper.getBoolean(negateString);
			
			//Set Negate Value
			INegatableStatusValue nsb = (INegatableStatusValue) cyclerBlock.get(slotName);
			nsb.setNegate(negate);
		}
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = cyclerBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BCycler.STAGES_ACTIVE.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	private void checkOutgoingLink(BCycler block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = cyclerBlock.checkLink(cyclerBlock, cyclerBlock.getSlot(prop.getName()), block.getSlot(prop.getName()), null);
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	private BCycler cyclerBlock;
	private BCycler cyclerBlock1;
  	private BCycler cyclerBlock3;
 	private BCycler cyclerBlock7;
  	private BCycler cyclerBlock8;
  	private BCycler cyclerBlock9;
  	private BCycler cyclerBlock10;
  	private BCycler cyclerBlock11;
  	
	private BExecutionParams executionParams;
	private BExecutionParams executionParamsMAXONOFFTIME;
}
