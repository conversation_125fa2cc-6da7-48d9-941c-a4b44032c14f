/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.control.BFlowControl;
import com.honeywell.honfunctionblocks.fbs.control.BFlowControlUnitEnum;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of FlowControl block as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Test case ID: F1PLT-ATC-252
 * <AUTHOR> - RSH.Lakshminarayanan
 * @since Jan 3, 2018
 */

@NiagaraType
public class BFlowControlTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BFlowControlTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFlowControlTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	BFlowControl flowControlBlock;
	
	@BeforeClass(alwaysRun=true)
	public void setUp() throws BlockInitializationException {
		flowControlBlock = new BFlowControl();
		flowControlBlock.initHoneywellComponent(null);
	}
	
	@DataProvider(name = "provideInSlotNames")
	public Object[][] inputSlotNames() {
		return new Object[][]{{"cmdFlowPercent"}, {"sensedFlowVol"}, {"minFlowSetPt"}, {"maxFlowSetPt"}, {"manFlowOverride"}, {"manFlowValue"}, {"ductArea"}};
	}
	
	@DataProvider(name = "provideOutSlotNames")
	public Object[][] outputSlotNames() {
		return new Object[][]{{"EFF_FLOW_SETPT"}, {"DAMPER_POS"}};
	}
	
	@DataProvider(name = "provideConfigSlotNames")
	public Object[][] configSlotNames() {
		return new Object[][]{{"units"}, {"motorSpeed"}};
	}
	
	@DataProvider(name = "provideInvalidSlotNames")
	public Object[][] invalidSlotNames() {
		return new Object[][] {{"CmdFlowPercent"}, {"SensedFlowVol"}, {"MinFlowSetPt"}, {"MaxFlowSetPt"}, {"ManFlowOverride"}, {"ManFlowValue"}, {"DuctArea"}, {"Units"}, {"MotorSpeed"},
			{"EffFlowSetPt"}, {"DamperPos"}};
	}
	
	@DataProvider(name="provideAllSlotNames")
	public Object[][] createAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(inputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(outputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(configSlotNames()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  }
	
	@Test(dataProvider="provideAllSlotNames")
	public void testSlotAvailability(String slotName){
		Assert.assertNotNull(flowControlBlock.getSlot(slotName));
	}
	
	@Test(dataProvider="provideInvalidSlotNames")
	public void testInvalidSlots(String slotName){
		Assert.assertNull(flowControlBlock.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "flow_control.png");
		BIcon actualFbIcon = flowControlBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		flowControlBlock.setIcon(expectedFbIcon);
		actualFbIcon = flowControlBlock.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "provideSampleValues")
	public Object[][] sampleValues() {
		return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {Double.POSITIVE_INFINITY}, {Double.NEGATIVE_INFINITY}, {Double.NaN}};
	}



	@Test(dataProvider="provideSampleValues")
	public void testSettingValueInFlowControlBlock(double snValue) {
		flowControlBlock.setCmdFlowPercent(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getCmdFlowPercent().getValue(), snValue, 0.1);

		flowControlBlock.setSensedFlowVol(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getSensedFlowVol().getValue(), snValue, 0.1);	  

		flowControlBlock.setMinFlowSetPt(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getMinFlowSetPt().getValue(), snValue, 0.1);

		flowControlBlock.setMaxFlowSetPt(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getMaxFlowSetPt().getValue(), snValue, 0.1);

		flowControlBlock.setDuctArea(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getDuctArea().getValue(), snValue, 0.1);

		flowControlBlock.setDAMPER_POS(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getDAMPER_POS().getValue(), snValue, 0.1);

		flowControlBlock.setEFF_FLOW_SETPT(new BHonStatusNumeric(snValue));
		Assert.assertEquals(flowControlBlock.getEFF_FLOW_SETPT().getValue(), snValue, 0.1);

		flowControlBlock.setOldError(snValue);
		Assert.assertEquals(flowControlBlock.getOldError(), snValue, 0.1);
	}

	@DataProvider(name = "provideSampleValuesForMotorSpeed")
	public Object[][] sampleValuesForMotorSpeed() {
		return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {70000}};
	}

	@Test(dataProvider="provideSampleValuesForMotorSpeed")
	public void testSettingValueInMotorSpeedSlot(int motorSpeed) {
		flowControlBlock.setMotorSpeed(motorSpeed);
		Assert.assertEquals(flowControlBlock.getMotorSpeed(),motorSpeed);
	}

	@DataProvider(name="provideTestData")
	public Object[][] getTestData() {
		return TestDataHelper.getSequencedTestDataInTestNGFormat(
				"local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/FlowControl_TestData.csv");
	}
	
	/**
	 * Testing the block execution with dataProvider which reads data from CSV
	 * file and passes each row as List<String> The advantage in this is that it
	 * can generate a readable report
	 * 
	 * @param inputs
	 * @throws BlockExecutionException
	 */
	@Test(dataProvider="provideTestData", dependsOnMethods={"testSlotAvailability"})
	public void testFlowControlLogicWithTestData(List<List<String>> inputData) throws BlockExecutionException{
		BFlowControl fcBlock = new BFlowControl();
		
		int stepNo=0;
		for (Iterator<List<String>> iterator = inputData.iterator(); iterator.hasNext();) {
			stepNo++;
			List<String> inputs = iterator.next();

			BExecutionParams executionParams = new BExecutionParams();
			executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 0));
			setupSlotValue(fcBlock, BFlowControl.cmdFlowPercent.getName(), inputs.get(1));
			setupSlotValue(fcBlock, BFlowControl.sensedFlowVol.getName(), inputs.get(2));
			setupSlotValue(fcBlock, BFlowControl.minFlowSetPt.getName(), inputs.get(3));
			setupSlotValue(fcBlock, BFlowControl.maxFlowSetPt.getName(), inputs.get(4));
			setupSlotValue(fcBlock, BFlowControl.manFlowOverride.getName(), inputs.get(5));
			setupSlotValue(fcBlock, BFlowControl.manFlowValue.getName(), inputs.get(6));
			setupSlotValue(fcBlock, BFlowControl.ductArea.getName(), inputs.get(7));
			if(inputs.get(8).equals("CFM_FT"))
				fcBlock.setUnits(BFlowControlUnitEnum.DEFAULT);
			else if(inputs.get(8).equals("LPS_MTR"))
				fcBlock.setUnits(BFlowControlUnitEnum.LpsMtr);
			else if(inputs.get(8).equals("CMH_MTR"))
				fcBlock.setUnits(BFlowControlUnitEnum.CmhMtr);			
			setupSlotValue(fcBlock, BFlowControl.motorSpeed.getName(), inputs.get(9));

			
			int loopCount;
			int travelTime = TestDataHelper.getInt(inputs.get(12), 90);
			for (loopCount = 0; loopCount < travelTime; loopCount++) {
				fcBlock.executeHoneywellComponent(executionParams);
			}

			Assert.assertEquals(fcBlock.getEFF_FLOW_SETPT().getValue(), TestDataHelper.getDouble(inputs.get(10), 0d), 0.1, "Failed EffFlowSetPt after "+loopCount+" sec at step#"+stepNo+"; InputData: "+inputs+ "OldErr :"+fcBlock.getOldError());
			Assert.assertEquals(fcBlock.getDAMPER_POS().getValue(), TestDataHelper.getDouble(inputs.get(11), 0d), 0.1, "Failed DamperPos after "+loopCount+" sec at step#"+stepNo+"; InputData: "+inputs+ "OldErr :"+fcBlock.getOldError());
		}
		fcBlock = null;
	}
	
	public void setupSlotValue(BFlowControl flowControlBlock, final String slotName, final String inputValue){
		if(TestDataHelper.isConnected(inputValue)){
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			flowControlBlock.linkTo(nm1, nm1.getSlot("out"), flowControlBlock.getSlot(slotName));
			return;
		}
		
		switch (slotName) {
		case "cmdFlowPercent":
			flowControlBlock.setCmdFlowPercent(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "sensedFlowVol":
			flowControlBlock.setSensedFlowVol(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "minFlowSetPt":
			flowControlBlock.setMinFlowSetPt(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "maxFlowSetPt":
			flowControlBlock.setMaxFlowSetPt(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "manFlowOverride":
			flowControlBlock.setManFlowOverride(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "manFlowValue":
			flowControlBlock.setManFlowValue(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "ductArea":
			flowControlBlock.setDuctArea(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
			
		case "motorSpeed":
			flowControlBlock.setMotorSpeed(TestDataHelper.getInt(inputValue, 90));
			break;
			
		default:
			break;
		}
	}
	
	//@Test(dataProvider="provideInSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForInSlot(String slotName) {
		BFlowControl tempBlock = new BFlowControl();
		LinkCheck checkLink = flowControlBlock.checkLink(flowControlBlock, flowControlBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
		Assert.assertEquals(checkLink.isValid(), false);
	}
	
	//@Test(dataProvider="provideConfigSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForConfigSlots(String slotName) {
		BFlowControl tempBlock = new BFlowControl();
		LinkCheck checkLink = flowControlBlock.checkLink(flowControlBlock, flowControlBlock.getSlot(slotName), tempBlock.getSlot(slotName), null);	   
		Assert.assertEquals(checkLink.isValid(), false);
	}

	//@Test(dataProvider="provideOutSlotNames", groups={"testLinkRules"})
	public void testLinkRulesForOutSlots(String slotName) {
		BFlowControl nm = new BFlowControl();
		LinkCheck checkLink = flowControlBlock.checkLink(nm, nm.getSlot(slotName), flowControlBlock.getSlot(slotName), null);
		Assert.assertFalse(checkLink.isValid());
	}
	
	@Test
	public void testConfigProperties() {
		List<Property> configList = flowControlBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParams = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParams);
		String[] expectedConfigParams = { BFlowControl.motorSpeed.getName(), BFlowControl.units.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParams, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = flowControlBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BFlowControl.cmdFlowPercent.getName(), BFlowControl.sensedFlowVol.getName(),BFlowControl.minFlowSetPt.getName(), BFlowControl.maxFlowSetPt.getName(),
				BFlowControl.manFlowOverride.getName(), BFlowControl.manFlowValue.getName(),BFlowControl.ductArea.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = flowControlBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BFlowControl.DAMPER_POS.getName(),BFlowControl.EFF_FLOW_SETPT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@AfterClass
	public void tearDown(){
		flowControlBlock=null;
	}
}
