/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.control.test;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusEnum;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.BRelTime;
import javax.baja.sys.BStation;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;
import javax.baja.util.BFolder;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.enums.BLeadLagStrategyEnum;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.BFBOverrideProperties;
import com.honeywell.honfunctionblocks.fbs.BFunctionBlock;
import com.honeywell.honfunctionblocks.fbs.control.BFOFOStrategy;
import com.honeywell.honfunctionblocks.fbs.control.BRuntimeStrategy;
import com.honeywell.honfunctionblocks.fbs.control.BStageDriver;
import com.honeywell.honfunctionblocks.fbs.control.BStageStaus;
import com.honeywell.honfunctionblocks.utils.test.LinkCheckUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Stage Driver block TestCase as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: F1PLT-ATC-193
 * <AUTHOR>
 * @since Jan 2 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStageDriverTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.control.test.BStageDriverTest(2979906276)1.0$ @*/
/* Generated Tue Jan 02 15:40:15 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStageDriverTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@BeforeClass(alwaysRun = true)
	public void setUp() throws Exception {
		stageDriverBlock = new BStageDriver();
		executionParams = new BExecutionParams();

		stationHandler = BTest.createTestStation();
		stationHandler.startStation();
		station = stationHandler.getStation();

		stationHandler.startStation();
	}
  
	@AfterClass
	public void tearDown() {
		stageDriverBlock = null;
		executionParams = null;

		stageDriverBlockOverride = null;
		fbContainer = null;
		stationHandler.stopStation();
		stationHandler.releaseStation();

		station = null;

		stationHandler = null;
	}

  @DataProvider(name="provideInSlotNames")
  public Object[][] createInputSlotNames() {
	  return new Object[][]{{"nStagesActive"}, {"runtimeReset"},{"leadLag"},{"maxStgs"}};	  
  }
  
  @DataProvider(name="provideOutputSlotNames")
  public Object[][] createOutputSlotNames() {
	  return new Object[][] {{"STAGE1"},{"STAGE2"},{"STAGE3"},{"STAGE4"},{"STAGE5"},{"STAGE6"},{"STAGE7"},{"STAGE8"},{"STAGE9"},{"STAGE10"},
		  {"STAGE11"},{"STAGE12"},{"STAGE13"},{"STAGE14"},{"STAGE15"},{"STAGE16"},{"STAGE17"},{"STAGE18"},{"STAGE19"},{"STAGE20"}};
  }
  
  @DataProvider(name="provideMiscSlotNames")
  public Object[][] createExecOrderSlotName() {
	  return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
  }
  
  @DataProvider(name="provideAllSlotNames")
  public Object[][] createAllSlotNames() {
	  List<Object[]> slotArrayList = Lists.newArrayList();
	  slotArrayList.addAll(Arrays.asList(createInputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createOutputSlotNames()));
	  slotArrayList.addAll(Arrays.asList(createExecOrderSlotName()));
	  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
  }
  
  @DataProvider(name = "provideInvalidSlotNames")
  public Object[][] invalidSlotNames() {
	  return new Object[][]{{"STAGE22"}, {"StagesActive"}, {"RuntimeReset"}, {"Offset"}, {"Leadlag"}, {"MaxStgs"}};
  }
  
  @Test(dataProvider="provideInvalidSlotNames")
  public void testInvalidSlots(String slotName){
	  Assert.assertNull(stageDriverBlock.getSlot(slotName));
  }  
  
  @Test(dataProvider="provideAllSlotNames")
  public void testSlotAvailability(String slotName) {
	  Assert.assertNotNull(stageDriverBlock.getSlot(slotName));
  }
  
  @Test(groups={"testIconSlot"})
  public void testIconSlot(){
	  //check if correct icon is used for AIA
	  BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "stage_driver.png");
	  BIcon actualFbIcon = stageDriverBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);

	  //check if new icon can be set on AIA to update modified state
	  expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
	  stageDriverBlock.setIcon(expectedFbIcon);
	  actualFbIcon = stageDriverBlock.getIcon();
	  Assert.assertEquals(expectedFbIcon, actualFbIcon);
  }
  
  @DataProvider(name = "provideSampleValues")
  public Object[][] sampleValues() {
	  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}};
  }

  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInStageDriverBlock(double snValue) {
	  stageDriverBlock.setNStagesActive(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(stageDriverBlock.getNStagesActive().getValue(), snValue, 0.1);
	  
	  stageDriverBlock.setRuntimeReset(new BHonStatusNumeric(snValue));
	  Assert.assertEquals(stageDriverBlock.getRuntimeReset().getValue(), snValue, 0.1);	  
  }
  
  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInStageDriverBlockFOFOData(int snValue) {
	  BFOFOStrategy lastFOFOParams = stageDriverBlock.getLlFOFOStrategyParams();
	  lastFOFOParams.setSeqStartPtr(snValue);
	  Assert.assertEquals(lastFOFOParams.getSeqStartPtr(), snValue);
	  
	  lastFOFOParams.setOldNumStages(snValue);
	  Assert.assertEquals(lastFOFOParams.getOldNumStages(), snValue);
	  
	  stageDriverBlock.setLlFOFOStrategyParams(lastFOFOParams);
	  Assert.assertNotNull(stageDriverBlock.getLlFOFOStrategyParams());
  }
  
  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInStageDriverBlockRunTimeData(double snValue) {
	  BRuntimeStrategy stageRunTime = stageDriverBlock.getLlRuntimeStrategyParams();
	  stageRunTime.setTotalRuntime(snValue);
	  Assert.assertEquals(stageRunTime.getTotalRuntime(), snValue, 0.1);
	  
	  stageRunTime.setRunTime1(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime1(), snValue, 0.1);
	  
	  stageRunTime.setRunTime2(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime2(), snValue, 0.1);
	  
	  stageRunTime.setRunTime3(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime3(), snValue, 0.1);
	  
	  stageRunTime.setRunTime4(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime4(), snValue, 0.1);
	  
	  stageRunTime.setRunTime5(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime5(), snValue, 0.1);
	  
	  stageRunTime.setRunTime6(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime6(), snValue, 0.1);
	  
	  stageRunTime.setRunTime7(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime7(), snValue, 0.1);
	  
	  stageRunTime.setRunTime8(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime8(), snValue, 0.1);
	  
	  stageRunTime.setRunTime9(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime9(), snValue, 0.1);
	  
	  stageRunTime.setRunTime10(snValue);
	  Assert.assertEquals(stageRunTime.getRunTime10(), snValue, 0.1);
	  
	  stageDriverBlock.setLlRuntimeStrategyParams(stageRunTime);
	  Assert.assertNotNull(stageDriverBlock.getLlRuntimeStrategyParams());
  }
  
  
  @Test(dataProvider="provideSampleValues")
  public void testSettingValueInStageDriverBlockStageStatusData(double snValue) {
	  BStageStaus ss = stageDriverBlock.getStageStatusParams();
			  
	  ss.setStageStatus1(true);
	  Assert.assertEquals(ss.getStageStatus1(),true);
	  
	  ss.setStageStatus1(false);
	  Assert.assertEquals(ss.getStageStatus1(),false);	  
	  
	  ss.setStageStatus2(true);
	  Assert.assertEquals(ss.getStageStatus2(),true);
	  
	  ss.setStageStatus2(false);
	  Assert.assertEquals(ss.getStageStatus2(),false);
	  
	  ss.setStageStatus3(true);
	  Assert.assertEquals(ss.getStageStatus3(),true);
	  
	  ss.setStageStatus3(false);
	  Assert.assertEquals(ss.getStageStatus3(),false);	  
	  
	  ss.setStageStatus4(true);
	  Assert.assertEquals(ss.getStageStatus4(),true);
	  
	  ss.setStageStatus4(false);
	  Assert.assertEquals(ss.getStageStatus4(),false);
	  
	  ss.setStageStatus5(true);
	  Assert.assertEquals(ss.getStageStatus5(),true);
	  
	  ss.setStageStatus5(false);
	  Assert.assertEquals(ss.getStageStatus5(),false);
	  
	  ss.setStageStatus6(true);
	  Assert.assertEquals(ss.getStageStatus6(),true);
	  
	  ss.setStageStatus6(false);
	  Assert.assertEquals(ss.getStageStatus6(),false);
	  
	  ss.setStageStatus7(true);
	  Assert.assertEquals(ss.getStageStatus7(),true);
	  
	  ss.setStageStatus7(false);
	  Assert.assertEquals(ss.getStageStatus7(),false);
	  
	  ss.setStageStatus8(true);
	  Assert.assertEquals(ss.getStageStatus8(),true);
	  
	  ss.setStageStatus8(false);
	  Assert.assertEquals(ss.getStageStatus8(),false);
	  
	  ss.setStageStatus9(true);
	  Assert.assertEquals(ss.getStageStatus9(),true);
	  
	  ss.setStageStatus9(false);
	  Assert.assertEquals(ss.getStageStatus9(),false);
	  
	  ss.setStageStatus10(true);
	  Assert.assertEquals(ss.getStageStatus10(),true);
	  
	  ss.setStageStatus10(false);
	  Assert.assertEquals(ss.getStageStatus10(),false);
	  
	  ss.setStageStatus11(true);
	  Assert.assertEquals(ss.getStageStatus11(),true);
	  
	  ss.setStageStatus11(false);
	  Assert.assertEquals(ss.getStageStatus11(),false);
	  
	  ss.setStageStatus12(true);
	  Assert.assertEquals(ss.getStageStatus12(),true);
	  
	  ss.setStageStatus12(false);
	  Assert.assertEquals(ss.getStageStatus12(),false);
	  
	  ss.setStageStatus13(true);
	  Assert.assertEquals(ss.getStageStatus13(),true);
	  
	  ss.setStageStatus13(false);
	  Assert.assertEquals(ss.getStageStatus13(),false);
	  
	  ss.setStageStatus14(true);
	  Assert.assertEquals(ss.getStageStatus14(),true);
	  
	  ss.setStageStatus14(false);
	  Assert.assertEquals(ss.getStageStatus14(),false);
	  
	  ss.setStageStatus15(true);
	  Assert.assertEquals(ss.getStageStatus15(),true);
	  
	  ss.setStageStatus15(false);
	  Assert.assertEquals(ss.getStageStatus15(),false);
	  
	  ss.setStageStatus16(true);
	  Assert.assertEquals(ss.getStageStatus16(),true);
	  
	  ss.setStageStatus16(false);
	  Assert.assertEquals(ss.getStageStatus16(),false);
	  
	  ss.setStageStatus17(true);
	  Assert.assertEquals(ss.getStageStatus17(),true);
	  
	  ss.setStageStatus17(false);
	  Assert.assertEquals(ss.getStageStatus17(),false);
	  
	  ss.setStageStatus18(true);
	  Assert.assertEquals(ss.getStageStatus18(),true);
	  
	  ss.setStageStatus18(false);
	  Assert.assertEquals(ss.getStageStatus18(),false);
	  
	  ss.setStageStatus19(true);
	  Assert.assertEquals(ss.getStageStatus19(),true);
	  
	  ss.setStageStatus19(false);
	  Assert.assertEquals(ss.getStageStatus19(),false);
	  
	  ss.setStageStatus20(true);
	  Assert.assertEquals(ss.getStageStatus20(),true);
	  
	  ss.setStageStatus20(false);
	  Assert.assertEquals(ss.getStageStatus20(),false);  
	  
	  stageDriverBlock.setStageStatusParams(ss);
	  Assert.assertNotNull(stageDriverBlock.getStageStatusParams());
  }
  
  
  
  @Test
  public void testSettingValueOutputValues() {
	  stageDriverBlock.setSTAGE1(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE1().getValue(),true);
	  
	  stageDriverBlock.setSTAGE1(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE1().getValue(),false);	  
	  
	  stageDriverBlock.setSTAGE2(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE2().getValue(),true);
	  
	  stageDriverBlock.setSTAGE2(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE2().getValue(),false);
	  
	  stageDriverBlock.setSTAGE3(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE3().getValue(),true);
	  
	  stageDriverBlock.setSTAGE3(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE3().getValue(),false);
	  
	  stageDriverBlock.setSTAGE4(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE4().getValue(),true);
	  
	  stageDriverBlock.setSTAGE4(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE4().getValue(),false);
	  
	  stageDriverBlock.setSTAGE5(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE5().getValue(),true);
	  
	  stageDriverBlock.setSTAGE5(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE5().getValue(),false);
	  
	  stageDriverBlock.setSTAGE6(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE6().getValue(),true);
	  
	  stageDriverBlock.setSTAGE6(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE6().getValue(),false);
	  
	  stageDriverBlock.setSTAGE7(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE7().getValue(),true);
	  
	  stageDriverBlock.setSTAGE7(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE7().getValue(),false);
	  
	  stageDriverBlock.setSTAGE8(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE8().getValue(),true);
	  
	  stageDriverBlock.setSTAGE8(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE8().getValue(),false);
	  
	  stageDriverBlock.setSTAGE9(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE9().getValue(),true);
	  
	  stageDriverBlock.setSTAGE9(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE9().getValue(),false);
	  
	  stageDriverBlock.setSTAGE10(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE10().getValue(),true);
	  
	  stageDriverBlock.setSTAGE10(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE10().getValue(),false);	  
	  
	  stageDriverBlock.setSTAGE11(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE11().getValue(),true);
	  
	  stageDriverBlock.setSTAGE11(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE11().getValue(),false);
	  
	  stageDriverBlock.setSTAGE12(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE12().getValue(),true);
	  
	  stageDriverBlock.setSTAGE12(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE12().getValue(),false);
	  
	  stageDriverBlock.setSTAGE13(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE13().getValue(),true);
	  
	  stageDriverBlock.setSTAGE13(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE13().getValue(),false);
	  
	  stageDriverBlock.setSTAGE14(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE14().getValue(),true);
	  
	  stageDriverBlock.setSTAGE14(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE14().getValue(),false);	  
	  
	  stageDriverBlock.setSTAGE15(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE15().getValue(),true);
	  
	  stageDriverBlock.setSTAGE15(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE15().getValue(),false);	  
	  
	  stageDriverBlock.setSTAGE16(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE16().getValue(),true);
	  
	  stageDriverBlock.setSTAGE16(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE16().getValue(),false);
	  
	  stageDriverBlock.setSTAGE17(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE17().getValue(),true);
	  
	  stageDriverBlock.setSTAGE17(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE17().getValue(),false);
	  
	  stageDriverBlock.setSTAGE18(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE18().getValue(),true);
	  
	  stageDriverBlock.setSTAGE18(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE18().getValue(),false);
	  
	  stageDriverBlock.setSTAGE19(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE19().getValue(),true);
	  
	  stageDriverBlock.setSTAGE19(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE19().getValue(),false);
	  
	  stageDriverBlock.setSTAGE20(new BHonStatusBoolean(true, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE20().getValue(),true);
	  
	  stageDriverBlock.setSTAGE20(new BHonStatusBoolean(false, BStatus.ok));
	  Assert.assertEquals(stageDriverBlock.getSTAGE20().getValue(),false);	  
  }
  
  public void testSettingConfigValues() {
	  stageDriverBlock.setMaxStgs(1);
	  Assert.assertEquals(stageDriverBlock.getMaxStgs(),1);
	  
	  stageDriverBlock.setLeadLag(BLeadLagStrategyEnum.make("llFOLO"));
	  Assert.assertEquals(stageDriverBlock.getLeadLag().getOrdinal(),0);
	  
	  stageDriverBlock.setLeadLag(BLeadLagStrategyEnum.make("llFOFO"));
	  Assert.assertEquals(stageDriverBlock.getLeadLag().getOrdinal(),1);
	  
	  stageDriverBlock.setLeadLag(BLeadLagStrategyEnum.make("llRUNEQ"));
	  Assert.assertEquals(stageDriverBlock.getLeadLag().getOrdinal(),2);
  }


    
  @DataProvider(name="provideSequencedTestData")
  public Object[][] getSequencedTesData() {
	  return TestDataHelper.getSequencedTestDataInTestNGFormat(
			  "local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/control/test/StageDriver_SequencedTestData.csv");

  }
  
  
  @SuppressWarnings("squid:S2925")
  @Test(dataProvider="provideSequencedTestData")
  public void testStageDriverBlockWithSequenceOfTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
	  BStageDriver sdBlock = new BStageDriver();
	  
	  int seqNo=1;
	  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {		  
		  List<String> inputs = iterator.next();	  

		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  sdBlock.setMaxStgs(TestDataHelper.getInt(inputs.get(1), 1));
		  sdBlock.setLeadLag(BLeadLagStrategyEnum.make(inputs.get(2)));
		  setupNumericSlot(sdBlock, BStageDriver.nStagesActive.getName(), inputs.get(3));
		  setupNumericSlot(sdBlock, BStageDriver.runtimeReset.getName(), inputs.get(4));

		  if(seqNo==1)	sdBlock.initHoneywellComponent(null);
		  if(inputs.get(2).equalsIgnoreCase("llRUNEQ")) {
			  for(int i=0;i<120;i++) {//each step execute for 2 minutes
				  sdBlock.executeHoneywellComponent(executionParams);
			  }			  
		  }else
			  sdBlock.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(sdBlock.getSTAGE1().getValue(), TestDataHelper.getBoolean(inputs.get(5)),
				  "Failed for 1 at step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE2().getValue(), TestDataHelper.getBoolean(inputs.get(6)),
				  "Failed for 2 at step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE3().getValue(), TestDataHelper.getBoolean(inputs.get(7)),
				  "Failed for 3 at step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE4().getValue(), TestDataHelper.getBoolean(inputs.get(8)),
				  "Failed for 4 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE5().getValue(), TestDataHelper.getBoolean(inputs.get(9)),
				  "Failed for 5 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE6().getValue(), TestDataHelper.getBoolean(inputs.get(10)),
				  "Failed for 6 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE7().getValue(), TestDataHelper.getBoolean(inputs.get(11)),
				  "Failed for 7 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE8().getValue(), TestDataHelper.getBoolean(inputs.get(12)),
				  "Failed for 8 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE9().getValue(), TestDataHelper.getBoolean(inputs.get(13)),
				  "Failed for 9 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE10().getValue(), TestDataHelper.getBoolean(inputs.get(14)),
				  "Failed for 10 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE11().getValue(), TestDataHelper.getBoolean(inputs.get(15)),
				  "Failed for 11 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE12().getValue(), TestDataHelper.getBoolean(inputs.get(16)),
				  "Failed for 12 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE13().getValue(), TestDataHelper.getBoolean(inputs.get(17)),
				  "Failed for 13 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE14().getValue(), TestDataHelper.getBoolean(inputs.get(18)),
				  "Failed for 14 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE15().getValue(), TestDataHelper.getBoolean(inputs.get(19)),
				  "Failed for 15 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE16().getValue(), TestDataHelper.getBoolean(inputs.get(20)),
				  "Failed for 16 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE17().getValue(), TestDataHelper.getBoolean(inputs.get(21)),
				  "Failed for 17 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE18().getValue(), TestDataHelper.getBoolean(inputs.get(22)),
				  "Failed for 18 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE19().getValue(), TestDataHelper.getBoolean(inputs.get(23)),
				  "Failed for 19 step #"+seqNo+" for input data "+inputs+"; ");
		  Assert.assertEquals(sdBlock.getSTAGE20().getValue(), TestDataHelper.getBoolean(inputs.get(24)),
				  "Failed for 20 step #"+seqNo+" for input data "+inputs+"; ");
	  }
	  
	  sdBlock = null;
  }
  
  
	public void setupNumericSlot(BStageDriver sdBlock, final String slotName, final String inputValue) {
		if (TestDataHelper.isConnected(inputValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = sdBlock.getProperty(slotName).getType();		
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
				BConverter converter = null;
				if(sdBlock.get("Link"+slotName)!=null) {
					sdBlock.remove("Link"+slotName);
				}
				converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),sdBlock.getSlot(slotName),converter);
				conversionLink.setEnabled(true);
				sdBlock.add("Link"+slotName,conversionLink );				
				conversionLink.activate();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			}else{
				sdBlock.linkTo(nm1, nm1.getSlot("out"), sdBlock.getSlot(slotName));
			}			
			
			return;
		}

		switch (slotName) {
		case "nStagesActive":
			sdBlock.setNStagesActive(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;

		case "runtimeReset":
			sdBlock.setRuntimeReset(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
			break;
		}
	}
	
	
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
	  BStageDriver src = new BStageDriver();
	  BStageDriver target = new BStageDriver();
	  
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("nStagesActive"), target, target.getSlot("nStagesActive"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("runtimeReset"), target, target.getSlot("runtimeReset"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("leadLag"), target, target.getSlot("leadLag"));
	  LinkCheckUtil.checkInvalidLink(src, src.getSlot("maxStgs"), target, target.getSlot("maxStgs"));
  }
	
	@Test
	public void testSlotsFlagStatus() {
		BStageDriver stageDriver = new BStageDriver();
		stageDriver.setMaxStgs(6);
		checkSlotFlagsStatus(stageDriver,6);
		
		stageDriver.setMaxStgs(15);
		checkSlotFlagsStatus(stageDriver,15);
		
		stageDriver.setMaxStgs(3);
		checkSlotFlagsStatus(stageDriver,3);

	}
	
	private void checkSlotFlagsStatus(BStageDriver stageDriver,int maxStages) {
		for(int i=0;i<maxStages;i++) {			
			Slot slot = stageDriver.getSlot("STAGE"+(i+1));
			Assert.assertTrue(!Flags.isHidden(stageDriver, slot));
		}
		for(int i=maxStages;i<20;i++) {
			Slot slot = stageDriver.getSlot("STAGE"+(i+1));
			Assert.assertTrue(Flags.isHidden(stageDriver, slot));
		}		
	}
	
	
	@Test
	public void testFacets() {
		stageDriverBlock.setLeadLag(BLeadLagStrategyEnum.llFOFO);
		 BFacets facets = stageDriverBlock.getSlotFacets(stageDriverBlock.getSlot("maxStgs"));
		 double d = facets.getd(BFacets.MAX, 0.0);
		 Assert.assertEquals(d,20.0);
		 
		stageDriverBlock.setLeadLag(BLeadLagStrategyEnum.llRUNEQ);
		 facets = stageDriverBlock.getSlotFacets(stageDriverBlock.getSlot("maxStgs"));
		 d = facets.getd(BFacets.MAX, 0.0);
		 Assert.assertEquals(d,10.0);
	}
	
	 @Test
	 public void testLeadLagStrategyChanges() {
		 BStageDriver sdBlock = new BStageDriver();
		 sdBlock.setMaxStgs(16);		 
		 sdBlock.setLeadLag(BLeadLagStrategyEnum.llRUNEQ);
		 Assert.assertEquals(sdBlock.getMaxStgs(), MAX_STAGES_LLRUNTIME);
		 Assert.assertTrue(checkIfAllStageStatusReset(sdBlock));
		 Assert.assertTrue(checkIfAllStatusRunTimeReset(sdBlock));
		 Assert.assertTrue(checkIfFOFOStrategyParamsReset(sdBlock));
	 }
	 
	@Test
	public void testConfigProperties() {
		List<Property> configList = stageDriverBlock.getConfigPropertiesList();
		List<String> configParamNames = configList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualConfigParamnames = configParamNames.toArray(new String[] {});
		Arrays.sort(actualConfigParamnames);
		String[] expectedConfigParams = { BStageDriver.leadLag.getName(), BStageDriver.maxStgs.getName() };
		Arrays.sort(expectedConfigParams);

		Assert.assertEquals(actualConfigParamnames, expectedConfigParams);
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = stageDriverBlock.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BStageDriver.nStagesActive.getName(), BStageDriver.runtimeReset.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = stageDriverBlock.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BStageDriver.STAGE1.getName(), BStageDriver.STAGE2.getName(),BStageDriver.STAGE3.getName(), BStageDriver.STAGE4.getName(),BStageDriver.STAGE5.getName(), 
				 BStageDriver.STAGE6.getName(),BStageDriver.STAGE7.getName(), BStageDriver.STAGE8.getName(),BStageDriver.STAGE9.getName(), BStageDriver.STAGE10.getName(),BStageDriver.STAGE11.getName(), 
				 BStageDriver.STAGE12.getName(),BStageDriver.STAGE13.getName(), BStageDriver.STAGE14.getName(),BStageDriver.STAGE15.getName(), BStageDriver.STAGE16.getName(),
				 BStageDriver.STAGE17.getName(), BStageDriver.STAGE18.getName(),BStageDriver.STAGE19.getName(), BStageDriver.STAGE20.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testOverrideAutoAction() {
		if (null == station.get("FB")) {
			fbContainer = new BFolder();
			station.add("FB", fbContainer);
			stageDriverBlockOverride = new BStageDriver();
			fbContainer.add("stage", stageDriverBlockOverride);
		}
		BFBOverrideProperties fbOverrideProperty = new BFBOverrideProperties();
		fbOverrideProperty.setOverrideDuration(BRelTime.makeMinutes(1));
		List<Property> properties = ((BFunctionBlock) stageDriverBlockOverride).getOutputPropertiesList();

		for (int i = 0; i < properties.size(); i++) {

			BValue outPutSlotValue = stageDriverBlockOverride.get(properties.get(i).getName());
			if (outPutSlotValue instanceof BStatusEnum) {
				BEnum f1SlotEnum = ((BStatusEnum) outPutSlotValue).getEnum();
				fbOverrideProperty.add(properties.get(i).getName(), f1SlotEnum.newCopy());

			} else if (outPutSlotValue instanceof BStatusBoolean) {
				boolean isTrueValueSet = ((BStatusBoolean) outPutSlotValue).getValue();
				fbOverrideProperty.add(properties.get(i).getName(), BBoolean.make(isTrueValueSet));

			} else if (outPutSlotValue instanceof BStatusNumeric) {
				fbOverrideProperty.add(properties.get(i).getName(),
						BDouble.make(((BStatusNumeric) outPutSlotValue).getValue()));

			}
		}

		stageDriverBlockOverride.doOverride(fbOverrideProperty);
		Assert.assertNotEquals(stageDriverBlockOverride.getOverrideExpiration(), BAbsTime.NULL);

		stageDriverBlockOverride.doAuto();
		Assert.assertEquals(stageDriverBlockOverride.getOverrideExpiration(), BAbsTime.NULL);

		if (null != station.get("FB"))
			station.remove((BFolder) station.get("FB"));

	}
	
	 
	 private boolean checkIfAllStageStatusReset(BStageDriver sdBlock) {
		 for(int i=0;i<MAX_STAGES_TOTAL;i++) {
			BStageStaus stageStatusStruct = sdBlock.getStageStatusParams();	
			if(stageStatusStruct.getBoolean(stageStatusStruct.getProperty("stageStatus"+(i+1))))			
				return false;
		 }
		 return true;
	 }
	 
	 private boolean checkIfAllStatusRunTimeReset(BStageDriver sdBlock) {
		 for(int i=0;i<MAX_STAGES_LLRUNTIME;i++) {
			 BRuntimeStrategy stageRunTime = sdBlock.getLlRuntimeStrategyParams();
			 if(Double.compare(stageRunTime.getDouble(stageRunTime.getProperty("RunTime"+(i+1))),0.0)!=0)
				 return false;
		 }
		 return true;
	 }
	 
	 private boolean checkIfFOFOStrategyParamsReset(BStageDriver sdBlock) {
		BFOFOStrategy fs = sdBlock.getLlFOFOStrategyParams();
		if(fs.getOldNumStages()!=0 || fs.getSeqStartPtr()!=0)
			return false;
		
		return true;
	 }
	
  
	private BStageDriver stageDriverBlock;
	private BExecutionParams executionParams;
	private static final int MAX_STAGES_TOTAL = 20;
	private static final int MAX_STAGES_LLRUNTIME = 10;

	private BStation station = null;
	private TestStationHandler stationHandler = null;
	private BFolder fbContainer = null;
	private BStageDriver stageDriverBlockOverride = null;

}
