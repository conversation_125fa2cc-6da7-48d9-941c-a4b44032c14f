/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * To test the method in the BFunctionBlock class
 * <AUTHOR> - <PERSON> .K
 * @since Sep 29, 2018
 */

@NiagaraType
public class BFunctionalBlockTest extends BTestNg {
/*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.BFunctionalBlockTest(2979906276)1.0$ @*/
/* Generated Sat Sep 29 15:25:01 IST 2018 by <PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BFunctionalBlockTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Test
	public void testPropertyConfiguredStatus() {
		BTestFb fb = new BTestFb();
		
		Assert.assertFalse(fb.isConfigured(BTestFb.x1));
		Assert.assertFalse(fb.isConfigured(BTestFb.x2));
		Assert.assertFalse(fb.isConfigured(BTestFb.x3));
		Assert.assertFalse(fb.isConfigured(BTestFb.x4));
		Assert.assertFalse(fb.isConfigured(BTestFb.x5));
		Assert.assertTrue(fb.isConfigured(BTestFb.x6));
		Assert.assertTrue(fb.isConfigured(BTestFb.x7));
		Assert.assertTrue(fb.isConfigured(BTestFb.x8));
	}
}
