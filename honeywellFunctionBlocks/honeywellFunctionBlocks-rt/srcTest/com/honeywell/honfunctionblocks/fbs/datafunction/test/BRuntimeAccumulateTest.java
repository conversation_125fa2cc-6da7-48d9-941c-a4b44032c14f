/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.datafunction.test;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BLink;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.INegatableStatusValue;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.datafunction.BRuntimeAccumulate;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of Runtime Accumulate block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TBD
 * <AUTHOR> - Lavanya B.
 * @since Feb 2, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BRuntimeAccumulateTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.fbs.datafunction.test.BRuntimeAccumulateTest(2979906276)1.0$ @*/
/* Generated Mon Aug 25 20:14:42 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRuntimeAccumulateTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
  @BeforeClass
	public void setUp() {
		runtimeAcc = new BRuntimeAccumulate();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass
	public void tearDown() {
		runtimeAcc = null;
		executionParams = null;
	}
	
	private Object[][] getInputSlotNames() {
		return new Object[][]{{"Input"}, {"Enable"}, {"Preset"}, {"PresetValue"}};
	}
	
	private Object[][] getOutputSlotNames() {
		return new Object[][]{{"RUNTIME_MIN"},{"RUNTIME_SEC"},{"RUNTIME_HRS"},{"RUNTIME_DAYS"}};
	}
	
	private Object[][] getExecOrderSlotNames() {
		return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	}
	
	@DataProvider(name="allSlotNames")
	public Object[][] getAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);	  
	}
	
	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(runtimeAcc.getSlot(slotName));
	}
	
	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"input"}, {"enable"}};
	}
	
	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(runtimeAcc.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "run_time_accumulate.png");
		BIcon actualFbIcon = runtimeAcc.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		runtimeAcc.setIcon(expectedFbIcon);
		actualFbIcon = runtimeAcc.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	}
	
	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		runtimeAcc.setInput(new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getInput().getValue(), false);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).getNegate(),false);
		
		runtimeAcc.setInput(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getInput().getValue(), false);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).getNegate(),true);
		
		runtimeAcc.setInput(new BNegatableFiniteStatusBoolean(true, BStatus.ok, false));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getInput().getValue(), true);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).getNegate(),false);
		
		runtimeAcc.setInput(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getInput().getValue(), true);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getInput()).getNegate(),true); 
		
		runtimeAcc.setEnable(new BNegatableStatusBoolean(false, BStatus.ok, false));
		((BNegatableStatusBoolean) runtimeAcc.getEnable()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getEnable().getValue(), false);
		Assert.assertEquals(((BNegatableStatusBoolean) runtimeAcc.getEnable()).getNegate(), false);
		
		runtimeAcc.setEnable(new BNegatableStatusBoolean(false, BStatus.ok, true));
		((BNegatableStatusBoolean) runtimeAcc.getEnable()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getEnable().getValue(), false);
		Assert.assertEquals(((BNegatableStatusBoolean) runtimeAcc.getEnable()).getNegate(), true);
		
		runtimeAcc.setEnable(new BNegatableStatusBoolean(true, BStatus.ok, false));
		((BNegatableStatusBoolean) runtimeAcc.getEnable()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getEnable().getValue(), true);
		Assert.assertEquals(((BNegatableStatusBoolean) runtimeAcc.getEnable()).getNegate(), false);
		
		runtimeAcc.setEnable(new BNegatableStatusBoolean(true, BStatus.ok, true));
		((BNegatableStatusBoolean) runtimeAcc.getEnable()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getEnable().getValue(), true);
		Assert.assertEquals(((BNegatableStatusBoolean) runtimeAcc.getEnable()).getNegate(), true); 
		
		runtimeAcc.setPreset(new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getPreset().getValue(), false);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).getNegate(),false);
		
		runtimeAcc.setPreset(new BNegatableFiniteStatusBoolean(false, BStatus.ok, true));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getPreset().getValue(), false);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).getNegate(),true);
		
		runtimeAcc.setPreset(new BNegatableFiniteStatusBoolean(true, BStatus.ok, false));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).setNegate(false);
		Assert.assertEquals(runtimeAcc.getPreset().getValue(), true);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).getNegate(),false);
		
		runtimeAcc.setPreset(new BNegatableFiniteStatusBoolean(true, BStatus.ok, true));
		((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).setNegate(true);
		Assert.assertEquals(runtimeAcc.getPreset().getValue(), true);
		Assert.assertEquals(((BNegatableFiniteStatusBoolean) runtimeAcc.getPreset()).getNegate(),true);
	
		runtimeAcc.setPresetValue(new BHonStatusNumeric(value));
		Assert.assertEquals(runtimeAcc.getPresetValue().getValue(), value, 0.1);

		runtimeAcc.setRUNTIME_MIN(new BHonStatusNumeric(value));
		Assert.assertEquals(runtimeAcc.getRUNTIME_MIN().getValue(), value, 0.1);		

		runtimeAcc.setRUNTIME_SEC(new BHonStatusNumeric(value));
		Assert.assertEquals(runtimeAcc.getRUNTIME_SEC().getValue(), value, 0.1);
		
		runtimeAcc.setRUNTIME_HRS(new BHonStatusNumeric(value));
		Assert.assertEquals(runtimeAcc.getRUNTIME_HRS().getValue(), value, 0.1);
		
		runtimeAcc.setRUNTIME_DAYS(new BHonStatusNumeric(value));
		Assert.assertEquals(runtimeAcc.getRUNTIME_DAYS().getValue(), value, 0.1);
	}
	
	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BRuntimeAccumulate runtimeAccB = new BRuntimeAccumulate();
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.Input, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.Enable, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.Preset, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.PresetValue, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.RUNTIME_DAYS, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.RUNTIME_HRS, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.RUNTIME_MIN, false);
		checkOutgoingLink(runtimeAccB, BRuntimeAccumulate.RUNTIME_SEC, false);
		checkOutgoingLinkForOutput(runtimeAccB, BRuntimeAccumulate.RUNTIME_DAYS, true);
		checkOutgoingLinkForOutput(runtimeAccB, BRuntimeAccumulate.RUNTIME_HRS, true);
		checkOutgoingLinkForOutput(runtimeAccB, BRuntimeAccumulate.RUNTIME_MIN, true);
		checkOutgoingLinkForOutput(runtimeAccB, BRuntimeAccumulate.RUNTIME_SEC, true);
	}

	private void checkOutgoingLinkForOutput(BRuntimeAccumulate block, Property prop, boolean isLinkValid) {
		BComponent tempComp = new BComponent();
		tempComp.add("in", new BHonStatusNumeric());
		LinkCheck checkLink = tempComp.checkLink(runtimeAcc, runtimeAcc.getSlot(prop.getName()), tempComp.getSlot("in"), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	private void checkOutgoingLink(BRuntimeAccumulate block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = runtimeAcc.checkLink(runtimeAcc, runtimeAcc.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}

	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BRuntimeAccumulate runtimeAccTemp = new BRuntimeAccumulate();
		executionParams.setIterationInterval(1000);
		runtimeAccTemp.setInput(new BNegatableFiniteStatusBoolean(true, BStatus.ok, false));
		runtimeAccTemp.setEnable(new BNegatableStatusBoolean(true, BStatus.ok, false));
		runtimeAccTemp.setPreset(new BNegatableFiniteStatusBoolean(false, BStatus.ok, false));
		runtimeAccTemp.setPresetValue(new BHonStatusNumeric(0.0));
		for (int j = 0; j < 5000; j++) {
			runtimeAccTemp.executeHoneywellComponent(executionParams);
		}
		
		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("RunTimeAccumulate", runtimeAccTemp);
			BRuntimeAccumulate runtimeAccSaved = (BRuntimeAccumulate) bogUtil.getComponentFromBogFile(bogFile);
			
			double runtimeMin = Math.floor(5000/60.0);
			
			Assert.assertEquals(runtimeAccSaved.getRUNTIME_MIN().getValue(), runtimeMin, 0.1, "Failed to verify Minute");
			Assert.assertEquals(runtimeAccSaved.getRUNTIME_SEC().getValue(), 0.0, "Failed to verify Second");
			Assert.assertEquals(runtimeAccSaved.getRUNTIME_HRS().getValue(), 0.0, "Failed to verify Hour");
			Assert.assertEquals(runtimeAccSaved.getRUNTIME_DAYS().getValue(),0.0, "Failed to verify Day");
			
			runtimeAccSaved.initHoneywellComponent(null);
			
			Assert.assertEquals(runtimeAccTemp.getRUNTIME_MIN().getValue(), runtimeMin, 0.1, "After init, failed to verify Minute");
			Assert.assertEquals(runtimeAccTemp.getRUNTIME_SEC().getValue(), 5000, 0.1, "After init, failed to verify Second");
			Assert.assertEquals(runtimeAccTemp.getRUNTIME_HRS().getValue(),
					BigDecimal.valueOf(runtimeMin / 60.0).setScale(6, RoundingMode.HALF_UP).doubleValue(), 0.1,
					"After init, failed to verify Hour");
			Assert.assertEquals(runtimeAccTemp.getRUNTIME_DAYS().getValue(),
					BigDecimal.valueOf(runtimeMin / 1440.0).setScale(6, RoundingMode.HALF_UP).doubleValue(), 0.1,
					"After init, failed to verify Day");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	@DataProvider(name = "runtimeAccTestData")
	public Object[][] getRuntimeAccTestData() throws FileNotFoundException{
	    	return TestDataHelper.getSequencedTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/datafunction/test/Neg_RuntimeAcc_TestData.csv");
	}

	@Test(dataProvider = "runtimeAccTestData")
	 public void testRuntimeAccumulateBlockWithTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
		  BRuntimeAccumulate rAccBlock = new BRuntimeAccumulate();
		  rAccBlock.initHoneywellComponent(null);
		  int seqNo=1;
		  for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
			  List<String> inputs = iterator.next();
			  //clear all the links added by the previous test run
			  BLink [] links = rAccBlock.getLinks();
			  for (int j = 0; j < links.length; j++) {
				  rAccBlock.remove(links[j]);
			  }
			 
			  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
			  setupNumericSlot(rAccBlock, BRuntimeAccumulate.Input.getName(), inputs.get(1));
			  setupNegateSlot(rAccBlock, BRuntimeAccumulate.Input.getName(), inputs.get(10));
			  setupNumericSlot(rAccBlock, BRuntimeAccumulate.Enable.getName(), inputs.get(2));
			  setupNegateSlot(rAccBlock, BRuntimeAccumulate.Enable.getName(), inputs.get(11));
			  setupNumericSlot(rAccBlock, BRuntimeAccumulate.Preset.getName(), inputs.get(3));
			  setupNegateSlot(rAccBlock, BRuntimeAccumulate.Preset.getName(), inputs.get(12));
			  setupNumericSlot(rAccBlock, BRuntimeAccumulate.PresetValue.getName(), inputs.get(4));

			  int runtimeLoops = TestDataHelper.getInt(inputs.get(5), 0);

			  for(int i=0;i<runtimeLoops;i++)
				  rAccBlock.executeHoneywellComponent(executionParams);  		  

			  Assert.assertEquals(rAccBlock.getRUNTIME_MIN().getValue(), TestDataHelper.getDouble(inputs.get(6), 0.0d), 0.1, "Failed in step#"+seqNo+" for Minute");
			  Assert.assertEquals(rAccBlock.getRUNTIME_SEC().getValue(), TestDataHelper.getDouble(inputs.get(7), 0.0d), 0.1, "Failed in step#"+seqNo+" for Second");
			  Assert.assertEquals(rAccBlock.getRUNTIME_HRS().getValue(), TestDataHelper.getDouble(inputs.get(8), 0.0d), 0.1, "Failed in step#"+seqNo+" for Hour");
			  Assert.assertEquals(rAccBlock.getRUNTIME_DAYS().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), 0.1, "Failed in step#"+seqNo+" for Day");
		  }
		  rAccBlock = null;
   }
	
	public void setupNegateSlot(BRuntimeAccumulate rAccBlock, final String slotName, final String negateString) {
		boolean negate = false;
		if(negateString != null) {
			negate =  TestDataHelper.getBoolean(negateString);
			
			//Set Negate Value
			INegatableStatusValue nsb = (INegatableStatusValue) rAccBlock.get(slotName);
			nsb.setNegate(negate);
		}
	}
	
	@Test
	public void testupgradesenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/datafunction/test/Station_bog/config/file.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BRuntimeAccumulate rAccBlock  = (BRuntimeAccumulate)folder.get("RuntimeAccumulate");
			Assert.assertEquals(rAccBlock.getInput().getType(), BNegatableFiniteStatusBoolean.TYPE);
			Assert.assertEquals(rAccBlock.getEnable().getType(), BNegatableStatusBoolean.TYPE);
			Assert.assertEquals(rAccBlock.getPreset().getType(), BNegatableFiniteStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = runtimeAcc.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BRuntimeAccumulate.Input.getName(), BRuntimeAccumulate.Enable.getName(),BRuntimeAccumulate.Preset.getName(),BRuntimeAccumulate.PresetValue.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = runtimeAcc.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BRuntimeAccumulate.RUNTIME_MIN.getName(),BRuntimeAccumulate.RUNTIME_SEC.getName(),BRuntimeAccumulate.RUNTIME_HRS.getName(),BRuntimeAccumulate.RUNTIME_DAYS.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	/**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param subtractBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BRuntimeAccumulate rtAccBlock, final String slotName, final String inputValue){
		  if(TestDataHelper.isConnected(inputValue)){
			  BNumericConst nm1 = new BNumericConst();
			  nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			  Type srcType = nm1.getOut().getType();
			  Type targetType = rtAccBlock.getProperty(slotName).getType();		
			  if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				  BConverter converter = new BStatusNumericToFiniteStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),rtAccBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  rtAccBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  }else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusBoolean.TYPE)) {
				  BConverter converter = new BStatusNumericToStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),rtAccBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  rtAccBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  }else{
				  rtAccBlock.linkTo(nm1, nm1.getSlot("out"), rtAccBlock.getSlot(slotName));
			  }
			  return;
		  }

		  switch (slotName) {
		  case "Input":
			  rtAccBlock.setInput(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			  break;

		  case "Enable":
			  rtAccBlock.setEnable(TestDataHelper.getNegatableStatusBoolean(inputValue, false));
			  break;	
			  
		  case "Preset":
			  rtAccBlock.setPreset(TestDataHelper.getNegatableFiniteStatusBoolean(inputValue, false));
			  break;	
			  
		  case "PresetValue":
			  rtAccBlock.setPresetValue(TestDataHelper.getHonStatusNumeric(inputValue, 0.0d));
			  break;
		  }
	  }
	
	private BRuntimeAccumulate runtimeAcc;
	private BExecutionParams executionParams;	
}
