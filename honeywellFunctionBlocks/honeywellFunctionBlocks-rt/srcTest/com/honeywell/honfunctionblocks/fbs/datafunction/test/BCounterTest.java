/*
 * Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 * This file contains trade secrets of Honeywell International, Inc. No part may be reproduced or
 * transmitted in any form by any means or for any purpose without the express written permission of
 * Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.datafunction.test;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.converters.BStatusNumericToStatusBoolean;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusBoolean;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.BLink;
import javax.baja.sys.BStation;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTest;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.datafunction.BCounter;
import com.honeywell.honfunctionblocks.utils.test.BogFileUtil;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Implementation of Counter block TestCase as per FB SDD rev26 Requirement ID: F1PLT-ADR-405
 * 
 * <AUTHOR> - Suresh Khatri
 * @since Feb 2, 2018
 */

@NiagaraType
public class BCounterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.datafunction.test.BCounterTest(2979906276)1.0$ @*/
/* Generated Fri Feb 02 18:41:40 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCounterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	@BeforeClass
	public void setUp() {
		counter = new BCounter();
		counter1 = new BCounter();
		try {
			counter.initHoneywellComponent(null);
			counter1.initHoneywellComponent(null);
		} catch (BlockInitializationException e) {
			e.printStackTrace();
		}
		executionParams = new BExecutionParams();
	}

	@AfterClass
	public void tearDown() {
		counter = null;
		counter1 = null;
		executionParams = null;
	}

	private Object[][] getInputSlotNames() {
		return new Object[][] { { "Input" }, { "Enable" }, { "Preset" }, { "PresetValue" }, { "CountValue" }, { "StopAtZero" } };
	}

	private Object[][] getOutputSlotNames() {
		return new Object[][] { { "COUNT" } };
	}

	private Object[][] getExecOrderSlotNames() {
		return new Object[][] { { "ExecutionOrder" }, { "toolVersion" } };
	}

	@DataProvider(name = "allSlotNames")
	public Object[][] getAllSlotNames() {
		List<Object[]> slotArrayList = Lists.newArrayList();
		slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		return slotArrayList.toArray(new Object[slotArrayList.size()][]);

	}

	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(counter.getSlot(slotName));
	}

	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][] { { "Invalid1" }, { "Invalid2" } };
	}

	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(counter.getSlot(slotName));
	}

	@Test(groups = { "testIconSlot" })
	public void testIconSlot() {
		// check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "counter.png");
		BIcon actualFbIcon = counter.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		// check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		counter.setIcon(expectedFbIcon);
		actualFbIcon = counter.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}

	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO() {
		return new Object[][] { { -23 }, { 0 }, { 60 }, { 32767 }, { 32768 }, { 40000 }, { 2.2250738585072014E-308 }, { -1.7976931348623157e+308 }, { Double.NEGATIVE_INFINITY }, { Double.POSITIVE_INFINITY }, { Double.NaN } };
	}

	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		counter.setInput(new BFiniteStatusBoolean(true));
		Assert.assertEquals(counter.getInput().getValue(), true);

        counter.setEnable(new BNegatableStatusBoolean(true, BStatus.ok, true));
        ((BNegatableStatusBoolean)counter.getEnable()).setNegate(true);
		Assert.assertEquals(counter.getEnable().getValue(), true);
		Assert.assertEquals(((BNegatableStatusBoolean)counter.getEnable()).getNegate(), true);

		counter.setPreset(new BFiniteStatusBoolean(true));
		Assert.assertEquals(counter.getPreset().getValue(), true);

		counter.setPresetValue(new BHonStatusNumeric(value));
		Assert.assertEquals(counter.getPresetValue().getValue(), value, 0.1);

		counter.setCountValue(new BHonStatusNumeric(value));
		Assert.assertEquals(counter.getCountValue().getValue(), value, 0.1);

		counter.setStopAtZero(new BFiniteStatusBoolean(true));
		Assert.assertEquals(counter.getStopAtZero().getValue(), true);

		counter.setCOUNT(new BHonStatusNumeric(value));
		Assert.assertEquals(counter.getCOUNT().getValue(), value, 0.1);
	}

	//@Test(groups={"testLinkRules"})
	public void testLinkRules() {
		BCounter counterB = new BCounter();
		checkOutgoingLink(counterB, BCounter.Input, false);
		checkOutgoingLink(counterB, BCounter.Enable, false);
		checkOutgoingLink(counterB, BCounter.Preset, false);
		checkOutgoingLink(counterB, BCounter.PresetValue, false);
		checkOutgoingLink(counterB, BCounter.CountValue, false);
		checkOutgoingLink(counterB, BCounter.StopAtZero, false);
		checkOutgoingLink(counterB, BCounter.COUNT, false);
	}

	private void checkOutgoingLink(BCounter block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = counter.checkLink(counter, counter.getSlot(prop.getName()), block.getSlot(prop.getName()), null);
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	@Test
	public void testDeviceRestartScenario() throws BlockExecutionException, BlockInitializationException {
		BCounter counterTemp = new BCounter();
		executionParams.setIterationInterval(1000);
		counterTemp.setInput(new BFiniteStatusBoolean(false));
		counterTemp.setEnable(new BNegatableStatusBoolean(true, BStatus.ok,false));
		counterTemp.setPreset(new BFiniteStatusBoolean(false));
		counterTemp.setPresetValue(new BHonStatusNumeric(50));
		counterTemp.setCountValue(new BHonStatusNumeric(5));
		counterTemp.setStopAtZero(new BFiniteStatusBoolean(false));
		for (int j = 0; j < 2500; j++) {
			counterTemp.setInput(new BFiniteStatusBoolean(true));
			counterTemp.executeHoneywellComponent(executionParams);
			counterTemp.setInput(new BFiniteStatusBoolean(false));
			counterTemp.executeHoneywellComponent(executionParams);
		}
		Assert.assertEquals(counterTemp.getCOUNT().getValue(), 2500*5, 0.1);
		
		BogFileUtil bogUtil = new BogFileUtil();
		try {
			File bogFile = bogUtil.saveComponentToBogFile("Counter", counterTemp);
			BCounter counterSaved = (BCounter) bogUtil.getComponentFromBogFile(bogFile);
			
			Assert.assertEquals(counterSaved.getCOUNT().getValue(), 2500*5, 0.1, "Failed to verify Count");
			counterSaved.initHoneywellComponent(null);
			Assert.assertEquals(counterTemp.getCOUNT().getValue(), 2500*5, 0.1, "After init, failed to verify Count");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@DataProvider(name = "counterTestData")
	public Object[][] getCounterTestData() throws FileNotFoundException {
		return TestDataHelper.getSequencedTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/datafunction/test/Neg_enable_Counter_TestData.csv");
	}

	@Test(dataProvider = "counterTestData")
	public void testCounterBlockWithTestData(List<List<String>> inputSequence) throws BlockExecutionException, BlockInitializationException {
		counter1.initHoneywellComponent(null);
		int seqNo=1;
		for (Iterator<List<String>> iterator = inputSequence.iterator(); iterator.hasNext();seqNo++) {
			List<String> inputs = iterator.next();
			//clear all the links added by the previous test run
			BLink[] links = counter1.getLinks();
			for (int j = 0; j < links.length; j++) {
				counter1.remove(links[j]);
			}
			
			executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
			setupNumericSlot(counter1, BCounter.Input.getName(), inputs.get(1));
			setupNumericSlot(counter1, BCounter.Enable.getName(), inputs.get(2));
			setupNumericSlot(counter1, BCounter.Preset.getName(), inputs.get(3));
			setupNumericSlot(counter1, BCounter.StopAtZero.getName(), inputs.get(4));
			setupNumericSlot(counter1, BCounter.PresetValue.getName(), inputs.get(5));
			setupNumericSlot(counter1, BCounter.CountValue.getName(), inputs.get(6));
			
            boolean negateEnable = (inputs.get(8).trim()).equals("0") ? false : true;
            ((BNegatableStatusBoolean)counter1.getEnable()).setNegate(negateEnable);

			counter1.executeHoneywellComponent(executionParams);
			Assert.assertEquals(counter1.getCOUNT().getValue(), TestDataHelper.getDouble(inputs.get(7), 0.0d), 0.1, "Failed in step#"+seqNo+" for input data: "+inputs);
		}
	}
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = counter.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BCounter.Input.getName(), BCounter.Enable.getName(), BCounter.Preset.getName(), BCounter.PresetValue.getName(), BCounter.CountValue.getName(), BCounter.StopAtZero.getName() };
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = counter.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BCounter.COUNT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	@Test
	public void testUpgradeScenario() throws Exception {
		BOrd fileOrd = BOrd.make("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/datafunction/test/CounterFile.bog");
		TestStationHandler stationHandler = BTest.createTestStation(fileOrd);
		stationHandler.startStation();
		BStation station = stationHandler.getStation();
		stationHandler.startStation();
		try {
			BComponent folder = (BComponent)((BComponent)station.get("Apps")).get("Folder");
			BCounter counter = (BCounter)folder.get("Counter");
			Assert.assertEquals(counter.getEnable().getType(), BNegatableStatusBoolean.TYPE);
		}catch(Exception e) {
			stationHandler.stopStation();
			stationHandler.releaseStation();
		}
		stationHandler.stopStation();
		stationHandler.releaseStation();
	}

	/**
	 * Configure numeric value to the given slot as per test data (either set constant value or by link
	 * propagation)
	 * 
	 * @param subtractBlock
	 * @param slotName
	 * @param inputValue
	 */
	public void setupNumericSlot(BCounter counterBlock, final String slotName, final String inputValue) {
		if (TestDataHelper.isConnected(inputValue)) {
			BNumericConst nm1 = new BNumericConst();
			nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
			Type srcType = nm1.getOut().getType();
			Type targetType = counterBlock.getProperty(slotName).getType();
			if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BFiniteStatusBoolean.TYPE)) {				
				  BConverter converter = new BStatusNumericToFiniteStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),counterBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  counterBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  }else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BStatusBoolean.TYPE)) {
				  BConverter converter = new BStatusNumericToStatusBoolean();
				  BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),counterBlock.getSlot(slotName),converter);
				  conversionLink.setEnabled(true);
				  counterBlock.add("Link?",conversionLink );				
				  conversionLink.activate();
			  } else  if (srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
				BConverter converter = new BStatusNumericToHonStatusNumeric();
				BConversionLink conversionLink = new BConversionLink(nm1, nm1.getSlot("out"), counterBlock.getSlot(slotName), converter);
				conversionLink.setEnabled(true);
				counterBlock.add("Link?", conversionLink);
				conversionLink.activate();
			}  else if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BNegatableStatusBoolean.TYPE)) {                
                BConverter converter = new BStatusNumericToNegatableStatusBoolean();
                BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),counterBlock.getSlot(slotName),converter);
                conversionLink.setEnabled(true);
                counterBlock.add("Link?",conversionLink );                
                conversionLink.activate();
              } else {
				counterBlock.linkTo(nm1, nm1.getSlot("out"), counterBlock.getSlot(slotName));
			}
			return;
		}

		switch (slotName) {
			case "Input":
				counterBlock.setInput(TestDataHelper.getFiniteStatusBoolean(inputValue));
				break;

			case "Enable":
				counterBlock.setEnable(TestDataHelper.getNegatableStatusBoolean(inputValue, false));
				break;

			case "Preset":
				counterBlock.setPreset(TestDataHelper.getFiniteStatusBoolean(inputValue));
				break;

			case "PresetValue":
				counterBlock.setPresetValue(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "CountValue":
				counterBlock.setCountValue(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				break;

			case "StopAtZero":
				counterBlock.setStopAtZero(TestDataHelper.getFiniteStatusBoolean(inputValue));
				break;
		}
	}

	private BCounter counter;
	private BCounter counter1;
	private BExecutionParams executionParams;
}
