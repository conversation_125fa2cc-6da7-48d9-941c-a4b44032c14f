/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.datafunction.test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;

import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON>.
 * @since Feb 2, 2018
 */

public class RuntimeAccumulatorTestDataGenerator {
	
	public static void main(String[] args) {
		
		RuntimeAccumulatorTestDataGenerator rtAccGenerator = new RuntimeAccumulatorTestDataGenerator();

//		ArrayList<String[]> testData = readTestDataFromCSVFile("C:\\Work\\Git\\f1softwaretool\\programmingTool\\programmingTool-rt\\srcTest\\com\\honeywell\\programmingtool\\fbs\\datafunction\\test\\RuntimeAcc_InputData.csv");
		File testDataFile = TestDataHelper.getTestDataFileReference(RuntimeAccumulatorTestDataGenerator.class, "RuntimeAcc_InputData.csv");
		ArrayList<String[]> testData = readTestDataFromCSVFile(testDataFile);
				
		rtAccGenerator.setRuntimeDays(0);
		rtAccGenerator.setRuntimeHrs(0);
		rtAccGenerator.setRuntimeMin(0);
		rtAccGenerator.setRuntimeSec(0);
		rtAccGenerator.setRuntimeSecondsTotal(0);
		
		for(int i=1;i<testData.size();i++) {
			String[] inputs = testData.get(i);
			int runtimeCount = TestDataHelper.getInt(inputs[5], 0);
			for(int j=0;j<runtimeCount;j++)
				rtAccGenerator.generateOutput(inputs[1],inputs[2],inputs[3],inputs[4]);
				
			String output1 = ""+rtAccGenerator.getRuntimeMin();
			String output2 = ""+rtAccGenerator.getRuntimeSec();
			String output3 = ""+rtAccGenerator.getRuntimeHrs();
			String output4 = ""+rtAccGenerator.getRuntimeDays();
			System.out.println(Arrays.toString(inputs) + ", " + output1+", " + output2+", " + output3+", " + output4);			
		}
		
		
		
	}
	
	
	private void generateOutput(String input, String enable, String preset, String presetValue) {
		double presetVal;
		if (TestDataHelper.isUnconnected(presetValue))
			presetVal = 0.0;
		else {
			presetVal = TestDataHelper.getDouble(presetValue, 0.0);
			if(presetVal<0.0 || isInvalidValue(presetVal))
				presetVal = 0.0;
		}
		
		double presetFlag;
		if (TestDataHelper.isUnconnected(preset))
			presetFlag = 0;
		else {
			presetFlag = TestDataHelper.getDouble(preset, 0.0);
			if((Double.compare(presetFlag, 0.0)==0) || isInvalidValue(presetFlag))
				presetFlag = 0;
			else if(Double.compare(presetFlag, 0.0) !=0)
				presetFlag = 1;
				
		}
		
		double inputFlag;
		if (TestDataHelper.isUnconnected(input))
			inputFlag = 0;
		else {
			inputFlag = TestDataHelper.getDouble(input, 0.0);
			if((Double.compare(inputFlag,0.0)==0) || isInvalidValue(inputFlag))
				inputFlag  = 0;
			else if(Double.compare(inputFlag,0.0)!=0)
				inputFlag  = 1;
		}
		
		double runTime = getRuntimeMin();
		
		double enableFlag;
		if (TestDataHelper.isUnconnected(enable))
			enableFlag = 1;
		else {
			enableFlag =TestDataHelper.getDouble(enable, 0.0);
			if(Double.compare(enableFlag, 0.0)==0)		
				enableFlag = 0;
			else
				enableFlag = 1;
		}
		
		if(presetFlag>0) {
			setRuntimeSecondsTotal(0);
			runTime = presetVal;
		}else if(inputFlag>0 && enableFlag>0) {
			double mins;			
			setRuntimeSecondsTotal(getRuntimeSecondsTotal()+ITERATION_INTERVAL);
			mins = Math.floor(getRuntimeSecondsTotal()/60.0F);
			setRuntimeSecondsTotal(getRuntimeSecondsTotal()%60.0F);
			runTime = runTime+mins;
		}
		
		setRuntimeMin(getValue(runTime));
		setRuntimeSec(getValue(runTime*60.0F + getRuntimeSecondsTotal()));
		setRuntimeHrs(getValue(runTime/60.0F));
		setRuntimeDays(getValue(runTime/1440.0F));
	}
	
	
	
	public double getRuntimeMin() {
		return runtimeMin;
	}


	public void setRuntimeMin(double runtimeMin) {
		this.runtimeMin = runtimeMin;
	}


	public double getRuntimeSec() {
		return runtimeSec;
	}


	public void setRuntimeSec(double runtimeSec) {
		this.runtimeSec = runtimeSec;
	}


	public double getRuntimeHrs() {
		return runtimeHrs;
	}


	public void setRuntimeHrs(double runtimeHrs) {
		this.runtimeHrs = runtimeHrs;
	}


	public double getRuntimeDays() {
		return runtimeDays;
	}


	public void setRuntimeDays(double runtimeDays) {
		this.runtimeDays = runtimeDays;
	}
	
	public double getRuntimeSecondsTotal() {
		return runtimeSecondsTotal;
	}


	public void setRuntimeSecondsTotal(double runtimeSecondsTotal) {
		this.runtimeSecondsTotal = runtimeSecondsTotal;
	}
	
	public static ArrayList<String[]> readTestDataFromCSVFile(File completeFile) {
		BufferedReader br = null;
		String line = "";
		String cvsSplitBy = ",";
		ArrayList<String[]> testData = new ArrayList<>();
		try {
			br = new BufferedReader(new FileReader(completeFile));
			while ((line = br.readLine()) != null) {
				String[] data = line.split(cvsSplitBy);
				testData.add(data);
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return testData;
	}
	
	
	private boolean isInvalidValue(double val) {
		if ((val > 0 && Double.isInfinite(val)) || Double.isNaN(val)) 
			return true;
		
		return false;		
	}
	
	
	 private double getValue(double d) {
		  try {
			  BigDecimal bigDecimal = BigDecimal.valueOf(d).setScale(DEFAULT_PRECISION,RoundingMode.HALF_UP);
			  return bigDecimal.doubleValue();
		  } catch (NumberFormatException nfe) {
			  //Handle when user enters other than Number like String, SpecialChar etc...
			  return d;
		  }
	  }

	private double runtimeMin;
	private double runtimeSec;
	private double runtimeHrs;
	private double runtimeDays;
	private double runtimeSecondsTotal;
	private int DEFAULT_PRECISION = 6;
	private int ITERATION_INTERVAL = 1;
	
}
