/*
 * Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 * This file contains trade secrets of Honeywell International, Inc. No part may be reproduced or
 * transmitted in any form by any means or for any purpose without the express written permission of
 * Honeywell.
 */

package com.honeywell.honfunctionblocks.fbs.datafunction.test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;

/**
 *
 * <AUTHOR> - <PERSON><PERSON>
 * @since Feb 2, 2018
 */
public class CountOutputGenerator {
	private static boolean prevInput;
	private static double count;
	
	public static void main(String[] args) {
//		ArrayList<String[]> testData = readTestDataFromCSVFile("C:\\D\\SVN_Checkouts\\38840-F1-IP-Products\\SourceCode\\programmingTool\\programmingTool-rt\\srcTest\\com\\honeywell\\programmingtool\\fbs\\datafunction\\test\\Counter_TestData.csv");

		File testDataFile = TestDataHelper.getTestDataFileReference(CountOutputGenerator.class, "Counter_TestData.csv");
		ArrayList<String[]> testData = readTestDataFromCSVFile(testDataFile);
		count = 0.0;
		for (int i = 1; i < testData.size(); i++) {
			String[] data = testData.get(i);
		   double prevCount;

		   double presetValue = TestDataHelper.getDouble(data[5], 0.0);
		   double countValue = TestDataHelper.getDouble(data[6], 0.0);
		   
		   double presetFlag;
			if (TestDataHelper.isUnconnected(data[3]))
				presetFlag = 0;
			else {
				presetFlag = TestDataHelper.getDouble(data[3], 0.0);
				if((Double.compare(presetFlag, 0.0)==0) || isInvalidValue(presetFlag))
					presetFlag = 0;
				else if(Double.compare(presetFlag, 0.0) !=0)
					presetFlag = 1;
					
			}
			
			double inputFlag;
			if (TestDataHelper.isUnconnected(data[1]))
				inputFlag = 0;
			else {
				inputFlag = TestDataHelper.getDouble(data[1], 0.0);
				if((Double.compare(inputFlag,0.0)==0) || isInvalidValue(inputFlag))
					inputFlag  = 0;
				else if(Double.compare(inputFlag,0.0)!=0)
					inputFlag  = 1;
			}
			
			
			double enableFlag;
			if (TestDataHelper.isUnconnected(data[2]))
				enableFlag = 1;
			else {
				enableFlag =TestDataHelper.getDouble(data[2], 0.0);
				if(Double.compare(enableFlag, 0.0)==0)		
					enableFlag = 0;
				else
					enableFlag = 1;
			}
			
			double stopAtZeroFlag;
			if (TestDataHelper.isUnconnected(data[4]))
				stopAtZeroFlag = 0;
			else {
				stopAtZeroFlag = TestDataHelper.getDouble(data[4], 0.0);
				if((Double.compare(stopAtZeroFlag,0.0)==0) || isInvalidValue(stopAtZeroFlag))
					stopAtZeroFlag  = 0;
				else if(Double.compare(stopAtZeroFlag,0.0)!=0)
					stopAtZeroFlag  = 1;
			}

		   if (TestDataHelper.isUnconnected(data[5]) || (Double.isInfinite(presetValue) && presetValue > 0) || Double.isNaN(presetValue))
		      presetValue = 0.0;
		   if (TestDataHelper.isUnconnected(data[6]) || (Double.isInfinite(countValue) && countValue> 0) || Double.isNaN(countValue))
		      countValue = 1.0;

		   if (presetFlag>0)
		      count = presetValue;

		   else if (enableFlag>0 && inputFlag>0 && !prevInput)
		   {
		      prevCount = count;
		      count += countValue;
		      if ((stopAtZeroFlag>0)  && (prevCount == 0.0 || (prevCount < 0.0 && count > 0.0) || (prevCount > 0.0 && count <0.0) ))
		         count = 0.0;
		   }

		   prevInput = inputFlag!=0;
		   System.out.println(Arrays.toString(data) + "," + count);
		}
	}
	
	private static boolean isInvalidValue(double val) {
		if ((val > 0 && Double.isInfinite(val)) || Double.isNaN(val)) 
			return true;
		
		return false;		
	}

	public static ArrayList<String[]> readTestDataFromCSVFile(File testDataFilePath) {
		BufferedReader br = null;
		String line = "";
		String cvsSplitBy = ",";
		ArrayList<String[]> testData = new ArrayList<>();
		try {
			br = new BufferedReader(new FileReader(testDataFilePath));
			while ((line = br.readLine()) != null) {
				String[] data = line.split(cvsSplitBy);
				testData.add(data);
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return testData;
	}

}
