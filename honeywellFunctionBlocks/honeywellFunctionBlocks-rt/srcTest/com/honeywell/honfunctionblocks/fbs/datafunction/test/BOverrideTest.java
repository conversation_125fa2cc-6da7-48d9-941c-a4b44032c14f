/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.fbs.datafunction.test;

import java.io.FileNotFoundException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BIcon;
import javax.baja.sys.LinkCheck;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.beust.jcommander.internal.Lists;
import com.honeywell.honfunctionblocks.constants.ResourceConstants;
import com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.exceptions.BlockExecutionException;
import com.honeywell.honfunctionblocks.exceptions.BlockInitializationException;
import com.honeywell.honfunctionblocks.fbs.BExecutionParams;
import com.honeywell.honfunctionblocks.fbs.datafunction.BOverride;
import com.honeywell.honfunctionblocks.utils.test.TestDataHelper;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * Testing of Override block implementation as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * Testcase ID: TBD
 * <AUTHOR> - Suresh Khatri
 * @since Feb 2, 2018
 */

@NiagaraType
@SuppressWarnings({"squid:S1845","squid:S1213","squid:S2387","squid:MaximumInheritanceDepth"})

public class BOverrideTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.fbs.datafunction.test.BOverrideTest(2979906276)1.0$ @*/
/* Generated Fri Feb 02 18:19:02 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOverrideTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		override = getOverride();
		executionParams = new BExecutionParams();
	}
	
	@AfterClass
	public void tearDown() {
		override = null;
		executionParams = null;
	}
	
	private Object[][] getInputSlotNames() {
		return new Object[][]{{"priority1Value"}, {"priority2Value"}, {"priority3Value"}, {"priority4Value"}, {"priority5Value"}, {"priority6Value"}, {"cntrlInput"}, {"defaultValue"}};
	}
	
	private Object[][] getOutputSlotNames() {
		return new Object[][]{{"EFF_OUTPUT"}};
	}
	
	private Object[][] getExecOrderSlotNames() {
		return new Object[][] {{"ExecutionOrder"}, {"toolVersion"}};	  
	}
	
	@DataProvider(name="allSlotNames")
	public Object[][] getAllSlotNames() {
		  List<Object[]> slotArrayList = Lists.newArrayList();
		  slotArrayList.addAll(Arrays.asList(getInputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getOutputSlotNames()));
		  slotArrayList.addAll(Arrays.asList(getExecOrderSlotNames()));
		  return slotArrayList.toArray(new Object[slotArrayList.size()][]);
	  
	}
	
	@Test(dataProvider = "allSlotNames")
	public void testAllSlotAvailibility(String slotName) {
		Assert.assertNotNull(override.getSlot(slotName));
	}
	
	@DataProvider(name = "invalidSlotNames")
	public Object[][] getInvalidSlotNames() {
		return new Object[][]{{"Invalid1"}, {"Invalid2"}};
	}
	
	@Test(dataProvider = "invalidSlotNames")
	public void testInvalidSlotNames(String slotName) {
		Assert.assertNull(override.getSlot(slotName));
	}
	
	@Test(groups={"testIconSlot"})
	public void testIconSlot(){
		//check if correct icon is used for AIA
		BIcon expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "override.png");
		BIcon actualFbIcon = override.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);

		//check if new icon can be set on AIA to update modified state
		expectedFbIcon = BIcon.make(ResourceConstants.ICON_DIR + "and.png");
		override.setIcon(expectedFbIcon);
		actualFbIcon = override.getIcon();
		Assert.assertEquals(expectedFbIcon, actualFbIcon);
	}
	
	@DataProvider(name = "validSampleDataForIO")
	public Object[][] getValidSampleDataForIO(){
		  return new Object[][]{{-23}, {0}, {60}, {32767}, {32768}, {40000}, {2.2250738585072014E-308}, 
			  {-1.7976931348623157e+308}, {Double.NEGATIVE_INFINITY}, {Double.POSITIVE_INFINITY},{Double.NaN}};
	}
	
	@Test(dataProvider = "validSampleDataForIO")
	public void testValidSampleDataForIO(double value) {
		override.setPriority1Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority1Value().getValue(), value, 0.1);
		
		override.setPriority2Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority2Value().getValue(), value, 0.1);
		
		override.setPriority3Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority3Value().getValue(), value, 0.1);
		
		override.setPriority4Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority4Value().getValue(), value, 0.1);
		
		override.setPriority5Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority5Value().getValue(), value, 0.1);
		
		override.setPriority6Value(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getPriority6Value().getValue(), value, 0.1);
		
		override.setCntrlInput(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getCntrlInput().getValue(), value, 0.1);
		
		override.setDefaultValue(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getDefaultValue().getValue(), value, 0.1);
		
		override.setEFF_OUTPUT(new BHonStatusNumeric(value));
		Assert.assertEquals(override.getEFF_OUTPUT().getValue(), value, 0.1);
		
	}
	
	//@Test(groups = { "testLinkRules" })
	public void testLinkRules() {
		BOverride overrideB = getOverride();
		checkOutgoingLink(overrideB, BOverride.priority1Value, false);
		checkOutgoingLink(overrideB, BOverride.priority2Value, false);
		checkOutgoingLink(overrideB, BOverride.priority3Value, false);
		checkOutgoingLink(overrideB, BOverride.priority4Value, false);
		checkOutgoingLink(overrideB, BOverride.priority5Value, false);
		checkOutgoingLink(overrideB, BOverride.priority6Value, false);
		checkOutgoingLink(overrideB, BOverride.cntrlInput, false);
		checkOutgoingLink(overrideB, BOverride.defaultValue, false);
	}

	private void checkOutgoingLink(BOverride block, Property prop, boolean isLinkValid) {
		LinkCheck checkLink = override.checkLink(override, override.getSlot(prop.getName()), block.getSlot(prop.getName()), null);	   
		Assert.assertEquals(checkLink.isValid(), isLinkValid);
	}
	
	@DataProvider(name = "overrideTestData")
	public Object[][] getOverrideTestData() throws FileNotFoundException{
	    	return TestDataHelper.getTestDataInTestNGFormat("local:|module://honeywellFunctionBlocksTest/com/honeywell/honfunctionblocks/fbs/datafunction/test/Override_TestData.csv");
	}

	@Test(dataProvider = "overrideTestData")
	 public void testOverrideBlockWithTestData(List<String> inputs) throws BlockExecutionException {
		  BOverride override = new BOverride();
		  executionParams.setIterationInterval(TestDataHelper.getInt(inputs.get(0), 1000));
		  setupNumericSlot(override, BOverride.priority1Value.getName(), inputs.get(1));
		  setupNumericSlot(override, BOverride.priority2Value.getName(), inputs.get(2));
		  setupNumericSlot(override, BOverride.priority3Value.getName(), inputs.get(3));
		  setupNumericSlot(override, BOverride.priority4Value.getName(), inputs.get(4));
		  setupNumericSlot(override, BOverride.priority5Value.getName(), inputs.get(5));
		  setupNumericSlot(override, BOverride.priority6Value.getName(), inputs.get(6));
		  setupNumericSlot(override, BOverride.cntrlInput.getName(), inputs.get(7));
		  setupNumericSlot(override, BOverride.defaultValue.getName(), inputs.get(8));

		  override.executeHoneywellComponent(executionParams);
		  Assert.assertEquals(override.getEFF_OUTPUT().getValue(), TestDataHelper.getDouble(inputs.get(9), 0.0d), Arrays.toString(inputs.toArray()));	  
		  override = null;
	  }
	
	@Test
	public void testInputPropertiesList() {
		List<Property> inputPropList = override.getInputPropertiesList();
		List<String> inputParamNames = inputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualInputParamNames = inputParamNames.toArray(new String[] {});
		Arrays.sort(actualInputParamNames);
		String[] expectedInputParamNames = { BOverride.priority1Value.getName(), BOverride.priority2Value.getName(), BOverride.priority3Value.getName(), BOverride.priority4Value.getName(), BOverride.priority5Value.getName(), BOverride.priority6Value.getName(), BOverride.cntrlInput.getName(), BOverride.defaultValue.getName()};
		Arrays.sort(expectedInputParamNames);

		Assert.assertEquals(actualInputParamNames, expectedInputParamNames);
	}
	
	@Test
	public void testOutputPropertiesList() {
		List<Property> outputPropList = override.getOutputPropertiesList();
		List<String> outputParamNames = outputPropList.stream().map(Slot::getName).collect(Collectors.toList());
		String[] actualOutputParamNames = outputParamNames.toArray(new String[] {});
		Arrays.sort(actualOutputParamNames);
		String[] expectedOutputParamNames = {BOverride.EFF_OUTPUT.getName()};
		Arrays.sort(expectedOutputParamNames);

		Assert.assertEquals(actualOutputParamNames, expectedOutputParamNames);
	}
	
	
	/**
	   * Configure numeric value to the given slot as per test data (either set constant value or by link propagation) 
	   * @param subtractBlock
	   * @param slotName
	   * @param inputValue
	   */
	  public void setupNumericSlot(BOverride overrideBlock, final String slotName, final String inputValue){
		  if(TestDataHelper.isConnected(inputValue)){
			  BNumericConst nm1 = new BNumericConst();
				nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				Type srcType = nm1.getOut().getType();
				Type targetType = overrideBlock.getProperty(slotName).getType();		
				if(srcType.is(BStatusNumeric.TYPE) && targetType.is(BHonStatusNumeric.TYPE)) {
					BConverter converter = null;
					if(overrideBlock.get("Link"+slotName)!=null) {
						overrideBlock.remove("Link"+slotName);
					}
					converter = new BStatusNumericToHonStatusNumeric();
					BConversionLink conversionLink = new BConversionLink(nm1,nm1.getSlot("out"),overrideBlock.getSlot(slotName),converter);
					conversionLink.setEnabled(true);
					overrideBlock.add("Link"+slotName,conversionLink );				
					conversionLink.activate();
					nm1.getOut().setValue(TestDataHelper.getDouble(inputValue, 0d));
				}else{
					overrideBlock.linkTo(nm1, nm1.getSlot("out"), overrideBlock.getSlot(slotName));
				}
		  }

		  switch (slotName) {
			  case "priority1Value":
				  overrideBlock.setPriority1Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "priority2Value":
				  overrideBlock.setPriority2Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "priority3Value":
				  overrideBlock.setPriority3Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "priority4Value":
				  overrideBlock.setPriority4Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "priority5Value":
				  overrideBlock.setPriority5Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "priority6Value":
				  overrideBlock.setPriority6Value(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;

			  case "cntrlInput":
				  overrideBlock.setCntrlInput(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
			  
			  case "defaultValue":
				  overrideBlock.setDefaultValue(TestDataHelper.getHonStatusNumeric(inputValue, 0d));
				  break;
		  }
	  }
	
	
	private BOverride getOverride() {
		BOverride override = new BOverride();
		try {
			override.initHoneywellComponent(null);
		} catch (BlockInitializationException e) {
			e.printStackTrace();
		}
		return override;
	}
	
	private BOverride override;
	private BExecutionParams executionParams;

}
