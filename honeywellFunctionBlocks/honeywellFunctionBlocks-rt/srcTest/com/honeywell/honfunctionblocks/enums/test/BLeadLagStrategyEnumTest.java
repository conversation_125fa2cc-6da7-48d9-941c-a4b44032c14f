/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.enums.BLeadLagStrategyEnum;

/**
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON>
 * @since May 11, 2018
 */
@NiagaraType
public class BLeadLagStrategyEnumTest extends BTestNg  {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.enums.test.BLeadLagStrategyEnumTest(**********)1.0$ @*/
/* Generated Fri May 11 21:46:17 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BLeadLagStrategyEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { 0, BLeadLagStrategyEnum.llFOLO }, { 1, BLeadLagStrategyEnum.llFOFO }, { 2, BLeadLagStrategyEnum.llRUNEQ } };
	}

	@Test(dataProvider = "enumOrdinal")
	public void testLeadLagStrategyByMakeOrdinal(int ordinal, BLeadLagStrategyEnum toEnum) {
		Assert.assertEquals(BLeadLagStrategyEnum.make(ordinal), toEnum);
	}

	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { "llFOLO", BLeadLagStrategyEnum.llFOLO }, { "llFOFO", BLeadLagStrategyEnum.llFOFO }, { "llRUNEQ", BLeadLagStrategyEnum.llRUNEQ } };
	}

	@Test(dataProvider = "enumTag")
	public void testLeadLagStrategyByMakeTag(String tag, BLeadLagStrategyEnum llsEEnum) {
		Assert.assertEquals(BLeadLagStrategyEnum.make(tag), llsEEnum);
	}

}
