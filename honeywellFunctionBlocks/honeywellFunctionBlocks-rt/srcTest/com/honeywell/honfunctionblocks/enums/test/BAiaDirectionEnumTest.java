/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.honfunctionblocks.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.enums.BAiaDirectionEnum;



/**
 * <AUTHOR> - <PERSON>gan<PERSON><PERSON>rida
 * @since May 11, 2018
 */
@NiagaraType
public class BAiaDirectionEnumTest extends BTestNg {
/*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.enums.test.BAiaDirectionEnumTest(**********)1.0$ @*/
/* Generated Fri May 11 21:35:39 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAiaDirectionEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { 0, BAiaDirectionEnum.directActing }, { 1, BAiaDirectionEnum.reverseActing } };
	}

	@Test(dataProvider = "enumOrdinal")
	public void testAiaDirectionByMakeOrdinal(int ordinal, BAiaDirectionEnum toEnum) {
		Assert.assertEquals(BAiaDirectionEnum.make(ordinal), toEnum);
	}

	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { "directActing", BAiaDirectionEnum.directActing }, { "reverseActing", BAiaDirectionEnum.reverseActing } };
	}

	@Test(dataProvider = "enumTag")
	public void testAiaDirectionByMakeTag(String tag, BAiaDirectionEnum adEEnum) {
		Assert.assertEquals(BAiaDirectionEnum.make(tag), adEEnum);
	}

}
