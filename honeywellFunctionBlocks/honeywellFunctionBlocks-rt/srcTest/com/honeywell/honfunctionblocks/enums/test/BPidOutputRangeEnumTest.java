package com.honeywell.honfunctionblocks.enums.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.enums.BPidOutputRangeEnum;

@NiagaraType
public class BPidOutputRangeEnumTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.enums.test.BPidOutputRangeEnumTest(**********)1.0$ @*/
/* Generated Wed Sep 26 16:06:29 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPidOutputRangeEnumTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name = "enumOrdinal")
	public Object[][] getDataToTestEnumOrdinal() {
		return new Object[][] { { 0, BPidOutputRangeEnum.pidOutputRange0to100 }, { 1, BPidOutputRangeEnum.pidOutputRangeMinus200to200 } };
	}

	@Test(dataProvider = "enumOrdinal")
	public void testPidOutputRangeByMakeOrdinal(int ordinal, BPidOutputRangeEnum toEnum) {
		Assert.assertEquals(BPidOutputRangeEnum.make(ordinal), toEnum);
	}

	@DataProvider(name = "enumTag")
	public Object[][] getDataToTestEnumTag() {
		return new Object[][] { { "pidOutputRange0to100", BPidOutputRangeEnum.pidOutputRange0to100 }, { "pidOutputRangeMinus200to200", BPidOutputRangeEnum.pidOutputRangeMinus200to200 } };
	}

	@Test(dataProvider = "enumTag")
	public void testPidOutputRangeByMakeTag(String tag, BPidOutputRangeEnum adEEnum) {
		Assert.assertEquals(BPidOutputRangeEnum.make(tag), adEEnum);
	}
}
