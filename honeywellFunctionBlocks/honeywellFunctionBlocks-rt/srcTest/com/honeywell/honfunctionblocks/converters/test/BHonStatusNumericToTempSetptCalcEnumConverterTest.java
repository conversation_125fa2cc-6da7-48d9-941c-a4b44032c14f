/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BHonStatusNumericToTempSetptCalcEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BStatusTempSetpointCalcEnum;

/**
 *
 * <AUTHOR>
 * @since Jun 27, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BHonStatusNumericToTempSetptCalcEnumConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToTempSetptCalcEnumConverterTest(**********)1.0$ @*/
/* Generated Wed Jun 27 16:11:47 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumericToTempSetptCalcEnumConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @DataProvider(name="provideTestDataForOccupancyEnum")
	public Object[][] provideTestDataForOccupancyEnum(){
		return new Object[][] {
			{ 5.0,BOccupancyEnum.Null },
			{ -5.0,BOccupancyEnum.Null },
			{ 0.0,BOccupancyEnum.Occupied },
			{ 1.0,BOccupancyEnum.Unoccupied },
			{ 2.0,BOccupancyEnum.Bypass },
			{ 3.0,BOccupancyEnum.Standby },
			{ 255,BOccupancyEnum.Null },
			{ 7.56,BOccupancyEnum.Null },
			{ -7.56,BOccupancyEnum.Null},
			{ 0.001,BOccupancyEnum.Occupied},
			{ -0.001,BOccupancyEnum.Occupied},
			{ Double.NEGATIVE_INFINITY,BOccupancyEnum.Null },
			{ Double.POSITIVE_INFINITY,BOccupancyEnum.Null },
			{ Double.NaN,BOccupancyEnum.Null }
		};		
	}
	
	@Test(dataProvider="provideTestDataForOccupancyEnum")
	public void testConverterForOccupancyEnum(double d,BOccupancyEnum occupancyEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusTempSetpointCalcEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BOccupancyEnum.TYPE)));

		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToTempSetptCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusTempSetpointCalcEnum fb = (BStatusTempSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(BOccupancyEnum.make(fb.getValue().getOrdinal()), occupancyEnum);		
	}
	
	
	@DataProvider(name="provideTestDataForEffectiveOccupancyEnum")
	public Object[][] provideTestDataForEffectiveOccupancyEnum(){
		return new Object[][] {
			{ 5.0,BEffectiveOccupancyEnum.Occupied },
			{ -5.0,BEffectiveOccupancyEnum.Occupied },
			{ 0.0,BEffectiveOccupancyEnum.Occupied },
			{ 1.0,BEffectiveOccupancyEnum.Unoccupied },
			{ 2.0,BEffectiveOccupancyEnum.Bypass },
			{ 3.0,BEffectiveOccupancyEnum.Standby },
			{ 255,BEffectiveOccupancyEnum.Occupied },
			{ 7.56,BEffectiveOccupancyEnum.Occupied },
			{ -7.56,BEffectiveOccupancyEnum.Occupied},
			{ 0.001,BEffectiveOccupancyEnum.Occupied},
			{ -0.001,BEffectiveOccupancyEnum.Occupied},
			{ Double.NEGATIVE_INFINITY,BEffectiveOccupancyEnum.Occupied },
			{ Double.POSITIVE_INFINITY,BEffectiveOccupancyEnum.Occupied },
			{ Double.NaN,BEffectiveOccupancyEnum.Occupied }
		};		
	}
	
	@Test(dataProvider="provideTestDataForEffectiveOccupancyEnum")
	public void testConverterForEffectiveOccupancyEnum(double d,BEffectiveOccupancyEnum occupancyEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusTempSetpointCalcEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BEffectiveOccupancyEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToTempSetptCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusTempSetpointCalcEnum fb = (BStatusTempSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(BEffectiveOccupancyEnum.make(fb.getValue().getOrdinal()), occupancyEnum);		
	}
	
	
	@DataProvider(name="provideTestDataForScheduledStateEnum")
	public Object[][] provideTestDataForScheduledStateEnum(){
		return new Object[][] {
			{ 5.0,BScheduledStateEnum.Null },
			{ -5.0,BScheduledStateEnum.Null },
			{ 0.0,BScheduledStateEnum.Occupied },
			{ 1.0,BScheduledStateEnum.Unoccupied },
			{ 2.0,BScheduledStateEnum.Null },
			{ 3.0,BScheduledStateEnum.Standby },
			{ 255,BScheduledStateEnum.Null },
			{ 7.56,BScheduledStateEnum.Null },
			{ -7.56,BScheduledStateEnum.Null},
			{ 0.001,BScheduledStateEnum.Occupied},
			{ -0.001,BScheduledStateEnum.Occupied},
			{ Double.NEGATIVE_INFINITY,BScheduledStateEnum.Null },
			{ Double.POSITIVE_INFINITY,BScheduledStateEnum.Null },
			{ Double.NaN,BScheduledStateEnum.Null }
		};		
	}
	
	@Test(dataProvider="provideTestDataForScheduledStateEnum")
	public void testConverterForScheduledStateEnum(double d,BScheduledStateEnum occupancyEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusTempSetpointCalcEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BScheduledStateEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToTempSetptCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusTempSetpointCalcEnum fb = (BStatusTempSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(BScheduledStateEnum.make(fb.getValue().getOrdinal()), occupancyEnum);		
	}
	

	@DataProvider(name="provideTestDataForUnSupportedEnum")
	public Object[][] provideTestDataForUnSupportedEnums(){
		return new Object[][] {
			{ 5.0,BDynamicEnum.make(5) },
			{ -5.0,BDynamicEnum.make(-5) },
			{ 0.0,BDynamicEnum.make(BOccupancySensorStateEnum.Occupied) },
			{ 1.0,BDynamicEnum.make(BOccupancySensorStateEnum.Unoccupied) },
			{ 2.0,BDynamicEnum.make(2) },
			{ 3.0,BDynamicEnum.make(3) },
			{ 255,BDynamicEnum.make(255) },
			{ 7.56,BDynamicEnum.make(7) },
			{ -7.56,BDynamicEnum.make(-7)},
			{ 0.001,BDynamicEnum.make(0)},
			{ -0.001,BDynamicEnum.make(0)},
			{ Double.NaN,BDynamicEnum.make(0) }
		};		
	}
	
	@Test(dataProvider="provideTestDataForUnSupportedEnum")
	public void testConverterForUnsupportedEnums(double d,BDynamicEnum dynamicEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusTempSetpointCalcEnum(BOccupancySensorStateEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BOccupancySensorStateEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToTempSetptCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusTempSetpointCalcEnum fb = (BStatusTempSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(fb.getValue().getOrdinal(), dynamicEnum.getOrdinal());		
	}


}
