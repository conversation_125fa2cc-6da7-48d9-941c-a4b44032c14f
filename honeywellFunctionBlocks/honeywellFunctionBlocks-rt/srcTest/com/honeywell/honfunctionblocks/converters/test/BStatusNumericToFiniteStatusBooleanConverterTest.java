/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * <AUTHOR>
 *
 */

@NiagaraType


public class BStatusNumericToFiniteStatusBooleanConverterTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusNumericToFiniteStatusBooleanConverterTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToFiniteStatusBooleanConverterTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  
	@Override
	@BeforeClass(alwaysRun=true)
	public void setup() {
	}
	
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ Double.valueOf(5.0),true },
			{ Double.valueOf(-5.0),true },
			{ Double.valueOf(0.0),false },
			{ Double.valueOf(7.56),true },
			{ Double.valueOf(-7.56),true},
			{ Double.valueOf(Double.NEGATIVE_INFINITY),true },
			{ Double.valueOf(Double.POSITIVE_INFINITY),false },
			{ Double.valueOf(Double.NaN),false }
		};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(double d,boolean expected) {
		BComponent c = new BComponent();
		c.add("in1", new BFiniteStatusBoolean());
		BNumericConst nm = new BNumericConst();		
		BConverter converter = new BStatusNumericToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(nm,nm.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		nm.getOut().setValue(d);
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)c.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	@AfterClass
	private void tearDown() {

	}
	
	
	
}
