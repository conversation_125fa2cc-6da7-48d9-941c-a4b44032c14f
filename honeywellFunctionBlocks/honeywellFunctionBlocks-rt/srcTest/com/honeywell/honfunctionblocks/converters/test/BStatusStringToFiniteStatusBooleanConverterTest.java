/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.control.BStringWritable;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusStringToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStatusStringToFiniteStatusBooleanConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusStringToFiniteStatusBooleanConverterTest(**********)1.0$ @*/
/* Generated Mon Nov 27 16:01:29 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusStringToFiniteStatusBooleanConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @Override
@BeforeClass(alwaysRun=true)
	public void setup() {
	}
	
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ "5.0",true },
			{ "-5.0",true },
			{ "0.0",false },
			{ "7.56",true },
			{ "-7.56",true},
			{ "+inf",false },
			{ "-inf",true },
			{ "NaN",false },
			{ "nan",false },
			{ "Nan",false },
			{ "naN",false },
			{ "false",false },
			{ "true",true },
			{ "good",false }
		};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(String s,boolean expected) {
		BComponent c = new BComponent();
		c.add("in1", new BFiniteStatusBoolean());
	
		BStringWritable sw = new BStringWritable();
		BConverter converter = new BStatusStringToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(sw,sw.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );
		sw.getOut().setValue(s);
		conversionLink.activate();	
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)c.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	class DataSet{
		double in;
		boolean out;		
		
		DataSet(double in, boolean out){
			this.in = in;
			this.out = out;
		}
	}
	

	
	@AfterClass
	private void tearDown() {

	}
  
  
}
