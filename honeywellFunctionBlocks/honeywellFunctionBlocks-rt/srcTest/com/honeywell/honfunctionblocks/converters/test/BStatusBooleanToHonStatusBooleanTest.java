/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.control.BBooleanWritable;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusBooleanToHonStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean;

/**
 * Testing StatusBooleanToHonStatusBoolean converter
 * <AUTHOR> - RSH<PERSON>
 *
 */

@NiagaraType


public class BStatusBooleanToHonStatusBooleanTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToHonStatusBooleanTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusBooleanToHonStatusBooleanTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  	@DataProvider(name="provideTestData")
	public Object[][] provideTestData() {
		return new Object[][] { { true, BStatus.ok }, { false, BStatus.nullStatus } };
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(boolean input, BStatus status) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BHonStatusBoolean());
		
		BBooleanWritable bw = new BBooleanWritable();		
		BConverter converter = new BStatusBooleanToHonStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(bw,bw.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link?",conversionLink );				
		conversionLink.activate();
		
		bw.getOut().setValue(input);
		bw.getOut().setStatus(status);
		
		BHonStatusBoolean honStatusBoolean = (BHonStatusBoolean)tgt.get("in1");
		Assert.assertEquals(honStatusBoolean.getBoolean(), input);
		Assert.assertEquals(honStatusBoolean.getStatus(), status);
	}
	
}
