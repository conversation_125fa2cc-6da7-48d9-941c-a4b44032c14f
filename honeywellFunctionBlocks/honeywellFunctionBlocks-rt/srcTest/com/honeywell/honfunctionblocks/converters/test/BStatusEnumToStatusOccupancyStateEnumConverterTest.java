/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusOccupancyStateEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 *
 * <AUTHOR> - Lavanya B.
 * @since Feb 12, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStatusEnumToStatusOccupancyStateEnumConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusEnumToStatusOccupancyStateEnumConverterTest(**********)1.0$ @*/
/* Generated Mon Feb 12 12:16:00 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusEnumToStatusOccupancyStateEnumConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	
	@DataProvider(name="provideTestDataForScheduleStateEnum")
	public Object[][] provideTestDataForScheduleStateEnum(){
		return new Object[][] {
			{ 5.0,BScheduledStateEnum.Null },
			{ -5.0,BScheduledStateEnum.Occupied },
			{ 0.0,BScheduledStateEnum.Occupied },
			{ 1.0,BScheduledStateEnum.Unoccupied },
			{ 2.0,BScheduledStateEnum.Null },
			{ 3.0,BScheduledStateEnum.Standby },
			{ 255,BScheduledStateEnum.Null },
			{ 7.56,BScheduledStateEnum.Null },
			{ -7.56,BScheduledStateEnum.Occupied},
			{ 0.001,BScheduledStateEnum.Occupied},
			{ -0.001,BScheduledStateEnum.Occupied},
		};		
	}
	
	@Test(dataProvider="provideTestDataForScheduleStateEnum")
	public void testConverterForScheduleStateEnum(double d,BScheduledStateEnum schedStateEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusOccupancyStateEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BScheduledStateEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusOccupancyStateEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusOccupancyStateEnum fb = (BStatusOccupancyStateEnum)c.get("in1");
		Assert.assertEquals(BScheduledStateEnum.make(fb.getValue().getOrdinal()), schedStateEnum);		
	}

	
	
	@DataProvider(name="provideTestDataForOccupancyEnum")
	public Object[][] provideTestDataForOccupancyEnum(){
		return new Object[][] {
			{ 5.0,BOccupancyEnum.Null },
			{ -5.0,BOccupancyEnum.Occupied },
			{ 0.0,BOccupancyEnum.Occupied },
			{ 1.0,BOccupancyEnum.Unoccupied },
			{ 2.0,BOccupancyEnum.Bypass },
			{ 3.0,BOccupancyEnum.Standby },
			{ 255,BOccupancyEnum.Null },
			{ 7.56,BOccupancyEnum.Null },
			{ -7.56,BOccupancyEnum.Occupied},
			{ 0.001,BOccupancyEnum.Occupied},
			{ -0.001,BOccupancyEnum.Occupied},
		};		
	}
	
	@Test(dataProvider="provideTestDataForOccupancyEnum")
	public void testConverterForOccupancyEnum(double d,BOccupancyEnum occupancyEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusOccupancyStateEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BOccupancyEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusOccupancyStateEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusOccupancyStateEnum fb = (BStatusOccupancyStateEnum)c.get("in1");
		Assert.assertEquals(BOccupancyEnum.make(fb.getValue().getOrdinal()), occupancyEnum);		
	}
	
	
	@DataProvider(name="provideTestDataForOccupancySensorStateEnum")
	public Object[][] provideTestDataForOccupancySensorStateEnum(){
		return new Object[][] {
			{ 5.0,BOccupancySensorStateEnum.Null },
			{ -5.0,BOccupancySensorStateEnum.Occupied },
			{ 0.0,BOccupancySensorStateEnum.Occupied },
			{ 1.0,BOccupancySensorStateEnum.Unoccupied },
			{ 2.0,BOccupancySensorStateEnum.Null },
			{ 3.0,BOccupancySensorStateEnum.Null },
			{ 255,BOccupancySensorStateEnum.Null },
			{ 7.56,BOccupancySensorStateEnum.Null },
			{ -7.56,BOccupancySensorStateEnum.Occupied},
			{ 0.001,BOccupancySensorStateEnum.Occupied},
			{ -0.001,BOccupancySensorStateEnum.Occupied},
		};		
	}
	
	@Test(dataProvider="provideTestDataForOccupancySensorStateEnum")
	public void testConverterForOccupancySensorStateEnum(double d,BOccupancySensorStateEnum occupancySensorStateEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusOccupancyStateEnum(BOccupancySensorStateEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BOccupancySensorStateEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusOccupancyStateEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusOccupancyStateEnum fb = (BStatusOccupancyStateEnum)c.get("in1");
		Assert.assertEquals(BOccupancySensorStateEnum.make(fb.getValue().getOrdinal()), occupancySensorStateEnum);		
	}

	@DataProvider(name="provideTestDataForEffectiveOccupancyEnum")
	public Object[][] provideTestDataForEffectiveOccupancyEnum(){
		return new Object[][] {
			{ 5.0,BEffectiveOccupancyEnum.Occupied },
			{ -5.0,BEffectiveOccupancyEnum.Occupied },
			{ 0.0,BEffectiveOccupancyEnum.Occupied },
			{ 1.0,BEffectiveOccupancyEnum.Unoccupied },
			{ 2.0,BEffectiveOccupancyEnum.Bypass },
			{ 3.0,BEffectiveOccupancyEnum.Standby },
			{ 255,BEffectiveOccupancyEnum.Occupied },
			{ 7.56,BEffectiveOccupancyEnum.Occupied },
			{ -7.56,BEffectiveOccupancyEnum.Occupied},
			{ 0.001,BEffectiveOccupancyEnum.Occupied},
			{ -0.001,BEffectiveOccupancyEnum.Occupied},
			{ Double.NEGATIVE_INFINITY,BEffectiveOccupancyEnum.Occupied },
			{ Double.POSITIVE_INFINITY,BEffectiveOccupancyEnum.Occupied },
			{ Double.NaN,BEffectiveOccupancyEnum.Occupied }
		};		
	}
	
	@Test(dataProvider="provideTestDataForEffectiveOccupancyEnum")
	public void testConverterForEffectiveOccupancyEnum(double d, BEffectiveOccupancyEnum effOccupancyStateEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusOccupancyStateEnum(BEffectiveOccupancyEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BEffectiveOccupancyEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusOccupancyStateEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusOccupancyStateEnum fb = (BStatusOccupancyStateEnum)c.get("in1");
		Assert.assertEquals(BEffectiveOccupancyEnum.make(fb.getValue().getOrdinal()), effOccupancyStateEnum);		
	}
}
