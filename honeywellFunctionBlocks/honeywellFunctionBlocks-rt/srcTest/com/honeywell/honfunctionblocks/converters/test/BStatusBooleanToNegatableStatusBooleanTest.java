/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.control.BBooleanWritable;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusBooleanToNegatableStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean;

/**
 * Testing StatusBooleanToNegatableStatusBoolean converter
 * <AUTHOR> - Sugandhika Parida
 *
 */

@NiagaraType
public class BStatusBooleanToNegatableStatusBooleanTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToNegatableStatusBooleanTest(**********)1.0$ @*/
/* Generated Wed May 16 14:54:06 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusBooleanToNegatableStatusBooleanTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData() {
		return new Object[][] { { true, BStatus.ok }, { false, BStatus.nullStatus } };
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(boolean input, BStatus status) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BNegatableStatusBoolean());
		
		BBooleanWritable bw = new BBooleanWritable();		
		BConverter converter = new BStatusBooleanToNegatableStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(bw,bw.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link?",conversionLink );				
		conversionLink.activate();
		
		bw.getOut().setValue(input);
		bw.getOut().setStatus(status);
		
		BNegatableStatusBoolean negatablehonStatusBoolean = (BNegatableStatusBoolean)tgt.get("in1");
		Assert.assertEquals(negatablehonStatusBoolean.getBoolean(), input);
		Assert.assertEquals(negatablehonStatusBoolean.getStatus(), status);
	}

}
