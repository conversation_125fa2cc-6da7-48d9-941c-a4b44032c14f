/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BHonStatusNumericToGenSetpointCalcEnumConverter;
import com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BStatusGenSetpointCalcEnum;
import com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum;

/**
 *
 * <AUTHOR> - Lavanya B.
 * @since Feb 15, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BHonStatusNumericToGenSetpointCalcEnumConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BHonStatusNumericToGenSetpointCalcEnumConverterTest(**********)1.0$ @*/
/* Generated Thu Feb 15 10:10:03 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonStatusNumericToGenSetpointCalcEnumConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  @DataProvider(name="provideTestDataForOccupancyEnum")
	public Object[][] provideTestDataForOccupancyEnum(){
		return new Object[][] {
			{ 5.0,BOccupancyEnum.Occupied },
			{ -5.0,BOccupancyEnum.Occupied },
			{ 0.0,BOccupancyEnum.Occupied },
			{ 1.0,BOccupancyEnum.Unoccupied },
			{ 2.0,BOccupancyEnum.Bypass },
			{ 3.0,BOccupancyEnum.Standby },
			{ 255,BOccupancyEnum.Null },
			{ 7.56,BOccupancyEnum.Occupied },
			{ -7.56,BOccupancyEnum.Occupied},
			{ 0.001,BOccupancyEnum.Occupied},
			{ -0.001,BOccupancyEnum.Occupied},
			{ Double.NEGATIVE_INFINITY,BOccupancyEnum.Occupied },
			{ Double.POSITIVE_INFINITY,BOccupancyEnum.Occupied },
			{ Double.NaN,BOccupancyEnum.Occupied }
		};		
	}
	
	@Test(dataProvider="provideTestDataForOccupancyEnum")
	public void testConverterForOccupancyEnum(double d,BOccupancyEnum occupancyEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusGenSetpointCalcEnum(BOccupancyEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BOccupancyEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToGenSetpointCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusGenSetpointCalcEnum fb = (BStatusGenSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(BOccupancyEnum.make(fb.getValue().getOrdinal()), occupancyEnum);		
	}
	
	@DataProvider(name="provideTestDataForScheduleStateEnum")
	public Object[][] provideTestDataForScheduleStateEnum(){
		return new Object[][] {
			{ 0.0,BScheduledStateEnum.Null },
			{ 1.0,BScheduledStateEnum.Null },
			{ 3.0,BScheduledStateEnum.Null },
			{ 255,BScheduledStateEnum.Null }
		};		
	}
	
	@Test(dataProvider="provideTestDataForScheduleStateEnum")
	public void testConverterForScheduleStateEnum(double d,BScheduledStateEnum schedStateEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusGenSetpointCalcEnum(BScheduledStateEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BScheduledStateEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BHonStatusNumeric());
		
		BConverter converter = new BHonStatusNumericToGenSetpointCalcEnumConverter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BHonStatusNumeric val = (BHonStatusNumeric)src.get(srcProperty);
		val.setValue(d);
		BStatusGenSetpointCalcEnum fb = (BStatusGenSetpointCalcEnum)c.get("in1");
		Assert.assertEquals(BScheduledStateEnum.make(fb.getValue().getOrdinal()), schedStateEnum);		
	}


}
