/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDouble;
import javax.baja.sys.BFloat;
import javax.baja.sys.BInteger;
import javax.baja.sys.BLong;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BNumberToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BNumberToFiniteStatusBooleanConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BNumberToFiniteStatusBooleanConverterTest(**********)1.0$ @*/
/* Generated Mon Nov 27 16:29:37 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNumberToFiniteStatusBooleanConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
  @Override
@BeforeClass(alwaysRun=true)
	public void setup() {
	}
	
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ Double.valueOf(5.0),true },
			{ Double.valueOf(-5.0),true },
			{ Double.valueOf(0.0),false },
			{ Double.valueOf(7.56),true },
			{ Double.valueOf(-7.56),true},
			{ Double.valueOf(Double.NEGATIVE_INFINITY),true },
			{ Double.valueOf(Double.POSITIVE_INFINITY),false },
			{ Double.valueOf(Double.NaN),false },
			{ Float.valueOf(5),true },
			{ Float.valueOf(-5),true },
			{ Float.valueOf(0),false },			
			{ Float.valueOf(Float.NEGATIVE_INFINITY),true },
			{ Float.valueOf(Float.POSITIVE_INFINITY),false },
			{ Float.valueOf(Float.NaN),false },
			{ Integer.valueOf(5),true },
			{ Integer.valueOf(-5),true },
			{ Integer.valueOf(0),false },			
			{ Integer.valueOf(Integer.MAX_VALUE),false },
			{ Integer.valueOf(Integer.MIN_VALUE),false },
			{ Long.valueOf(5),true },
			{ Long.valueOf(-5),true },
			{ Long.valueOf(0),false },
			{ Long.valueOf(Long.MAX_VALUE),false },
			{ Long.valueOf(Long.MIN_VALUE),false }
		};		
	}
	
	
	@DataProvider(name="provideDoubleTestData")
	public Object[][] provideDoubleTestData(){
		return new Object[][] {
			{ Double.valueOf(5.0),true },
			{ Double.valueOf(-5.0),true },
			{ Double.valueOf(0.0),false },
			{ Double.valueOf(7.56),true },
			{ Double.valueOf(-7.56),true},
			{ Double.valueOf(Double.NEGATIVE_INFINITY),true },
			{ Double.valueOf(Double.POSITIVE_INFINITY),false },
			{ Double.valueOf(Double.NaN),false }			
		};		
	}
	
	
	@Test(dataProvider="provideDoubleTestData")
	public void testDoubleConverter(double d,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		
		BComponent src = new BComponent();
		src.add("out", BDouble.make(d));
				
		BConverter converter = new BNumberToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	@DataProvider(name="provideFloatTestData")
	public Object[][] provideFloatTestData(){
		return new Object[][] {
			{ Float.valueOf(5),true },
			{ Float.valueOf(-5),true },
			{ Float.valueOf(0),false },			
			{ Float.valueOf(Float.NEGATIVE_INFINITY),true },
			{ Float.valueOf(Float.POSITIVE_INFINITY),false },
			{ Float.valueOf(Float.NaN),false },
		};		
	}
	
	
	@Test(dataProvider="provideFloatTestData")
	public void testFloatConverter(float d,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		
		BComponent src = new BComponent();
		src.add("out", BFloat.make(d));
				
		BConverter converter = new BNumberToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	@DataProvider(name="provideIntTestData")
	public Object[][] provideIntTestData(){
		return new Object[][] {
			{ Integer.valueOf(5),true },
			{ Integer.valueOf(-5),true },
			{ Integer.valueOf(0),false },			
			{ Integer.valueOf(Integer.MAX_VALUE),true },
			{ Integer.valueOf(Integer.MIN_VALUE),true },
		};		
	}
	
	
	@Test(dataProvider="provideIntTestData")
	public void testIntConverter(int d,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		
		BComponent src = new BComponent();
		src.add("out", BInteger.make(d));
				
		BConverter converter = new BNumberToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	@DataProvider(name="provideLongTestData")
	public Object[][] provideLongTestData(){
		return new Object[][] {
			{ Long.valueOf(5),true },
			{ Long.valueOf(-5),true },
			{ Long.valueOf(0),false },
			{ Long.valueOf(Long.MAX_VALUE),true },
			{ Long.valueOf(Long.MIN_VALUE),true }
		};		
	}
	
	
	@Test(dataProvider="provideLongTestData")
	public void testLongConverter(long d,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		
		BComponent src = new BComponent();
		src.add("out", BLong.make(d));
				
		BConverter converter = new BNumberToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	@DataProvider(name="provideDoubleTestDataForBNegatableFiniteStatusBooleanTarget")
	public Object[][] provideDoubleTestDataForBNegatableFiniteStatusBooleanTarget(){
		return new Object[][] {
			{ Double.valueOf(5.0),true },
			{ Double.valueOf(-5.0),true },
			{ Double.valueOf(0.0),false },
			{ Double.valueOf(Double.NEGATIVE_INFINITY),true },
			{ Double.valueOf(Double.POSITIVE_INFINITY),false },
			{ Double.valueOf(Double.NaN),false }			
		};		
	}
	
	
	@Test(dataProvider="provideDoubleTestDataForBNegatableFiniteStatusBooleanTarget")
	public void testDoubleTestDataForBNegatableFiniteStatusBooleanTargetConverter(double d,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BNegatableFiniteStatusBoolean());
		
		
		BComponent src = new BComponent();
		src.add("out", BDouble.make(d));
				
		BConverter converter = new BNumberToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	class DataSet{
		double in;
		boolean out;		
		
		DataSet(double in, boolean out){
			this.in = in;
			this.out = out;
		}
	}
	

	
	@AfterClass
	private void tearDown() {

	}

}
