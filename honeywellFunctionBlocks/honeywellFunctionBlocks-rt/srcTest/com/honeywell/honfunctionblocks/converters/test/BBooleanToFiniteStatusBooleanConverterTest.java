/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BBooleanToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType


public class BBooleanToFiniteStatusBooleanConverterTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.test.BBooleanToFiniteStatusBooleanConverterTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BBooleanToFiniteStatusBooleanConverterTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  @BeforeClass(alwaysRun=true)
	public void setUp() {
		
	}  

@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ true,true },
			{ false,false }
		};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(boolean input,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		BComponent src = new BComponent();
		src.add("out", BBoolean.make(input));
		
		BConverter converter = new BBooleanToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);		
	}
	
	
	
	@AfterClass
	public void tearDown() {
		
	}

}
