/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusSyncEdgeTriggerEnumConverter;
import com.honeywell.honfunctionblocks.fbs.io.enums.BStatusSyncEdgeTriggerEnum;
import com.honeywell.honfunctionblocks.fbs.io.enums.BSyncEdgeTriggerEnum;
import com.tridium.kitControl.constants.BNumericConst;

/**
 * <AUTHOR> - Sandeep Mekapotula
 * @since 06 June,2018
 */
@NiagaraType
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845"})
public class BStatusNumericToStatusSyncEdgeTriggerEnumConverterTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusNumericToStatusSyncEdgeTriggerEnumConverterTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToStatusSyncEdgeTriggerEnumConverterTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @BeforeClass(alwaysRun = true)
  public void setUp() {
  }

  @DataProvider(name="testEnums")
  public Object[][] provideTestData(){
    return new Object[][]{
      {-2,0},
      {-1.5,0},
      {-1,0},
      {-0.5,0},
      {0,0},
      {0.5,0},
      {1,1},
      {2,2},
      {2.5,2},
      {3,0},
      {3.5,0},
      {4,0}
    };
  }

  @Test(dataProvider = "testEnums")
  public void testConverter(double input, int output){
    BComponent tgt = new BComponent();
    tgt.add("in", new BStatusSyncEdgeTriggerEnum(BSyncEdgeTriggerEnum.SyncClosed, BStatus.nullStatus));

    BNumericConst nm = new BNumericConst();
    BConverter converter = new BStatusNumericToStatusSyncEdgeTriggerEnumConverter();
    BConversionLink conversionLink = new BConversionLink(nm,nm.getSlot("out"),tgt.getSlot("in"),converter);
    conversionLink.setEnabled(true);
    tgt.add("Link",conversionLink );
    conversionLink.activate();

    nm.getOut().setValue(input);
    BStatusSyncEdgeTriggerEnum fb = (BStatusSyncEdgeTriggerEnum) tgt.get("in");
    Assert.assertEquals(fb.getValue().getOrdinal(), output);
  }

  @AfterClass
  public void tearDown() {
  }


}
