/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatusNumeric;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusNumericToNegatableHonStatusNumeric;
import com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric;

/**
 * Testing StatusNumeric to NegatableHonStatusNumeric converter
 * <AUTHOR>
 *
 */

@NiagaraType


public class BStatusNumericToNegatableHonStatusNumericConverterTest extends BTestNg {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusNumericToNegatableHonStatusNumericConverterTest(**********)1.0$ @*/
/* Generated Mon Aug 25 20:14:41 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusNumericToNegatableHonStatusNumericConverterTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] { { Double.valueOf("23") }, { Double.valueOf("-23") }, { Double.valueOf("0") },
			{ Double.valueOf("0.0") }, { Double.valueOf("-0.0") }, { Double.valueOf("23.23") },
			{ Double.valueOf("-23.23") }, { Double.POSITIVE_INFINITY }, { Double.NEGATIVE_INFINITY },
			{ Double.NaN } };		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(double data) {
		BComponent source = new BComponent();
		BStatusNumeric statusNumeric = new BStatusNumeric(data);
		source.add("statusNumeric", statusNumeric);
		
		BComponent target = new BComponent();
		BNegatableHonStatusNumeric honStatusNumeric = new BNegatableHonStatusNumeric();
		target.add("honStatusNumeric", honStatusNumeric);
		
		BStatusNumericToNegatableHonStatusNumeric snTohsn = new BStatusNumericToNegatableHonStatusNumeric();
		BConversionLink conversionLink = new BConversionLink(source, source.getSlot("statusNumeric"), target.getSlot("honStatusNumeric"), snTohsn);
		conversionLink.setEnabled(true);
		target.add("Link?",conversionLink );				
		conversionLink.activate();
		
		Assert.assertEquals(honStatusNumeric.getValue(), statusNumeric.getValue());
	}

}
