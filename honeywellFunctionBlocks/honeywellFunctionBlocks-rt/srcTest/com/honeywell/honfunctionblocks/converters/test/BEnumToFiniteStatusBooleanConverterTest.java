/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BMonth;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BEnumToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType

public class BEnumToFiniteStatusBooleanConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BEnumToFiniteStatusBooleanConverterTest(2979906276)1.0$ @*/
/* Generated Mon Nov 27 17:34:19 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BEnumToFiniteStatusBooleanConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	BDynamicEnum d1;
  	BDynamicEnum d2;
  	BDynamicEnum d3;
  
	@BeforeClass(alwaysRun=true)
	public void setUp() {
	}
	
	
	
	@Test
	public void testConverterWithFrozenEnum() {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
				
//		BEnumWritable src = new BEnumWritable();
//		src.setFacets(BFacets.makeEnum(BEnumRange.make(BMonth.TYPE)));		
		
		BComponent src = new BComponent();
		src.add("out", BMonth.april);
		
		BConverter converter = new BEnumToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();				
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), true);
	}
	
	
	@Test
	public void testConverterWithDynamicEnum() {
		BEnumRange enumRange = BEnumRange.make(new int[]{-1,0,1},new String[] {"minus","zero","plus"});
		d1 = BDynamicEnum.make(-1, enumRange);
		d2 = BDynamicEnum.make(0, enumRange);
		d3 = BDynamicEnum.make(1, enumRange);
		
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
				
//		BEnumWritable src = new BEnumWritable();
//		src.setFacets(BFacets.makeEnum(enumRange));
		
		BComponent src = new BComponent();
		src.add("out", d1);
		
		BConverter converter = new BEnumToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		src.set("out", d1);
		Assert.assertEquals(fb.getBoolean(), true);		
		src.set("out", d2);		
		Assert.assertEquals(fb.getBoolean(), false);
		src.set("out", d3);	
		Assert.assertEquals(fb.getBoolean(), true);
	}	
	
	
	@AfterClass
	public void tearDown() {
		
	}

}
