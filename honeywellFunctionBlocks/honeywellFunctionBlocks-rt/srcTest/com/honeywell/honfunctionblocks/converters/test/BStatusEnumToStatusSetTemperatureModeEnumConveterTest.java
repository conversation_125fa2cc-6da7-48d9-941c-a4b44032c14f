/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusSetTemperatureModeEnumConveter;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BStatusSetTemperatureModeEnum;
import com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum;

/**
 * Implementation Set Temperature Mode of as per FB SDD rev26
 * Requirement ID: F1PLT-ADR-405
 * <AUTHOR> - Suresh Khatri
 * @since Feb 19, 2018
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStatusEnumToStatusSetTemperatureModeEnumConveterTest extends BTestNg{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusEnumToStatusSetTemperatureModeEnumConveterTest(**********)1.0$ @*/
/* Generated Mon Feb 19 18:22:49 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusEnumToStatusSetTemperatureModeEnumConveterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
	@Override
	@BeforeClass
	public void setup() {
	}
	
	@AfterClass
	private void tearDown() {
	}

	@DataProvider(name="provideTestDataForSystemSwitchEnum")
	public Object[][] provideTestDataForSystemSwitchEnum(){
		return new Object[][] {
			{ -5.0,BSystemSwitchEnum.Ss_Others },
			{ 0.0,BSystemSwitchEnum.Ss_Auto },
			{ 1.0,BSystemSwitchEnum.Ss_Cool },
			{ 2.0,BSystemSwitchEnum.Ss_Heat },
			{ 3.0,BSystemSwitchEnum.Ss_Emerg_Heat },
			{ 50.0,BSystemSwitchEnum.Ss_Eff_Heat_Mode },
			{ 255,BSystemSwitchEnum.Ss_Off },
			{ 1000,BSystemSwitchEnum.Ss_Others }
		};		
	}
	
	@Test(dataProvider="provideTestDataForSystemSwitchEnum")
	public void testConverterForSystemSwitchEnum(double d,BSystemSwitchEnum schedStateEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusSetTemperatureModeEnum(BSystemSwitchEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BSystemSwitchEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusSetTemperatureModeEnumConveter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusSetTemperatureModeEnum fb = (BStatusSetTemperatureModeEnum)c.get("in1");
		Assert.assertEquals(BSystemSwitchEnum.make(fb.getValue().getOrdinal()), schedStateEnum);		
	}

	
	
	@DataProvider(name="provideTestDataForCommandModeEnum")
	public Object[][] provideTestDataForCommandModeEnum(){
		return new Object[][] {
			{ -5.0,BCommandModeEnum.Cmd_Auto_Mode },
			{ 0.0,BCommandModeEnum.Cmd_Auto_Mode },
			{ 1.0,BCommandModeEnum.Cmd_Heat_Mode },
			{ 2.0,BCommandModeEnum.Cmd_Cool_Mode },
			{ 3.0,BCommandModeEnum.Cmd_Off_Mode},
			{ 4.0,BCommandModeEnum.Cmd_Emerg_Heat_Mode},
			{ 50.0,BCommandModeEnum.Cmd_Eff_Heat_Mode},
			{ 255,BCommandModeEnum.Cmd_Nul_Mode},
			{ 1000,BCommandModeEnum.Cmd_Auto_Mode}
		};		
	}
	
	@Test(dataProvider="provideTestDataForCommandModeEnum")
	public void testConverterForCommandModeEnum(double d,BCommandModeEnum CommandModeEnum) {
		BComponent c = new BComponent();
		c.add("in1", new BStatusSetTemperatureModeEnum(BCommandModeEnum.DEFAULT, BStatus.nullStatus));
		c.setFacets(c.getSlot("in1"), BFacets.make(BFacets.RANGE, BEnumRange.make(BCommandModeEnum.TYPE)));
		
		BComponent src = new BComponent();
		Property srcProperty = src.add("out", new BStatusEnum());
		
		BConverter converter = new BStatusEnumToStatusSetTemperatureModeEnumConveter();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),c.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		c.add("Link",conversionLink );				
		conversionLink.activate();
		
		BStatusEnum val = (BStatusEnum)src.get(srcProperty);
		val.setValue(BDynamicEnum.make((int)d));
		BStatusSetTemperatureModeEnum fb = (BStatusSetTemperatureModeEnum)c.get("in1");
		Assert.assertEquals(BCommandModeEnum.make(fb.getValue().getOrdinal()), CommandModeEnum);		
	}
}
