/**
 * 
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.BString;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStringToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType

@SuppressWarnings({ "squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845" })

public class BStringToFiniteStatusNumericConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStringToFiniteStatusNumericConverterTest(**********)1.0$ @*/
/* Generated Mon Nov 27 16:52:34 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStringToFiniteStatusNumericConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	
	
	
  	@Override
	@BeforeClass(alwaysRun=true)
	public void setup() {
	}
	
	@DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ "5.0",true },
			{ "-5.0",true },
			{ "0.0",false },
			{ "7.56",true },
			{ "-7.56",true},
			{ "+inf",false },
			{ "-inf",true },
			{ "NaN",false },
			{ "nan",false },
			{ "Nan",false },
			{ "naN",false },
			{ "false",false },
			{ "true",true },
			{ "dfsdf",false }
		};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(String s,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		BComponent src = new BComponent();
		src.add("out", BString.make(s));
	
		BConverter converter = new BStringToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(src,src.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );		
		conversionLink.activate();	
		
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);	
	}
	
	@AfterClass
	private void tearDown() {

	}
}
