/*
 *  Copyright (c) 2018 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import com.honeywell.honfunctionblocks.converters.BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter;
import com.honeywell.honfunctionblocks.fbs.logic.BOr;
import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.registry.Registry;
import javax.baja.registry.TypeInfo;
import javax.baja.status.BStatus;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

/**
 * <AUTHOR>
 * @since Aug 27,2018
 */
@NiagaraType
@SuppressWarnings({"squid:S2387", "squid:MaximumInheritanceDepth", "squid:S2160", "squid:S1213", "squid:S1845", "squid:S00103"})
public class BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverterTest(**********)1.0$ @*/
/* Generated Mon Aug 27 14:12:21 IST 2018 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @DataProvider(name="testData")
  public Object[][] provideTestData() {
    return new Object[][]{{true, BStatus.ok, false}, {false, BStatus.nullStatus, true},
        {true, BStatus.overridden, false}, {false, BStatus.disabled, true}};
  }

  @Test(dataProvider = "testData")
  public void testConverter(boolean val, BStatus status, boolean invalidVal){
    BOr or = new BOr();
    BConverter converter = new BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter();
    BConversionLink conversionLink = new BConversionLink(or, or.getSlot("OUTPUT"), or.getSlot("in1"), converter);
    conversionLink.setEnabled(true);
    or.add("Link?", conversionLink);
    conversionLink.activate();

    or.getOUTPUT().setValue(val);
    or.getOUTPUT().setStatus(status);
    or.getOUTPUT().setIsInvalidValue(invalidVal);

    Assert.assertEquals(or.getIn1().getValue(),val);
    Assert.assertEquals(or.getIn1().getStatus(),status);
    Assert.assertEquals(or.getIn1().getIsInvalidValue(),invalidVal);

  }

  @Test
  public void createsCorrectLink(){
    BOr or = new BOr();
    Type sourceType = or.getSlot("OUTPUT").asProperty().getType();
    Type targetType = or.getSlot("in1").asProperty().getType();
    Assert.assertFalse(sourceType.is(targetType));
    Registry registry = Sys.getRegistry();
    TypeInfo[] adapters = registry.getAdapters(sourceType.getTypeInfo(), targetType.getTypeInfo());

    for(int i = adapters.length - 1; i >= 0; --i) {
      if (registry.isAgent(adapters[i], BConversionLink.TYPE.getTypeInfo())) {
        Assert.assertEquals(adapters[i].getTypeName(),BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter.TYPE.getTypeName());
        break;
      }
    }

  }
}
