/*
 *  Copyright (c) 2017 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.honfunctionblocks.converters.test;

import javax.baja.control.BBooleanWritable;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BConversionLink;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import javax.baja.util.BConverter;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.honeywell.honfunctionblocks.converters.BStatusBooleanToFiniteStatusBoolean;
import com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean;

/**
 * <AUTHOR>
 *
 */

@NiagaraType


public class BStatusBooleanToFiniteStatusBooleanConveterTest extends BTestNg {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.honfunctionblocks.converters.test.BStatusBooleanToFiniteStatusBooleanConveterTest(**********)1.0$ @*/
/* Generated Mon Nov 27 14:10:43 IST 2017 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BStatusBooleanToFiniteStatusBooleanConveterTest.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@BeforeClass(alwaysRun=true)
	public void setUp() {
		
	}  
  
  @DataProvider(name="provideTestData")
	public Object[][] provideTestData(){
		return new Object[][] {
			{ true,true },
			{ false,false }
		};		
	}
	
	@Test(dataProvider="provideTestData")
	public void testConverter(boolean input,boolean expected) {
		BComponent tgt = new BComponent();
		tgt.add("in1", new BFiniteStatusBoolean());
		
		BBooleanWritable bw = new BBooleanWritable();		
		BConverter converter = new BStatusBooleanToFiniteStatusBoolean();
		BConversionLink conversionLink = new BConversionLink(bw,bw.getSlot("out"),tgt.getSlot("in1"),converter);
		conversionLink.setEnabled(true);
		tgt.add("Link",conversionLink );				
		conversionLink.activate();
		
		bw.getOut().setValue(input);
		BFiniteStatusBoolean fb = (BFiniteStatusBoolean)tgt.get("in1");
		Assert.assertEquals(fb.getBoolean(), expected);
		
	}
	
	
	
	@AfterClass
	public void tearDown() {
		
	}
}
