#
# Lexicon for the function block module.
#
help.guide.base=module://docHoneywellFunctionBlocks/doc
link.invalid.outslot = Target slot is read-only
link.invalid.inputSlot = Invalid source slot. Cannot link from block's input slot
link.invalid.targetSlot = Invalid target slot. Cannot link to this slot, it can only be configured
invalid.string.to.double.conversion.0=Invalid string to double conversion {0}

# UX and AX names
widget.outsave = OUT_SAVE
widget.negate = Negate
widget.null = null

#Pid Output Range lexicon
pidOutputRange0to100=0 to 100
pidOutputRangeMinus200to200=-200 to 200

#String Handling Changes
negatable.not = (NOT)
logarithm.displayname.ynatural = Y(NATURAL)
logarithm.displayname.ybase10 = Y(BASE10)


cannot.remove.Override.slot=Removing Override Action slot from Function Block is not allowed.

#license related messages
license.exception=Not a Honeywell licensed device, cannot execute Function Block
ipc.feature.not.licensed=Not licensed to run IPC platform
brand.not.licensed=Brand not licensed

# Engine Thread
engine.thread.starting=Starting function block engine
engine.thread.exiting.gracefully=Engine Thread exiting gracefully
hfb.feature.not.licensed=Not licensed to run Honeywell Function Block
engine.stopped.please.use.action=Engine stopped, please use action to start the INSTANCE
duplicate.instance.error=Only one instance of {0} will be allowed per IPC device