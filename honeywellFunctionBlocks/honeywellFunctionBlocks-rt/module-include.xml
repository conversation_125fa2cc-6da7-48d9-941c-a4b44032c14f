<!-- Module Include File -->
<!-- Types -->
<types>
  <!--com.honeywell.honfunctionblocks.converters-->
  <type class="com.honeywell.honfunctionblocks.converters.BBooleanToFiniteStatusBoolean" name="BooleanToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:Boolean" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BEnumToFiniteStatusBoolean" name="EnumToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:Enum" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusNumericToGenSetpointCalcEnumConverter" name="HonStatusNumericToGenSetpointCalcEnumConverter"/>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusNumericToStatusOccupancyStateEnumConverter" name="HonStatusNumericToStatusOccupancyStateEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:HonStatusNumeric" to="honeywellFunctionBlocks:StatusOccupancyStateEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusNumericToStatusSetTemperatureModeEnumConveter" name="HonStatusNumericToStatusSetTemperatureModeEnumConveter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:HonStatusNumeric" to="honeywellFunctionBlocks:StatusSetTemperatureModeEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BNumberToFiniteStatusBoolean" name="NumberToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:Number" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusBooleanToFiniteStatusBoolean" name="StatusBooleanToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusBoolean" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusBooleanToHonStatusBoolean" name="StatusBooleanToHonStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusBoolean" to="honeywellFunctionBlocks:HonStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusBooleanToNegatableStatusBoolean" name="StatusBooleanToNegatableStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusBoolean" to="honeywellFunctionBlocks:NegatableStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToFiniteStatusBoolean" name="StatusEnumToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToGenSetpointCalcEnumConverter" name="StatusEnumToGenSetpointCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:StatusGenSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusOccupancyStateEnumConverter" name="StatusEnumToStatusOccupancyStateEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:StatusOccupancyStateEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusSetTemperatureModeEnumConveter" name="StatusEnumToStatusSetTemperatureModeEnumConveter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:StatusSetTemperatureModeEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToFiniteStatusBoolean" name="StatusNumericToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToGenSetpointCalcEnumConverter" name="StatusNumericToGenSetpointCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:StatusGenSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToHonStatusNumeric" name="StatusNumericToHonStatusNumeric">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:HonStatusNumeric"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToNegatableHonStatusNumeric" name="StatusNumericToNegatableHonStatusNumeric">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:NegatableHonStatusNumeric"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToNegatableStatusBoolean" name="StatusNumericToNegatableStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:NegatableStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusOccupancyStateEnumConverter" name="StatusNumericToStatusOccupancyStateEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:StatusOccupancyStateEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusSetTemperatureModeEnumConveter" name="StatusNumericToStatusSetTemperatureModeEnumConveter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:StatusSetTemperatureModeEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusStringToFiniteStatusBoolean" name="StatusStringToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusString" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStringToFiniteStatusBoolean" name="StringToFiniteStatusBoolean">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:String" to="honeywellFunctionBlocks:FiniteStatusBoolean"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusNumericToStatusSyncEdgeTriggerEnumConverter" name="HonStatusNumericToStatusSyncEdgeTriggerEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:HonStatusNumeric" to="honeywellFunctionBlocks:StatusSyncEdgeTriggerEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToStatusSyncEdgeTriggerEnumConverter" name="StatusEnumToStatusSyncEdgeTriggerEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:StatusSyncEdgeTriggerEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToStatusSyncEdgeTriggerEnumConverter" name="StatusNumericToStatusSyncEdgeTriggerEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:StatusSyncEdgeTriggerEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusNumericToTempSetptCalcEnumConverter" name="HonStatusNumericToTempSetptCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:HonStatusEnum" to="honeywellFunctionBlocks:StatusTempSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusEnumToTempSetptCalcEnumConverter" name="StatusEnumToTempSetptCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusEnum" to="honeywellFunctionBlocks:StatusTempSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BStatusNumericToTempSetptCalcEnumConverter" name="StatusNumericToTempSetptCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="baja:StatusNumeric" to="honeywellFunctionBlocks:StatusTempSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BHonStatusEnumToTempSetptCalcEnumConverter" name="HonStatusEnumToTempSetptCalcEnumConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:HonStatusEnum" to="honeywellFunctionBlocks:StatusTempSetpointCalcEnum"/>
  </type>
  <type class="com.honeywell.honfunctionblocks.converters.BNegatableStatusBooleanToNegatableFiniteStatusBooleanConverter" name="NegatableStatusBooleanToNegatableFiniteStatusBooleanConverter">
    <agent requiredPermissions="w">
      <on type="baja:ConversionLink"/>
    </agent>
    <adapter from="honeywellFunctionBlocks:NegatableStatusBoolean" to="honeywellFunctionBlocks:NegatableFiniteStatusBoolean"/>
  </type>
  <!--com.honeywell.honfunctionblocks.datatypes-->
  <type class="com.honeywell.honfunctionblocks.datatypes.BFiniteStatusBoolean" name="FiniteStatusBoolean"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BHonStatusBoolean" name="HonStatusBoolean"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BHonStatusEnum" name="HonStatusEnum"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BHonStatusNumeric" name="HonStatusNumeric"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BNegatableFiniteStatusBoolean" name="NegatableFiniteStatusBoolean"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BNegatableHonStatusNumeric" name="NegatableHonStatusNumeric"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BNegatableStatusBoolean" name="NegatableStatusBoolean"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BStatusGenSetpointCalcEnum" name="StatusGenSetpointCalcEnum"/>
  <type class="com.honeywell.honfunctionblocks.datatypes.BStatusOccupancyStateEnum" name="StatusOccupancyStateEnum"/>
  <!--com.honeywell.honfunctionblocks.enums-->
  <type class="com.honeywell.honfunctionblocks.enums.BAiaDirectionEnum" name="AiaDirectionEnum"/>
  <type class="com.honeywell.honfunctionblocks.enums.BLeadLagStrategyEnum" name="LeadLagStrategyEnum"/>
  <type class="com.honeywell.honfunctionblocks.enums.BPidOutputRangeEnum" name="PidOutputRangeEnum"/>
  <!--com.honeywell.honfunctionblocks.fbs.analog-->
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BAnalogLatch" name="AnalogLatch"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BAverage" name="Average"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BCompare" name="Compare"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BCompareOperationEnum" name="CompareOperationEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BDecisionBox" name="DecisionBox"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BDecisionOperationEnum" name="DecisionOperationEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BEdge" name="Edge"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BEncode" name="Encode"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BHystereticRelay" name="HystereticRelay"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BMaximum" name="Maximum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BMinimum" name="Minimum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BMinMaxAverageBlock" name="MinMaxAverageBlock"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BPrioritySelect" name="PrioritySelect"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BSelect" name="Select"/>
  <type class="com.honeywell.honfunctionblocks.fbs.analog.BSwitch" name="Switch"/>
  <!--com.honeywell.honfunctionblocks.fbs-->
  <type class="com.honeywell.honfunctionblocks.fbs.BFunctionBlock" name="FunctionBlock"/>
  <type class="com.honeywell.honfunctionblocks.fbs.BExecutionParams" name="ExecutionParams"/>
  <type class="com.honeywell.honfunctionblocks.fbs.BFBOverrideAction" name="FBOverrideAction"/>
  <type class="com.honeywell.honfunctionblocks.fbs.BFBOverrideProperties" name="FBOverrideProperties"/>
  <!--com.honeywell.honfunctionblocks.fbs.control-->
  <type class="com.honeywell.honfunctionblocks.fbs.control.BAia" name="Aia"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BCycler" name="Cycler"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BDirectReverseSignOfTREnum" name="DirectReverseSignOfTREnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BFlowControl" name="FlowControl"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BFlowControlUnitEnum" name="FlowControlUnitEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BFOFOStrategy" name="FOFOStrategy"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BHVACOverrideEnum" name="HVACOverrideEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BPid" name="Pid"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BRateLimit" name="RateLimit"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BRuntimeStrategy" name="RuntimeStrategy"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BStageDriver" name="StageDriver"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BStager" name="Stager"/>
  <type class="com.honeywell.honfunctionblocks.fbs.control.BStageStaus" name="StageStaus"/>
  <!--com.honeywell.honfunctionblocks.fbs.datafunction-->
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.BCounter" name="Counter"/>
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.BOverride" name="Override"/>
  <type class="com.honeywell.honfunctionblocks.fbs.datafunction.BRuntimeAccumulate" name="RuntimeAccumulate"/>
  <!--com.honeywell.honfunctionblocks.fbs.logic-->
  <type class="com.honeywell.honfunctionblocks.fbs.logic.BAnd" name="And"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.BLogicBlock" name="LogicBlock"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.BOneShot" name="OneShot"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.BOr" name="Or"/>
  <type class="com.honeywell.honfunctionblocks.fbs.logic.BXor" name="Xor"/>
  <!--com.honeywell.honfunctionblocks.fbs.math-->
  <type class="com.honeywell.honfunctionblocks.fbs.math.BAdd" name="Add"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BArithmetic" name="Arithmetic"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BDigitalFilter" name="DigitalFilter"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BDivide" name="Divide"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BDivOperationEnum" name="DivOperationEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BEnthalpy" name="Enthalpy"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BExponential" name="Exponential"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BFlowVelocity" name="FlowVelocity"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BLimit" name="Limit"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BLogarithm" name="Logarithm"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BLogTypeEnum" name="LogTypeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BMultiply" name="Multiply"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BRatio" name="Ratio"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BReset" name="Reset"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BSquareRoot" name="SquareRoot"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BSubtract" name="Subtract"/>
  <type class="com.honeywell.honfunctionblocks.fbs.math.BTailOperationEnum" name="TailOperationEnum"/>
  <!--com.honeywell.honfunctionblocks.fbs.math.enums-->
  <type class="com.honeywell.honfunctionblocks.fbs.math.enums.BRatioOperationEnum" name="RatioOperationEnum"/>
  <!--com.honeywell.honfunctionblocks.fbs.zonecontrol-->
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BEffectiveOccupancyEnum" name="EffectiveOccupancyEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BGeneralSetpointCalculator" name="GeneralSetpointCalculator"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BNetworkLastInWinsEnum" name="NetworkLastInWinsEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancyArbitrator" name="OccupancyArbitrator"/>
  <!--com.honeywell.honfunctionblocks.fbs.enums-->
  <type class="com.honeywell.honfunctionblocks.fbs.enums.BOccupancyEnum" name="OccupancyEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.enums.BCompareEnum" name="CompareEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.enums.BCompareStatusEnum" name="CompareStatusEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorOperationEnum" name="OccupancySensorOperationEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BOccupancySensorStateEnum" name="OccupancySensorStateEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BScheduledStateEnum" name="ScheduledStateEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetpointTypeEnum" name="SetpointTypeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BSetTemperatureMode" name="SetTemperatureMode"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BTemperatureSetpointCalculator" name="TemperatureSetpointCalculator"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.BStatusTempSetpointCalcEnum" name="StatusTempSetpointCalcEnum"/>
  <!--com.honeywell.honfunctionblocks.fbs.zonecontrol.enums-->
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BCommandModeEnum" name="CommandModeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BControlTypeEnum" name="ControlTypeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BEffTempModeEnum" name="EffTempModeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BStatusSetTemperatureModeEnum" name="StatusSetTemperatureModeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BSystemSwitchEnum" name="SystemSwitchEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.zonecontrol.enums.BBehaviorTypeEnum" name="BehaviorTypeEnum"/>
  <!--com.honeywell.honfunctionblocks.utils-->
  <type class="com.honeywell.honfunctionblocks.utils.BPassThru" name="PassThru"/>
  <!--com.honeywell.honfunctionblocks.fbs.io.enums-->
  <type class="com.honeywell.honfunctionblocks.fbs.io.enums.BMotorActionEnum" name="MotorActionEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.io.enums.BSyncEdgeTriggerEnum" name="SyncEdgeTriggerEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.io.enums.BSyncTypeEnum" name="SyncTypeEnum"/>
  <type class="com.honeywell.honfunctionblocks.fbs.io.enums.BStatusSyncEdgeTriggerEnum" name="StatusSyncEdgeTriggerEnum"/>
  <!--com.honeywell.honfunctionblocks.fbs.builtin-->
  <type class="com.honeywell.honfunctionblocks.fbs.builtin.BTuncos" name="Tuncos"/>
  <!--com.honeywell.honfunctionblocks.engine-->
  <type class="com.honeywell.honfunctionblocks.engine.BHoneywellFunctionBlockEngine" name="HoneywellFunctionBlockEngine"/>
</types>