<permissions>
  <niagara-permissions-groups type="all">
    <!-- Insert any global permissions here. -->
  </niagara-permissions-groups>
  <niagara-permissions-groups type="workbench">
    <!-- Insert any workbench specific permissions here. -->
  </niagara-permissions-groups>
  <niagara-permissions-groups type="station">
    <!--<req-permission>-->
    <!--<name>NETWORK_COMMUNICATION</name>-->
    <!--<purposeKey>Outside access for Driver</purposeKey>-->
    <!--<parameters>-->
      <!--<parameter name="hosts" value="127.0.0.1"/>-->
      <!--<parameter name="ports" value="*"/>-->
      <!--<parameter name="type" value="all"/>-->
    <!--</parameters>-->
    <!--</req-permission>-->
  </niagara-permissions-groups>
</permissions>
