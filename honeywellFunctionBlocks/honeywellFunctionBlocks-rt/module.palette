<?xml version="1.0" encoding="UTF-8"?>
<bajaObjectGraph version="4.0" reversibleEncodingKeySource="none" reversibleEncodingValidator="[null.1]=" >
<p m="b=baja" t="b:UnrestrictedFolder" >
	<p n="Engine" t="b:UnrestrictedFolder" m="honfbs=honeywellFunctionBlocks">
		<p n="HonFunctionBlockEngine" t="honfbs:HoneywellFunctionBlockEngine"> </p>
	</p>
	<p n="Analog" t="b:UnrestrictedFolder">
  		<p n="AnalogLatch" t="honfbs:AnalogLatch"> </p>
  		<p n="Average" t="honfbs:Average"> </p>
  		<p n="Compare" t="honfbs:Compare"> </p>
  		<p n="DecisionBox" t="honfbs:DecisionBox"> </p>
  		<p n="Edge" t="honfbs:Edge"> </p>
  		<p n="Encode" t="honfbs:Encode"> </p>
  		<p n="HystereticRelay" t="honfbs:HystereticRelay"> </p>
  		<p n="Maximum" t="honfbs:Maximum"> </p>
		<p n="Minimum" t="honfbs:Minimum"> </p>
  		<p n="PrioritySelect" t="honfbs:PrioritySelect"> </p>
  		<p n="Select" t="honfbs:Select"> </p>
  		<p n="Switch" t="honfbs:Switch"> </p>
  	</p>
  	
  	<p n="Control" t="b:UnrestrictedFolder">
  	  	<p n="AIA" t="honfbs:Aia"></p>
  	  	<p n="Cycler" t="honfbs:Cycler"></p>
  	  	<p n="FlowControl" t="honfbs:FlowControl"></p>
  	  	<p n="PID" t="honfbs:Pid"></p>
  		<p n="RateLimit" t="honfbs:RateLimit"></p>
  	  	<p n="Stager" t="honfbs:Stager"></p>
  		<p n="StageDriver" t="honfbs:StageDriver"> </p>
  	</p>
  	
  	<p n="Logic" t="b:UnrestrictedFolder">
		<p n="AND" t="honfbs:And"> </p>
  		<p n="OneShot" t="honfbs:OneShot"> </p>
  		<p n="OR" t="honfbs:Or"> </p>
  		<p n="XOR" t="honfbs:Xor"> </p>
  	</p>

  	<p n="Math" t="b:UnrestrictedFolder">
  		<p n="Add" t="honfbs:Add"> </p>
		<p n="DigitalFilter" t="honfbs:DigitalFilter"> </p>
		<p n="Divide" t="honfbs:Divide"> </p>
		<p n="Enthalpy" t="honfbs:Enthalpy"> </p>
        <p n="Exponential" t="honfbs:Exponential"> </p>
		<p n="FlowVelocity" t="honfbs:FlowVelocity"> </p>
		<p n="Limit" t="honfbs:Limit"> </p>
		<p n="Logarithm" t="honfbs:Logarithm"> </p>
		<p n="Multiply" t="honfbs:Multiply"> </p>
		<p n="Ratio" t="honfbs:Ratio"> </p>
		<p n="Reset" t="honfbs:Reset"> </p>
		<p n="SquareRoot" t="honfbs:SquareRoot"> </p>
		<p n="Subtract" t="honfbs:Subtract"> </p>
  	</p>

	<p n="DataFunction" t="b:UnrestrictedFolder">
		<p n="Counter" t="honfbs:Counter"> </p>
        <p n="Override" t="honfbs:Override"> </p>
		<p n="RuntimeAccumulate" t="honfbs:RuntimeAccumulate"> </p>
	</p>
	
	<p n="ZoneControl" t="b:UnrestrictedFolder">
		<p n="GeneralSetpointCalculator" t="honfbs:GeneralSetpointCalculator"> </p>
		<p n="OccupancyArbitrator" t="honfbs:OccupancyArbitrator"> </p>
		<p n="SetTemperatureMode" t="honfbs:SetTemperatureMode"> </p>
		<p n="TemperatureSetpointCalculator" t="honfbs:TemperatureSetpointCalculator"> </p>
  	</p>

	<p n="Utils" t="b:UnrestrictedFolder">
  		<p n="PassThru" t="honfbs:PassThru"></p>
  	</p>
</p>
</bajaObjectGraph>
