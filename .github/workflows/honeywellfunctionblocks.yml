name: HoneywellFunctionBlocks_Build
on:
  workflow_dispatch:
    inputs:
      NIAGARA_FULL_VERSION:
        required: true
        default: '4.15.1.16'
      RELEASE_TYPE:
        required: true
        default: 'DAILY'
      TOOL_VERSION:
        required: true
        default: '5.1.0'
      OBFUSCATE_MODULES:
        required: true
        default: 'FALSE'
      
env:
  ARTIFACTORY_REPO_URL: https://artifactory-na.honeywell.com:443/artifactory
  SIGNING_REPO: 'HON-BA/bmsdevops'
  WORKFLOW_FILE: 'signing_jars.yml'
  PAT_TOKEN: "${{ secrets.PAT_TOKEN }}"
  PRODUCT_NAME: 'HoneywellFunctionBlocks'
  TOOLS_REPO_PATH: bmsdevops-generic-stable-local
  REPO_NAME: btools-generic-stable-local
  USERNAME: "${{ vars.USERNAME}}"
  PASSWORD: "${{ secrets.PASSWORD }}"
jobs:
  HoneywellFunctionBlocks_Build:
    runs-on: 'niagara_tools'
    steps:
      - name: Checkout HoneywellFunctionBlocks Repository          
        uses: actions/checkout@v4.1.0
        env:
          REPO_NAME: "HON-BA/HoneywellFunctionBlocks"
        with:
          repository: "${{ env.REPO_NAME }}"
          ref: '${{github.ref_name}}'
          token: "${{secrets.PAT_TOKEN}}"
          clean: true

      - name: Build and Upload Modules ${{github.repository}}    
        run: ".\\buildScripts\\build.bat ${{ github.event.inputs.RELEASE_TYPE }} ${{ github.event.inputs.TOOL_VERSION }} ${{ github.event.inputs.NIAGARA_FULL_VERSION }} ${{ env.PRODUCT_NAME }} ${{ env.ARTIFACTORY_REPO_URL }} ${{ env.USERNAME }} ${{ env.PASSWORD }} ${{ env.TOOLS_REPO_PATH }} ${{ env.SIGNING_REPO }} ${{ env.WORKFLOW_FILE }} ${{ env.PAT_TOKEN }} ${{ github.event.inputs.OBFUSCATE_MODULES }} ${{ env.REPO_NAME }}"
        shell: cmd

      - name: Sonar scanner
        env:
          JAVA_HOME: "C:\\Program Files\\Java\\jdk-11.0.23+9"
        run: |
          C:\sonar-scanner-4.2.0.1873-windows\bin\sonar-scanner.bat -Dsonar.projectBaseDir=${{ github.workspace }} -Dsonar.projectKey=HFB -Dsonar.projectName=HoneywellFunctionBlocks -Dsonar.branch.name=${{github.ref_name}} -Dsonar.java.binaries=**/src/** -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info -Dsonar.exclusions=**/src/doc/** -Dsonar.branch.name=${{github.ref_name}} -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} -Dsonar.login=${{ secrets.SONAR_TOKEN }} -Dsonar.projectVersion=${{ github.event.inputs.TOOL_VERSION }} 
        shell: cmd

      - name: Upload Jacoco Coverage Report to SonarQube
        env:
          JAVA_HOME: "C:\\Program Files\\Java\\jdk-11.0.23+9"
        run: |
          C:\sonar-scanner-4.2.0.1873-windows\bin\sonar-scanner.bat ^
          -Dsonar.projectBaseDir=${{ github.workspace }} ^
          -Dsonar.projectKey=HFB ^
          -Dsonar.projectName=HoneywellFunctionBlocks ^
          -Dsonar.branch.name=${{github.ref_name}} ^
          -Dsonar.java.binaries=**/src/** ^
          -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info ^
          -Dsonar.exclusions=**/src/doc/**,**/srcTest/**,**/*.js ^
          -Dsonar.branch.name=${{github.ref_name}} ^
          -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} ^
          -Dsonar.login=${{ secrets.SONAR_TOKEN }} ^
          -Dsonar.projectVersion=${{ github.event.inputs.TOOL_VERSION }} ^
          -Dsonar.coverage.jacoco.xmlReportPaths=${{ github.workspace }}/honeywellFunctionBlocks/honeywellFunctionBlocks-rt/build/reports/jacoco/niagaraTest/jacocoNiagaraTestReport.xml,${{ github.workspace }}/honeywellFunctionBlocks/honeywellFunctionBlocks-ux/build/reports/jacoco/niagaraTest/jacocoNiagaraTestReport.xml,${{ github.workspace }}/honeywellFunctionBlocks/honeywellFunctionBlocks-wb/build/reports/jacoco/niagaraTest/jacocoNiagaraTestReport.xml
        shell: cmd

      - name: Tag Repository ${{ github.repository }}
        if: github.event.inputs.RELEASE_TYPE != 'DAILY'
        env:
          TAG_NAME: ${{ env.PRODUCT_NAME }}_${{ github.event.inputs.RELEASE_TYPE }}_${{ github.event.inputs.TOOL_VERSION }}
        run: |
          git fetch --tags
          if (git tag -l $env:TAG_NAME) {
            git tag -d $env:TAG_NAME
            git push origin :refs/tags/$env:TAG_NAME
          }
          git tag $env:TAG_NAME
          git push origin $env:TAG_NAME
        shell: powershell