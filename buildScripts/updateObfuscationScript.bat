@echo off
setlocal

REM Define the obfuscation script file
set NIAGARA_HOME=%1
set OBFUSCATION_SCRIPT_FILE=%2
set OBFUSCATED_PATH=%3
set UNSIGNED_PATH=%4

REM Check if the file exists
if not exist "%OBFUSCATION_SCRIPT_FILE%" (
    echo File not found: %OBFUSCATION_SCRIPT_FILE%
    exit /b 1
)

REM Create a temporary file
set TEMP_FILE=%TEMP%\temp_obfuscation_script.txt

REM Replace JARFILE_LOCATION with OBFUSCATED_PATH, NIAGARA_HOME with %NIAGARA_HOME%, and MODULE with honeywellSylkDevice-rt.jar
(for /f "delims=" %%i in ('type "%OBFUSCATION_SCRIPT_FILE%"') do (
    set "line=%%i"
    setlocal enabledelayedexpansion
    set "line=!line:JARFILE_LOCATION=%OBFUSCATED_PATH%!"
    set "line=!line:NIAGARA_HOME\modules\MODULE=%UNSIGNED_PATH%\*.jar!"
    set "line=!line:NIAGARA_HOME=%NIAGARA_HOME%!"
    echo !line! >> "%TEMP_FILE%"
    endlocal
))

REM Move the temporary file to the original file
move /y "%TEMP_FILE%" "%OBFUSCATION_SCRIPT_FILE%"

echo Replacement complete.

endlocal