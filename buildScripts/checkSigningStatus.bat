REM ---------------------------------------------- INITIALIZE ------------------------------------------------------

set PAT_TOKEN=%1

REM ---------------------------------------------- CHECK STATUS OF BUILD ------------------------------------------------------
@echo off
echo Wait for a few seconds to ensure the build is triggered
waitfor SomethingThatIsNeverHappening /t 20 2>NUL

REM Download jq.exe if it doesn't exist
if not exist jq.exe (
    echo Downloading jq...
    curl -L -o jq.exe https://github.com/stedolan/jq/releases/download/jq-1.6/jq-win64.exe
)

REM Use jq.exe instead of jq
for /f "tokens=*" %%i in ('curl -s -H "Accept: application/vnd.github.v3+json" -H "Authorization: token %PAT_TOKEN%" https://api.github.com/repos/HON-BA/bmsdevops/actions/runs?branch=master ^| jq.exe -r ".workflow_runs[0].id"') do set RUN_ID=%%i

:CHECK_BUILD_STATUS
for /f "tokens=*" %%i in ('curl -s -H "Accept: application/vnd.github.v3+json" -H "Authorization: token %PAT_TOKEN%" https://api.github.com/repos/HON-BA/bmsdevops/actions/runs/%RUN_ID% ^| jq.exe -r ".status"') do set BUILD_STATUS=%%i

REM Print the status of the latest build
echo The status of the latest build is: %BUILD_STATUS%
if "%BUILD_STATUS%"=="completed" (
    echo Build completed successfully.
    goto CHECK_BUILD_CONCLUSION
) else if "%BUILD_STATUS%"=="in_progress" (
    echo Build is still in progress. Checking again in 2 minutes...
    waitfor SomethingThatIsNeverHappening /t 120 2>NUL
    goto CHECK_BUILD_STATUS
) else if "%BUILD_STATUS%"=="queued" (
    echo Build is still queued. Checking again in 2 minutes...
    waitfor SomethingThatIsNeverHappening /t 120 2>NUL
    goto CHECK_BUILD_STATUS
) else (
    echo Build status: %BUILD_STATUS%
    goto END
)

:CHECK_BUILD_CONCLUSION
REM Check if the build is successful
for /f "tokens=*" %%i in ('curl -s -H "Accept: application/vnd.github.v3+json" -H "Authorization: token %PAT_TOKEN%" https://api.github.com/repos/HON-BA/bmsdevops/actions/runs/%RUN_ID% ^| jq.exe -r ".conclusion"') do set BUILD_CONCLUSION=%%i

REM Print the conclusion of the latest build
echo The conclusion of the latest build is: %BUILD_CONCLUSION%
if "%BUILD_CONCLUSION%"=="success" (
    echo Build is successful.
) else if "%BUILD_CONCLUSION%"=="failure" (
    echo Build failed. Conclusion: %BUILD_CONCLUSION%
    exit /b 1
) else if "%BUILD_CONCLUSION%"=="cancelled" (
    echo Build was cancelled. Conclusion: %BUILD_CONCLUSION%
    exit /b 1
) else (
    echo Build conclusion: %BUILD_CONCLUSION%
    goto END
)

:END